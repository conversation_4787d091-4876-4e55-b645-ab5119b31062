import { Request, Response } from 'express'
import { WhatsAppService } from '../services/whatsapp.service'
import { WhatsAppConnectionRepository } from '../repositories/whatsapp-connection.repository'
import { MessageRepository } from '../repositories/message.repository'
import { ContactRepository } from '../repositories/contact.repository'
import { getWebSocketService } from '../services/websocket.service'
import { ConnectionStatus, MessageDirection, MessageStatus, MessageType } from '@prisma/client'

export class WebhookController {
  private whatsappService: WhatsAppService
  private connectionRepository: WhatsAppConnectionRepository
  private messageRepository: MessageRepository
  private contactRepository: ContactRepository

  constructor() {
    this.whatsappService = new WhatsAppService()
    this.connectionRepository = new WhatsAppConnectionRepository()
    this.messageRepository = new MessageRepository()
    this.contactRepository = new ContactRepository()
  }

  // Webhook da Evolution API
  evolutionWebhook = async (req: Request, res: Response) => {
    try {
      const webhookData = req.body
      console.log('[Webhook] Evolution API:', JSON.stringify(webhookData, null, 2))

      // Responder imediatamente para não bloquear a Evolution API
      res.status(200).json({ received: true })

      // Processar webhook em background
      this.processEvolutionWebhook(webhookData).catch(error => {
        console.error('[Webhook] Erro ao processar webhook:', error)
      })
    } catch (error) {
      console.error('[Webhook] Erro no webhook da Evolution API:', error)
      res.status(500).json({ error: 'Erro interno do servidor' })
    }
  }

  private async processEvolutionWebhook(data: any) {
    try {
      const { event, instance, data: eventData } = data

      // Buscar conexão pela instanceId
      const connection = await this.connectionRepository.findByInstanceId(instance)
      if (!connection) {
        console.warn(`[Webhook] Conexão não encontrada para instância: ${instance}`)
        return
      }

      switch (event) {
        case 'qrcode.updated':
          await this.handleQrCodeUpdate(connection.id, eventData)
          break

        case 'connection.update':
          await this.handleConnectionUpdate(connection.id, eventData)
          break

        case 'messages.upsert':
          await this.handleMessagesUpsert(connection, eventData)
          break

        case 'messages.update':
          await this.handleMessagesUpdate(connection, eventData)
          break

        case 'send.message':
          await this.handleSendMessage(connection, eventData)
          break

        default:
          console.log(`[Webhook] Evento não tratado: ${event}`)
      }
    } catch (error) {
      console.error('[Webhook] Erro ao processar evento:', error)
    }
  }

  private async handleQrCodeUpdate(connectionId: string, data: any) {
    try {
      if (data.qrcode) {
        const connection = await this.connectionRepository.updateQrCode(connectionId, data.qrcode)
        console.log(`[Webhook] QR Code atualizado para conexão: ${connectionId}`)

        // Emitir evento via WebSocket
        const wsService = getWebSocketService()
        if (wsService) {
          wsService.emitQrCodeUpdate({
            type: 'qr_code_update',
            data: { qrCode: data.qrcode },
            companyId: connection.companyId,
            connectionId
          })
        }
      }
    } catch (error) {
      console.error('[Webhook] Erro ao atualizar QR Code:', error)
    }
  }

  private async handleConnectionUpdate(connectionId: string, data: any) {
    try {
      const { state } = data
      let status: ConnectionStatus

      switch (state) {
        case 'open':
          status = ConnectionStatus.CONNECTED
          break
        case 'close':
          status = ConnectionStatus.DISCONNECTED
          break
        case 'connecting':
          status = ConnectionStatus.CONNECTING
          break
        default:
          status = ConnectionStatus.PENDING
      }

      const connection = await this.connectionRepository.updateStatus(connectionId, status)

      if (status === ConnectionStatus.CONNECTED) {
        await this.connectionRepository.clearQrCode(connectionId)
      }

      console.log(`[Webhook] Status da conexão atualizado: ${connectionId} -> ${status}`)

      // Emitir evento via WebSocket
      const wsService = getWebSocketService()
      if (wsService) {
        wsService.emitConnectionUpdate({
          type: 'connection_status',
          data: { status, state },
          companyId: connection.companyId,
          connectionId
        })
      }
    } catch (error) {
      console.error('[Webhook] Erro ao atualizar status da conexão:', error)
    }
  }

  private async handleMessagesUpsert(connection: any, data: any) {
    try {
      const messages = Array.isArray(data) ? data : [data]

      for (const messageData of messages) {
        await this.processIncomingMessage(connection, messageData)
      }
    } catch (error) {
      console.error('[Webhook] Erro ao processar mensagens recebidas:', error)
    }
  }

  private async handleMessagesUpdate(connection: any, data: any) {
    try {
      const updates = Array.isArray(data) ? data : [data]

      for (const update of updates) {
        await this.processMessageUpdate(connection, update)
      }
    } catch (error) {
      console.error('[Webhook] Erro ao processar atualizações de mensagem:', error)
    }
  }

  private async handleSendMessage(connection: any, data: any) {
    try {
      // Processar confirmação de envio de mensagem
      console.log(`[Webhook] Mensagem enviada confirmada:`, data)
    } catch (error) {
      console.error('[Webhook] Erro ao processar confirmação de envio:', error)
    }
  }

  private async processIncomingMessage(connection: any, messageData: any) {
    try {
      const { key, message, messageTimestamp, pushName } = messageData

      // Verificar se é uma mensagem de entrada (não enviada por nós)
      if (key.fromMe) {
        return // Ignorar mensagens enviadas por nós
      }

      // Extrair número do contato
      const contactPhone = this.extractPhoneNumber(key.remoteJid)
      if (!contactPhone) {
        console.warn('[Webhook] Não foi possível extrair número do contato:', key.remoteJid)
        return
      }

      // Verificar se a mensagem já existe
      const existingMessage = await this.messageRepository.findByMessageId(key.id)
      if (existingMessage) {
        console.log('[Webhook] Mensagem já existe:', key.id)
        return
      }

      // Buscar ou criar contato
      const contact = await this.contactRepository.findOrCreate({
        phone: contactPhone,
        name: pushName,
        whatsappConnectionId: connection.id,
        companyId: connection.companyId
      })

      // Determinar tipo e conteúdo da mensagem
      const { type, content } = this.extractMessageContent(message)

      // Criar mensagem
      const newMessage = await this.messageRepository.create({
        messageId: key.id,
        whatsappConnectionId: connection.id,
        contactPhone,
        contactId: contact.id,
        content,
        type,
        direction: MessageDirection.INBOUND,
        status: MessageStatus.DELIVERED,
        timestamp: new Date(messageTimestamp * 1000),
        companyId: connection.companyId,
        metadata: { evolutionData: messageData }
      })

      // Atualizar estatísticas do contato
      await this.contactRepository.updateMessageStats(contact.id, new Date())

      console.log(`[Webhook] Nova mensagem processada: ${newMessage.id}`)

      // Emitir evento via WebSocket
      const wsService = getWebSocketService()
      if (wsService) {
        wsService.emitNewMessage({
          type: 'new_message',
          data: newMessage,
          companyId: connection.companyId,
          connectionId: connection.id,
          contactPhone
        })
      }
    } catch (error) {
      console.error('[Webhook] Erro ao processar mensagem recebida:', error)
    }
  }

  private async processMessageUpdate(connection: any, updateData: any) {
    try {
      const { key, update } = updateData

      // Buscar mensagem existente
      const message = await this.messageRepository.findByMessageId(key.id)
      if (!message) {
        console.warn('[Webhook] Mensagem não encontrada para atualização:', key.id)
        return
      }

      // Atualizar status baseado no update
      if (update.status) {
        let status: MessageStatus

        switch (update.status) {
          case 1: // PENDING
            status = MessageStatus.PENDING
            break
          case 2: // SERVER_ACK (enviado)
            status = MessageStatus.SENT
            break
          case 3: // DELIVERY_ACK (entregue)
            status = MessageStatus.DELIVERED
            break
          case 4: // READ (lido)
            status = MessageStatus.READ
            break
          default:
            return
        }

        await this.messageRepository.updateStatus(message.id, status)
        console.log(`[Webhook] Status da mensagem atualizado: ${message.id} -> ${status}`)
      }
    } catch (error) {
      console.error('[Webhook] Erro ao processar atualização de mensagem:', error)
    }
  }

  private extractPhoneNumber(jid: string): string | null {
    try {
      // Remover sufixos do WhatsApp
      const phone = jid.replace('@s.whatsapp.net', '').replace('@c.us', '')
      
      // Validar se é um número válido
      if (phone && /^\d+$/.test(phone)) {
        return phone
      }
      
      return null
    } catch (error) {
      console.error('[Webhook] Erro ao extrair número de telefone:', error)
      return null
    }
  }

  private extractMessageContent(message: any): { type: MessageType; content: any } {
    try {
      // Mensagem de texto
      if (message.conversation) {
        return {
          type: MessageType.TEXT,
          content: { text: message.conversation }
        }
      }

      // Mensagem de texto estendida
      if (message.extendedTextMessage) {
        return {
          type: MessageType.TEXT,
          content: { text: message.extendedTextMessage.text }
        }
      }

      // Mensagem de imagem
      if (message.imageMessage) {
        return {
          type: MessageType.IMAGE,
          content: {
            caption: message.imageMessage.caption,
            media: {
              type: 'image',
              mimetype: message.imageMessage.mimetype,
              url: message.imageMessage.url
            }
          }
        }
      }

      // Mensagem de vídeo
      if (message.videoMessage) {
        return {
          type: MessageType.VIDEO,
          content: {
            caption: message.videoMessage.caption,
            media: {
              type: 'video',
              mimetype: message.videoMessage.mimetype,
              url: message.videoMessage.url
            }
          }
        }
      }

      // Mensagem de áudio
      if (message.audioMessage) {
        return {
          type: MessageType.AUDIO,
          content: {
            media: {
              type: 'audio',
              mimetype: message.audioMessage.mimetype,
              url: message.audioMessage.url
            }
          }
        }
      }

      // Mensagem de documento
      if (message.documentMessage) {
        return {
          type: MessageType.DOCUMENT,
          content: {
            filename: message.documentMessage.fileName,
            media: {
              type: 'document',
              mimetype: message.documentMessage.mimetype,
              url: message.documentMessage.url
            }
          }
        }
      }

      // Localização
      if (message.locationMessage) {
        return {
          type: MessageType.LOCATION,
          content: {
            latitude: message.locationMessage.degreesLatitude,
            longitude: message.locationMessage.degreesLongitude,
            address: message.locationMessage.name
          }
        }
      }

      // Contato
      if (message.contactMessage) {
        return {
          type: MessageType.CONTACT,
          content: {
            name: message.contactMessage.displayName,
            vcard: message.contactMessage.vcard
          }
        }
      }

      // Sticker
      if (message.stickerMessage) {
        return {
          type: MessageType.STICKER,
          content: {
            media: {
              type: 'sticker',
              mimetype: message.stickerMessage.mimetype,
              url: message.stickerMessage.url
            }
          }
        }
      }

      // Tipo não suportado
      return {
        type: MessageType.TEXT,
        content: { text: '[Tipo de mensagem não suportado]' }
      }
    } catch (error) {
      console.error('[Webhook] Erro ao extrair conteúdo da mensagem:', error)
      return {
        type: MessageType.TEXT,
        content: { text: '[Erro ao processar mensagem]' }
      }
    }
  }
}
