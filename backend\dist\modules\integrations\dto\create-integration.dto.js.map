{"version": 3, "file": "create-integration.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/integrations/dto/create-integration.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAYyB;AACzB,yDAAyC;AACzC,6CAAmE;AACnE,sFAAiH;AAEjH,MAAa,aAAa;IAGxB,QAAQ,CAAqD;IAK7D,MAAM,CAAU;IAKhB,WAAW,CAAU;IAKrB,SAAS,CAGP;IAKF,MAAM,CAOJ;CACH;AAlCD,sCAkCC;AA/BC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;IAC5G,IAAA,wBAAM,EAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;;+CACI;AAK7D;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACU;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDAIT;AAKF;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CAQT;AAGJ,MAAa,qBAAqB;IAGhC,aAAa,CAAS;IAItB,SAAS,CAAS;IAKlB,KAAK,CAAU;IAKf,iBAAiB,CAAU;CAC5B;AAlBD,sDAkBC;AAfC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,iCAAiC,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;;4DACW;AAItB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;wDACO;AAKlB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qCAAqC,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACI;AAKf;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,CAAC;IACxE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gEACgB;AAG7B,MAAa,YAAY;IAGvB,MAAM,CAAS;IAIf,UAAU,CAAS;IAInB,YAAY,CAAyB;IAKrC,YAAY,CAAuB;CACpC;AAjBD,oCAiBC;AAdC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC;IACjD,IAAA,uBAAK,GAAE;;4CACO;AAIf;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,4CAA4C,EAAE,CAAC;IAC1E,IAAA,0BAAQ,GAAE;;gDACQ;AAInB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,0BAAQ,GAAE;;kDAC0B;AAKrC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACwB;AAGrC,MAAa,cAAc;IAIzB,MAAM,CAAU;IAKhB,UAAU,CAAU;IAKpB,SAAS,CAAU;IAKnB,QAAQ,CAAU;CACnB;AApBD,wCAoBC;AAhBC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACS;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,CAAC;IAC1D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iDACQ;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC;IACzD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACO;AAGpB,MAAa,cAAc;IAGzB,SAAS,CAAS;IAIlB,QAAQ,CAAS;IAKjB,aAAa,CAAU;CACxB;AAbD,wCAaC;AAVC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACpD,IAAA,0BAAQ,GAAE;;iDACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IAC5C,IAAA,0BAAQ,GAAE;;gDACM;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACY;AAGzB,MAAa,UAAU;IAKrB,WAAW,CAAY;IAMvB,YAAY,CAAY;IAMxB,aAAa,CAAY;IAMzB,OAAO,CAAY;IAKnB,gBAAgB,CAAuB;CACxC;AA7BD,gCA6BC;AAxBC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACpF,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;+CACF;AAMvB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACtF,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;gDACD;AAMxB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACnF,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;iDACA;AAMzB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,6BAA6B,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IACnF,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;2CACN;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC9D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDAC4B;AAGzC,MAAa,cAAc;IAIzB,YAAY,CAAuB;IAKnC,aAAa,CAA0B;IAKvC,eAAe,CAIZ;CACJ;AAnBD,wCAmBC;AAfC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACwB;AAKnC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDAC4B;AAKvC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;8BACQ,KAAK;uDAIpB;AAGL,MAAa,oBAAoB;IAI/B,GAAG,CAAU;IAKb,MAAM,CAA+C;IAKrD,OAAO,CAA0B;IAOjC,OAAO,CAAU;IAOjB,aAAa,CAAU;IAMvB,UAAU,CAAU;IAMpB,QAAQ,CAAsD;IAK9D,MAAM,CAAU;IAKhB,WAAW,CAAU;IAMrB,YAAY,CAAyB;IAMrC,GAAG,CAAgB;IAMnB,KAAK,CAAkB;IAMvB,KAAK,CAAkB;IAMvB,OAAO,CAAc;IAMrB,WAAW,CAAkB;CAC9B;AAvFD,oDAuFC;AAnFC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,uBAAK,GAAE;;iDACK;AAKb;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;IACpG,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;;oDACG;AAKrD;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACsB;AAOjC;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;IACT,IAAA,qBAAG,EAAC,MAAM,CAAC;;qDACK;AAOjB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACxE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;2DACe;AAMvB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yCAAyC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC9F,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;wDACU;AAMpB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACzF,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,aAAa,CAAC;;sDACoC;AAK9D;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;oDACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,CAAC;IACpD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACU;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,IAAI,EAAE,qBAAqB,EAAE,CAAC;IAClG,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,qBAAqB,CAAC;8BACnB,qBAAqB;0DAAC;AAMrC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;IAC/E,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,YAAY,CAAC;8BACnB,YAAY;iDAAC;AAMnB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IACnF,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACnB,cAAc;mDAAC;AAMvB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IACnF,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACnB,cAAc;mDAAC;AAMvB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;IAC5E,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,UAAU,CAAC;8BACb,UAAU;qDAAC;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;IACjF,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,cAAc,CAAC;8BACb,cAAc;yDAAC;AAG/B,MAAa,oBAAoB;IAG/B,IAAI,CAAS;IAKb,WAAW,CAAU;IAIrB,IAAI,CAAkB;IAKtB,aAAa,CAAiB;IAK9B,MAAM,CAAuB;IAK7B,MAAM,CAAW;IAMjB,SAAS,CAAU;IAOnB,UAAU,CAAU;IAMpB,UAAU,CAAU;IAOpB,OAAO,CAAU;IAKjB,QAAQ,CAAuB;IAM/B,IAAI,CAAY;CACjB;AAjED,oDAiEC;AA9DC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IAC/E,IAAA,0BAAQ,GAAE;;kDACE;AAKb;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC3G,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACU;AAIrB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,IAAI,EAAE,oCAAe,EAAE,CAAC;IACzE,IAAA,wBAAM,EAAC,oCAAe,CAAC;;kDACF;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,mCAAmC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,iCAAY,EAAE,CAAC;IACrG,IAAA,yBAAO,GAAE;IACT,IAAA,wBAAM,EAAC,iCAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;2DACP;AAK9B;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,IAAI,EAAE,oBAAoB,EAAE,CAAC;IACtF,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,oBAAoB,CAAC;8BACzB,oBAAoB;oDAAC;AAK7B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IACjF,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;oDACK;AAMjB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,kCAAkC,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACjG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;uDACY;AAOnB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACpF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;wDACY;AAMpB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yCAAyC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC7G,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;;wDACU;AAOpB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAChG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,IAAI,CAAC;IACT,IAAA,qBAAG,EAAC,MAAM,CAAC;;qDACK;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACoB;AAM/B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;IAC1E,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;kDACT"}