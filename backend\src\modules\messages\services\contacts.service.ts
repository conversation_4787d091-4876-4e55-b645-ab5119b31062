import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Contact, ContactDocument, ContactStatus, UserRole } from '../../../database/entities';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';

export interface FindContactsOptions {
  companyId?: string;
  whatsappConnectionId?: string;
  status?: ContactStatus;
  isLead?: boolean;
  assignedTo?: string;
  tags?: string[];
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

@Injectable()
export class ContactsService {
  private readonly logger = new Logger(ContactsService.name);

  constructor(
    @InjectModel(Contact.name)
    private contactModel: Model<ContactDocument>,
  ) {}

  async create(
    createContactDto: CreateContactDto,
    currentUser: AuthenticatedUser,
  ): Promise<ContactDocument> {
    // Verificar se o contato já existe
    const existingContact = await this.contactModel.findOne({
      phoneNumber: createContactDto.phoneNumber,
      companyId: new Types.ObjectId(currentUser.companyId),
    });

    if (existingContact) {
      throw new ConflictException('Contato já existe para este número');
    }

    // Adicionar informações de auditoria
    const contactData = {
      ...createContactDto,
      companyId: new Types.ObjectId(currentUser.companyId),
      createdBy: currentUser.id,
      tags: createContactDto.tags?.map(tag => ({
        ...tag,
        id: new Types.ObjectId().toString(),
        createdAt: new Date(),
        createdBy: currentUser.id,
      })),
      source: createContactDto.source ? {
        ...createContactDto.source,
        capturedAt: new Date(),
      } : undefined,
    };

    const contact = new this.contactModel(contactData);
    const savedContact = await contact.save();

    this.logger.log(`Contact created: ${savedContact._id} for phone ${createContactDto.phoneNumber}`);
    return savedContact;
  }

  async findAll(
    options: FindContactsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    contacts: ContactDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      companyId,
      whatsappConnectionId,
      status,
      isLead,
      assignedTo,
      tags,
      search,
      page = 1,
      limit = 10,
      sortBy = 'lastMessageAt',
      sortOrder = 'desc',
    } = options;

    // Filtro base por empresa
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
    };

    // Filtros adicionais
    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      filter.companyId = new Types.ObjectId(companyId);
    }

    if (whatsappConnectionId) {
      filter.whatsappConnectionId = whatsappConnectionId;
    }

    if (status) {
      filter.status = status;
    }

    if (typeof isLead === 'boolean') {
      filter.isLead = isLead;
    }

    if (assignedTo) {
      filter.assignedTo = assignedTo;
    }

    if (tags && tags.length > 0) {
      filter['tags.name'] = { $in: tags };
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { phoneNumber: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { notes: { $regex: search, $options: 'i' } },
      ];
    }

    // Paginação
    const skip = (page - 1) * limit;

    // Ordenação
    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const [contacts, total] = await Promise.all([
      this.contactModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.contactModel.countDocuments(filter),
    ]);

    return {
      contacts,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<ContactDocument> {
    const contact = await this.contactModel.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(currentUser.companyId),
    });

    if (!contact) {
      throw new NotFoundException('Contato não encontrado');
    }

    return contact;
  }

  async findByPhone(
    phoneNumber: string,
    whatsappConnectionId: string,
    currentUser: AuthenticatedUser,
  ): Promise<ContactDocument | null> {
    return this.contactModel.findOne({
      phoneNumber,
      whatsappConnectionId,
      companyId: new Types.ObjectId(currentUser.companyId),
    });
  }

  async update(
    id: string,
    updateContactDto: UpdateContactDto,
    currentUser: AuthenticatedUser,
  ): Promise<ContactDocument> {
    const contact = await this.findOne(id, currentUser);

    // Atualizar tags se fornecidas
    if (updateContactDto.tags) {
      updateContactDto.tags = updateContactDto.tags.map(tag => ({
        ...tag,
        id: tag.id || new Types.ObjectId().toString(),
        createdAt: tag.createdAt || new Date(),
        createdBy: tag.createdBy || currentUser.id,
      }));
    }

    const updatedContact = await this.contactModel.findByIdAndUpdate(
      contact._id,
      {
        ...updateContactDto,
        updatedBy: currentUser.id,
      },
      { new: true },
    );

    this.logger.log(`Contact updated: ${id}`);
    return updatedContact!;
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const contact = await this.findOne(id, currentUser);
    
    await this.contactModel.findByIdAndDelete(contact._id);
    this.logger.log(`Contact deleted: ${id}`);
  }

  async addTag(
    id: string,
    tagName: string,
    tagColor: string,
    currentUser: AuthenticatedUser,
  ): Promise<ContactDocument> {
    const contact = await this.findOne(id, currentUser);

    // Verificar se a tag já existe
    const existingTag = contact.tags?.find(tag => tag.name === tagName);
    if (existingTag) {
      throw new ConflictException('Tag já existe para este contato');
    }

    const newTag = {
      id: new Types.ObjectId().toString(),
      name: tagName,
      color: tagColor,
      createdAt: new Date(),
      createdBy: currentUser.id,
    };

    const updatedContact = await this.contactModel.findByIdAndUpdate(
      contact._id,
      {
        $push: { tags: newTag },
        updatedBy: currentUser.id,
      },
      { new: true },
    );

    this.logger.log(`Tag added to contact: ${id} - ${tagName}`);
    return updatedContact!;
  }

  async removeTag(
    id: string,
    tagId: string,
    currentUser: AuthenticatedUser,
  ): Promise<ContactDocument> {
    const contact = await this.findOne(id, currentUser);

    const updatedContact = await this.contactModel.findByIdAndUpdate(
      contact._id,
      {
        $pull: { tags: { id: tagId } },
        updatedBy: currentUser.id,
      },
      { new: true },
    );

    this.logger.log(`Tag removed from contact: ${id} - ${tagId}`);
    return updatedContact!;
  }

  async updateLastMessage(
    phoneNumber: string,
    whatsappConnectionId: string,
    companyId: string,
  ): Promise<void> {
    await this.contactModel.updateOne(
      {
        phoneNumber,
        whatsappConnectionId,
        companyId: new Types.ObjectId(companyId),
      },
      {
        $set: {
          lastMessageAt: new Date(),
          lastInteractionAt: new Date(),
        },
        $inc: { messageCount: 1 },
      },
    );
  }

  async getContactStats(currentUser: AuthenticatedUser): Promise<{
    total: number;
    active: number;
    leads: number;
    blocked: number;
    archived: number;
  }> {
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };

    const [total, active, leads, blocked, archived] = await Promise.all([
      this.contactModel.countDocuments(companyFilter),
      this.contactModel.countDocuments({ ...companyFilter, status: ContactStatus.ACTIVE }),
      this.contactModel.countDocuments({ ...companyFilter, isLead: true }),
      this.contactModel.countDocuments({ ...companyFilter, status: ContactStatus.BLOCKED }),
      this.contactModel.countDocuments({ ...companyFilter, status: ContactStatus.ARCHIVED }),
    ]);

    return { total, active, leads, blocked, archived };
  }

  async getOrCreateContact(
    phoneNumber: string,
    whatsappConnectionId: string,
    companyId: string,
    name?: string,
  ): Promise<ContactDocument> {
    let contact = await this.contactModel.findOne({
      phoneNumber,
      whatsappConnectionId,
      companyId: new Types.ObjectId(companyId),
    });

    if (!contact) {
      contact = new this.contactModel({
        phoneNumber,
        whatsappConnectionId,
        companyId: new Types.ObjectId(companyId),
        name,
        status: ContactStatus.ACTIVE,
        isLead: false,
        messageCount: 0,
      });
      
      await contact.save();
      this.logger.log(`Auto-created contact for phone: ${phoneNumber}`);
    } else if (name && !contact.name) {
      // Atualizar nome se não existir
      contact.name = name;
      await contact.save();
    }

    return contact;
  }
}
