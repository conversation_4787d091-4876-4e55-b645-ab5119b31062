{"version": 3, "file": "Token.js", "sourceRoot": "", "sources": ["../../src/parser/Token.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D;;GAEG;AACH,IAAY,SA4KX;AA5KD,WAAY,SAAS;IACnB;;;OAGG;IACH,wDAAiB,CAAA;IAEjB;;;;OAIG;IACH,kDAAc,CAAA;IAEd;;OAEG;IACH,kDAAc,CAAA;IAEd;;OAEG;IACH,sDAAgB,CAAA;IAEhB;;;OAGG;IACH,oEAAuB,CAAA;IAEvB;;OAEG;IACH,8CAAY,CAAA;IAEZ;;;OAGG;IACH,sDAAgB,CAAA;IAEhB;;;OAGG;IACH,oDAAe,CAAA;IAEf;;;OAGG;IACH,0DAAkB,CAAA;IAElB;;;OAGG;IACH,gDAAa,CAAA;IAEb;;;OAGG;IACH,0DAAkB,CAAA;IAElB;;;OAGG;IACH,0DAAkB,CAAA;IAElB;;;OAGG;IACH,8CAAY,CAAA;IAEZ;;;OAGG;IACH,gDAAa,CAAA;IAEb;;;OAGG;IACH,gDAAa,CAAA;IAEb;;;OAGG;IACH,oEAAuB,CAAA;IAEvB;;;OAGG;IACH,sEAAwB,CAAA;IAExB;;;OAGG;IACH,oDAAe,CAAA;IAEf;;;OAGG;IACH,gDAAa,CAAA;IAEb;;;OAGG;IACH,8CAAY,CAAA;IAEZ;;;OAGG;IACH,8CAAY,CAAA;IAEZ;;;OAGG;IACH,sEAAwB,CAAA;IAExB;;;OAGG;IACH,wEAAyB,CAAA;IAEzB;;;OAGG;IACH,4CAAW,CAAA;IAEX;;;OAGG;IACH,kEAAsB,CAAA;IAEtB;;;OAGG;IACH,oEAAuB,CAAA;IAEvB;;;OAGG;IACH,0DAAkB,CAAA;IAElB;;;OAGG;IACH,4CAAW,CAAA;IAEX;;;OAGG;IACH,wDAAiB,CAAA;AACnB,CAAC,EA5KW,SAAS,yBAAT,SAAS,QA4KpB;AAED;;;;GAIG;AACH;IAgBE,eAAmB,IAAe,EAAE,KAAgB,EAAE,IAAe;QACnE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAEM,wBAAQ,GAAf;QACE,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC/B,CAAC;IACH,YAAC;AAAD,CAAC,AA5BD,IA4BC;AA5BY,sBAAK", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport type { TextRange } from './TextRange';\r\n\r\n/**\r\n * Distinguishes different types of Token objects.\r\n */\r\nexport enum TokenKind {\r\n  /**\r\n   * A token representing the end of the input.  The Token.range will be an empty range\r\n   * at the end of the provided input.\r\n   */\r\n  EndOfInput = 2001,\r\n\r\n  /**\r\n   * A token representing a virtual newline.\r\n   * The Token.range will be an empty range, because the actual newline character may\r\n   * be noncontiguous due to the doc comment delimiter trimming.\r\n   */\r\n  Newline = 2002,\r\n\r\n  /**\r\n   * A token representing one or more spaces and tabs (but not newlines or end of input).\r\n   */\r\n  Spacing = 2003,\r\n\r\n  /**\r\n   * A token representing one or more ASCII letters, numbers, and underscores.\r\n   */\r\n  AsciiWord = 2004,\r\n\r\n  /**\r\n   * A single ASCII character that behaves like punctuation, e.g. doesn't need whitespace\r\n   * around it when adjacent to a letter.  The Token.range will always be a string of length 1.\r\n   */\r\n  OtherPunctuation = 2005,\r\n\r\n  /**\r\n   * A token representing a sequence of non-ASCII printable characters that are not punctuation.\r\n   */\r\n  Other = 2006,\r\n\r\n  /**\r\n   * The backslash character `\\`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Backslash = 2007,\r\n\r\n  /**\r\n   * The less-than character `<`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  LessThan = 2008,\r\n\r\n  /**\r\n   * The greater-than character `>`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  GreaterThan = 2009,\r\n\r\n  /**\r\n   * The equals character `=`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Equals = 2010,\r\n\r\n  /**\r\n   * The single-quote character `'`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  SingleQuote = 2011,\r\n\r\n  /**\r\n   * The double-quote character `\"`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  DoubleQuote = 2012,\r\n\r\n  /**\r\n   * The slash character `/`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Slash = 2013,\r\n\r\n  /**\r\n   * The hyphen character `-`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Hyphen = 2014,\r\n\r\n  /**\r\n   * The at-sign character `@`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  AtSign = 2015,\r\n\r\n  /**\r\n   * The left curly bracket character `{`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  LeftCurlyBracket = 2016,\r\n\r\n  /**\r\n   * The right curly bracket character `}`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  RightCurlyBracket = 2017,\r\n\r\n  /**\r\n   * The backtick character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Backtick = 2018,\r\n\r\n  /**\r\n   * The period character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Period = 2019,\r\n\r\n  /**\r\n   * The colon character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Colon = 2020,\r\n\r\n  /**\r\n   * The comma character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Comma = 2021,\r\n\r\n  /**\r\n   * The left square bracket character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  LeftSquareBracket = 2022,\r\n\r\n  /**\r\n   * The right square bracket character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  RightSquareBracket = 2023,\r\n\r\n  /**\r\n   * The pipe character `|`.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Pipe = 2024,\r\n\r\n  /**\r\n   * The left parenthesis character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  LeftParenthesis = 2025,\r\n\r\n  /**\r\n   * The right parenthesis character.\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  RightParenthesis = 2026,\r\n\r\n  /**\r\n   * The pound character (\"#\").\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  PoundSymbol = 2027,\r\n\r\n  /**\r\n   * The plus character (\"+\").\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  Plus = 2028,\r\n\r\n  /**\r\n   * The dollar sign character (\"$\").\r\n   * The Token.range will always be a string of length 1.\r\n   */\r\n  DollarSign = 2029\r\n}\r\n\r\n/**\r\n * Represents a contiguous range of characters extracted from one of the doc comment lines\r\n * being processed by the Tokenizer.  There is a token representing a newline, but otherwise\r\n * a single token cannot span multiple lines.\r\n */\r\nexport class Token {\r\n  /**\r\n   * The kind of token\r\n   */\r\n  public readonly kind: TokenKind;\r\n  /**\r\n   * The contiguous input range corresponding to the token.  This range will never\r\n   * contain a newline character.\r\n   */\r\n  public readonly range: TextRange;\r\n\r\n  /**\r\n   * The doc comment \"line\" that this Token was extracted from.\r\n   */\r\n  public readonly line: TextRange;\r\n\r\n  public constructor(kind: TokenKind, range: TextRange, line: TextRange) {\r\n    this.kind = kind;\r\n    this.range = range;\r\n    this.line = line;\r\n  }\r\n\r\n  public toString(): string {\r\n    if (this.kind === TokenKind.Newline) {\r\n      return '\\n';\r\n    }\r\n    return this.range.toString();\r\n  }\r\n}\r\n"]}