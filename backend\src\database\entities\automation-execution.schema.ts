import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';

export type AutomationExecutionDocument = AutomationExecution & Document;

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused',
}

export enum StepStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
}

export interface ExecutionStep {
  stepId: string;
  stepName: string;
  status: StepStatus;
  startedAt?: Date;
  completedAt?: Date;
  duration?: number; // milliseconds
  input?: Record<string, any>;
  output?: Record<string, any>;
  error?: {
    code: string;
    message: string;
    stack?: string;
  };
  retryCount?: number;
  nextStepId?: string;
}

export interface ExecutionContext {
  contact: {
    id: string;
    phoneNumber: string;
    name?: string;
    tags?: string[];
    customFields?: Record<string, any>;
  };
  message?: {
    id: string;
    content?: string;
    type: string;
    timestamp: Date;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  connection: {
    id: string;
    phoneNumber: string;
    name: string;
  };
  variables: Record<string, any>;
  session: Record<string, any>;
}

@Schema({ timestamps: true })
export class AutomationExecution {
  @Prop({ required: true, type: Types.ObjectId, ref: 'Automation' })
  automationId: Types.ObjectId;

  @Prop({ required: true })
  automationName: string;

  @Prop({ required: true, type: Number })
  automationVersion: number;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Company' })
  companyId: Types.ObjectId;

  @Prop({ required: true, type: String, enum: ExecutionStatus, default: ExecutionStatus.PENDING })
  status: ExecutionStatus;

  @Prop({ required: true, type: mongoose.Schema.Types.Mixed })
  context: ExecutionContext;

  @Prop({ type: [mongoose.Schema.Types.Mixed], default: [] })
  steps: ExecutionStep[];

  @Prop({ type: String })
  currentStepId?: string;

  @Prop({ type: Date })
  startedAt?: Date;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Number })
  duration?: number; // milliseconds

  @Prop({ type: mongoose.Schema.Types.Mixed })
  error?: {
    code: string;
    message: string;
    stack?: string;
    stepId?: string;
  };

  @Prop({ type: Number, default: 0 })
  retryCount: number;

  @Prop({ type: Number, default: 3 })
  maxRetries: number;

  @Prop({ type: Date })
  nextRetryAt?: Date;

  @Prop({ type: String })
  triggeredBy?: string; // User ID who triggered manually

  @Prop({ type: String })
  triggerType: string;

  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  triggerData: Record<string, any>;

  // Resultados da execução
  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  results: {
    messagesSent?: number;
    tagsAdded?: string[];
    tagsRemoved?: string[];
    contactUpdated?: boolean;
    leadCreated?: boolean;
    webhooksCalled?: number;
    aiInteractions?: number;
    humanHandoff?: boolean;
  };

  // Métricas
  @Prop({ type: Number, default: 0 })
  totalSteps: number;

  @Prop({ type: Number, default: 0 })
  completedSteps: number;

  @Prop({ type: Number, default: 0 })
  failedSteps: number;

  @Prop({ type: Number, default: 0 })
  skippedSteps: number;

  // Metadados
  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: String })
  executionId: string; // Unique execution identifier

  @Prop({ type: Boolean, default: false })
  isTest: boolean;

  @Prop({ type: String })
  parentExecutionId?: string; // For nested automations

  @Prop({ type: [String], default: [] })
  childExecutionIds: string[]; // For spawned automations
}

export const AutomationExecutionSchema = SchemaFactory.createForClass(AutomationExecution);

// Índices para performance
AutomationExecutionSchema.index({ automationId: 1, status: 1 });
AutomationExecutionSchema.index({ companyId: 1, status: 1 });
AutomationExecutionSchema.index({ 'context.contact.phoneNumber': 1 });
AutomationExecutionSchema.index({ 'context.connection.id': 1 });
AutomationExecutionSchema.index({ startedAt: -1 });
AutomationExecutionSchema.index({ completedAt: -1 });
AutomationExecutionSchema.index({ executionId: 1 }, { unique: true });
AutomationExecutionSchema.index({ triggerType: 1 });
AutomationExecutionSchema.index({ triggeredBy: 1 });

// TTL para limpeza automática (manter execuções por 90 dias)
AutomationExecutionSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 }); // 90 dias

// Métodos do schema
AutomationExecutionSchema.methods.addStep = function(step: ExecutionStep): void {
  this.steps.push(step);
  this.totalSteps = this.steps.length;
};

AutomationExecutionSchema.methods.updateStep = function(stepId: string, updates: Partial<ExecutionStep>): void {
  const stepIndex = this.steps.findIndex(step => step.stepId === stepId);
  if (stepIndex !== -1) {
    Object.assign(this.steps[stepIndex], updates);
    
    // Atualizar contadores
    this.completedSteps = this.steps.filter(s => s.status === StepStatus.COMPLETED).length;
    this.failedSteps = this.steps.filter(s => s.status === StepStatus.FAILED).length;
    this.skippedSteps = this.steps.filter(s => s.status === StepStatus.SKIPPED).length;
  }
};

AutomationExecutionSchema.methods.getCurrentStep = function(): ExecutionStep | null {
  if (!this.currentStepId) return null;
  return this.steps.find(step => step.stepId === this.currentStepId) || null;
};

AutomationExecutionSchema.methods.getNextStep = function(): ExecutionStep | null {
  const currentStep = this.getCurrentStep();
  if (!currentStep || !currentStep.nextStepId) return null;
  return this.steps.find(step => step.stepId === currentStep.nextStepId) || null;
};

AutomationExecutionSchema.methods.markAsCompleted = function(): void {
  this.status = ExecutionStatus.COMPLETED;
  this.completedAt = new Date();
  this.duration = this.completedAt.getTime() - (this.startedAt?.getTime() || 0);
};

AutomationExecutionSchema.methods.markAsFailed = function(error: any): void {
  this.status = ExecutionStatus.FAILED;
  this.completedAt = new Date();
  this.duration = this.completedAt.getTime() - (this.startedAt?.getTime() || 0);
  this.error = {
    code: error.code || 'EXECUTION_FAILED',
    message: error.message || 'Execution failed',
    stack: error.stack,
    stepId: this.currentStepId,
  };
};

AutomationExecutionSchema.methods.canRetry = function(): boolean {
  return this.status === ExecutionStatus.FAILED && 
         this.retryCount < this.maxRetries &&
         (!this.nextRetryAt || new Date() >= this.nextRetryAt);
};

AutomationExecutionSchema.methods.scheduleRetry = function(delayMinutes: number = 5): void {
  this.retryCount += 1;
  this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
  this.status = ExecutionStatus.PENDING;
};

AutomationExecutionSchema.methods.getProgress = function(): number {
  if (this.totalSteps === 0) return 0;
  return (this.completedSteps / this.totalSteps) * 100;
};

AutomationExecutionSchema.methods.getDuration = function(): number {
  if (!this.startedAt) return 0;
  const endTime = this.completedAt || new Date();
  return endTime.getTime() - this.startedAt.getTime();
};
