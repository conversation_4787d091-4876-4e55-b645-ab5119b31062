"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const entities_1 = require("../../../database/entities");
class ContactResponseDto {
    id;
    phoneNumber;
    name;
    email;
    profilePicture;
    status;
    tags;
    source;
    customFields;
    lastMessageAt;
    lastInteractionAt;
    messageCount;
    isLead;
    leadScore;
    leadStage;
    assignedTo;
    notes;
    whatsappConnectionId;
    createdAt;
    updatedAt;
}
exports.ContactResponseDto = ContactResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do contato',
        example: 'contact-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj._id?.toString() || obj.id),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de telefone',
        example: '+5511999999999',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nome do contato',
        example: 'João Silva',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Email do contato',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL da foto de perfil',
        example: 'https://example.com/profile.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "profilePicture", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do contato',
        enum: entities_1.ContactStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_a = typeof entities_1.ContactStatus !== "undefined" && entities_1.ContactStatus) === "function" ? _a : Object)
], ContactResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Etiquetas do contato',
        type: [Object],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], ContactResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fonte do contato',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_b = typeof entities_1.ContactSource !== "undefined" && entities_1.ContactSource) === "function" ? _b : Object)
], ContactResponseDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Campos customizados',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ContactResponseDto.prototype, "customFields", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Última mensagem',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ContactResponseDto.prototype, "lastMessageAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Última interação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ContactResponseDto.prototype, "lastInteractionAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de mensagens',
        example: 15,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ContactResponseDto.prototype, "messageCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é um lead',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], ContactResponseDto.prototype, "isLead", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pontuação do lead',
        example: 85,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ContactResponseDto.prototype, "leadScore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Estágio do lead',
        example: 'qualificado',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "leadStage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do usuário responsável',
        example: 'uuid-do-usuario',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "assignedTo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Observações sobre o contato',
        example: 'Cliente interessado em produtos premium',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da conexão WhatsApp',
        example: 'uuid-da-conexao',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ContactResponseDto.prototype, "whatsappConnectionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ContactResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de atualização',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ContactResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=contact-response.dto.js.map