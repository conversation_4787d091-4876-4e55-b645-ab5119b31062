import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type MessageDocument = Message & Document;

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  DOCUMENT = 'document',
  LOCATION = 'location',
  CONTACT = 'contact',
  STICKER = 'sticker',
  SYSTEM = 'system',
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
}

export interface MessageMedia {
  type: string;
  url?: string;
  filename?: string;
  mimetype?: string;
  size?: number;
  caption?: string;
  thumbnail?: string;
}

export interface MessageLocation {
  latitude: number;
  longitude: number;
  address?: string;
  name?: string;
}

export interface MessageContact {
  name: string;
  phone: string;
  email?: string;
}

@Schema({ timestamps: true })
export class Message {
  @Prop({ required: true })
  messageId: string; // ID único da mensagem no WhatsApp

  @Prop({ type: String, enum: MessageType, required: true })
  type: MessageType;

  @Prop({ type: String, enum: MessageDirection, required: true })
  direction: MessageDirection;

  @Prop({ type: String, enum: MessageStatus, default: MessageStatus.PENDING })
  status: MessageStatus;

  @Prop()
  content?: string;

  @Prop({ type: Schema.Types.Mixed })
  media?: MessageMedia;

  @Prop({ type: Schema.Types.Mixed })
  location?: MessageLocation;

  @Prop({ type: Schema.Types.Mixed })
  contact?: MessageContact;

  @Prop()
  quotedMessageId?: string;

  @Prop({ type: Schema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop()
  timestamp: Date;

  @Prop()
  sentAt?: Date;

  @Prop()
  deliveredAt?: Date;

  @Prop()
  readAt?: Date;

  @Prop()
  errorMessage?: string;

  @Prop({ default: false })
  isAutomated: boolean;

  @Prop()
  automationId?: string;

  @Prop({ default: false })
  isTemplate: boolean;

  @Prop()
  templateName?: string;

  // Relacionamentos
  @Prop({ required: true, type: Types.ObjectId, ref: 'Company' })
  companyId: Types.ObjectId;

  @Prop({ required: true })
  whatsappConnectionId: string;

  @Prop({ required: true })
  contactPhone: string;

  @Prop()
  sentBy?: string; // User ID (para mensagens enviadas)

  @Prop()
  conversationId?: string;

  // Campos para análise
  @Prop({ default: false })
  isFirstMessage: boolean;

  @Prop()
  responseTime?: number; // Tempo de resposta em segundos

  @Prop({ type: [String], default: [] })
  tags: string[];
}

export const MessageSchema = SchemaFactory.createForClass(Message);

// Índices
MessageSchema.index({ messageId: 1 }, { unique: true });
MessageSchema.index({ companyId: 1, whatsappConnectionId: 1, contactPhone: 1 });
MessageSchema.index({ companyId: 1, conversationId: 1 });
MessageSchema.index({ companyId: 1, direction: 1, timestamp: -1 });
MessageSchema.index({ companyId: 1, isAutomated: 1 });
MessageSchema.index({ companyId: 1, sentBy: 1, timestamp: -1 });
MessageSchema.index({ timestamp: -1 });
MessageSchema.index({ createdAt: -1 });
