import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { ContactStatus, ContactTag, ContactSource } from '../../../database/entities';

export class ContactResponseDto {
  @ApiProperty({
    description: 'ID do contato',
    example: 'contact-id',
  })
  @Expose()
  @Transform(({ obj }) => obj._id?.toString() || obj.id)
  id: string;

  @ApiProperty({
    description: 'Número de telefone',
    example: '+5511999999999',
  })
  @Expose()
  phoneNumber: string;

  @ApiPropertyOptional({
    description: 'Nome do contato',
    example: '<PERSON>',
  })
  @Expose()
  name?: string;

  @ApiPropertyOptional({
    description: 'Email do contato',
    example: '<EMAIL>',
  })
  @Expose()
  email?: string;

  @ApiPropertyOptional({
    description: 'URL da foto de perfil',
    example: 'https://example.com/profile.jpg',
  })
  @Expose()
  profilePicture?: string;

  @ApiProperty({
    description: 'Status do contato',
    enum: ContactStatus,
  })
  @Expose()
  status: ContactStatus;

  @ApiPropertyOptional({
    description: 'Etiquetas do contato',
    type: [Object],
  })
  @Expose()
  tags?: ContactTag[];

  @ApiPropertyOptional({
    description: 'Fonte do contato',
    type: Object,
  })
  @Expose()
  source?: ContactSource;

  @ApiPropertyOptional({
    description: 'Campos customizados',
  })
  @Expose()
  customFields?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Última mensagem',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastMessageAt?: Date;

  @ApiPropertyOptional({
    description: 'Última interação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastInteractionAt?: Date;

  @ApiProperty({
    description: 'Número de mensagens',
    example: 15,
  })
  @Expose()
  messageCount: number;

  @ApiProperty({
    description: 'Se é um lead',
    example: true,
  })
  @Expose()
  isLead: boolean;

  @ApiPropertyOptional({
    description: 'Pontuação do lead',
    example: 85,
  })
  @Expose()
  leadScore?: number;

  @ApiPropertyOptional({
    description: 'Estágio do lead',
    example: 'qualificado',
  })
  @Expose()
  leadStage?: string;

  @ApiPropertyOptional({
    description: 'ID do usuário responsável',
    example: 'uuid-do-usuario',
  })
  @Expose()
  assignedTo?: string;

  @ApiPropertyOptional({
    description: 'Observações sobre o contato',
    example: 'Cliente interessado em produtos premium',
  })
  @Expose()
  notes?: string;

  @ApiProperty({
    description: 'ID da conexão WhatsApp',
    example: 'uuid-da-conexao',
  })
  @Expose()
  whatsappConnectionId: string;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;
}
