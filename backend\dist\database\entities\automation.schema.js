"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationSchema = exports.Automation = exports.ActionType = exports.TriggerType = exports.AutomationStatus = exports.AutomationType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoose = require("mongoose");
var AutomationType;
(function (AutomationType) {
    AutomationType["WELCOME_MESSAGE"] = "welcome_message";
    AutomationType["KEYWORD_RESPONSE"] = "keyword_response";
    AutomationType["CHATBOT_FLOW"] = "chatbot_flow";
    AutomationType["SCHEDULED_MESSAGE"] = "scheduled_message";
    AutomationType["FOLLOW_UP"] = "follow_up";
    AutomationType["LEAD_QUALIFICATION"] = "lead_qualification";
    AutomationType["AWAY_MESSAGE"] = "away_message";
    AutomationType["BUSINESS_HOURS"] = "business_hours";
})(AutomationType || (exports.AutomationType = AutomationType = {}));
var AutomationStatus;
(function (AutomationStatus) {
    AutomationStatus["ACTIVE"] = "active";
    AutomationStatus["INACTIVE"] = "inactive";
    AutomationStatus["DRAFT"] = "draft";
    AutomationStatus["PAUSED"] = "paused";
})(AutomationStatus || (exports.AutomationStatus = AutomationStatus = {}));
var TriggerType;
(function (TriggerType) {
    TriggerType["MESSAGE_RECEIVED"] = "message_received";
    TriggerType["KEYWORD_MATCH"] = "keyword_match";
    TriggerType["FIRST_MESSAGE"] = "first_message";
    TriggerType["SCHEDULE"] = "schedule";
    TriggerType["WEBHOOK"] = "webhook";
    TriggerType["MANUAL"] = "manual";
    TriggerType["CONTACT_CREATED"] = "contact_created";
    TriggerType["TAG_ADDED"] = "tag_added";
    TriggerType["INACTIVITY"] = "inactivity";
})(TriggerType || (exports.TriggerType = TriggerType = {}));
var ActionType;
(function (ActionType) {
    ActionType["SEND_MESSAGE"] = "send_message";
    ActionType["ADD_TAG"] = "add_tag";
    ActionType["REMOVE_TAG"] = "remove_tag";
    ActionType["ASSIGN_USER"] = "assign_user";
    ActionType["CREATE_LEAD"] = "create_lead";
    ActionType["UPDATE_CONTACT"] = "update_contact";
    ActionType["WEBHOOK_CALL"] = "webhook_call";
    ActionType["WAIT"] = "wait";
    ActionType["CONDITION"] = "condition";
    ActionType["AI_RESPONSE"] = "ai_response";
    ActionType["TRANSFER_TO_HUMAN"] = "transfer_to_human";
})(ActionType || (exports.ActionType = ActionType = {}));
let Automation = class Automation {
    name;
    description;
    type;
    status;
    companyId;
    createdBy;
    updatedBy;
    trigger;
    actions;
    flowSteps;
    startStepId;
    settings;
    executionCount;
    successCount;
    errorCount;
    lastExecutedAt;
    lastModifiedAt;
    version;
    isTemplate;
    templateCategory;
    metadata;
};
exports.Automation = Automation;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Automation.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Automation.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: AutomationType }),
    __metadata("design:type", String)
], Automation.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: AutomationStatus, default: AutomationStatus.DRAFT }),
    __metadata("design:type", String)
], Automation.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Types.ObjectId, ref: 'Company' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Automation.prototype, "companyId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], Automation.prototype, "createdBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], Automation.prototype, "updatedBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, required: true }),
    __metadata("design:type", Object)
], Automation.prototype, "trigger", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [mongoose.Schema.Types.Mixed], default: [] }),
    __metadata("design:type", Array)
], Automation.prototype, "actions", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [mongoose.Schema.Types.Mixed], default: [] }),
    __metadata("design:type", Array)
], Automation.prototype, "flowSteps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], Automation.prototype, "startStepId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], Automation.prototype, "settings", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], Automation.prototype, "executionCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], Automation.prototype, "successCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], Automation.prototype, "errorCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], Automation.prototype, "lastExecutedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], Automation.prototype, "lastModifiedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 1 }),
    __metadata("design:type", Number)
], Automation.prototype, "version", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Boolean, default: false }),
    __metadata("design:type", Boolean)
], Automation.prototype, "isTemplate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], Automation.prototype, "templateCategory", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, default: {} }),
    __metadata("design:type", Object)
], Automation.prototype, "metadata", void 0);
exports.Automation = Automation = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Automation);
exports.AutomationSchema = mongoose_1.SchemaFactory.createForClass(Automation);
exports.AutomationSchema.index({ companyId: 1, status: 1 });
exports.AutomationSchema.index({ companyId: 1, type: 1 });
exports.AutomationSchema.index({ 'trigger.type': 1, status: 1 });
exports.AutomationSchema.index({ 'trigger.keywords': 1 });
exports.AutomationSchema.index({ createdBy: 1 });
exports.AutomationSchema.index({ lastExecutedAt: -1 });
exports.AutomationSchema.index({ executionCount: -1 });
exports.AutomationSchema.methods.canExecute = function (contact, message) {
    if (this.status !== AutomationStatus.ACTIVE) {
        return false;
    }
    if (this.settings.businessHoursOnly) {
        const now = new Date();
        const businessHours = this.settings.businessHours;
        if (businessHours) {
            const currentHour = now.getHours();
            const currentDay = now.getDay();
            const startHour = parseInt(businessHours.start.split(':')[0]);
            const endHour = parseInt(businessHours.end.split(':')[0]);
            if (!businessHours.days.includes(currentDay) ||
                currentHour < startHour ||
                currentHour >= endHour) {
                return false;
            }
        }
    }
    if (this.settings.cooldownMinutes && this.lastExecutedAt) {
        const cooldownMs = this.settings.cooldownMinutes * 60 * 1000;
        if (Date.now() - this.lastExecutedAt.getTime() < cooldownMs) {
            return false;
        }
    }
    if (this.settings.excludedContacts?.includes(contact.phoneNumber)) {
        return false;
    }
    return true;
};
exports.AutomationSchema.methods.incrementExecution = function (success = true) {
    this.executionCount += 1;
    if (success) {
        this.successCount += 1;
    }
    else {
        this.errorCount += 1;
    }
    this.lastExecutedAt = new Date();
};
exports.AutomationSchema.methods.getSuccessRate = function () {
    if (this.executionCount === 0)
        return 0;
    return (this.successCount / this.executionCount) * 100;
};
//# sourceMappingURL=automation.schema.js.map