import {
  IsString,
  IsEmail,
  IsOptional,
  IsPhoneNumber,
  IsBoolean,
  IsArray,
  IsObject,
  IsEnum,
  IsNumber,
  Min,
  Max,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ContactStatus {
  ACTIVE = 'active',
  BLOCKED = 'blocked',
  ARCHIVED = 'archived',
}

export class ContactTagDto {
  @ApiPropertyOptional({
    description: 'ID da etiqueta',
    example: 'tag-id',
  })
  @IsOptional()
  @IsString({ message: 'ID da etiqueta deve ser uma string' })
  id?: string;

  @ApiProperty({
    description: 'Nome da etiqueta',
    example: 'Cliente VIP',
  })
  @IsString({ message: 'Nome da etiqueta deve ser uma string' })
  name: string;

  @ApiProperty({
    description: 'Cor da etiqueta',
    example: '#FF5733',
  })
  @IsString({ message: 'Cor da etiqueta deve ser uma string' })
  color: string;

  @ApiPropertyOptional({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @IsOptional()
  createdAt?: Date;

  @ApiPropertyOptional({
    description: 'Criado por',
    example: 'user-id',
  })
  @IsOptional()
  @IsString({ message: 'Criado por deve ser uma string' })
  createdBy?: string;
}

export class ContactSourceDto {
  @ApiProperty({
    description: 'Tipo da fonte',
    example: 'google_ads',
  })
  @IsString({ message: 'Tipo da fonte deve ser uma string' })
  type: string;

  @ApiPropertyOptional({
    description: 'Campanha',
    example: 'Campanha Black Friday',
  })
  @IsOptional()
  @IsString({ message: 'Campanha deve ser uma string' })
  campaign?: string;

  @ApiPropertyOptional({
    description: 'Meio',
    example: 'cpc',
  })
  @IsOptional()
  @IsString({ message: 'Meio deve ser uma string' })
  medium?: string;

  @ApiPropertyOptional({
    description: 'Fonte',
    example: 'google',
  })
  @IsOptional()
  @IsString({ message: 'Fonte deve ser uma string' })
  source?: string;

  @ApiPropertyOptional({
    description: 'Parâmetros UTM',
    example: { utm_campaign: 'black-friday', utm_medium: 'cpc' },
  })
  @IsOptional()
  @IsObject({ message: 'Parâmetros UTM devem ser um objeto' })
  utm_params?: Record<string, string>;

  @ApiPropertyOptional({
    description: 'Referrer',
    example: 'https://google.com',
  })
  @IsOptional()
  @IsString({ message: 'Referrer deve ser uma string' })
  referrer?: string;
}

export class CreateContactDto {
  @ApiProperty({
    description: 'Número de telefone',
    example: '+5511999999999',
  })
  @IsPhoneNumber('BR', { message: 'Número de telefone deve ser válido' })
  phoneNumber: string;

  @ApiPropertyOptional({
    description: 'Nome do contato',
    example: 'João Silva',
  })
  @IsOptional()
  @IsString({ message: 'Nome deve ser uma string' })
  name?: string;

  @ApiPropertyOptional({
    description: 'Email do contato',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  email?: string;

  @ApiPropertyOptional({
    description: 'URL da foto de perfil',
    example: 'https://example.com/profile.jpg',
  })
  @IsOptional()
  @IsString({ message: 'URL da foto deve ser uma string' })
  profilePicture?: string;

  @ApiProperty({
    description: 'Status do contato',
    enum: ContactStatus,
    default: ContactStatus.ACTIVE,
  })
  @IsEnum(ContactStatus, { message: 'Status deve ser um valor válido' })
  status: ContactStatus;

  @ApiPropertyOptional({
    description: 'Etiquetas do contato',
    type: [ContactTagDto],
  })
  @IsOptional()
  @IsArray({ message: 'Etiquetas devem ser um array' })
  @ValidateNested({ each: true })
  @Type(() => ContactTagDto)
  tags?: ContactTagDto[];

  @ApiPropertyOptional({
    description: 'Fonte do contato',
    type: ContactSourceDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactSourceDto)
  source?: ContactSourceDto;

  @ApiPropertyOptional({
    description: 'Campos customizados',
    example: { empresa: 'Tech Corp', cargo: 'Desenvolvedor' },
  })
  @IsOptional()
  @IsObject({ message: 'Campos customizados devem ser um objeto' })
  customFields?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Se é um lead',
    example: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'isLead deve ser um boolean' })
  isLead?: boolean;

  @ApiPropertyOptional({
    description: 'Pontuação do lead',
    example: 85,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Pontuação do lead deve ser um número' })
  @Min(0, { message: 'Pontuação deve ser maior ou igual a 0' })
  @Max(100, { message: 'Pontuação deve ser menor ou igual a 100' })
  leadScore?: number;

  @ApiPropertyOptional({
    description: 'Estágio do lead',
    example: 'qualificado',
  })
  @IsOptional()
  @IsString({ message: 'Estágio do lead deve ser uma string' })
  leadStage?: string;

  @ApiPropertyOptional({
    description: 'ID do usuário responsável',
    example: 'uuid-do-usuario',
  })
  @IsOptional()
  @IsString({ message: 'ID do usuário deve ser uma string' })
  assignedTo?: string;

  @ApiPropertyOptional({
    description: 'Observações sobre o contato',
    example: 'Cliente interessado em produtos premium',
  })
  @IsOptional()
  @IsString({ message: 'Observações devem ser uma string' })
  notes?: string;

  @ApiProperty({
    description: 'ID da conexão WhatsApp',
    example: 'uuid-da-conexao',
  })
  @IsString({ message: 'ID da conexão deve ser uma string' })
  whatsappConnectionId: string;
}
