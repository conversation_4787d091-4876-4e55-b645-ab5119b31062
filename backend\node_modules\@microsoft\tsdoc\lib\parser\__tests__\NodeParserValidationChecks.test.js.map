{"version": 3, "file": "NodeParserValidationChecks.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserValidationChecks.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,IAAI,CAAC,oCAAoC,EAAE;IACzC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACtE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oCAAoC,EAAE;IACzC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjE,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Deprecated block: positive test', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * @deprecated', ' * Use the other thing', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 Deprecated block: negative test', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * @deprecated', ' * ', ' * @public', ' */'].join('\\n')\r\n  );\r\n});\r\n"]}