"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const whatsapp_service_1 = require("./whatsapp.service");
const create_connection_dto_1 = require("./dto/create-connection.dto");
const update_connection_dto_1 = require("./dto/update-connection.dto");
const send_message_dto_1 = require("./dto/send-message.dto");
const connection_response_dto_1 = require("./dto/connection-response.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const tenant_guard_1 = require("../../common/guards/tenant.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const public_decorator_1 = require("../../common/decorators/public.decorator");
const entities_1 = require("../../database/entities");
let WhatsAppController = class WhatsAppController {
    whatsappService;
    constructor(whatsappService) {
        this.whatsappService = whatsappService;
    }
    async createConnection(createConnectionDto, currentUser) {
        const connection = await this.whatsappService.createConnection(createConnectionDto, currentUser);
        return (0, class_transformer_1.plainToClass)(connection_response_dto_1.ConnectionResponseDto, connection, { excludeExtraneousValues: true });
    }
    async findAllConnections(query, currentUser) {
        const result = await this.whatsappService.findAll(query, currentUser);
        return {
            ...result,
            connections: result.connections.map(connection => (0, class_transformer_1.plainToClass)(connection_response_dto_1.ConnectionResponseDto, connection, { excludeExtraneousValues: true })),
        };
    }
    async findOneConnection(id, currentUser) {
        const connection = await this.whatsappService.findOne(id, currentUser);
        return (0, class_transformer_1.plainToClass)(connection_response_dto_1.ConnectionResponseDto, connection, { excludeExtraneousValues: true });
    }
    async updateConnection(id, updateConnectionDto, currentUser) {
        const connection = await this.whatsappService.update(id, updateConnectionDto, currentUser);
        return (0, class_transformer_1.plainToClass)(connection_response_dto_1.ConnectionResponseDto, connection, { excludeExtraneousValues: true });
    }
    async removeConnection(id, currentUser) {
        return this.whatsappService.remove(id, currentUser);
    }
    async connectWhatsApp(id, currentUser) {
        return this.whatsappService.connect(id, currentUser);
    }
    async disconnectWhatsApp(id, currentUser) {
        return this.whatsappService.disconnect(id, currentUser);
    }
    async sendMessage(id, sendMessageDto, currentUser) {
        return this.whatsappService.sendMessage(id, sendMessageDto, currentUser);
    }
    async sendBulkMessage(id, bulkMessageDto, currentUser) {
        return this.whatsappService.sendBulkMessage(id, bulkMessageDto, currentUser);
    }
    async handleWebhook(data) {
        return this.whatsappService.handleWebhook(data);
    }
};
exports.WhatsAppController = WhatsAppController;
__decorate([
    (0, common_1.Post)('connections'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_CONNECT),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova conexão WhatsApp' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Conexão criada com sucesso',
        type: connection_response_dto_1.ConnectionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Número de telefone já está em uso',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_connection_dto_1.CreateConnectionDto, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "createConnection", null);
__decorate([
    (0, common_1.Get)('connections'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_READ_MESSAGES),
    (0, swagger_1.ApiOperation)({ summary: 'Listar conexões WhatsApp' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: entities_1.ConnectionStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, enum: entities_1.ConnectionType, description: 'Filtrar por tipo' }),
    (0, swagger_1.ApiQuery)({ name: 'assignedUserId', required: false, description: 'Filtrar por usuário responsável' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar por nome ou número' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de conexões',
        type: [connection_response_dto_1.ConnectionResponseDto],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "findAllConnections", null);
__decorate([
    (0, common_1.Get)('connections/:id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_READ_MESSAGES),
    (0, swagger_1.ApiOperation)({ summary: 'Obter conexão por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da conexão',
        type: connection_response_dto_1.ConnectionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conexão não encontrada',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "findOneConnection", null);
__decorate([
    (0, common_1.Patch)('connections/:id'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_CONNECT),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar conexão' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conexão atualizada com sucesso',
        type: connection_response_dto_1.ConnectionResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conexão não encontrada',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_connection_dto_1.UpdateConnectionDto, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "updateConnection", null);
__decorate([
    (0, common_1.Delete)('connections/:id'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_DISCONNECT),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar conexão' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Conexão deletada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conexão não encontrada',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "removeConnection", null);
__decorate([
    (0, common_1.Post)('connections/:id/connect'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_CONNECT),
    (0, swagger_1.ApiOperation)({ summary: 'Conectar WhatsApp' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'QR Code gerado para conexão',
        schema: {
            type: 'object',
            properties: {
                qrCode: { type: 'string', description: 'QR Code em base64' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "connectWhatsApp", null);
__decorate([
    (0, common_1.Post)('connections/:id/disconnect'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_DISCONNECT),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Desconectar WhatsApp' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'WhatsApp desconectado com sucesso',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "disconnectWhatsApp", null);
__decorate([
    (0, common_1.Post)('connections/:id/send-message'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_SEND_MESSAGE),
    (0, swagger_1.ApiOperation)({ summary: 'Enviar mensagem' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mensagem enviada com sucesso',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, send_message_dto_1.SendMessageDto, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Post)('connections/:id/send-bulk-message'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.WHATSAPP_SEND_MESSAGE),
    (0, swagger_1.ApiOperation)({ summary: 'Enviar mensagem em massa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mensagens enviadas',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    phoneNumber: { type: 'string' },
                    success: { type: 'boolean' },
                    result: { type: 'object' },
                    error: { type: 'string' },
                },
            },
        },
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, send_message_dto_1.BulkMessageDto, Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "sendBulkMessage", null);
__decorate([
    (0, common_1.Post)('webhook'),
    (0, public_decorator_1.Public)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook para receber eventos do WhatsApp' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Webhook processado com sucesso',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WhatsAppController.prototype, "handleWebhook", null);
exports.WhatsAppController = WhatsAppController = __decorate([
    (0, swagger_1.ApiTags)('WhatsApp'),
    (0, common_1.Controller)('whatsapp'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [whatsapp_service_1.WhatsAppService])
], WhatsAppController);
//# sourceMappingURL=whatsapp.controller.js.map