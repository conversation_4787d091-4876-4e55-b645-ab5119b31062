import { MessagesService, FindMessagesOptions } from '../services/messages.service';
import { MessageResponseDto } from '../dto/message-response.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { MessageStatus } from '../../../database/entities';
export declare class MessagesController {
    private readonly messagesService;
    constructor(messagesService: MessagesService);
    findAll(query: FindMessagesOptions, currentUser: AuthenticatedUser): Promise<{
        messages: MessageResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    getStats(dateFrom?: Date, dateTo?: Date, currentUser?: AuthenticatedUser): Promise<{
        total: number;
        sent: number;
        received: number;
        automated: number;
        failed: number;
    }>;
    getConversation(contactPhone: string, whatsappConnectionId: string, limit?: number, currentUser?: AuthenticatedUser): Promise<MessageResponseDto[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<MessageResponseDto>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    updateStatus(messageId: string, statusData: {
        status: MessageStatus;
        timestamp?: Date;
    }): Promise<void>;
}
