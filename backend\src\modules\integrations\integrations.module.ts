import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { Integration } from '../../database/entities/integration.entity';
import { WebhookLog } from '../../database/entities/webhook-log.entity';
import { IntegrationsService } from './services/integrations.service';
import { IntegrationEventsService } from './services/integration-events.service';
import { IntegrationsController } from './integrations.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Integration,
      WebhookLog,
    ]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
  ],
  controllers: [IntegrationsController],
  providers: [IntegrationsService, IntegrationEventsService],
  exports: [IntegrationsService, IntegrationEventsService],
})
export class IntegrationsModule {}
