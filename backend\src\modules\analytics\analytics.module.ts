import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { AnalyticsEvent, AnalyticsEventSchema } from '../../database/entities/analytics-event.schema';
import { AnalyticsService } from './services/analytics.service';
import { TrackingService } from './services/tracking.service';
import { AnalyticsController } from './analytics.controller';
import { 
  AnalyticsInterceptor,
  AuthAnalyticsInterceptor,
  WhatsAppAnalyticsInterceptor,
  ContactAnalyticsInterceptor 
} from './interceptors/analytics.interceptor';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AnalyticsEvent.name, schema: AnalyticsEventSchema },
    ]),
  ],
  controllers: [AnalyticsController],
  providers: [
    AnalyticsService,
    TrackingService,
    // Interceptors globais para tracking automático
    {
      provide: APP_INTERCEPTOR,
      useClass: AnalyticsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: AuthAnalyticsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: WhatsAppAnalyticsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ContactAnalyticsInterceptor,
    },
  ],
  exports: [AnalyticsService, TrackingService],
})
export class AnalyticsModule {}
