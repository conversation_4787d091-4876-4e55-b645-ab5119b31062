import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { 
  MessageType, 
  MessageDirection, 
  MessageStatus, 
  MessageMedia, 
  MessageLocation, 
  MessageContact 
} from '../../../database/entities';

export class MessageResponseDto {
  @ApiProperty({
    description: 'ID da mensagem',
    example: 'message-id',
  })
  @Expose()
  @Transform(({ obj }) => obj._id?.toString() || obj.id)
  id: string;

  @ApiProperty({
    description: 'ID único da mensagem no WhatsApp',
    example: 'wamid.123456789',
  })
  @Expose()
  messageId: string;

  @ApiProperty({
    description: 'Tipo da mensagem',
    enum: MessageType,
  })
  @Expose()
  type: MessageType;

  @ApiProperty({
    description: 'Direção da mensagem',
    enum: MessageDirection,
  })
  @Expose()
  direction: MessageDirection;

  @ApiProperty({
    description: 'Status da mensagem',
    enum: MessageStatus,
  })
  @Expose()
  status: MessageStatus;

  @ApiPropertyOptional({
    description: 'Conteúdo da mensagem',
    example: 'Olá! Como posso ajudá-lo?',
  })
  @Expose()
  content?: string;

  @ApiPropertyOptional({
    description: 'Dados de mídia',
    type: Object,
  })
  @Expose()
  media?: MessageMedia;

  @ApiPropertyOptional({
    description: 'Dados de localização',
    type: Object,
  })
  @Expose()
  location?: MessageLocation;

  @ApiPropertyOptional({
    description: 'Dados de contato',
    type: Object,
  })
  @Expose()
  contact?: MessageContact;

  @ApiPropertyOptional({
    description: 'ID da mensagem citada',
    example: 'quoted-message-id',
  })
  @Expose()
  quotedMessageId?: string;

  @ApiPropertyOptional({
    description: 'Metadados da mensagem',
  })
  @Expose()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Timestamp da mensagem',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  timestamp: Date;

  @ApiPropertyOptional({
    description: 'Data de envio',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  sentAt?: Date;

  @ApiPropertyOptional({
    description: 'Data de entrega',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  deliveredAt?: Date;

  @ApiPropertyOptional({
    description: 'Data de leitura',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  readAt?: Date;

  @ApiPropertyOptional({
    description: 'Mensagem de erro',
    example: 'Falha na entrega',
  })
  @Expose()
  errorMessage?: string;

  @ApiProperty({
    description: 'Se é uma mensagem automatizada',
    example: false,
  })
  @Expose()
  isAutomated: boolean;

  @ApiPropertyOptional({
    description: 'ID da automação',
    example: 'automation-id',
  })
  @Expose()
  automationId?: string;

  @ApiProperty({
    description: 'Se é um template',
    example: false,
  })
  @Expose()
  isTemplate: boolean;

  @ApiPropertyOptional({
    description: 'Nome do template',
    example: 'welcome_message',
  })
  @Expose()
  templateName?: string;

  @ApiProperty({
    description: 'ID da conexão WhatsApp',
    example: 'uuid-da-conexao',
  })
  @Expose()
  whatsappConnectionId: string;

  @ApiProperty({
    description: 'Telefone do contato',
    example: '+5511999999999',
  })
  @Expose()
  contactPhone: string;

  @ApiPropertyOptional({
    description: 'ID do usuário que enviou',
    example: 'uuid-do-usuario',
  })
  @Expose()
  sentBy?: string;

  @ApiPropertyOptional({
    description: 'ID da conversa',
    example: 'conversation-id',
  })
  @Expose()
  conversationId?: string;

  @ApiProperty({
    description: 'Se é a primeira mensagem',
    example: false,
  })
  @Expose()
  isFirstMessage: boolean;

  @ApiPropertyOptional({
    description: 'Tempo de resposta em segundos',
    example: 120,
  })
  @Expose()
  responseTime?: number;

  @ApiPropertyOptional({
    description: 'Etiquetas da mensagem',
    type: [String],
  })
  @Expose()
  tags?: string[];

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;
}
