import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class MetricDataPoint {
  @ApiProperty({
    description: 'Timestamp do ponto de dados',
    example: '2023-12-01T00:00:00Z',
  })
  @Expose()
  timestamp: string;

  @ApiProperty({
    description: 'Valor da métrica',
    example: 150,
  })
  @Expose()
  value: number;

  @ApiPropertyOptional({
    description: 'Valor do período anterior (para comparação)',
    example: 120,
  })
  @Expose()
  previousValue?: number;

  @ApiPropertyOptional({
    description: 'Percentual de mudança',
    example: 25.0,
  })
  @Expose()
  changePercent?: number;

  @ApiPropertyOptional({
    description: 'Metadados adicionais',
  })
  @Expose()
  metadata?: Record<string, any>;
}

export class AnalyticsMetric {
  @ApiProperty({
    description: 'Nome da métrica',
    example: 'messages_sent',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Valor atual',
    example: 1250,
  })
  @Expose()
  value: number;

  @ApiPropertyOptional({
    description: 'Valor do período anterior',
    example: 980,
  })
  @Expose()
  previousValue?: number;

  @ApiPropertyOptional({
    description: 'Percentual de mudança',
    example: 27.55,
  })
  @Expose()
  changePercent?: number;

  @ApiPropertyOptional({
    description: 'Tendência (up, down, stable)',
    example: 'up',
  })
  @Expose()
  trend?: string;

  @ApiProperty({
    description: 'Série temporal de dados',
    type: [MetricDataPoint],
  })
  @Expose()
  timeSeries: MetricDataPoint[];

  @ApiPropertyOptional({
    description: 'Unidade da métrica',
    example: 'count',
  })
  @Expose()
  unit?: string;

  @ApiPropertyOptional({
    description: 'Formato de exibição',
    example: 'number',
  })
  @Expose()
  format?: string;
}

export class DashboardMetrics {
  @ApiProperty({
    description: 'Total de mensagens enviadas',
    type: AnalyticsMetric,
  })
  @Expose()
  messagesSent: AnalyticsMetric;

  @ApiProperty({
    description: 'Total de mensagens recebidas',
    type: AnalyticsMetric,
  })
  @Expose()
  messagesReceived: AnalyticsMetric;

  @ApiProperty({
    description: 'Taxa de entrega',
    type: AnalyticsMetric,
  })
  @Expose()
  deliveryRate: AnalyticsMetric;

  @ApiProperty({
    description: 'Taxa de leitura',
    type: AnalyticsMetric,
  })
  @Expose()
  readRate: AnalyticsMetric;

  @ApiProperty({
    description: 'Tempo médio de resposta (segundos)',
    type: AnalyticsMetric,
  })
  @Expose()
  averageResponseTime: AnalyticsMetric;

  @ApiProperty({
    description: 'Novos contatos',
    type: AnalyticsMetric,
  })
  @Expose()
  newContacts: AnalyticsMetric;

  @ApiProperty({
    description: 'Leads convertidos',
    type: AnalyticsMetric,
  })
  @Expose()
  leadsConverted: AnalyticsMetric;

  @ApiProperty({
    description: 'Taxa de conversão (%)',
    type: AnalyticsMetric,
  })
  @Expose()
  conversionRate: AnalyticsMetric;

  @ApiProperty({
    description: 'Conexões ativas',
    type: AnalyticsMetric,
  })
  @Expose()
  activeConnections: AnalyticsMetric;

  @ApiProperty({
    description: 'Usuários ativos',
    type: AnalyticsMetric,
  })
  @Expose()
  activeUsers: AnalyticsMetric;
}

export class ConversationAnalytics {
  @ApiProperty({
    description: 'Total de conversas',
    example: 450,
  })
  @Expose()
  totalConversations: number;

  @ApiProperty({
    description: 'Conversas ativas',
    example: 120,
  })
  @Expose()
  activeConversations: number;

  @ApiProperty({
    description: 'Tempo médio de conversa (minutos)',
    example: 15.5,
  })
  @Expose()
  averageConversationDuration: number;

  @ApiProperty({
    description: 'Mensagens por conversa (média)',
    example: 8.2,
  })
  @Expose()
  averageMessagesPerConversation: number;

  @ApiProperty({
    description: 'Taxa de primeira resposta (%)',
    example: 85.5,
  })
  @Expose()
  firstResponseRate: number;

  @ApiProperty({
    description: 'Tempo médio de primeira resposta (minutos)',
    example: 12.3,
  })
  @Expose()
  averageFirstResponseTime: number;
}

export class UserActivityAnalytics {
  @ApiProperty({
    description: 'Usuários ativos hoje',
    example: 25,
  })
  @Expose()
  activeToday: number;

  @ApiProperty({
    description: 'Usuários ativos esta semana',
    example: 45,
  })
  @Expose()
  activeThisWeek: number;

  @ApiProperty({
    description: 'Usuários ativos este mês',
    example: 78,
  })
  @Expose()
  activeThisMonth: number;

  @ApiProperty({
    description: 'Tempo médio de sessão (minutos)',
    example: 45.2,
  })
  @Expose()
  averageSessionDuration: number;

  @ApiProperty({
    description: 'Atividade por hora do dia',
    type: [MetricDataPoint],
  })
  @Expose()
  activityByHour: MetricDataPoint[];

  @ApiProperty({
    description: 'Atividade por dia da semana',
    type: [MetricDataPoint],
  })
  @Expose()
  activityByDayOfWeek: MetricDataPoint[];
}

export class PerformanceAnalytics {
  @ApiProperty({
    description: 'Tempo médio de resposta da API (ms)',
    example: 150,
  })
  @Expose()
  averageApiResponseTime: number;

  @ApiProperty({
    description: 'Taxa de erro (%)',
    example: 0.5,
  })
  @Expose()
  errorRate: number;

  @ApiProperty({
    description: 'Uptime (%)',
    example: 99.9,
  })
  @Expose()
  uptime: number;

  @ApiProperty({
    description: 'Requests por minuto',
    example: 1250,
  })
  @Expose()
  requestsPerMinute: number;

  @ApiProperty({
    description: 'Conexões WebSocket ativas',
    example: 85,
  })
  @Expose()
  activeWebSocketConnections: number;
}

export class AnalyticsReportResponse {
  @ApiProperty({
    description: 'Métricas do dashboard',
    type: DashboardMetrics,
  })
  @Expose()
  dashboard: DashboardMetrics;

  @ApiProperty({
    description: 'Analytics de conversas',
    type: ConversationAnalytics,
  })
  @Expose()
  conversations: ConversationAnalytics;

  @ApiProperty({
    description: 'Analytics de atividade de usuários',
    type: UserActivityAnalytics,
  })
  @Expose()
  userActivity: UserActivityAnalytics;

  @ApiProperty({
    description: 'Analytics de performance',
    type: PerformanceAnalytics,
  })
  @Expose()
  performance: PerformanceAnalytics;

  @ApiProperty({
    description: 'Período do relatório',
    example: { start: '2023-12-01T00:00:00Z', end: '2023-12-07T23:59:59Z' },
  })
  @Expose()
  period: {
    start: string;
    end: string;
  };

  @ApiProperty({
    description: 'Timestamp de geração',
    example: '2023-12-08T10:30:00Z',
  })
  @Expose()
  generatedAt: string;
}
