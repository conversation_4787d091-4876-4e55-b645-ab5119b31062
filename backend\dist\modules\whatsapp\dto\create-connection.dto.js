"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateConnectionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const entities_1 = require("../../../database/entities");
class CreateConnectionDto {
    name;
    phoneNumber;
    type;
    companyId;
    assignedUserId;
    settings;
}
exports.CreateConnectionDto = CreateConnectionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da conexão',
        example: 'WhatsApp Vendas',
    }),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    (0, class_validator_1.Length)(2, 255, { message: 'Nome deve ter entre 2 e 255 caracteres' }),
    __metadata("design:type", String)
], CreateConnectionDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de telefone',
        example: '+5511999999999',
    }),
    (0, class_validator_1.IsPhoneNumber)('BR', { message: 'Número de telefone deve ser válido' }),
    __metadata("design:type", String)
], CreateConnectionDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo de conexão',
        enum: entities_1.ConnectionType,
        default: entities_1.ConnectionType.EVOLUTION_API,
    }),
    (0, class_validator_1.IsEnum)(entities_1.ConnectionType, { message: 'Tipo de conexão deve ser válido' }),
    __metadata("design:type", String)
], CreateConnectionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 'uuid-da-empresa',
    }),
    (0, class_validator_1.IsUUID)(4, { message: 'ID da empresa deve ser um UUID válido' }),
    __metadata("design:type", String)
], CreateConnectionDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do usuário responsável',
        example: 'uuid-do-usuario',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(4, { message: 'ID do usuário deve ser um UUID válido' }),
    __metadata("design:type", String)
], CreateConnectionDto.prototype, "assignedUserId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configurações específicas da conexão',
        example: { webhook: 'https://example.com/webhook', autoReconnect: true },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Configurações devem ser um objeto' }),
    __metadata("design:type", Object)
], CreateConnectionDto.prototype, "settings", void 0);
//# sourceMappingURL=create-connection.dto.js.map