import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { MessagesService } from '../services/messages.service';
import { ContactsService } from '../services/contacts.service';
interface AuthenticatedSocket extends Socket {
    user?: AuthenticatedUser;
}
export declare class MessagesGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private jwtService;
    private configService;
    private messagesService;
    private contactsService;
    server: Server;
    private readonly logger;
    private connectedUsers;
    constructor(jwtService: JwtService, configService: ConfigService, messagesService: MessagesService, contactsService: ContactsService);
    handleConnection(client: AuthenticatedSocket): Promise<void>;
    handleDisconnect(client: AuthenticatedSocket): void;
    handleJoinConversation(client: AuthenticatedSocket, data: {
        contactPhone: string;
        whatsappConnectionId: string;
    }): Promise<void>;
    handleLeaveConversation(client: AuthenticatedSocket, data: {
        contactPhone: string;
        whatsappConnectionId: string;
    }): Promise<void>;
    handleTypingStart(client: AuthenticatedSocket, data: {
        contactPhone: string;
        whatsappConnectionId: string;
    }): Promise<void>;
    handleTypingStop(client: AuthenticatedSocket, data: {
        contactPhone: string;
        whatsappConnectionId: string;
    }): Promise<void>;
    handleMarkAsRead(client: AuthenticatedSocket, data: {
        messageId: string;
    }): Promise<void>;
    emitNewMessage(message: any, companyId: string): Promise<void>;
    emitMessageStatusUpdate(messageId: string, status: string, companyId: string): Promise<void>;
    emitContactUpdate(contact: any, companyId: string): Promise<void>;
    emitConnectionStatusUpdate(connectionId: string, status: string, companyId: string): Promise<void>;
    getConnectedUsers(): Map<string, Set<string>>;
    isUserOnline(userId: string): boolean;
    sendNotificationToUser(userId: string, notification: any): Promise<void>;
}
export {};
