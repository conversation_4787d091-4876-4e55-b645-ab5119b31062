"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAutomationDto = exports.AutomationSettingsDto = exports.FlowStepDto = exports.ActionStepDto = exports.TriggerConditionDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const automation_schema_1 = require("../../../database/entities/automation.schema");
class TriggerConditionDto {
    type;
    keywords;
    schedule;
    inactivityMinutes;
    webhookUrl;
    customConditions;
}
exports.TriggerConditionDto = TriggerConditionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo do gatilho',
        enum: automation_schema_1.TriggerType,
    }),
    (0, class_validator_1.IsEnum)(automation_schema_1.TriggerType),
    __metadata("design:type", String)
], TriggerConditionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Palavras-chave para gatilho',
        type: [String],
        example: ['oi', 'olá', 'help'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], TriggerConditionDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configuração de agendamento',
        example: { time: '09:00', days: [1, 2, 3, 4, 5], timezone: 'America/Sao_Paulo' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], TriggerConditionDto.prototype, "schedule", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minutos de inatividade para gatilho',
        example: 30,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TriggerConditionDto.prototype, "inactivityMinutes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL do webhook',
        example: 'https://example.com/webhook',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TriggerConditionDto.prototype, "webhookUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Condições customizadas',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], TriggerConditionDto.prototype, "customConditions", void 0);
class ActionStepDto {
    id;
    type;
    name;
    description;
    config;
    nextStepId;
    conditions;
    delay;
}
exports.ActionStepDto = ActionStepDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único do passo',
        example: 'step-1',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionStepDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da ação',
        enum: automation_schema_1.ActionType,
    }),
    (0, class_validator_1.IsEnum)(automation_schema_1.ActionType),
    __metadata("design:type", String)
], ActionStepDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome do passo',
        example: 'Enviar mensagem de boas-vindas',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionStepDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Descrição do passo',
        example: 'Envia uma mensagem de boas-vindas para novos contatos',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionStepDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Configuração da ação',
        example: { message: 'Olá! Bem-vindo ao nosso atendimento.' },
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], ActionStepDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do próximo passo',
        example: 'step-2',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ActionStepDto.prototype, "nextStepId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Condições para próximos passos',
        type: [Object],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], ActionStepDto.prototype, "conditions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Delay em segundos antes de executar',
        example: 5,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ActionStepDto.prototype, "delay", void 0);
class FlowStepDto {
    id;
    name;
    type;
    position;
    config;
    nextSteps;
}
exports.FlowStepDto = FlowStepDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único do passo',
        example: 'flow-step-1',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FlowStepDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome do passo',
        example: 'Mensagem inicial',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FlowStepDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo do passo',
        enum: ['message', 'condition', 'action', 'ai', 'human_handoff'],
    }),
    (0, class_validator_1.IsEnum)(['message', 'condition', 'action', 'ai', 'human_handoff']),
    __metadata("design:type", String)
], FlowStepDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Posição no canvas',
        example: { x: 100, y: 200 },
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FlowStepDto.prototype, "position", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Configuração do passo',
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FlowStepDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'IDs dos próximos passos',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FlowStepDto.prototype, "nextSteps", void 0);
class AutomationSettingsDto {
    isActive;
    priority;
    maxExecutionsPerContact;
    cooldownMinutes;
    businessHoursOnly;
    businessHours;
    allowedConnections;
    excludedContacts;
    tags;
}
exports.AutomationSettingsDto = AutomationSettingsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Se a automação está ativa',
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AutomationSettingsDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Prioridade da automação',
        example: 1,
        minimum: 1,
        maximum: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], AutomationSettingsDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Máximo de execuções por contato',
        example: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AutomationSettingsDto.prototype, "maxExecutionsPerContact", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Cooldown em minutos',
        example: 60,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], AutomationSettingsDto.prototype, "cooldownMinutes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Apenas em horário comercial',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AutomationSettingsDto.prototype, "businessHoursOnly", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configuração de horário comercial',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AutomationSettingsDto.prototype, "businessHours", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Conexões permitidas',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AutomationSettingsDto.prototype, "allowedConnections", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contatos excluídos',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AutomationSettingsDto.prototype, "excludedContacts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tags necessárias',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AutomationSettingsDto.prototype, "tags", void 0);
class CreateAutomationDto {
    name;
    description;
    type;
    trigger;
    actions;
    flowSteps;
    startStepId;
    settings;
    isTemplate;
    templateCategory;
    metadata;
}
exports.CreateAutomationDto = CreateAutomationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da automação',
        example: 'Boas-vindas para novos contatos',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAutomationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Descrição da automação',
        example: 'Envia mensagem de boas-vindas e coleta informações básicas',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAutomationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da automação',
        enum: automation_schema_1.AutomationType,
    }),
    (0, class_validator_1.IsEnum)(automation_schema_1.AutomationType),
    __metadata("design:type", String)
], CreateAutomationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Configuração do gatilho',
        type: TriggerConditionDto,
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => TriggerConditionDto),
    __metadata("design:type", TriggerConditionDto)
], CreateAutomationDto.prototype, "trigger", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Passos de ação (para automações simples)',
        type: [ActionStepDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ActionStepDto),
    __metadata("design:type", Array)
], CreateAutomationDto.prototype, "actions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Passos do fluxo (para chatbot)',
        type: [FlowStepDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => FlowStepDto),
    __metadata("design:type", Array)
], CreateAutomationDto.prototype, "flowSteps", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do passo inicial',
        example: 'step-1',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAutomationDto.prototype, "startStepId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configurações da automação',
        type: AutomationSettingsDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AutomationSettingsDto),
    __metadata("design:type", AutomationSettingsDto)
], CreateAutomationDto.prototype, "settings", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Se é um template',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateAutomationDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Categoria do template',
        example: 'vendas',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAutomationDto.prototype, "templateCategory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados adicionais',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateAutomationDto.prototype, "metadata", void 0);
//# sourceMappingURL=create-automation.dto.js.map