import { User, User<PERSON><PERSON>, User<PERSON>tatus, Prisma } from '@prisma/client'
import prisma from '../lib/prisma'

export interface CreateUserData {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  role?: UserRole
  status?: UserStatus
  companyId: string
  permissions?: string[]
}

export interface UpdateUserData {
  firstName?: string
  lastName?: string
  email?: string
  password?: string
  phone?: string
  role?: UserRole
  status?: UserStatus
  avatar?: string
  lastLoginAt?: Date
  emailVerifiedAt?: Date
  preferences?: any
  permissions?: string[]
  refreshToken?: string
  refreshTokenExpiry?: Date
}

export interface UserFilters {
  companyId?: string
  role?: UserRole
  status?: UserStatus
  search?: string
}

export class UserRepository {
  async create(data: CreateUserData): Promise<User> {
    return prisma.user.create({
      data,
      include: {
        company: true
      }
    })
  }

  async findById(id: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { id },
      include: {
        company: true
      }
    })
  }

  async findByEmail(email: string): Promise<User | null> {
    return prisma.user.findUnique({
      where: { email },
      include: {
        company: true
      }
    })
  }

  async findByRefreshToken(refreshToken: string): Promise<User | null> {
    return prisma.user.findFirst({
      where: { 
        refreshToken,
        refreshTokenExpiry: {
          gt: new Date()
        }
      },
      include: {
        company: true
      }
    })
  }

  async update(id: string, data: UpdateUserData): Promise<User> {
    return prisma.user.update({
      where: { id },
      data,
      include: {
        company: true
      }
    })
  }

  async delete(id: string): Promise<User> {
    return prisma.user.delete({
      where: { id }
    })
  }

  async findMany(filters: UserFilters = {}, page = 1, limit = 10): Promise<{
    users: User[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.UserWhereInput = {}

    if (filters.companyId) {
      where.companyId = filters.companyId
    }

    if (filters.role) {
      where.role = filters.role
    }

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search, mode: 'insensitive' } },
        { lastName: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          company: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.user.count({ where })
    ])

    return {
      users,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async updateLastLogin(id: string): Promise<User> {
    return prisma.user.update({
      where: { id },
      data: {
        lastLoginAt: new Date()
      },
      include: {
        company: true
      }
    })
  }

  async clearRefreshToken(id: string): Promise<User> {
    return prisma.user.update({
      where: { id },
      data: {
        refreshToken: null,
        refreshTokenExpiry: null
      }
    })
  }

  async setRefreshToken(id: string, refreshToken: string, expiresAt: Date): Promise<User> {
    return prisma.user.update({
      where: { id },
      data: {
        refreshToken,
        refreshTokenExpiry: expiresAt
      }
    })
  }

  async verifyEmail(id: string): Promise<User> {
    return prisma.user.update({
      where: { id },
      data: {
        emailVerifiedAt: new Date()
      }
    })
  }

  async countByCompany(companyId: string): Promise<number> {
    return prisma.user.count({
      where: { companyId }
    })
  }

  async findByCompany(companyId: string): Promise<User[]> {
    return prisma.user.findMany({
      where: { companyId },
      include: {
        company: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }
}
