{"version": 3, "file": "evolution-api.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/whatsapp/services/evolution-api.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAyE;AACzE,2CAA+C;AAC/C,yCAA4C;AAC5C,+BAAsC;AACtC,yDAA8D;AA2CvD,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAMX;IACA;IANF,MAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;IAC9C,OAAO,CAAS;IAChB,MAAM,CAAS;IAEhC,YACmB,aAA4B,EAC5B,WAAwB;QADxB,kBAAa,GAAb,aAAa,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAAa;QAEzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,uBAAuB,CAAC;QAC9F,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,EAAE,CAAC;IAC1E,CAAC;IAEO,UAAU;QAChB,OAAO;YACL,cAAc,EAAE,kBAAkB;YAClC,QAAQ,EAAE,IAAI,CAAC,MAAM;SACtB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,YAAoB,EAAE,UAAmB;QAC5D,IAAI,CAAC;YACH,MAAM,OAAO,GAAQ;gBACnB,YAAY;gBACZ,MAAM,EAAE,IAAI;gBACZ,WAAW,EAAE,kBAAkB;aAChC,CAAC;YAEF,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,CAAC,OAAO,GAAG;oBAChB,GAAG,EAAE,UAAU;oBACf,MAAM,EAAE;wBACN,qBAAqB;wBACrB,gBAAgB;wBAChB,mBAAmB;wBACnB,iBAAiB;wBACjB,iBAAiB;wBACjB,cAAc;qBACf;iBACF,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,OAAO,kBAAkB,EACjC,OAAO,EACP,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;YACrD,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACvG,MAAM,IAAI,4BAAmB,CAAC,sCAAsC,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,YAAoB;QACvC,IAAI,CAAC;YACH,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,MAAM,CACrB,GAAG,IAAI,CAAC,OAAO,oBAAoB,YAAY,EAAE,EACjD,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,YAAY,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACvG,MAAM,IAAI,4BAAmB,CAAC,wCAAwC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,YAAoB;QAC1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAClB,GAAG,IAAI,CAAC,OAAO,6BAA6B,YAAY,EAAE,EAC1D,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3G,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,YAAoB;QAClC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,GAAG,CAClB,GAAG,IAAI,CAAC,OAAO,qBAAqB,YAAY,EAAE,EAClD,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACnG,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,YAAoB,EAAE,OAAyB;QAC/D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,OAAO,qBAAqB,YAAY,EAAE,EAClD,OAAO,EACP,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qBAAqB,YAAY,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACzG,MAAM,IAAI,4BAAmB,CAAC,0BAA0B,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,YAAoB,EAAE,OAAyB;QACpE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YACjE,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,OAAO,YAAY,QAAQ,IAAI,YAAY,EAAE,EACrD,OAAO,EACP,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,YAAY,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAChF,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YAC/G,MAAM,IAAI,4BAAmB,CAAC,mCAAmC,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,YAAoB;QAC/B,IAAI,CAAC;YACH,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,MAAM,CACrB,GAAG,IAAI,CAAC,OAAO,oBAAoB,YAAY,EAAE,EACjD,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,YAAY,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACvG,MAAM,IAAI,4BAAmB,CAAC,gCAAgC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,YAAoB;QAChC,IAAI,CAAC;YACH,MAAM,IAAA,qBAAc,EAClB,IAAI,CAAC,WAAW,CAAC,GAAG,CAClB,GAAG,IAAI,CAAC,OAAO,qBAAqB,YAAY,EAAE,EAClD,EAAE,EACF,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,CAC/B,CACF,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,YAAY,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;YACxG,MAAM,IAAI,4BAAmB,CAAC,8BAA8B,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,SAAkB;QACzC,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC;YACrB,KAAK,OAAO;gBACV,OAAO,mBAAmB,CAAC;YAC7B,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC;YACrB,KAAK,UAAU;gBACb,OAAO,WAAW,CAAC;YACrB;gBACE,OAAO,WAAW,CAAC;QACvB,CAAC;IACH,CAAC;IAED,oCAAoC,CAAC,eAAuB;QAC1D,QAAQ,eAAe,EAAE,WAAW,EAAE,EAAE,CAAC;YACvC,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW;gBACd,OAAO,2BAAgB,CAAC,SAAS,CAAC;YACpC,KAAK,YAAY,CAAC;YAClB,KAAK,SAAS;gBACZ,OAAO,2BAAgB,CAAC,UAAU,CAAC;YACrC,KAAK,OAAO,CAAC;YACb,KAAK,QAAQ;gBACX,OAAO,2BAAgB,CAAC,YAAY,CAAC;YACvC;gBACE,OAAO,2BAAgB,CAAC,KAAK,CAAC;QAClC,CAAC;IACH,CAAC;IAED,oBAAoB,CAAC,SAAiB,EAAE,WAAmB;QAEzD,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEnD,MAAM,cAAc,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjD,OAAO,GAAG,cAAc,IAAI,WAAW,EAAE,CAAC;IAC5C,CAAC;CACF,CAAA;AAtNY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAOuB,sBAAa;QACf,mBAAW;GAPhC,mBAAmB,CAsN/B"}