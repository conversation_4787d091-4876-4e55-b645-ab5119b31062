{"version": 3, "file": "subscription-dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/billing/dto/subscription-dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAWyB;AAEzB,6CAAmE;AACnE,wFAAsG;AAEtG,MAAa,qBAAqB;IAGhC,SAAS,CAAS;IAIlB,MAAM,CAAS;IAKf,IAAI,CAAoB;IAKxB,SAAS,CAAU;IAKnB,YAAY,CAAU;IAMtB,KAAK,CAAU;IAMf,QAAQ,CAAU;IAOlB,kBAAkB,CAAU;IAM5B,cAAc,CAAU;IAKxB,eAAe,CAAU;IAKzB,UAAU,CAAU;IAKpB,QAAQ,CAAU;IAOlB,0BAA0B,CAAU;IAMpC,qBAAqB,CAAU;IAK/B,eAAe,CAAU;IAKzB,SAAS,CAAW;IAKpB,UAAU,CAAW;IAKrB,cAAc,CAAuB;IAKrC,QAAQ,CAAuB;IAK/B,kBAAkB,CAAW;IAK7B,oBAAoB,CAAW;IAK/B,UAAU,CAAU;CACrB;AApHD,sDAoHC;AAjHC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IAC7C,IAAA,0BAAQ,GAAE;;wDACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC3C,IAAA,0BAAQ,GAAE;;qDACI;AAKf;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,IAAI,EAAE,sCAAgB,EAAE,OAAO,EAAE,sCAAgB,CAAC,MAAM,EAAE,CAAC;IACpH,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,sCAAgB,CAAC;;mDACD;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACvF,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;wDACI;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC7F,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;2DACO;AAMtB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC1E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;oDACQ;AAMf;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC9E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;uDACW;AAOlB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACvF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;iEACmB;AAM5B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAClF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;6DACiB;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChG,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;8DACU;AAKzB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IAC9E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACS;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACO;AAOlB;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mCAAmC,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAClG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;yEAC2B;AAMpC;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IACzF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;oEACwB;AAK/B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACc;AAKzB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;wDACQ;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IACnE,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;yDACS;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DAC0B;AAKrC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACoB;AAK/B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC7E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;iEACiB;AAK7B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAChF,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;mEACmB;AAK/B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACS;AAGtB,MAAa,qBAAqB;IAIhC,MAAM,CAAsB;IAK5B,OAAO,CAAU;IAKjB,YAAY,CAAU;IAMtB,KAAK,CAAU;IAOf,kBAAkB,CAAU;IAM5B,cAAc,CAAU;IAKxB,eAAe,CAAU;IAKzB,UAAU,CAAU;IAKpB,eAAe,CAAU;IAKzB,SAAS,CAAW;IAKpB,iBAAiB,CAAW;IAK5B,cAAc,CAAuB;IAKrC,QAAQ,CAAuB;IAK/B,kBAAkB,CAAW;IAK7B,oBAAoB,CAAW;IAK/B,UAAU,CAAU;CACrB;AApFD,sDAoFC;AAhFC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,IAAI,EAAE,wCAAkB,EAAE,CAAC;IACtF,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,wCAAkB,CAAC;;qDACC;AAK5B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACpF,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;sDACE;AAKjB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAC7F,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;2DACO;AAMtB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC1E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;oDACQ;AAOf;IALC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;iEACmB;AAM5B;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IACtE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;6DACiB;AAKxB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,yBAAyB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChG,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;8DACU;AAKzB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IAC9E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACS;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,2BAA2B,EAAE,CAAC;IACjE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACc;AAKzB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;wDACQ;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;gEACgB;AAK5B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IACpE,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DAC0B;AAKrC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACoB;AAK/B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC;IAC9D,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;iEACiB;AAK7B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,0BAA0B,EAAE,CAAC;IAChE,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;mEACmB;AAK/B;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IACtD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACS;AAGtB,MAAa,aAAa;IAGxB,SAAS,CAAS;IAKlB,UAAU,CAAU;IAKpB,UAAU,CAAW;IAMrB,WAAW,CAAU;IAKrB,MAAM,CAAU;CACjB;AAzBD,sCAyBC;AAtBC;IAFC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IAChD,IAAA,0BAAQ,GAAE;;gDACO;AAKlB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACxF,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;iDACK;AAKpB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;iDACS;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,qCAAqC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC5F,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;kDACc;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC;IACvG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6CACK;AAGlB,MAAa,qBAAqB;IAIhC,WAAW,CAAW;IAKtB,MAAM,CAAU;IAKhB,QAAQ,CAAU;CACnB;AAfD,sDAeC;AAXC;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC9E,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;0DACU;AAKtB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACtG,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;qDACK;AAKhB;IAHC,IAAA,6BAAmB,EAAC,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC;IACvF,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACO;AAGpB,MAAa,oBAAoB;IAI/B,WAAW,CAAS;IAKpB,QAAQ,CAAS;IAKjB,QAAQ,CAAS;IAKjB,KAAK,CAAS;IAKd,WAAW,CAAS;IAKpB,QAAQ,CAAS;IAKjB,aAAa,CAAS;IAKtB,QAAQ,CAAS;IAKjB,YAAY,CAAS;CACtB;AA7CD,oDA6CC;AAzCC;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;yDACa;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;sDACU;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IACpE,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;sDACU;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;mDACO;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAClE,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;yDACa;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,qBAAqB,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/D,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;sDACU;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,+BAA+B,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC5E,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;2DACe;AAKtB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC1E,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;sDACU;AAKjB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gCAAgC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;IAC5E,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;0DACc"}