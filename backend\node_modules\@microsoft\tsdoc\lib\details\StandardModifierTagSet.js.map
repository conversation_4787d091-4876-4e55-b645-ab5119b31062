{"version": 3, "file": "StandardModifierTagSet.js", "sourceRoot": "", "sources": ["../../src/details/StandardModifierTagSet.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C;;;GAGG;AACH;IAA4C,0CAAc;IAA1D;;IA6EA,CAAC;IA5EC;;OAEG;IACI,wCAAO,GAAd;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,uCAAM,GAAb;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,gDAAe,GAAtB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,+CAAc,GAArB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,2CAAU,GAAjB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,2CAAU,GAAjB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,uDAAsB,GAA7B;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,yCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,2CAAU,GAAjB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACI,yCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,0CAAS,GAAhB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IACH,6BAAC;AAAD,CAAC,AA7ED,CAA4C,cAAc,GA6EzD", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { ModifierTagSet } from './ModifierTagSet';\r\nimport { StandardTags } from './StandardTags';\r\n\r\n/**\r\n * Extends the ModifierTagSet base class with getters for modifiers that\r\n * are part of the standardized core tags for TSDoc.\r\n */\r\nexport class StandardModifierTagSet extends ModifierTagSet {\r\n  /**\r\n   * Returns true if the `@alpha` modifier tag was specified.\r\n   */\r\n  public isAlpha(): boolean {\r\n    return this.hasTag(StandardTags.alpha);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@beta` modifier tag was specified.\r\n   */\r\n  public isBeta(): boolean {\r\n    return this.hasTag(StandardTags.beta);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@eventProperty` modifier tag was specified.\r\n   */\r\n  public isEventProperty(): boolean {\r\n    return this.hasTag(StandardTags.eventProperty);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@experimental` modifier tag was specified.\r\n   */\r\n  public isExperimental(): boolean {\r\n    return this.hasTag(StandardTags.experimental);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@internal` modifier tag was specified.\r\n   */\r\n  public isInternal(): boolean {\r\n    return this.hasTag(StandardTags.internal);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@override` modifier tag was specified.\r\n   */\r\n  public isOverride(): boolean {\r\n    return this.hasTag(StandardTags.override);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@packageDocumentation` modifier tag was specified.\r\n   */\r\n  public isPackageDocumentation(): boolean {\r\n    return this.hasTag(StandardTags.packageDocumentation);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@public` modifier tag was specified.\r\n   */\r\n  public isPublic(): boolean {\r\n    return this.hasTag(StandardTags.public);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@readonly` modifier tag was specified.\r\n   */\r\n  public isReadonly(): boolean {\r\n    return this.hasTag(StandardTags.readonly);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@sealed` modifier tag was specified.\r\n   */\r\n  public isSealed(): boolean {\r\n    return this.hasTag(StandardTags.sealed);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the `@virtual` modifier tag was specified.\r\n   */\r\n  public isVirtual(): boolean {\r\n    return this.hasTag(StandardTags.virtual);\r\n  }\r\n}\r\n"]}