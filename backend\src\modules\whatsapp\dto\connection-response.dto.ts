import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { ConnectionType, ConnectionStatus } from '../../../database/entities';

export class ConnectionResponseDto {
  @ApiProperty({
    description: 'ID da conexão',
    example: 'uuid-da-conexao',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Nome da conexão',
    example: 'WhatsApp Vendas',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Número de telefone',
    example: '+5511999999999',
  })
  @Expose()
  phoneNumber: string;

  @ApiProperty({
    description: 'Número formatado',
    example: '+55 (11) 99999-9999',
  })
  @Expose()
  @Transform(({ obj }) => obj.formattedPhoneNumber)
  formattedPhoneNumber: string;

  @ApiProperty({
    description: 'Tipo de conexão',
    enum: ConnectionType,
  })
  @Expose()
  type: ConnectionType;

  @ApiProperty({
    description: 'Status da conexão',
    enum: ConnectionStatus,
  })
  @Expose()
  status: ConnectionStatus;

  @ApiProperty({
    description: 'Se está conectado',
    example: true,
  })
  @Expose()
  @Transform(({ obj }) => obj.isConnected)
  isConnected: boolean;

  @ApiPropertyOptional({
    description: 'QR Code para conexão',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
  })
  @Expose()
  qrCode?: string;

  @ApiPropertyOptional({
    description: 'ID da instância',
    example: 'instance-123',
  })
  @Expose()
  instanceId?: string;

  @ApiPropertyOptional({
    description: 'Último login',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastConnectedAt?: Date;

  @ApiPropertyOptional({
    description: 'Última desconexão',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastDisconnectedAt?: Date;

  @ApiPropertyOptional({
    description: 'Mensagem de erro',
    example: 'Falha na autenticação',
  })
  @Expose()
  errorMessage?: string;

  @ApiProperty({
    description: 'Se a conexão está ativa',
    example: true,
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    description: 'ID da empresa',
    example: 'uuid-da-empresa',
  })
  @Expose()
  companyId: string;

  @ApiPropertyOptional({
    description: 'ID do usuário responsável',
    example: 'uuid-do-usuario',
  })
  @Expose()
  assignedUserId?: string;

  @ApiPropertyOptional({
    description: 'Configurações da conexão',
  })
  @Expose()
  settings?: Record<string, any>;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;
}
