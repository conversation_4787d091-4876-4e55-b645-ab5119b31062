{"version": 3, "file": "tracking.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/analytics/services/tracking.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2DAAuD;AACvD,8FAA8G;AAGvG,IAAM,eAAe,uBAArB,MAAM,eAAe;IAGN;IAFH,MAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAE3D,YAAoB,gBAAkC;QAAlC,qBAAgB,GAAhB,gBAAgB,CAAkB;IAAG,CAAC;IAG1D,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,SAAiB,EAAE,SAAiB,EAAE,QAAc;QACvF,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,UAAU,EACpB,sCAAa,CAAC,IAAI,EAClB,SAAS,EACT;YACE,MAAM;YACN,SAAS;YACT,GAAG,QAAQ;SACZ,EACD,MAAM,EACN,SAAS,CACV,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,SAAiB,EAAE,eAAwB;QAClG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,WAAW,EACrB,sCAAa,CAAC,IAAI,EAClB,SAAS,EACT;YACE,MAAM;YACN,SAAS;YACT,QAAQ,EAAE,eAAe;SAC1B,EACD,MAAM,EACN,SAAS,CACV,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,SAAiB,EAAE,SAAiB,EAAE,QAAgB;QAC3F,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,YAAY,EACtB,sCAAa,CAAC,IAAI,EAClB,SAAS,EACT;YACE,MAAM;YACN,SAAS;YACT,QAAQ;SACT,EACD,SAAS,CACV,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,sBAAsB,CAAC,YAAoB,EAAE,SAAiB,EAAE,MAAc,EAAE,WAAmB;QACvG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,kBAAkB,EAC5B,sCAAa,CAAC,QAAQ,EACtB,SAAS,EACT;YACE,YAAY;YACZ,WAAW;YACX,MAAM;SACP,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,YAAoB,EAAE,SAAiB,EAAE,MAAe,EAAE,MAAe;QACvG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,qBAAqB,EAC/B,sCAAa,CAAC,QAAQ,EACtB,SAAS,EACT;YACE,YAAY;YACZ,MAAM;YACN,MAAM;SACP,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,YAAoB,EAAE,SAAiB,EAAE,MAAc;QAChF,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,qBAAqB,EAC/B,sCAAa,CAAC,QAAQ,EACtB,SAAS,EACT;YACE,YAAY;YACZ,MAAM;SACP,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,gBAAgB,CACpB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,WAAmB,EACnB,cAAuB,KAAK,EAC5B,MAAe,EACf,YAAqB;QAErB,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,YAAY,EACtB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,gBAAgB,EAAE,UAAU;YAC5B,WAAW;YACX,YAAY;YACZ,MAAM;SACP,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,WAAmB,EACnB,iBAA0B,KAAK;QAE/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,gBAAgB,EAC1B,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,gBAAgB,EAAE,SAAS;YAC3B,cAAc;SACf,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,SAAiB,EAAE,YAAqB;QACrF,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,iBAAiB,EAC3B,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;SACb,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,SAAiB,EAAE,SAAiB,EAAE,QAAiB;QAC5E,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,YAAY,EACtB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,QAAQ;SACT,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,SAAiB,EAAE,SAAkB,EAAE,YAAqB;QACtG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,cAAc,EACxB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,SAAS;YACT,YAAY;SACb,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,SAAiB,EACjB,YAAoB,EACpB,cAAsB,EACtB,WAAmB,EACnB,MAAc,EACd,UAAmB;QAEnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,iBAAiB,EAC3B,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,YAAY;YACZ,cAAc;YACd,WAAW;YACX,UAAU;YACV,KAAK,EAAE,cAAc;SACtB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,mBAAmB,CACvB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,MAAe,EACf,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,eAAe,EACzB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,YAAY;YACZ,aAAa,EAAE,MAAM;SACtB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,aAAuB,EACvB,MAAc;QAEd,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,eAAe,EACzB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,aAAa;SACd,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,OAAe,EACf,MAAc;QAEd,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,cAAc,EACxB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,OAAO;SACR,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,cAAsB,EACtB,eAAwB,EACxB,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,cAAc,EACxB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,cAAc;YACd,eAAe;YACf,KAAK,EAAE,eAAe,IAAI,CAAC;SAC5B,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,SAAiB,EACjB,aAAsB,EACtB,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,WAAW,EACrB,sCAAa,CAAC,OAAO,EACrB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,SAAS;YACT,aAAa;YACb,KAAK,EAAE,SAAS;SACjB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,wBAAwB,CAC5B,cAAsB,EACtB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,WAA+B;QAE/B,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,oBAAoB,EAC9B,sCAAa,CAAC,UAAU,EACxB,SAAS,EACT;YACE,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,WAAW;SACZ,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,cAAsB,EACtB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,QAAgB,EAChB,YAAoB;QAEpB,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,kBAAkB,EAC5B,sCAAa,CAAC,UAAU,EACxB,SAAS,EACT;YACE,cAAc;YACd,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,YAAY;YACZ,KAAK,EAAE,YAAY;SACpB,CACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,YAAoB,EACpB,MAAc;QAEd,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,cAAc,EACxB,sCAAa,CAAC,UAAU,EACxB,SAAS,EACT;YACE,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,KAAK,EAAE,YAAY;SACpB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,SAAiB,EACjB,YAAoB,EACpB,YAAoB,EACpB,YAAoB,EACpB,MAAc;QAEd,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,sBAAsB,EAChC,sCAAa,CAAC,UAAU,EACxB,SAAS,EACT;YACE,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,KAAK,EAAE,YAAY;SACpB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,eAAe,CACnB,SAAiB,EACjB,QAAgB,EAChB,MAAc,EACd,YAAoB,EACpB,UAAkB,EAClB,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,WAAW,EACrB,sCAAa,CAAC,MAAM,EACpB,SAAS,EACT;YACE,QAAQ;YACR,MAAM;YACN,YAAY;YACZ,UAAU;YACV,QAAQ,EAAE,YAAY;SACvB,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CACd,SAAiB,EACjB,SAAiB,EACjB,YAAoB,EACpB,OAAgB,EAChB,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,cAAc,EACxB,sCAAa,CAAC,MAAM,EACpB,SAAS,EACT;YACE,SAAS;YACT,YAAY;YACZ,OAAO;SACR,EACD,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,SAAiB,EACjB,UAAkB,EAClB,KAAa,EACb,IAAY,EACZ,OAAgB;QAEhB,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,kCAAS,CAAC,kBAAkB,EAC5B,sCAAa,CAAC,MAAM,EACpB,SAAS,EACT;YACE,UAAU;YACV,KAAK;YACL,IAAI;YACJ,OAAO;SACR,CACF,CAAC;IACJ,CAAC;CACF,CAAA;AA9cY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAI2B,oCAAgB;GAH3C,eAAe,CA8c3B"}