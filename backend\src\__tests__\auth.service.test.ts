import { AuthService } from '../services/auth.service'
import { UserRepository } from '../repositories/user.repository'
import { CompanyRepository } from '../repositories/company.repository'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { UserStatus, CompanyStatus, UserRole } from '@prisma/client'

// Mock das dependências
jest.mock('../repositories/user.repository')
jest.mock('../repositories/company.repository')
jest.mock('bcryptjs')
jest.mock('jsonwebtoken')

describe('AuthService', () => {
  let authService: AuthService
  let mockUserRepository: jest.Mocked<UserRepository>
  let mockCompanyRepository: jest.Mocked<CompanyRepository>

  beforeEach(() => {
    mockUserRepository = new UserRepository() as jest.Mocked<UserRepository>
    mockCompanyRepository = new CompanyRepository() as jest.Mocked<CompanyRepository>
    authService = new AuthService()
    
    // Injetar mocks
    ;(authService as any).userRepository = mockUserRepository
    ;(authService as any).companyRepository = mockCompanyRepository
  })

  describe('login', () => {
    const mockUser = {
      id: 'user-1',
      firstName: 'João',
      lastName: 'Silva',
      email: '<EMAIL>',
      password: 'hashedPassword',
      status: UserStatus.ACTIVE,
      companyId: 'company-1',
      company: {
        id: 'company-1',
        name: 'Empresa Teste',
        status: CompanyStatus.ACTIVE,
      },
      refreshToken: null,
      refreshTokenExpiry: null,
    }

    it('deve fazer login com credenciais válidas', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      mockUserRepository.findByEmail.mockResolvedValue(mockUser as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(true)
      ;(jwt.sign as jest.Mock).mockReturnValue('mock-token')
      mockUserRepository.setRefreshToken.mockResolvedValue(mockUser as any)
      mockUserRepository.updateLastLogin.mockResolvedValue(mockUser as any)

      // Act
      const result = await authService.login(loginData)

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(loginData.email)
      expect(bcrypt.compare).toHaveBeenCalledWith(loginData.password, mockUser.password)
      expect(result).toHaveProperty('user')
      expect(result).toHaveProperty('accessToken')
      expect(result).toHaveProperty('refreshToken')
      expect(result.user).not.toHaveProperty('password')
    })

    it('deve rejeitar login com email inválido', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      mockUserRepository.findByEmail.mockResolvedValue(null)

      // Act & Assert
      await expect(authService.login(loginData)).rejects.toThrow('Credenciais inválidas')
    })

    it('deve rejeitar login com senha inválida', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }

      mockUserRepository.findByEmail.mockResolvedValue(mockUser as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(false)

      // Act & Assert
      await expect(authService.login(loginData)).rejects.toThrow('Credenciais inválidas')
    })

    it('deve rejeitar login com usuário inativo', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const inactiveUser = { ...mockUser, status: UserStatus.INACTIVE }
      mockUserRepository.findByEmail.mockResolvedValue(inactiveUser as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(true)

      // Act & Assert
      await expect(authService.login(loginData)).rejects.toThrow('Usuário inativo')
    })

    it('deve rejeitar login com empresa suspensa', async () => {
      // Arrange
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const userWithSuspendedCompany = {
        ...mockUser,
        company: { ...mockUser.company, status: CompanyStatus.SUSPENDED }
      }
      mockUserRepository.findByEmail.mockResolvedValue(userWithSuspendedCompany as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(true)

      // Act & Assert
      await expect(authService.login(loginData)).rejects.toThrow('Empresa inativa')
    })
  })

  describe('register', () => {
    const registerData = {
      firstName: 'João',
      lastName: 'Silva',
      email: '<EMAIL>',
      password: 'password123',
      phone: '11999999999',
      companyName: 'Empresa Teste',
      cnpj: '12345678000199'
    }

    it('deve registrar novo usuário e empresa', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null)
      mockCompanyRepository.findByCnpj.mockResolvedValue(null)
      ;(bcrypt.hash as jest.Mock).mockResolvedValue('hashedPassword')
      
      const mockCompany = {
        id: 'company-1',
        name: 'Empresa Teste',
        cnpj: '12345678000199'
      }
      
      const mockUser = {
        id: 'user-1',
        firstName: 'João',
        lastName: 'Silva',
        email: '<EMAIL>',
        companyId: 'company-1'
      }

      mockCompanyRepository.create.mockResolvedValue(mockCompany as any)
      mockUserRepository.create.mockResolvedValue(mockUser as any)
      ;(jwt.sign as jest.Mock).mockReturnValue('mock-token')
      mockUserRepository.setRefreshToken.mockResolvedValue(mockUser as any)

      // Act
      const result = await authService.register(registerData)

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(registerData.email)
      expect(mockCompanyRepository.findByCnpj).toHaveBeenCalledWith(registerData.cnpj)
      expect(mockCompanyRepository.create).toHaveBeenCalled()
      expect(mockUserRepository.create).toHaveBeenCalled()
      expect(result).toHaveProperty('user')
      expect(result).toHaveProperty('accessToken')
      expect(result).toHaveProperty('refreshToken')
    })

    it('deve rejeitar registro com email já existente', async () => {
      // Arrange
      const existingUser = { id: 'user-1', email: '<EMAIL>' }
      mockUserRepository.findByEmail.mockResolvedValue(existingUser as any)

      // Act & Assert
      await expect(authService.register(registerData)).rejects.toThrow('Email já está em uso')
    })

    it('deve rejeitar registro com CNPJ já existente', async () => {
      // Arrange
      mockUserRepository.findByEmail.mockResolvedValue(null)
      const existingCompany = { id: 'company-1', cnpj: '12345678000199' }
      mockCompanyRepository.findByCnpj.mockResolvedValue(existingCompany as any)

      // Act & Assert
      await expect(authService.register(registerData)).rejects.toThrow('CNPJ já está em uso')
    })
  })

  describe('verifyToken', () => {
    it('deve verificar token válido', async () => {
      // Arrange
      const token = 'valid-token'
      const decoded = { userId: 'user-1' }
      ;(jwt.verify as jest.Mock).mockReturnValue(decoded)

      // Act
      const result = await authService.verifyToken(token)

      // Assert
      expect(jwt.verify).toHaveBeenCalledWith(token, process.env.JWT_SECRET)
      expect(result).toEqual(decoded)
    })

    it('deve rejeitar token inválido', async () => {
      // Arrange
      const token = 'invalid-token'
      ;(jwt.verify as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token')
      })

      // Act & Assert
      await expect(authService.verifyToken(token)).rejects.toThrow('Token inválido')
    })
  })

  describe('changePassword', () => {
    const mockUser = {
      id: 'user-1',
      password: 'hashedCurrentPassword'
    }

    it('deve alterar senha com senha atual correta', async () => {
      // Arrange
      const userId = 'user-1'
      const currentPassword = 'currentPassword'
      const newPassword = 'newPassword'

      mockUserRepository.findById.mockResolvedValue(mockUser as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(true)
      ;(bcrypt.hash as jest.Mock).mockResolvedValue('hashedNewPassword')
      mockUserRepository.update.mockResolvedValue(mockUser as any)
      mockUserRepository.clearRefreshToken.mockResolvedValue(mockUser as any)

      // Act
      await authService.changePassword(userId, currentPassword, newPassword)

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId)
      expect(bcrypt.compare).toHaveBeenCalledWith(currentPassword, mockUser.password)
      expect(bcrypt.hash).toHaveBeenCalledWith(newPassword, 12)
      expect(mockUserRepository.update).toHaveBeenCalled()
      expect(mockUserRepository.clearRefreshToken).toHaveBeenCalledWith(userId)
    })

    it('deve rejeitar alteração com senha atual incorreta', async () => {
      // Arrange
      const userId = 'user-1'
      const currentPassword = 'wrongPassword'
      const newPassword = 'newPassword'

      mockUserRepository.findById.mockResolvedValue(mockUser as any)
      ;(bcrypt.compare as jest.Mock).mockResolvedValue(false)

      // Act & Assert
      await expect(authService.changePassword(userId, currentPassword, newPassword))
        .rejects.toThrow('Senha atual incorreta')
    })
  })
})
