'use client'

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Smartphone, 
  Plus, 
  QrCode, 
  Wifi, 
  WifiOff, 
  MoreVertical,
  ArrowLeft,
  Settings,
  Trash2,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock,
  MessageSquare,
  Users,
  TrendingUp
} from "lucide-react"
import Link from "next/link"

interface Connection {
  id: string
  name: string
  phone: string
  status: 'connected' | 'disconnected' | 'connecting' | 'error'
  lastConnected: Date
  messagesCount: number
  contactsCount: number
  qrCode?: string
}

export default function ConnectionsPage() {
  const [connections, setConnections] = useState<Connection[]>([
    {
      id: '1',
      name: '<PERSON>endas Principal',
      phone: '+55 11 99999-9999',
      status: 'connected',
      lastConnected: new Date(Date.now() - 5 * 60 * 1000),
      messagesCount: 1247,
      contactsCount: 156
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON> T<PERSON>',
      phone: '+55 11 88888-8888',
      status: 'connected',
      lastConnected: new Date(Date.now() - 10 * 60 * 1000),
      messagesCount: 856,
      contactsCount: 89
    },
    {
      id: '3',
      name: 'Marketing',
      phone: '+55 11 77777-7777',
      status: 'disconnected',
      lastConnected: new Date(Date.now() - 2 * 60 * 60 * 1000),
      messagesCount: 423,
      contactsCount: 67
    }
  ])

  const [showNewConnectionModal, setShowNewConnectionModal] = useState(false)
  const [newConnection, setNewConnection] = useState({
    name: '',
    phone: ''
  })

  const getStatusIcon = (status: Connection['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'disconnected':
        return <WifiOff className="w-5 h-5 text-red-500" />
      case 'connecting':
        return <Clock className="w-5 h-5 text-yellow-500 animate-spin" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />
    }
  }

  const getStatusColor = (status: Connection['status']) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'disconnected':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'connecting':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
    }
  }

  const getStatusText = (status: Connection['status']) => {
    switch (status) {
      case 'connected':
        return 'Conectado'
      case 'disconnected':
        return 'Desconectado'
      case 'connecting':
        return 'Conectando...'
      case 'error':
        return 'Erro'
    }
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    
    if (minutes < 60) {
      return `${minutes} min atrás`
    } else if (hours < 24) {
      return `${hours}h atrás`
    } else {
      return date.toLocaleDateString('pt-BR')
    }
  }

  const connectWhatsApp = (connectionId: string) => {
    setConnections(prev => 
      prev.map(conn => 
        conn.id === connectionId 
          ? { ...conn, status: 'connecting' }
          : conn
      )
    )

    // Simular conexão
    setTimeout(() => {
      setConnections(prev => 
        prev.map(conn => 
          conn.id === connectionId 
            ? { ...conn, status: 'connected', lastConnected: new Date() }
            : conn
        )
      )
    }, 3000)
  }

  const disconnectWhatsApp = (connectionId: string) => {
    setConnections(prev => 
      prev.map(conn => 
        conn.id === connectionId 
          ? { ...conn, status: 'disconnected' }
          : conn
      )
    )
  }

  const createConnection = () => {
    if (!newConnection.name || !newConnection.phone) return

    const connection: Connection = {
      id: Date.now().toString(),
      name: newConnection.name,
      phone: newConnection.phone,
      status: 'disconnected',
      lastConnected: new Date(),
      messagesCount: 0,
      contactsCount: 0
    }

    setConnections(prev => [...prev, connection])
    setNewConnection({ name: '', phone: '' })
    setShowNewConnectionModal(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-md border-b border-white/20 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="w-5 h-5" />
                </Button>
              </Link>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-xl flex items-center justify-center">
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Conexões WhatsApp</h1>
                  <p className="text-sm text-gray-600">Gerencie suas conexões WhatsApp Business</p>
                </div>
              </div>
            </div>

            <Button 
              onClick={() => setShowNewConnectionModal(true)}
              variant="whatsapp" 
              className="shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              Nova Conexão
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Conexões</p>
                <p className="text-3xl font-bold text-gray-900">{connections.length}</p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600">
                <Smartphone className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Conexões Ativas</p>
                <p className="text-3xl font-bold text-gray-900">
                  {connections.filter(c => c.status === 'connected').length}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600">
                <Wifi className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Mensagens</p>
                <p className="text-3xl font-bold text-gray-900">
                  {connections.reduce((acc, c) => acc + c.messagesCount, 0).toLocaleString()}
                </p>
              </div>
              <div className="p-3 rounded-xl bg-gradient-to-r from-purple-500 to-purple-600">
                <MessageSquare className="w-6 h-6 text-white" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Connections List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {connections.map((connection, index) => (
            <motion.div
              key={connection.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center">
                    <Smartphone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900">{connection.name}</h3>
                    <p className="text-sm text-gray-600">{connection.phone}</p>
                  </div>
                </div>

                <Button variant="ghost" size="icon">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>

              <div className="mb-4">
                <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(connection.status)}`}>
                  {getStatusIcon(connection.status)}
                  <span>{getStatusText(connection.status)}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-gray-600">
                    <MessageSquare className="w-4 h-4" />
                    <span className="text-sm">Mensagens</span>
                  </div>
                  <p className="text-xl font-bold text-gray-900">{connection.messagesCount.toLocaleString()}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-gray-600">
                    <Users className="w-4 h-4" />
                    <span className="text-sm">Contatos</span>
                  </div>
                  <p className="text-xl font-bold text-gray-900">{connection.contactsCount}</p>
                </div>
              </div>

              <div className="text-center text-sm text-gray-500 mb-4">
                Última conexão: {formatTime(connection.lastConnected)}
              </div>

              <div className="flex space-x-2">
                {connection.status === 'connected' ? (
                  <>
                    <Button
                      onClick={() => disconnectWhatsApp(connection.id)}
                      variant="outline"
                      size="sm"
                      className="flex-1"
                    >
                      <WifiOff className="w-4 h-4 mr-2" />
                      Desconectar
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Settings className="w-4 h-4" />
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      onClick={() => connectWhatsApp(connection.id)}
                      variant="whatsapp"
                      size="sm"
                      className="flex-1"
                      loading={connection.status === 'connecting'}
                    >
                      {connection.status === 'connecting' ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Conectando...
                        </>
                      ) : (
                        <>
                          <QrCode className="w-4 h-4 mr-2" />
                          Conectar
                        </>
                      )}
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal Nova Conexão */}
      {showNewConnectionModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-2xl p-6 w-full max-w-md"
          >
            <h2 className="text-xl font-bold text-gray-900 mb-4">Nova Conexão WhatsApp</h2>

            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nome da Conexão</Label>
                <Input
                  id="name"
                  value={newConnection.name}
                  onChange={(e) => setNewConnection({ ...newConnection, name: e.target.value })}
                  placeholder="Ex: Vendas Principal"
                />
              </div>

              <div>
                <Label htmlFor="phone">Número do WhatsApp</Label>
                <Input
                  id="phone"
                  value={newConnection.phone}
                  onChange={(e) => setNewConnection({ ...newConnection, phone: e.target.value })}
                  placeholder="Ex: +55 11 99999-9999"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                onClick={() => setShowNewConnectionModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancelar
              </Button>
              <Button
                onClick={createConnection}
                variant="whatsapp"
                className="flex-1"
              >
                Criar Conexão
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}
