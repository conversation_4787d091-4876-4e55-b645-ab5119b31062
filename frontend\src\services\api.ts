import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { 
  ApiResponse, 
  PaginatedResponse, 
  User, 
  AuthResponse, 
  LoginRequest, 
  RegisterRequest,
  WhatsAppConnection,
  Message,
  Contact,
  Conversation,
  DashboardStats,
  MessageFilters,
  ContactFilters,
  ConnectionFilters,
  SendMessageForm,
  CreateConnectionForm
} from '@/types'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor para adicionar token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor para tratar erros
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expirado, tentar renovar
          const refreshToken = this.getRefreshToken()
          if (refreshToken) {
            try {
              const response = await this.refreshToken()
              this.setTokens(response.data.accessToken, response.data.refreshToken)
              
              // Repetir a requisição original
              const originalRequest = error.config
              originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`
              return this.api.request(originalRequest)
            } catch (refreshError) {
              this.clearTokens()
              window.location.href = '/auth/login'
            }
          } else {
            this.clearTokens()
            window.location.href = '/auth/login'
          }
        }
        return Promise.reject(error)
      }
    )
  }

  // Token management
  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('accessToken')
    }
    return null
  }

  private getRefreshToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('refreshToken')
    }
    return null
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', accessToken)
      localStorage.setItem('refreshToken', refreshToken)
    }
  }

  private clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
    }
  }

  // Auth endpoints
  async login(data: LoginRequest): Promise<AxiosResponse<AuthResponse>> {
    const response = await this.api.post('/auth/login', data)
    this.setTokens(response.data.accessToken, response.data.refreshToken)
    return response
  }

  async register(data: RegisterRequest): Promise<AxiosResponse<AuthResponse>> {
    const response = await this.api.post('/auth/register', data)
    this.setTokens(response.data.accessToken, response.data.refreshToken)
    return response
  }

  async logout(): Promise<AxiosResponse<ApiResponse>> {
    const response = await this.api.post('/auth/logout')
    this.clearTokens()
    return response
  }

  async refreshToken(): Promise<AxiosResponse<AuthResponse>> {
    const refreshToken = this.getRefreshToken()
    return this.api.post('/auth/refresh', { refreshToken })
  }

  async getMe(): Promise<AxiosResponse<ApiResponse<User>>> {
    return this.api.get('/auth/me')
  }

  // WhatsApp Connection endpoints
  async getConnections(filters?: ConnectionFilters): Promise<AxiosResponse<ApiResponse<WhatsAppConnection[]>>> {
    return this.api.get('/whatsapp/connections', { params: filters })
  }

  async getConnection(id: string): Promise<AxiosResponse<ApiResponse<WhatsAppConnection>>> {
    return this.api.get(`/whatsapp/connections/${id}`)
  }

  async createConnection(data: CreateConnectionForm): Promise<AxiosResponse<ApiResponse<WhatsAppConnection>>> {
    return this.api.post('/whatsapp/connections', data)
  }

  async updateConnection(id: string, data: Partial<WhatsAppConnection>): Promise<AxiosResponse<ApiResponse<WhatsAppConnection>>> {
    return this.api.patch(`/whatsapp/connections/${id}`, data)
  }

  async deleteConnection(id: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.delete(`/whatsapp/connections/${id}`)
  }

  async connectWhatsApp(id: string): Promise<AxiosResponse<ApiResponse<{ qrCode?: string }>>> {
    return this.api.post(`/whatsapp/connections/${id}/connect`)
  }

  async disconnectWhatsApp(id: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.post(`/whatsapp/connections/${id}/disconnect`)
  }

  // Message endpoints
  async getMessages(filters?: MessageFilters & { page?: number; limit?: number }): Promise<AxiosResponse<PaginatedResponse<Message>>> {
    return this.api.get('/messages', { params: filters })
  }

  async getMessage(id: string): Promise<AxiosResponse<ApiResponse<Message>>> {
    return this.api.get(`/messages/${id}`)
  }

  async getConversation(contactPhone: string, whatsappConnectionId: string): Promise<AxiosResponse<ApiResponse<Message[]>>> {
    return this.api.get(`/messages/conversation/${contactPhone}/${whatsappConnectionId}`)
  }

  async sendMessage(data: SendMessageForm): Promise<AxiosResponse<ApiResponse<Message>>> {
    return this.api.post(`/whatsapp/connections/${data.whatsappConnectionId}/send-message`, {
      contactPhone: data.contactPhone,
      content: data.content,
      type: data.type
    })
  }

  async sendBulkMessage(connectionId: string, data: { contacts: string[]; content: string; type: string }): Promise<AxiosResponse<ApiResponse>> {
    return this.api.post(`/whatsapp/connections/${connectionId}/send-bulk-message`, data)
  }

  async markMessageAsRead(messageId: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.patch(`/messages/${messageId}/status`, { status: 'read' })
  }

  // Contact endpoints
  async getContacts(filters?: ContactFilters & { page?: number; limit?: number }): Promise<AxiosResponse<PaginatedResponse<Contact>>> {
    return this.api.get('/contacts', { params: filters })
  }

  async getContact(id: string): Promise<AxiosResponse<ApiResponse<Contact>>> {
    return this.api.get(`/contacts/${id}`)
  }

  async createContact(data: Partial<Contact>): Promise<AxiosResponse<ApiResponse<Contact>>> {
    return this.api.post('/contacts', data)
  }

  async updateContact(id: string, data: Partial<Contact>): Promise<AxiosResponse<ApiResponse<Contact>>> {
    return this.api.patch(`/contacts/${id}`, data)
  }

  async deleteContact(id: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.delete(`/contacts/${id}`)
  }

  async addContactTag(id: string, tag: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.post(`/contacts/${id}/tags`, { tag })
  }

  async removeContactTag(id: string, tagId: string): Promise<AxiosResponse<ApiResponse>> {
    return this.api.delete(`/contacts/${id}/tags/${tagId}`)
  }

  // Analytics endpoints
  async getDashboardStats(): Promise<AxiosResponse<ApiResponse<DashboardStats>>> {
    return this.api.get('/analytics/dashboard')
  }

  async getConversationStats(filters?: { startDate?: Date; endDate?: Date }): Promise<AxiosResponse<ApiResponse<any>>> {
    return this.api.get('/analytics/conversations', { params: filters })
  }

  async getUserActivity(filters?: { startDate?: Date; endDate?: Date }): Promise<AxiosResponse<ApiResponse<any>>> {
    return this.api.get('/analytics/user-activity', { params: filters })
  }

  // User endpoints
  async getUsers(): Promise<AxiosResponse<ApiResponse<User[]>>> {
    return this.api.get('/users')
  }

  async getUser(id: string): Promise<AxiosResponse<ApiResponse<User>>> {
    return this.api.get(`/users/${id}`)
  }

  async updateUser(id: string, data: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> {
    return this.api.patch(`/users/${id}`, data)
  }

  async changePassword(id: string, data: { currentPassword: string; newPassword: string }): Promise<AxiosResponse<ApiResponse>> {
    return this.api.patch(`/users/${id}/change-password`, data)
  }

  // File upload
  async uploadFile(file: File, type: 'avatar' | 'media' | 'document'): Promise<AxiosResponse<ApiResponse<{ url: string }>>> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', type)
    
    return this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  }
}

export const apiService = new ApiService()
export default apiService
