"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionUsageDto = exports.CancelSubscriptionDto = exports.ChangePlanDto = exports.UpdateSubscriptionDto = exports.CreateSubscriptionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const subscription_entity_1 = require("../../../database/entities/subscription.entity");
class CreateSubscriptionDto {
    companyId;
    planId;
    type;
    startDate;
    trialEndDate;
    price;
    setupFee;
    discountPercentage;
    discountAmount;
    discountEndDate;
    couponCode;
    agencyId;
    agencyCommissionPercentage;
    agencyFixedCommission;
    paymentMethodId;
    autoRenew;
    isProrated;
    customFeatures;
    metadata;
    emailNotifications;
    webhookNotifications;
    webhookUrl;
}
exports.CreateSubscriptionDto = CreateSubscriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID da empresa' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID do plano' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "planId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tipo da assinatura', enum: subscription_entity_1.SubscriptionType, default: subscription_entity_1.SubscriptionType.DIRECT }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(subscription_entity_1.SubscriptionType),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de início', example: '2023-12-01T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de fim do trial', example: '2023-12-15T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "trialEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Preço customizado', example: 199.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Taxa de setup', example: 50, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "setupFee", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Percentual de desconto', example: 10, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "discountPercentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Valor do desconto', example: 50, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de fim do desconto', example: '2024-01-01T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "discountEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Código do cupom', example: 'DESCONTO20' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "couponCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID da agência' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "agencyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Percentual de comissão da agência', example: 15, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "agencyCommissionPercentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Comissão fixa da agência', example: 30, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateSubscriptionDto.prototype, "agencyFixedCommission", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do método de pagamento' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "paymentMethodId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Renovação automática', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSubscriptionDto.prototype, "autoRenew", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'É proration', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSubscriptionDto.prototype, "isProrated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Funcionalidades customizadas' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateSubscriptionDto.prototype, "customFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Metadados adicionais' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateSubscriptionDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notificações por email', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSubscriptionDto.prototype, "emailNotifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notificações por webhook', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateSubscriptionDto.prototype, "webhookNotifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL do webhook' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateSubscriptionDto.prototype, "webhookUrl", void 0);
class UpdateSubscriptionDto {
    status;
    endDate;
    trialEndDate;
    price;
    discountPercentage;
    discountAmount;
    discountEndDate;
    couponCode;
    paymentMethodId;
    autoRenew;
    cancelAtPeriodEnd;
    customFeatures;
    metadata;
    emailNotifications;
    webhookNotifications;
    webhookUrl;
}
exports.UpdateSubscriptionDto = UpdateSubscriptionDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Status da assinatura', enum: subscription_entity_1.SubscriptionStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(subscription_entity_1.SubscriptionStatus),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de fim', example: '2024-12-01T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de fim do trial', example: '2023-12-15T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "trialEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Preço customizado', example: 199.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateSubscriptionDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Percentual de desconto', example: 10 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], UpdateSubscriptionDto.prototype, "discountPercentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Valor do desconto', example: 50 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateSubscriptionDto.prototype, "discountAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data de fim do desconto', example: '2024-01-01T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "discountEndDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Código do cupom', example: 'DESCONTO20' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "couponCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do método de pagamento' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "paymentMethodId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Renovação automática' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSubscriptionDto.prototype, "autoRenew", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Cancelar no fim do período' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSubscriptionDto.prototype, "cancelAtPeriodEnd", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Funcionalidades customizadas' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateSubscriptionDto.prototype, "customFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Metadados adicionais' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateSubscriptionDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notificações por email' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSubscriptionDto.prototype, "emailNotifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Notificações por webhook' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateSubscriptionDto.prototype, "webhookNotifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL do webhook' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateSubscriptionDto.prototype, "webhookUrl", void 0);
class ChangePlanDto {
    newPlanId;
    changeDate;
    isProrated;
    customPrice;
    reason;
}
exports.ChangePlanDto = ChangePlanDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID do novo plano' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ChangePlanDto.prototype, "newPlanId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Data da mudança', example: '2023-12-01T00:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ChangePlanDto.prototype, "changeDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'É proration', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ChangePlanDto.prototype, "isProrated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Preço customizado para o novo plano', example: 299.99 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ChangePlanDto.prototype, "customPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Motivo da mudança', example: 'Upgrade para mais funcionalidades' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ChangePlanDto.prototype, "reason", void 0);
class CancelSubscriptionDto {
    immediately;
    reason;
    feedback;
}
exports.CancelSubscriptionDto = CancelSubscriptionDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Cancelar imediatamente', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CancelSubscriptionDto.prototype, "immediately", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Motivo do cancelamento', example: 'Não preciso mais do serviço' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CancelSubscriptionDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Feedback adicional', example: 'Preço muito alto' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CancelSubscriptionDto.prototype, "feedback", void 0);
class SubscriptionUsageDto {
    connections;
    contacts;
    messages;
    users;
    automations;
    chatbots;
    storageUsedGB;
    apiCalls;
    webhookCalls;
}
exports.SubscriptionUsageDto = SubscriptionUsageDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Conexões utilizadas', example: 3 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "connections", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Contatos utilizados', example: 5000 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "contacts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mensagens utilizadas', example: 25000 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "messages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Usuários utilizados', example: 5 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "users", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Automações utilizadas', example: 10 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "automations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Chatbots utilizados', example: 2 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "chatbots", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Armazenamento utilizado em GB', example: 25.5 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "storageUsedGB", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Chamadas de API utilizadas', example: 10000 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "apiCalls", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Chamadas de webhook utilizadas', example: 500 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SubscriptionUsageDto.prototype, "webhookCalls", void 0);
//# sourceMappingURL=subscription-dto.js.map