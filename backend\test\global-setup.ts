import { MongoMemoryServer } from 'mongodb-memory-server';

let mongod: MongoMemoryServer;

export default async function globalSetup() {
  // Configurar MongoDB em memória para testes
  mongod = await MongoMemoryServer.create({
    instance: {
      dbName: 'test',
      port: 27017,
    },
  });

  const uri = mongod.getUri();
  process.env.MONGODB_URI = uri;

  // Configurar variáveis de ambiente para testes
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-secret-key-for-testing';
  process.env.JWT_EXPIRES_IN = '1h';
  process.env.DATABASE_URL = 'sqlite::memory:';
  process.env.REDIS_URL = 'redis://localhost:6379/1';
  process.env.PORT = '3001';
  process.env.LOG_LEVEL = 'error';

  // Configurações específicas para testes
  process.env.WHATSAPP_API_URL = 'http://localhost:8080';
  process.env.WHATSAPP_API_KEY = 'test-api-key';
  process.env.STRIPE_SECRET_KEY = 'sk_test_123';
  process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_123';

  console.log('Global test setup completed');
}
