import { Model } from 'mongoose';
import { ContactDocument, ContactStatus } from '../../../database/entities';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
export interface FindContactsOptions {
    companyId?: string;
    whatsappConnectionId?: string;
    status?: ContactStatus;
    isLead?: boolean;
    assignedTo?: string;
    tags?: string[];
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class ContactsService {
    private contactModel;
    private readonly logger;
    constructor(contactModel: Model<ContactDocument>);
    create(createContactDto: CreateContactDto, currentUser: AuthenticatedUser): Promise<ContactDocument>;
    findAll(options: FindContactsOptions, currentUser: AuthenticatedUser): Promise<{
        contacts: ContactDocument[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<ContactDocument>;
    findByPhone(phoneNumber: string, whatsappConnectionId: string, currentUser: AuthenticatedUser): Promise<ContactDocument | null>;
    update(id: string, updateContactDto: UpdateContactDto, currentUser: AuthenticatedUser): Promise<ContactDocument>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    addTag(id: string, tagName: string, tagColor: string, currentUser: AuthenticatedUser): Promise<ContactDocument>;
    removeTag(id: string, tagId: string, currentUser: AuthenticatedUser): Promise<ContactDocument>;
    updateLastMessage(phoneNumber: string, whatsappConnectionId: string, companyId: string): Promise<void>;
    getContactStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        leads: number;
        blocked: number;
        archived: number;
    }>;
    getOrCreateContact(phoneNumber: string, whatsappConnectionId: string, companyId: string, name?: string): Promise<ContactDocument>;
}
