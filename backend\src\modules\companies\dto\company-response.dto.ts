import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { CompanyStatus } from '../../../database/entities';

export class CompanyResponseDto {
  @ApiProperty({
    description: 'ID da empresa',
    example: 'uuid-da-empresa',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Empresa Exemplo Ltda',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'CNPJ da empresa',
    example: '12.345.678/0001-90',
  })
  @Expose()
  cnpj: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '+5511999999999',
  })
  @Expose()
  phone: string;

  @ApiPropertyOptional({
    description: 'Endereço da empresa',
    example: 'Rua Exemplo, 123 - São Paulo, SP',
  })
  @Expose()
  address?: string;

  @ApiPropertyOptional({
    description: 'Website da empresa',
    example: 'https://www.empresa.com',
  })
  @Expose()
  website?: string;

  @ApiProperty({
    description: 'Status da empresa',
    enum: CompanyStatus,
  })
  @Expose()
  status: CompanyStatus;

  @ApiPropertyOptional({
    description: 'Logo da empresa',
    example: 'https://example.com/logo.jpg',
  })
  @Expose()
  logo?: string;

  @ApiPropertyOptional({
    description: 'ID da agência',
    example: 'uuid-da-agencia',
  })
  @Expose()
  agencyId?: string;

  @ApiProperty({
    description: 'É uma agência',
    example: false,
  })
  @Expose()
  @Transform(({ obj }) => obj.agencyId === null)
  isAgency: boolean;

  @ApiProperty({
    description: 'Número de usuários ativos',
    example: 5,
  })
  @Expose()
  @Transform(({ obj }) => obj.users?.filter(user => user.isActive).length || 0)
  activeUsersCount: number;

  @ApiPropertyOptional({
    description: 'Configurações da empresa',
  })
  @Expose()
  settings?: Record<string, any>;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;
}
