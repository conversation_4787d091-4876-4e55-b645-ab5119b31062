{"version": 3, "file": "DocSection.js", "sourceRoot": "", "sources": ["../../src/nodes/DocSection.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAAsD;AACtD,+CAA8C;AAC9C,uDAI4B;AAY5B;;GAEG;AACH;IAAgC,8BAAgB;IAC9C;;;OAGG;IACH,oBACE,UAA+D,EAC/D,UAAmC;QAEnC,OAAA,MAAK,YAAC,UAAU,EAAE,UAAU,CAAC,SAAC;IAChC,CAAC;IAGD,sBAAW,4BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;;;OAAA;IAED;;;OAGG;IACI,0CAAqB,GAA5B,UAA6B,OAAgB;QAC3C,IAAI,aAAa,GAA6B,SAAS,CAAC;QAExD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAM,QAAQ,GAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5D,IAAI,QAAQ,CAAC,IAAI,KAAK,qBAAW,CAAC,SAAS,EAAE,CAAC;gBAC5C,aAAa,GAAG,QAAwB,CAAC;YAC3C,CAAC;QACH,CAAC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,IAAI,2BAAY,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QACjC,CAAC;QAED,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAEM,2CAAsB,GAA7B,UAA8B,QAAgC;QAC5D,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;YAA5B,IAAM,OAAO,iBAAA;YAChB,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AA3CD,CAAgC,mCAAgB,GA2C/C;AA3CY,gCAAU", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { type DocNode, DocNodeKind } from './DocNode';\r\nimport { DocParagraph } from './DocParagraph';\r\nimport {\r\n  DocNodeContainer,\r\n  type IDocNodeContainerParameters,\r\n  type IDocNodeContainerParsedParameters\r\n} from './DocNodeContainer';\r\n\r\n/**\r\n * Constructor parameters for {@link DocSection}.\r\n */\r\nexport interface IDocSectionParameters extends IDocNodeContainerParameters {}\r\n\r\n/**\r\n * Constructor parameters for {@link DocSection}.\r\n */\r\nexport interface IDocSectionParsedParameters extends IDocNodeContainerParsedParameters {}\r\n\r\n/**\r\n * Represents a general block of rich text.\r\n */\r\nexport class DocSection extends DocNodeContainer {\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(\r\n    parameters: IDocSectionParameters | IDocSectionParsedParameters,\r\n    childNodes?: ReadonlyArray<DocNode>\r\n  ) {\r\n    super(parameters, childNodes);\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.Section;\r\n  }\r\n\r\n  /**\r\n   * If the last item in DocSection.nodes is not a DocParagraph, a new paragraph\r\n   * is started.  Either way, the provided docNode will be appended to the paragraph.\r\n   */\r\n  public appendNodeInParagraph(docNode: DocNode): void {\r\n    let paragraphNode: DocParagraph | undefined = undefined;\r\n\r\n    if (this.nodes.length > 0) {\r\n      const lastNode: DocNode = this.nodes[this.nodes.length - 1];\r\n      if (lastNode.kind === DocNodeKind.Paragraph) {\r\n        paragraphNode = lastNode as DocParagraph;\r\n      }\r\n    }\r\n    if (!paragraphNode) {\r\n      paragraphNode = new DocParagraph({ configuration: this.configuration });\r\n      this.appendNode(paragraphNode);\r\n    }\r\n\r\n    paragraphNode.appendNode(docNode);\r\n  }\r\n\r\n  public appendNodesInParagraph(docNodes: ReadonlyArray<DocNode>): void {\r\n    for (const docNode of docNodes) {\r\n      this.appendNodeInParagraph(docNode);\r\n    }\r\n  }\r\n}\r\n"]}