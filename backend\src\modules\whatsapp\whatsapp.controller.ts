import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { WhatsAppService, FindConnectionsOptions } from './whatsapp.service';
import { CreateConnectionDto } from './dto/create-connection.dto';
import { UpdateConnectionDto } from './dto/update-connection.dto';
import { SendMessageDto, BulkMessageDto } from './dto/send-message.dto';
import { ConnectionResponseDto } from './dto/connection-response.dto';
import { CurrentUser, AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions, PERMISSIONS } from '../../common/decorators/permissions.decorator';
import { Public } from '../../common/decorators/public.decorator';
import { UserRole, ConnectionStatus, ConnectionType } from '../../database/entities';

@ApiTags('WhatsApp')
@Controller('whatsapp')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class WhatsAppController {
  constructor(private readonly whatsappService: WhatsAppService) {}

  @Post('connections')
  @Roles(UserRole.SUPER_ADMIN, UserRole.AGENCY_ADMIN, UserRole.COMPANY_ADMIN)
  @RequirePermissions(PERMISSIONS.WHATSAPP_CONNECT)
  @ApiOperation({ summary: 'Criar nova conexão WhatsApp' })
  @ApiResponse({
    status: 201,
    description: 'Conexão criada com sucesso',
    type: ConnectionResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Número de telefone já está em uso',
  })
  async createConnection(
    @Body() createConnectionDto: CreateConnectionDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ConnectionResponseDto> {
    const connection = await this.whatsappService.createConnection(createConnectionDto, currentUser);
    return plainToClass(ConnectionResponseDto, connection, { excludeExtraneousValues: true });
  }

  @Get('connections')
  @RequirePermissions(PERMISSIONS.WHATSAPP_READ_MESSAGES)
  @ApiOperation({ summary: 'Listar conexões WhatsApp' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filtrar por empresa' })
  @ApiQuery({ name: 'status', required: false, enum: ConnectionStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'type', required: false, enum: ConnectionType, description: 'Filtrar por tipo' })
  @ApiQuery({ name: 'assignedUserId', required: false, description: 'Filtrar por usuário responsável' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nome ou número' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiResponse({
    status: 200,
    description: 'Lista de conexões',
    type: [ConnectionResponseDto],
  })
  async findAllConnections(
    @Query() query: FindConnectionsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    const result = await this.whatsappService.findAll(query, currentUser);
    
    return {
      ...result,
      connections: result.connections.map(connection => 
        plainToClass(ConnectionResponseDto, connection, { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('connections/:id')
  @RequirePermissions(PERMISSIONS.WHATSAPP_READ_MESSAGES)
  @ApiOperation({ summary: 'Obter conexão por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados da conexão',
    type: ConnectionResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Conexão não encontrada',
  })
  async findOneConnection(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ConnectionResponseDto> {
    const connection = await this.whatsappService.findOne(id, currentUser);
    return plainToClass(ConnectionResponseDto, connection, { excludeExtraneousValues: true });
  }

  @Patch('connections/:id')
  @Roles(UserRole.SUPER_ADMIN, UserRole.AGENCY_ADMIN, UserRole.COMPANY_ADMIN)
  @RequirePermissions(PERMISSIONS.WHATSAPP_CONNECT)
  @ApiOperation({ summary: 'Atualizar conexão' })
  @ApiResponse({
    status: 200,
    description: 'Conexão atualizada com sucesso',
    type: ConnectionResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Conexão não encontrada',
  })
  async updateConnection(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateConnectionDto: UpdateConnectionDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ConnectionResponseDto> {
    const connection = await this.whatsappService.update(id, updateConnectionDto, currentUser);
    return plainToClass(ConnectionResponseDto, connection, { excludeExtraneousValues: true });
  }

  @Delete('connections/:id')
  @Roles(UserRole.SUPER_ADMIN, UserRole.AGENCY_ADMIN, UserRole.COMPANY_ADMIN)
  @RequirePermissions(PERMISSIONS.WHATSAPP_DISCONNECT)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deletar conexão' })
  @ApiResponse({
    status: 204,
    description: 'Conexão deletada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Conexão não encontrada',
  })
  async removeConnection(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.whatsappService.remove(id, currentUser);
  }

  @Post('connections/:id/connect')
  @RequirePermissions(PERMISSIONS.WHATSAPP_CONNECT)
  @ApiOperation({ summary: 'Conectar WhatsApp' })
  @ApiResponse({
    status: 200,
    description: 'QR Code gerado para conexão',
    schema: {
      type: 'object',
      properties: {
        qrCode: { type: 'string', description: 'QR Code em base64' },
      },
    },
  })
  async connectWhatsApp(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.whatsappService.connect(id, currentUser);
  }

  @Post('connections/:id/disconnect')
  @RequirePermissions(PERMISSIONS.WHATSAPP_DISCONNECT)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Desconectar WhatsApp' })
  @ApiResponse({
    status: 204,
    description: 'WhatsApp desconectado com sucesso',
  })
  async disconnectWhatsApp(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.whatsappService.disconnect(id, currentUser);
  }

  @Post('connections/:id/send-message')
  @RequirePermissions(PERMISSIONS.WHATSAPP_SEND_MESSAGE)
  @ApiOperation({ summary: 'Enviar mensagem' })
  @ApiResponse({
    status: 200,
    description: 'Mensagem enviada com sucesso',
  })
  async sendMessage(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() sendMessageDto: SendMessageDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.whatsappService.sendMessage(id, sendMessageDto, currentUser);
  }

  @Post('connections/:id/send-bulk-message')
  @RequirePermissions(PERMISSIONS.WHATSAPP_SEND_MESSAGE)
  @ApiOperation({ summary: 'Enviar mensagem em massa' })
  @ApiResponse({
    status: 200,
    description: 'Mensagens enviadas',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          phoneNumber: { type: 'string' },
          success: { type: 'boolean' },
          result: { type: 'object' },
          error: { type: 'string' },
        },
      },
    },
  })
  async sendBulkMessage(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() bulkMessageDto: BulkMessageDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.whatsappService.sendBulkMessage(id, bulkMessageDto, currentUser);
  }

  @Post('webhook')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook para receber eventos do WhatsApp' })
  @ApiResponse({
    status: 200,
    description: 'Webhook processado com sucesso',
  })
  async handleWebhook(@Body() data: any) {
    return this.whatsappService.handleWebhook(data);
  }
}
