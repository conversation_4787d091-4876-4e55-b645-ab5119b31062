"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const common_2 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_entity_1 = require("./database/entities/user.entity");
const subscription_entity_1 = require("./database/entities/subscription.entity");
const payment_entity_1 = require("./database/entities/payment.entity");
const integration_entity_1 = require("./database/entities/integration.entity");
let HealthController = class HealthController {
    getHealth() {
        return {
            status: 'ok',
            message: 'WhatsApp Platform API está funcionando!',
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };
    }
    getHealthCheck() {
        return {
            status: 'healthy',
            database: 'connected',
            services: {
                auth: 'active',
                whatsapp: 'ready',
                billing: 'active'
            }
        };
    }
};
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "getHealth", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "getHealthCheck", null);
HealthController = __decorate([
    (0, common_1.Controller)()
], HealthController);
let TestAppModule = class TestAppModule {
};
TestAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            typeorm_1.TypeOrmModule.forRoot({
                type: 'sqlite',
                database: ':memory:',
                entities: [user_entity_1.User, subscription_entity_1.Subscription, payment_entity_1.Payment, integration_entity_1.Integration],
                synchronize: true,
                logging: true,
            }),
        ],
        controllers: [HealthController],
    })
], TestAppModule);
async function bootstrap() {
    const app = await core_1.NestFactory.create(TestAppModule);
    app.enableCors({
        origin: true,
        credentials: true,
    });
    app.useGlobalPipes(new common_2.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    app.setGlobalPrefix('api');
    const config = new swagger_1.DocumentBuilder()
        .setTitle('WhatsApp Platform API - Test')
        .setDescription('API de teste para plataforma de WhatsApp Business')
        .setVersion('1.0')
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const port = 3000;
    await app.listen(port);
    console.log(`🚀 Servidor de teste rodando em: http://localhost:${port}`);
    console.log(`📚 Documentação da API: http://localhost:${port}/api/docs`);
    console.log(`💚 Health check: http://localhost:${port}/api/health`);
}
bootstrap().catch(console.error);
//# sourceMappingURL=test-server.js.map