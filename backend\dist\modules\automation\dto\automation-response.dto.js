"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationExecutionResponseDto = exports.AutomationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const automation_schema_1 = require("../../../database/entities/automation.schema");
class AutomationResponseDto {
    id;
    name;
    description;
    type;
    status;
    companyId;
    createdBy;
    updatedBy;
    trigger;
    actions;
    flowSteps;
    startStepId;
    settings;
    executionCount;
    successCount;
    errorCount;
    successRate;
    lastExecutedAt;
    lastModifiedAt;
    version;
    isTemplate;
    templateCategory;
    isActive;
    stepCount;
    metadata;
    createdAt;
    updatedAt;
}
exports.AutomationResponseDto = AutomationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da automação',
        example: 'automation-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj._id?.toString() || obj.id),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da automação',
        example: 'Boas-vindas para novos contatos',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Descrição da automação',
        example: 'Envia mensagem de boas-vindas e coleta informações básicas',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da automação',
        enum: automation_schema_1.AutomationType,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da automação',
        enum: automation_schema_1.AutomationStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 'company-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.companyId?.toString()),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Criado por',
        example: 'user-id',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Atualizado por',
        example: 'user-id',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Configuração do gatilho',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationResponseDto.prototype, "trigger", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Passos de ação',
        type: [Object],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], AutomationResponseDto.prototype, "actions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Passos do fluxo',
        type: [Object],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], AutomationResponseDto.prototype, "flowSteps", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do passo inicial',
        example: 'step-1',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "startStepId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Configurações da automação',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationResponseDto.prototype, "settings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de execuções',
        example: 150,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "executionCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de sucessos',
        example: 142,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "successCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de erros',
        example: 8,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "errorCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de sucesso (%)',
        example: 94.67,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.executionCount === 0)
            return 0;
        return Math.round((obj.successCount / obj.executionCount) * 100 * 100) / 100;
    }),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "successRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Última execução',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationResponseDto.prototype, "lastExecutedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Última modificação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationResponseDto.prototype, "lastModifiedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Versão da automação',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "version", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é um template',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], AutomationResponseDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Categoria do template',
        example: 'vendas',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationResponseDto.prototype, "templateCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se está ativa',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.status === automation_schema_1.AutomationStatus.ACTIVE),
    __metadata("design:type", Boolean)
], AutomationResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de passos',
        example: 5,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => (obj.actions?.length || 0) + (obj.flowSteps?.length || 0)),
    __metadata("design:type", Number)
], AutomationResponseDto.prototype, "stepCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados adicionais',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de atualização',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationResponseDto.prototype, "updatedAt", void 0);
class AutomationExecutionResponseDto {
    id;
    automationId;
    automationName;
    status;
    context;
    steps;
    currentStepId;
    startedAt;
    completedAt;
    duration;
    error;
    retryCount;
    triggerType;
    results;
    progress;
    totalSteps;
    completedSteps;
    failedSteps;
    executionId;
    isTest;
    createdAt;
}
exports.AutomationExecutionResponseDto = AutomationExecutionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da execução',
        example: 'execution-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj._id?.toString() || obj.id),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da automação',
        example: 'automation-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.automationId?.toString()),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "automationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da automação',
        example: 'Boas-vindas para novos contatos',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "automationName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da execução',
        example: 'completed',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Contexto da execução',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationExecutionResponseDto.prototype, "context", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Passos executados',
        type: [Object],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], AutomationExecutionResponseDto.prototype, "steps", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Passo atual',
        example: 'step-2',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "currentStepId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de início',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationExecutionResponseDto.prototype, "startedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de conclusão',
        example: '2023-12-01T10:05:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationExecutionResponseDto.prototype, "completedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duração em milissegundos',
        example: 300000,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Erro da execução',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationExecutionResponseDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de tentativas',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "retryCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo do gatilho',
        example: 'message_received',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "triggerType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Resultados da execução',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AutomationExecutionResponseDto.prototype, "results", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Progresso (%)',
        example: 100,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => {
        if (obj.totalSteps === 0)
            return 0;
        return Math.round((obj.completedSteps / obj.totalSteps) * 100);
    }),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "progress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de passos',
        example: 5,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "totalSteps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Passos concluídos',
        example: 5,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "completedSteps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Passos falhados',
        example: 0,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AutomationExecutionResponseDto.prototype, "failedSteps", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único da execução',
        example: 'exec_123456789',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AutomationExecutionResponseDto.prototype, "executionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é um teste',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], AutomationExecutionResponseDto.prototype, "isTest", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], AutomationExecutionResponseDto.prototype, "createdAt", void 0);
//# sourceMappingURL=automation-response.dto.js.map