// PostgreSQL Entities
export { BaseEntity } from './base.entity';
export { Company, CompanyStatus } from './company.entity';
export { User, UserRole, UserStatus } from './user.entity';
export { WhatsAppConnection, ConnectionType, ConnectionStatus } from './whatsapp-connection.entity';
export { Subscription, SubscriptionStatus, SubscriptionPlan } from './subscription.entity';

// MongoDB Schemas
export { Contact, ContactDocument, ContactStatus, ContactTag, ContactSource } from './contact.schema';
export { Message, MessageDocument, MessageType, MessageDirection, MessageStatus, MessageMedia, MessageLocation, MessageContact } from './message.schema';

import { ContactSchema } from './contact.schema';
import { MessageSchema } from './message.schema';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
import { Contact } from './contact.schema';
import { Message } from './message.schema';

// Array de entidades para TypeORM
export const TypeOrmEntities = [
  Company,
  User,
  WhatsAppConnection,
  Subscription,
];

// Array de schemas para Mongoose
export const MongooseSchemas = [
  { name: Contact.name, schema: ContactSchema },
  { name: Message.name, schema: MessageSchema },
];

// Exportar schemas
export { ContactSchema } from './contact.schema';
export { MessageSchema } from './message.schema';
