// PostgreSQL Entities
export { BaseEntity } from './base.entity';
export { Company, CompanyStatus } from './company.entity';
export { User, UserRole, UserStatus } from './user.entity';
export { WhatsAppConnection, ConnectionType, ConnectionStatus } from './whatsapp-connection.entity';
export { Subscription, SubscriptionStatus, SubscriptionPlan } from './subscription.entity';

// MongoDB Schemas
export { Contact, ContactDocument, ContactStatus, ContactTag, ContactSource } from './contact.schema';
export { Message, MessageDocument, MessageType, MessageDirection, MessageStatus, MessageMedia, MessageLocation, MessageContact } from './message.schema';
export { AnalyticsEvent, AnalyticsEventDocument, EventType, EventCategory, EventProperties } from './analytics-event.schema';
export { Automation, AutomationDocument, AutomationType, AutomationStatus, TriggerType, ActionType, TriggerCondition, ActionStep, FlowStep } from './automation.schema';
export { AutomationExecution, AutomationExecutionDocument, ExecutionStatus, StepStatus, ExecutionContext, ExecutionStep } from './automation-execution.schema';

import { ContactSchema } from './contact.schema';
import { MessageSchema } from './message.schema';
import { AnalyticsEventSchema } from './analytics-event.schema';
import { AutomationSchema } from './automation.schema';
import { AutomationExecutionSchema } from './automation-execution.schema';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
import { Contact } from './contact.schema';
import { Message } from './message.schema';
import { AnalyticsEvent } from './analytics-event.schema';
import { Automation } from './automation.schema';
import { AutomationExecution } from './automation-execution.schema';

// Array de entidades para TypeORM
export const TypeOrmEntities = [
  Company,
  User,
  WhatsAppConnection,
  Subscription,
];

// Array de schemas para Mongoose
export const MongooseSchemas = [
  { name: Contact.name, schema: ContactSchema },
  { name: Message.name, schema: MessageSchema },
  { name: AnalyticsEvent.name, schema: AnalyticsEventSchema },
  { name: Automation.name, schema: AutomationSchema },
  { name: AutomationExecution.name, schema: AutomationExecutionSchema },
];

// Exportar schemas
export { ContactSchema } from './contact.schema';
export { MessageSchema } from './message.schema';
export { AnalyticsEventSchema } from './analytics-event.schema';
export { AutomationSchema } from './automation.schema';
export { AutomationExecutionSchema } from './automation-execution.schema';
