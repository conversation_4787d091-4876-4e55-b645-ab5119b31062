// PostgreSQL Entities
export { BaseEntity } from './base.entity';
export { Company, CompanyStatus } from './company.entity';
export { User, UserRole, UserStatus } from './user.entity';
export { WhatsAppConnection, ConnectionType, ConnectionStatus } from './whatsapp-connection.entity';
export { Subscription, SubscriptionStatus, SubscriptionType, UsageMetrics } from './subscription.entity';
export { Plan, PlanType, PlanStatus, BillingCycle, PlanFeatures } from './plan.entity';
export { Invoice, InvoiceStatus, InvoiceType, InvoiceLineItem, TaxDetails, DiscountDetails } from './invoice.entity';
export { Payment, PaymentStatus, PaymentMethod, PaymentGateway, PaymentMethodDetails, RefundDetails } from './payment.entity';
export { Integration, IntegrationType, IntegrationStatus, TriggerEvent, IntegrationConfig } from './integration.entity';
export { WebhookLog, WebhookStatus, WebhookRequest, WebhookResponse } from './webhook-log.entity';

// MongoDB Schemas (temporariamente desabilitado)
// export { Contact, ContactDocument, ContactStatus, ContactTag, ContactSource } from './contact.schema';
// export { Message, MessageDocument, MessageType, MessageDirection, MessageStatus, MessageMedia, MessageLocation, MessageContact } from './message.schema';
// export { AnalyticsEvent, AnalyticsEventDocument, EventType, EventCategory, EventProperties } from './analytics-event.schema';
// export { Automation, AutomationDocument, AutomationType, AutomationStatus, TriggerType, ActionType, TriggerCondition, ActionStep, FlowStep } from './automation.schema';
// export { AutomationExecution, AutomationExecutionDocument, ExecutionStatus, StepStatus, ExecutionContext, ExecutionStep } from './automation-execution.schema';

// import { ContactSchema } from './contact.schema';
// import { MessageSchema } from './message.schema';
// import { AnalyticsEventSchema } from './analytics-event.schema';
// import { AutomationSchema } from './automation.schema';
// import { AutomationExecutionSchema } from './automation-execution.schema';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
import { Plan } from './plan.entity';
import { Invoice } from './invoice.entity';
import { Payment } from './payment.entity';
import { Integration } from './integration.entity';
import { WebhookLog } from './webhook-log.entity';
// import { Contact } from './contact.schema';
// import { Message } from './message.schema';
// import { AnalyticsEvent } from './analytics-event.schema';
// import { Automation } from './automation.schema';
// import { AutomationExecution } from './automation-execution.schema';

// Array de entidades para TypeORM
export const TypeOrmEntities = [
  Company,
  User,
  WhatsAppConnection,
  Subscription,
  Plan,
  Invoice,
  Payment,
  Integration,
  WebhookLog,
];

// Array de schemas para Mongoose (temporariamente desabilitado)
// export const MongooseSchemas = [
//   { name: Contact.name, schema: ContactSchema },
//   { name: Message.name, schema: MessageSchema },
//   { name: AnalyticsEvent.name, schema: AnalyticsEventSchema },
//   { name: Automation.name, schema: AutomationSchema },
//   { name: AutomationExecution.name, schema: AutomationExecutionSchema },
// ];

// Exportar schemas (temporariamente desabilitado)
// export { ContactSchema } from './contact.schema';
// export { MessageSchema } from './message.schema';
// export { AnalyticsEventSchema } from './analytics-event.schema';
// export { AutomationSchema } from './automation.schema';
// export { AutomationExecutionSchema } from './automation-execution.schema';
