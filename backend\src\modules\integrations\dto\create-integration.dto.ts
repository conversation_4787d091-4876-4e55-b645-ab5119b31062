import {
  IsString,
  IsEnum,
  IsBoolean,
  IsOptional,
  IsObject,
  IsArray,
  IsNumber,
  IsUrl,
  ValidateNested,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IntegrationType, TriggerEvent, IntegrationConfig } from '../../../database/entities/integration.entity';

export class AuthConfigDto {
  @ApiProperty({ description: 'Tipo de autenticação', enum: ['none', 'bearer', 'basic', 'api_key', 'oauth2'] })
  @IsEnum(['none', 'bearer', 'basic', 'api_key', 'oauth2'])
  authType: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';

  @ApiPropertyOptional({ description: 'Chave da API' })
  @IsOptional()
  @IsString()
  apiKey?: string;

  @ApiPropertyOptional({ description: 'Token Bearer' })
  @IsOptional()
  @IsString()
  bearerToken?: string;

  @ApiPropertyOptional({ description: 'Autenticação básica' })
  @IsOptional()
  @IsObject()
  basicAuth?: {
    username: string;
    password: string;
  };

  @ApiPropertyOptional({ description: 'Configuração OAuth2' })
  @IsOptional()
  @IsObject()
  oauth2?: {
    clientId: string;
    clientSecret: string;
    accessToken: string;
    refreshToken: string;
    tokenExpiry?: Date;
    scope?: string[];
  };
}

export class GoogleSheetsConfigDto {
  @ApiProperty({ description: 'ID da planilha do Google Sheets' })
  @IsString()
  spreadsheetId: string;

  @ApiProperty({ description: 'Nome da aba' })
  @IsString()
  sheetName: string;

  @ApiPropertyOptional({ description: 'Intervalo de células (ex: A1:Z1000)' })
  @IsOptional()
  @IsString()
  range?: string;

  @ApiPropertyOptional({ description: 'Chave da conta de serviço (JSON)' })
  @IsOptional()
  @IsString()
  serviceAccountKey?: string;
}

export class CrmConfigDto {
  @ApiProperty({ description: 'URL da API do CRM' })
  @IsUrl()
  apiUrl: string;

  @ApiProperty({ description: 'Tipo de objeto (contact, lead, deal, etc.)' })
  @IsString()
  objectType: string;

  @ApiProperty({ description: 'Mapeamento de campos' })
  @IsObject()
  fieldMapping: Record<string, string>;

  @ApiPropertyOptional({ description: 'Campos customizados' })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;
}

export class EmailConfigDto {
  @ApiPropertyOptional({ description: 'ID da lista de email' })
  @IsOptional()
  @IsString()
  listId?: string;

  @ApiPropertyOptional({ description: 'ID do template' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiPropertyOptional({ description: 'Email do remetente' })
  @IsOptional()
  @IsString()
  fromEmail?: string;

  @ApiPropertyOptional({ description: 'Nome do remetente' })
  @IsOptional()
  @IsString()
  fromName?: string;
}

export class SlackConfigDto {
  @ApiProperty({ description: 'ID do canal do Slack' })
  @IsString()
  channelId: string;

  @ApiProperty({ description: 'Token do bot' })
  @IsString()
  botToken: string;

  @ApiPropertyOptional({ description: 'Formato da mensagem' })
  @IsOptional()
  @IsString()
  messageFormat?: string;
}

export class FiltersDto {
  @ApiPropertyOptional({ description: 'Tags de contato para filtrar', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contactTags?: string[];

  @ApiPropertyOptional({ description: 'Tipos de mensagem para filtrar', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  messageTypes?: string[];

  @ApiPropertyOptional({ description: 'IDs de conexão para filtrar', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  connectionIds?: string[];

  @ApiPropertyOptional({ description: 'IDs de usuário para filtrar', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  userIds?: string[];

  @ApiPropertyOptional({ description: 'Condições customizadas' })
  @IsOptional()
  @IsObject()
  customConditions?: Record<string, any>;
}

export class DataMappingDto {
  @ApiPropertyOptional({ description: 'Campos estáticos' })
  @IsOptional()
  @IsObject()
  staticFields?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Mapeamento de campos dinâmicos' })
  @IsOptional()
  @IsObject()
  dynamicFields?: Record<string, string>;

  @ApiPropertyOptional({ description: 'Transformações de dados' })
  @IsOptional()
  @IsArray()
  transformations?: Array<{
    field: string;
    type: 'uppercase' | 'lowercase' | 'date_format' | 'number_format' | 'custom';
    config?: any;
  }>;
}

export class IntegrationConfigDto implements IntegrationConfig {
  @ApiPropertyOptional({ description: 'URL do webhook' })
  @IsOptional()
  @IsUrl()
  url?: string;

  @ApiPropertyOptional({ description: 'Método HTTP', enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] })
  @IsOptional()
  @IsEnum(['GET', 'POST', 'PUT', 'PATCH', 'DELETE'])
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

  @ApiPropertyOptional({ description: 'Headers HTTP' })
  @IsOptional()
  @IsObject()
  headers?: Record<string, string>;

  @ApiPropertyOptional({ description: 'Timeout em milissegundos', example: 30000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeout?: number;

  @ApiPropertyOptional({ description: 'Número de tentativas', example: 3 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  retryAttempts?: number;

  @ApiPropertyOptional({ description: 'Delay entre tentativas em milissegundos', example: 5000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  retryDelay?: number;

  @ApiPropertyOptional({ description: 'Configuração de autenticação', type: AuthConfigDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => AuthConfigDto)
  authType?: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';

  @ApiPropertyOptional({ description: 'Chave da API' })
  @IsOptional()
  @IsString()
  apiKey?: string;

  @ApiPropertyOptional({ description: 'Token Bearer' })
  @IsOptional()
  @IsString()
  bearerToken?: string;

  @ApiPropertyOptional({ description: 'Configuração do Google Sheets', type: GoogleSheetsConfigDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => GoogleSheetsConfigDto)
  googleSheets?: GoogleSheetsConfigDto;

  @ApiPropertyOptional({ description: 'Configuração do CRM', type: CrmConfigDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CrmConfigDto)
  crm?: CrmConfigDto;

  @ApiPropertyOptional({ description: 'Configuração de email', type: EmailConfigDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => EmailConfigDto)
  email?: EmailConfigDto;

  @ApiPropertyOptional({ description: 'Configuração do Slack', type: SlackConfigDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => SlackConfigDto)
  slack?: SlackConfigDto;

  @ApiPropertyOptional({ description: 'Filtros de eventos', type: FiltersDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => FiltersDto)
  filters?: FiltersDto;

  @ApiPropertyOptional({ description: 'Mapeamento de dados', type: DataMappingDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => DataMappingDto)
  dataMapping?: DataMappingDto;
}

export class CreateIntegrationDto {
  @ApiProperty({ description: 'Nome da integração', example: 'Webhook para CRM' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Descrição da integração', example: 'Envia novos contatos para o CRM' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Tipo da integração', enum: IntegrationType })
  @IsEnum(IntegrationType)
  type: IntegrationType;

  @ApiProperty({ description: 'Eventos que disparam a integração', type: [String], enum: TriggerEvent })
  @IsArray()
  @IsEnum(TriggerEvent, { each: true })
  triggerEvents: TriggerEvent[];

  @ApiProperty({ description: 'Configuração da integração', type: IntegrationConfigDto })
  @ValidateNested()
  @Type(() => IntegrationConfigDto)
  config: IntegrationConfigDto;

  @ApiPropertyOptional({ description: 'Se a integração está ativa', default: true })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiPropertyOptional({ description: 'Limite de requisições por minuto', example: 60, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  rateLimit?: number;

  @ApiPropertyOptional({ description: 'Máximo de tentativas', example: 3, default: 3 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxRetries?: number;

  @ApiPropertyOptional({ description: 'Delay entre tentativas em milissegundos', example: 5000, default: 5000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  retryDelay?: number;

  @ApiPropertyOptional({ description: 'Timeout em milissegundos', example: 30000, default: 30000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(300000)
  timeout?: number;

  @ApiPropertyOptional({ description: 'Metadados adicionais' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tags da integração', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
