import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Plan } from '../../../database/entities/plan.entity';
import { Subscription, SubscriptionStatus } from '../../../database/entities/subscription.entity';
import { Invoice } from '../../../database/entities/invoice.entity';
import { Payment } from '../../../database/entities/payment.entity';
import { Company } from '../../../database/entities/company.entity';
import { CreatePlanDto } from '../dto/create-plan.dto';
import { CreateSubscriptionDto, UpdateSubscriptionDto, ChangePlanDto, CancelSubscriptionDto, SubscriptionUsageDto } from '../dto/subscription-dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { User<PERSON>ole } from '../../../database/entities';

export interface FindPlansOptions {
  type?: string;
  status?: string;
  isPublic?: boolean;
  isAgencyPlan?: boolean;
  targetAudience?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface FindSubscriptionsOptions {
  companyId?: string;
  status?: string;
  type?: string;
  agencyId?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(
    @InjectRepository(Plan)
    private planRepository: Repository<Plan>,
    @InjectRepository(Subscription)
    private subscriptionRepository: Repository<Subscription>,
    @InjectRepository(Invoice)
    private invoiceRepository: Repository<Invoice>,
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  // ==================== PLANOS ====================

  async createPlan(createPlanDto: CreatePlanDto, currentUser: AuthenticatedUser): Promise<Plan> {
    // Verificar se já existe plano com o mesmo nome
    const existingPlan = await this.planRepository.findOne({
      where: { name: createPlanDto.name },
    });

    if (existingPlan) {
      throw new ConflictException('Já existe um plano com este nome');
    }

    const plan = this.planRepository.create(createPlanDto);
    const savedPlan = await this.planRepository.save(plan);

    this.logger.log(`Plan created: ${savedPlan.id} by ${currentUser.email}`);
    return savedPlan;
  }

  async findAllPlans(options: FindPlansOptions): Promise<{
    plans: Plan[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      type,
      status,
      isPublic,
      isAgencyPlan,
      targetAudience,
      search,
      page = 1,
      limit = 10,
      sortBy = 'sortOrder',
      sortOrder = 'ASC',
    } = options;

    const queryBuilder = this.planRepository.createQueryBuilder('plan');

    // Filtros
    if (type) {
      queryBuilder.andWhere('plan.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('plan.status = :status', { status });
    }

    if (typeof isPublic === 'boolean') {
      queryBuilder.andWhere('plan.isPublic = :isPublic', { isPublic });
    }

    if (typeof isAgencyPlan === 'boolean') {
      queryBuilder.andWhere('plan.isAgencyPlan = :isAgencyPlan', { isAgencyPlan });
    }

    if (targetAudience) {
      queryBuilder.andWhere('plan.targetAudience = :targetAudience', { targetAudience });
    }

    if (search) {
      queryBuilder.andWhere(
        '(plan.name ILIKE :search OR plan.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Paginação
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Ordenação
    queryBuilder.orderBy(`plan.${sortBy}`, sortOrder);

    const [plans, total] = await queryBuilder.getManyAndCount();

    return { plans, total, page, limit };
  }

  async findPlanById(id: string): Promise<Plan> {
    const plan = await this.planRepository.findOne({ where: { id } });
    
    if (!plan) {
      throw new NotFoundException('Plano não encontrado');
    }

    return plan;
  }

  async updatePlan(id: string, updateData: Partial<CreatePlanDto>, currentUser: AuthenticatedUser): Promise<Plan> {
    const plan = await this.findPlanById(id);

    // Verificar se nome já existe (se estiver sendo alterado)
    if (updateData.name && updateData.name !== plan.name) {
      const existingPlan = await this.planRepository.findOne({
        where: { name: updateData.name },
      });

      if (existingPlan) {
        throw new ConflictException('Já existe um plano com este nome');
      }
    }

    Object.assign(plan, updateData);
    const updatedPlan = await this.planRepository.save(plan);

    this.logger.log(`Plan updated: ${id} by ${currentUser.email}`);
    return updatedPlan;
  }

  async deletePlan(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const plan = await this.findPlanById(id);

    // Verificar se há assinaturas ativas
    const activeSubscriptions = await this.subscriptionRepository.count({
      where: { planId: id, status: SubscriptionStatus.ACTIVE },
    });

    if (activeSubscriptions > 0) {
      throw new ConflictException('Não é possível deletar plano com assinaturas ativas');
    }

    await this.planRepository.remove(plan);
    this.logger.log(`Plan deleted: ${id} by ${currentUser.email}`);
  }

  // ==================== ASSINATURAS ====================

  async createSubscription(createSubscriptionDto: CreateSubscriptionDto, currentUser: AuthenticatedUser): Promise<Subscription> {
    // Verificar se empresa existe
    const company = await this.companyRepository.findOne({
      where: { id: createSubscriptionDto.companyId },
    });

    if (!company) {
      throw new NotFoundException('Empresa não encontrada');
    }

    // Verificar se plano existe
    const plan = await this.findPlanById(createSubscriptionDto.planId);

    // Verificar se empresa já tem assinatura ativa
    const existingSubscription = await this.subscriptionRepository.findOne({
      where: {
        companyId: createSubscriptionDto.companyId,
        status: SubscriptionStatus.ACTIVE,
      },
    });

    if (existingSubscription) {
      throw new ConflictException('Empresa já possui uma assinatura ativa');
    }

    // Calcular datas
    const startDate = createSubscriptionDto.startDate ? new Date(createSubscriptionDto.startDate) : new Date();
    const endDate = this.calculateEndDate(startDate, plan.billingCycle);

    // Criar assinatura
    const subscription = this.subscriptionRepository.create({
      ...createSubscriptionDto,
      startDate,
      endDate,
      price: createSubscriptionDto.price || plan.price,
      setupFee: createSubscriptionDto.setupFee || plan.setupFee,
      status: createSubscriptionDto.trialEndDate ? SubscriptionStatus.TRIAL : SubscriptionStatus.PENDING,
      currentUsage: {
        connections: 0,
        contacts: 0,
        messages: 0,
        users: 0,
        automations: 0,
        chatbots: 0,
        storageUsedGB: 0,
        apiCalls: 0,
        webhookCalls: 0,
        lastUpdated: new Date(),
      },
    });

    const savedSubscription = await this.subscriptionRepository.save(subscription);

    this.logger.log(`Subscription created: ${savedSubscription.id} for company ${createSubscriptionDto.companyId}`);
    return savedSubscription;
  }

  async findAllSubscriptions(options: FindSubscriptionsOptions, currentUser: AuthenticatedUser): Promise<{
    subscriptions: Subscription[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      companyId,
      status,
      type,
      agencyId,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = options;

    const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.company', 'company')
      .leftJoinAndSelect('subscription.plan', 'plan');

    // Filtro por empresa (se não for super admin)
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('subscription.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    // Filtros adicionais
    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('subscription.companyId = :companyId', { companyId });
    }

    if (status) {
      queryBuilder.andWhere('subscription.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('subscription.type = :type', { type });
    }

    if (agencyId) {
      queryBuilder.andWhere('subscription.agencyId = :agencyId', { agencyId });
    }

    // Paginação
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Ordenação
    queryBuilder.orderBy(`subscription.${sortBy}`, sortOrder);

    const [subscriptions, total] = await queryBuilder.getManyAndCount();

    return { subscriptions, total, page, limit };
  }

  async findSubscriptionById(id: string, currentUser: AuthenticatedUser): Promise<Subscription> {
    const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.company', 'company')
      .leftJoinAndSelect('subscription.plan', 'plan')
      .where('subscription.id = :id', { id });

    // Filtro por empresa (se não for super admin)
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('subscription.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    const subscription = await queryBuilder.getOne();
    
    if (!subscription) {
      throw new NotFoundException('Assinatura não encontrada');
    }

    return subscription;
  }

  async updateSubscription(id: string, updateSubscriptionDto: UpdateSubscriptionDto, currentUser: AuthenticatedUser): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id, currentUser);

    Object.assign(subscription, updateSubscriptionDto);
    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    this.logger.log(`Subscription updated: ${id} by ${currentUser.email}`);
    return updatedSubscription;
  }

  async changePlan(id: string, changePlanDto: ChangePlanDto, currentUser: AuthenticatedUser): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id, currentUser);
    const newPlan = await this.findPlanById(changePlanDto.newPlanId);

    if (!subscription.canUpgrade() && !subscription.canDowngrade()) {
      throw new BadRequestException('Não é possível alterar o plano desta assinatura');
    }

    // Verificar se a mudança é permitida
    const currentPlan = await this.findPlanById(subscription.planId);
    const isUpgrade = newPlan.price > currentPlan.price;
    const isDowngrade = newPlan.price < currentPlan.price;

    if (isUpgrade && !currentPlan.canUpgradeTo(changePlanDto.newPlanId)) {
      throw new BadRequestException('Upgrade para este plano não é permitido');
    }

    if (isDowngrade && !currentPlan.canDowngradeTo(changePlanDto.newPlanId)) {
      throw new BadRequestException('Downgrade para este plano não é permitido');
    }

    // Agendar mudança
    subscription.pendingPlanId = changePlanDto.newPlanId;
    subscription.planChangeDate = changePlanDto.changeDate ? new Date(changePlanDto.changeDate) : new Date();
    subscription.isProrated = changePlanDto.isProrated ?? true;

    if (changePlanDto.customPrice) {
      subscription.price = changePlanDto.customPrice;
    }

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    this.logger.log(`Plan change scheduled: ${id} to plan ${changePlanDto.newPlanId} by ${currentUser.email}`);
    return updatedSubscription;
  }

  async cancelSubscription(id: string, cancelSubscriptionDto: CancelSubscriptionDto, currentUser: AuthenticatedUser): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id, currentUser);

    if (!subscription.canCancel()) {
      throw new BadRequestException('Não é possível cancelar esta assinatura');
    }

    subscription.cancelledAt = new Date();
    subscription.cancelReason = cancelSubscriptionDto.reason || null;

    if (cancelSubscriptionDto.immediately) {
      subscription.status = SubscriptionStatus.CANCELLED;
    } else {
      subscription.cancelAtPeriodEnd = true;
    }

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    this.logger.log(`Subscription cancelled: ${id} by ${currentUser.email}`);
    return updatedSubscription;
  }

  async updateUsage(id: string, usageDto: SubscriptionUsageDto, currentUser: AuthenticatedUser): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id, currentUser);

    subscription.currentUsage = {
      ...usageDto,
      lastUpdated: new Date(),
    };

    // Salvar histórico de uso (por mês)
    const monthKey = new Date().toISOString().substring(0, 7); // YYYY-MM
    subscription.usageHistory = {
      ...subscription.usageHistory,
      [monthKey]: subscription.currentUsage,
    };

    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    this.logger.log(`Usage updated for subscription: ${id}`);
    return updatedSubscription;
  }

  // ==================== MÉTODOS AUXILIARES ====================

  private calculateEndDate(startDate: Date, billingCycle: string): Date {
    const endDate = new Date(startDate);

    switch (billingCycle) {
      case 'MONTHLY':
        endDate.setMonth(endDate.getMonth() + 1);
        break;
      case 'QUARTERLY':
        endDate.setMonth(endDate.getMonth() + 3);
        break;
      case 'SEMI_ANNUAL':
        endDate.setMonth(endDate.getMonth() + 6);
        break;
      case 'ANNUAL':
        endDate.setFullYear(endDate.getFullYear() + 1);
        break;
      default:
        endDate.setMonth(endDate.getMonth() + 1);
    }

    return endDate;
  }

  async getSubscriptionStats(currentUser: AuthenticatedUser): Promise<{
    total: number;
    active: number;
    trial: number;
    cancelled: number;
    expired: number;
    totalRevenue: number;
    monthlyRevenue: number;
  }> {
    const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription');

    // Filtro por empresa (se não for super admin)
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.where('subscription.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    const [total, active, trial, cancelled, expired] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('subscription.status = :status', { status: SubscriptionStatus.ACTIVE }).getCount(),
      queryBuilder.clone().andWhere('subscription.status = :status', { status: SubscriptionStatus.TRIAL }).getCount(),
      queryBuilder.clone().andWhere('subscription.status = :status', { status: SubscriptionStatus.CANCELLED }).getCount(),
      queryBuilder.clone().andWhere('subscription.status = :status', { status: SubscriptionStatus.EXPIRED }).getCount(),
    ]);

    // Calcular receita
    const revenueQuery = queryBuilder.clone()
      .select('SUM(subscription.price)', 'totalRevenue')
      .andWhere('subscription.status IN (:...statuses)', { statuses: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIAL] });

    const revenueResult = await revenueQuery.getRawOne();
    const totalRevenue = parseFloat(revenueResult.totalRevenue) || 0;

    // Receita mensal (assumindo que a maioria é mensal)
    const monthlyRevenue = totalRevenue; // Simplificado

    return {
      total,
      active,
      trial,
      cancelled,
      expired,
      totalRevenue,
      monthlyRevenue,
    };
  }
}
