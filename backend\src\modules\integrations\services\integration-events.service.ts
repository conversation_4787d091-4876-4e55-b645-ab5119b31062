import { Injectable, Logger } from '@nestjs/common';
import { IntegrationsService } from './integrations.service';
import { TriggerEvent } from '../../../database/entities/integration.entity';

@Injectable()
export class IntegrationEventsService {
  private readonly logger = new Logger(IntegrationEventsService.name);

  constructor(
    private integrationsService: IntegrationsService,
  ) {}

  async onMessageReceived(data: {
    messageId: string;
    contactId: string;
    contactPhone: string;
    contactName?: string;
    contactTags?: string[];
    connectionId: string;
    content: string;
    type: string;
    timestamp: Date;
    companyId: string;
    userId?: string;
  }): Promise<void> {
    this.logger.log(`Triggering MESSAGE_RECEIVED integrations for contact ${data.contactPhone}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.MESSAGE_RECEIVED,
      {
        message: {
          id: data.messageId,
          content: data.content,
          type: data.type,
          timestamp: data.timestamp,
        },
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
          tags: data.contactTags || [],
        },
        connection: {
          id: data.connectionId,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: data.timestamp,
      },
      data.companyId,
    );
  }

  async onMessageSent(data: {
    messageId: string;
    contactId: string;
    contactPhone: string;
    contactName?: string;
    contactTags?: string[];
    connectionId: string;
    content: string;
    type: string;
    timestamp: Date;
    companyId: string;
    userId: string;
  }): Promise<void> {
    this.logger.log(`Triggering MESSAGE_SENT integrations for contact ${data.contactPhone}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.MESSAGE_SENT,
      {
        message: {
          id: data.messageId,
          content: data.content,
          type: data.type,
          timestamp: data.timestamp,
        },
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
          tags: data.contactTags || [],
        },
        connection: {
          id: data.connectionId,
        },
        user: {
          id: data.userId,
        },
        timestamp: data.timestamp,
      },
      data.companyId,
    );
  }

  async onContactCreated(data: {
    contactId: string;
    contactPhone: string;
    contactName?: string;
    contactTags?: string[];
    connectionId: string;
    companyId: string;
    userId?: string;
    source?: string;
    customFields?: Record<string, any>;
  }): Promise<void> {
    this.logger.log(`Triggering CONTACT_CREATED integrations for contact ${data.contactPhone}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.CONTACT_CREATED,
      {
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
          tags: data.contactTags || [],
          source: data.source,
          customFields: data.customFields || {},
        },
        connection: {
          id: data.connectionId,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onContactUpdated(data: {
    contactId: string;
    contactPhone: string;
    contactName?: string;
    contactTags?: string[];
    connectionId: string;
    companyId: string;
    userId: string;
    changes: Record<string, { old: any; new: any }>;
    customFields?: Record<string, any>;
  }): Promise<void> {
    this.logger.log(`Triggering CONTACT_UPDATED integrations for contact ${data.contactPhone}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.CONTACT_UPDATED,
      {
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
          tags: data.contactTags || [],
          customFields: data.customFields || {},
        },
        connection: {
          id: data.connectionId,
        },
        user: {
          id: data.userId,
        },
        changes: data.changes,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onLeadConverted(data: {
    leadId: string;
    contactId: string;
    contactPhone: string;
    contactName?: string;
    contactTags?: string[];
    connectionId: string;
    companyId: string;
    userId: string;
    conversionData?: Record<string, any>;
  }): Promise<void> {
    this.logger.log(`Triggering LEAD_CONVERTED integrations for lead ${data.leadId}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.LEAD_CONVERTED,
      {
        lead: {
          id: data.leadId,
          conversionData: data.conversionData || {},
        },
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
          tags: data.contactTags || [],
        },
        connection: {
          id: data.connectionId,
        },
        user: {
          id: data.userId,
        },
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onAutomationTriggered(data: {
    automationId: string;
    automationName: string;
    executionId: string;
    contactId: string;
    contactPhone: string;
    contactName?: string;
    connectionId: string;
    companyId: string;
    triggerType: string;
    userId?: string;
  }): Promise<void> {
    this.logger.log(`Triggering AUTOMATION_TRIGGERED integrations for automation ${data.automationName}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.AUTOMATION_TRIGGERED,
      {
        automation: {
          id: data.automationId,
          name: data.automationName,
          executionId: data.executionId,
          triggerType: data.triggerType,
        },
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
        },
        connection: {
          id: data.connectionId,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onAutomationCompleted(data: {
    automationId: string;
    automationName: string;
    executionId: string;
    contactId: string;
    contactPhone: string;
    contactName?: string;
    connectionId: string;
    companyId: string;
    status: string;
    duration: number;
    results: Record<string, any>;
    userId?: string;
  }): Promise<void> {
    this.logger.log(`Triggering AUTOMATION_COMPLETED integrations for automation ${data.automationName}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.AUTOMATION_COMPLETED,
      {
        automation: {
          id: data.automationId,
          name: data.automationName,
          executionId: data.executionId,
          status: data.status,
          duration: data.duration,
          results: data.results,
        },
        contact: {
          id: data.contactId,
          phoneNumber: data.contactPhone,
          name: data.contactName,
        },
        connection: {
          id: data.connectionId,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onConnectionStatusChanged(data: {
    connectionId: string;
    connectionName: string;
    phoneNumber: string;
    oldStatus: string;
    newStatus: string;
    companyId: string;
    userId?: string;
  }): Promise<void> {
    this.logger.log(`Triggering CONNECTION_STATUS_CHANGED integrations for connection ${data.connectionName}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.CONNECTION_STATUS_CHANGED,
      {
        connection: {
          id: data.connectionId,
          name: data.connectionName,
          phoneNumber: data.phoneNumber,
          oldStatus: data.oldStatus,
          newStatus: data.newStatus,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onUserCreated(data: {
    userId: string;
    userName: string;
    userEmail: string;
    userRole: string;
    companyId: string;
    createdBy: string;
  }): Promise<void> {
    this.logger.log(`Triggering USER_CREATED integrations for user ${data.userName}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.USER_CREATED,
      {
        user: {
          id: data.userId,
          name: data.userName,
          email: data.userEmail,
          role: data.userRole,
        },
        createdBy: {
          id: data.createdBy,
        },
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onSubscriptionChanged(data: {
    subscriptionId: string;
    planName: string;
    oldStatus?: string;
    newStatus: string;
    companyId: string;
    userId?: string;
    changeType: 'created' | 'updated' | 'cancelled' | 'expired';
  }): Promise<void> {
    this.logger.log(`Triggering SUBSCRIPTION_CHANGED integrations for subscription ${data.subscriptionId}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.SUBSCRIPTION_CHANGED,
      {
        subscription: {
          id: data.subscriptionId,
          planName: data.planName,
          oldStatus: data.oldStatus,
          newStatus: data.newStatus,
          changeType: data.changeType,
        },
        user: data.userId ? { id: data.userId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onPaymentReceived(data: {
    paymentId: string;
    subscriptionId?: string;
    amount: number;
    currency: string;
    paymentMethod: string;
    status: string;
    companyId: string;
  }): Promise<void> {
    this.logger.log(`Triggering PAYMENT_RECEIVED integrations for payment ${data.paymentId}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.PAYMENT_RECEIVED,
      {
        payment: {
          id: data.paymentId,
          subscriptionId: data.subscriptionId,
          amount: data.amount,
          currency: data.currency,
          paymentMethod: data.paymentMethod,
          status: data.status,
        },
        timestamp: new Date(),
      },
      data.companyId,
    );
  }

  async onCustomEvent(data: {
    eventName: string;
    eventData: Record<string, any>;
    companyId: string;
    userId?: string;
    contactId?: string;
    connectionId?: string;
  }): Promise<void> {
    this.logger.log(`Triggering CUSTOM_EVENT integrations for event ${data.eventName}`);
    
    await this.integrationsService.triggerIntegration(
      TriggerEvent.CUSTOM_EVENT,
      {
        customEvent: {
          name: data.eventName,
          data: data.eventData,
        },
        user: data.userId ? { id: data.userId } : undefined,
        contact: data.contactId ? { id: data.contactId } : undefined,
        connection: data.connectionId ? { id: data.connectionId } : undefined,
        timestamp: new Date(),
      },
      data.companyId,
    );
  }
}
