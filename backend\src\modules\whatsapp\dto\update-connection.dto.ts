import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateConnectionDto } from './create-connection.dto';
import { ConnectionStatus } from '../../../database/entities';

export class UpdateConnectionDto extends PartialType(
  OmitType(CreateConnectionDto, ['phoneNumber', 'type', 'companyId'] as const)
) {
  @ApiPropertyOptional({
    description: 'Status da conexão',
    enum: ConnectionStatus,
  })
  @IsOptional()
  @IsEnum(ConnectionStatus, { message: 'Status deve ser um valor válido' })
  status?: ConnectionStatus;

  @ApiPropertyOptional({
    description: 'Se a conexão está ativa',
    example: true,
  })
  @IsOptional()
  @IsBoolean({ message: 'isActive deve ser um boolean' })
  isActive?: boolean;
}
