import { Router } from 'express'
import { WebhookController } from '../controllers/webhook.controller'

const router = Router()
const webhookController = new WebhookController()

// Webhook da Evolution API
router.post('/evolution', webhookController.evolutionWebhook)

// Health check para webhooks
router.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'webhook'
  })
})

export default router
