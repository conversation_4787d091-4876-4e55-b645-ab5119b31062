import request from 'supertest'
import express from 'express'
import { WebhookController } from '../controllers/webhook.controller'
import { WhatsAppConnectionRepository } from '../repositories/whatsapp-connection.repository'
import { MessageRepository } from '../repositories/message.repository'
import { ContactRepository } from '../repositories/contact.repository'
import { ConnectionStatus, MessageDirection, MessageStatus, MessageType } from '@prisma/client'

// Mock das dependências
jest.mock('../repositories/whatsapp-connection.repository')
jest.mock('../repositories/message.repository')
jest.mock('../repositories/contact.repository')

describe('WebhookController', () => {
  let app: express.Application
  let webhookController: WebhookController
  let mockConnectionRepository: jest.Mocked<WhatsAppConnectionRepository>
  let mockMessageRepository: jest.Mocked<MessageRepository>
  let mockContactRepository: jest.Mocked<ContactRepository>

  beforeEach(() => {
    app = express()
    app.use(express.json())
    
    webhookController = new WebhookController()
    
    // Injetar mocks
    mockConnectionRepository = new WhatsAppConnectionRepository() as jest.Mocked<WhatsAppConnectionRepository>
    mockMessageRepository = new MessageRepository() as jest.Mocked<MessageRepository>
    mockContactRepository = new ContactRepository() as jest.Mocked<ContactRepository>
    
    ;(webhookController as any).connectionRepository = mockConnectionRepository
    ;(webhookController as any).messageRepository = mockMessageRepository
    ;(webhookController as any).contactRepository = mockContactRepository

    // Configurar rota
    app.post('/webhook/evolution', webhookController.evolutionWebhook)
  })

  describe('POST /webhook/evolution', () => {
    it('deve processar webhook de QR Code atualizado', async () => {
      // Arrange
      const webhookData = {
        event: 'qrcode.updated',
        instance: 'instance_connection-1',
        data: {
          qrcode: 'mock-qr-code-base64'
        }
      }

      const mockConnection = {
        id: 'connection-1',
        companyId: 'company-1'
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.updateQrCode.mockResolvedValue(mockConnection as any)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
      
      // Aguardar processamento assíncrono
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(mockConnectionRepository.findByInstanceId).toHaveBeenCalledWith('instance_connection-1')
      expect(mockConnectionRepository.updateQrCode).toHaveBeenCalledWith('connection-1', 'mock-qr-code-base64')
    })

    it('deve processar webhook de atualização de conexão', async () => {
      // Arrange
      const webhookData = {
        event: 'connection.update',
        instance: 'instance_connection-1',
        data: {
          state: 'open'
        }
      }

      const mockConnection = {
        id: 'connection-1',
        companyId: 'company-1'
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.updateStatus.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.clearQrCode.mockResolvedValue(mockConnection as any)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
      
      // Aguardar processamento assíncrono
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(mockConnectionRepository.findByInstanceId).toHaveBeenCalledWith('instance_connection-1')
      expect(mockConnectionRepository.updateStatus).toHaveBeenCalledWith('connection-1', ConnectionStatus.CONNECTED)
      expect(mockConnectionRepository.clearQrCode).toHaveBeenCalledWith('connection-1')
    })

    it('deve processar webhook de nova mensagem', async () => {
      // Arrange
      const webhookData = {
        event: 'messages.upsert',
        instance: 'instance_connection-1',
        data: {
          key: {
            id: 'msg_123',
            remoteJid: '<EMAIL>',
            fromMe: false
          },
          message: {
            conversation: 'Olá, como vai?'
          },
          messageTimestamp: Math.floor(Date.now() / 1000),
          pushName: 'João Silva'
        }
      }

      const mockConnection = {
        id: 'connection-1',
        companyId: 'company-1'
      }

      const mockContact = {
        id: 'contact-1',
        phone: '5511999999999'
      }

      const mockMessage = {
        id: 'message-1',
        messageId: 'msg_123'
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(mockConnection as any)
      mockMessageRepository.findByMessageId.mockResolvedValue(null)
      mockContactRepository.findOrCreate.mockResolvedValue(mockContact as any)
      mockMessageRepository.create.mockResolvedValue(mockMessage as any)
      mockContactRepository.updateMessageStats.mockResolvedValue(mockContact as any)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
      
      // Aguardar processamento assíncrono
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(mockConnectionRepository.findByInstanceId).toHaveBeenCalledWith('instance_connection-1')
      expect(mockMessageRepository.findByMessageId).toHaveBeenCalledWith('msg_123')
      expect(mockContactRepository.findOrCreate).toHaveBeenCalled()
      expect(mockMessageRepository.create).toHaveBeenCalled()
    })

    it('deve processar webhook de atualização de status de mensagem', async () => {
      // Arrange
      const webhookData = {
        event: 'messages.update',
        instance: 'instance_connection-1',
        data: {
          key: {
            id: 'msg_123'
          },
          update: {
            status: 3 // DELIVERY_ACK
          }
        }
      }

      const mockConnection = {
        id: 'connection-1',
        companyId: 'company-1'
      }

      const mockMessage = {
        id: 'message-1',
        messageId: 'msg_123',
        companyId: 'company-1',
        whatsappConnectionId: 'connection-1',
        contactPhone: '5511999999999'
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(mockConnection as any)
      mockMessageRepository.findByMessageId.mockResolvedValue(mockMessage as any)
      mockMessageRepository.updateStatus.mockResolvedValue(mockMessage as any)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
      
      // Aguardar processamento assíncrono
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(mockConnectionRepository.findByInstanceId).toHaveBeenCalledWith('instance_connection-1')
      expect(mockMessageRepository.findByMessageId).toHaveBeenCalledWith('msg_123')
      expect(mockMessageRepository.updateStatus).toHaveBeenCalledWith('message-1', MessageStatus.DELIVERED)
    })

    it('deve ignorar mensagens enviadas por nós mesmos', async () => {
      // Arrange
      const webhookData = {
        event: 'messages.upsert',
        instance: 'instance_connection-1',
        data: {
          key: {
            id: 'msg_123',
            remoteJid: '<EMAIL>',
            fromMe: true // Mensagem enviada por nós
          },
          message: {
            conversation: 'Mensagem enviada por nós'
          },
          messageTimestamp: Math.floor(Date.now() / 1000),
          pushName: 'Nossa Empresa'
        }
      }

      const mockConnection = {
        id: 'connection-1',
        companyId: 'company-1'
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(mockConnection as any)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
      
      // Aguardar processamento assíncrono
      await new Promise(resolve => setTimeout(resolve, 100))
      
      expect(mockConnectionRepository.findByInstanceId).toHaveBeenCalledWith('instance_connection-1')
      // Não deve criar mensagem para mensagens enviadas por nós
      expect(mockMessageRepository.create).not.toHaveBeenCalled()
    })

    it('deve retornar 200 mesmo com dados inválidos', async () => {
      // Arrange
      const webhookData = {
        event: 'unknown.event',
        instance: 'unknown-instance',
        data: {}
      }

      mockConnectionRepository.findByInstanceId.mockResolvedValue(null)

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
    })

    it('deve lidar com erros de processamento graciosamente', async () => {
      // Arrange
      const webhookData = {
        event: 'qrcode.updated',
        instance: 'instance_connection-1',
        data: {
          qrcode: 'mock-qr-code-base64'
        }
      }

      mockConnectionRepository.findByInstanceId.mockRejectedValue(new Error('Database error'))

      // Act
      const response = await request(app)
        .post('/webhook/evolution')
        .send(webhookData)

      // Assert
      expect(response.status).toBe(200)
      expect(response.body).toEqual({ received: true })
    })
  })
})
