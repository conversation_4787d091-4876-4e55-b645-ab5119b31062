import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { UserRole } from '../../database/entities';

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: UserRole;
  companyId: string;
  permissions?: string[];
  canAccessCompany?: (companyId: string) => boolean;
}

export const CurrentUser = createParamDecorator(
  (data: keyof AuthenticatedUser | undefined, ctx: ExecutionContext): AuthenticatedUser => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user as AuthenticatedUser;

    return data ? (user?.[data] as any) : user;
  },
);
