"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const entities_1 = require("../../../database/entities");
class MessageResponseDto {
    id;
    messageId;
    type;
    direction;
    status;
    content;
    media;
    location;
    contact;
    quotedMessageId;
    metadata;
    timestamp;
    sentAt;
    deliveredAt;
    readAt;
    errorMessage;
    isAutomated;
    automationId;
    isTemplate;
    templateName;
    whatsappConnectionId;
    contactPhone;
    sentBy;
    conversationId;
    isFirstMessage;
    responseTime;
    tags;
    createdAt;
    updatedAt;
}
exports.MessageResponseDto = MessageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da mensagem',
        example: 'message-id',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj._id?.toString() || obj.id),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único da mensagem no WhatsApp',
        example: 'wamid.123456789',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "messageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da mensagem',
        enum: entities_1.MessageType,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_a = typeof entities_1.MessageType !== "undefined" && entities_1.MessageType) === "function" ? _a : Object)
], MessageResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Direção da mensagem',
        enum: entities_1.MessageDirection,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_b = typeof entities_1.MessageDirection !== "undefined" && entities_1.MessageDirection) === "function" ? _b : Object)
], MessageResponseDto.prototype, "direction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da mensagem',
        enum: entities_1.MessageStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_c = typeof entities_1.MessageStatus !== "undefined" && entities_1.MessageStatus) === "function" ? _c : Object)
], MessageResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Conteúdo da mensagem',
        example: 'Olá! Como posso ajudá-lo?',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de mídia',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_d = typeof entities_1.MessageMedia !== "undefined" && entities_1.MessageMedia) === "function" ? _d : Object)
], MessageResponseDto.prototype, "media", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de localização',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_e = typeof entities_1.MessageLocation !== "undefined" && entities_1.MessageLocation) === "function" ? _e : Object)
], MessageResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de contato',
        type: Object,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", typeof (_f = typeof entities_1.MessageContact !== "undefined" && entities_1.MessageContact) === "function" ? _f : Object)
], MessageResponseDto.prototype, "contact", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da mensagem citada',
        example: 'quoted-message-id',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "quotedMessageId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados da mensagem',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], MessageResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp da mensagem',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de envio',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "sentAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de entrega',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "deliveredAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de leitura',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "readAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mensagem de erro',
        example: 'Falha na entrega',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é uma mensagem automatizada',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], MessageResponseDto.prototype, "isAutomated", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da automação',
        example: 'automation-id',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "automationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é um template',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], MessageResponseDto.prototype, "isTemplate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nome do template',
        example: 'welcome_message',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "templateName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da conexão WhatsApp',
        example: 'uuid-da-conexao',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "whatsappConnectionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do contato',
        example: '+5511999999999',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do usuário que enviou',
        example: 'uuid-do-usuario',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "sentBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da conversa',
        example: 'conversation-id',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MessageResponseDto.prototype, "conversationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se é a primeira mensagem',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], MessageResponseDto.prototype, "isFirstMessage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tempo de resposta em segundos',
        example: 120,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], MessageResponseDto.prototype, "responseTime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Etiquetas da mensagem',
        type: [String],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], MessageResponseDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de atualização',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], MessageResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=message-response.dto.js.map