"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationExecutionSchema = exports.AutomationExecution = exports.StepStatus = exports.ExecutionStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
var ExecutionStatus;
(function (ExecutionStatus) {
    ExecutionStatus["PENDING"] = "pending";
    ExecutionStatus["RUNNING"] = "running";
    ExecutionStatus["COMPLETED"] = "completed";
    ExecutionStatus["FAILED"] = "failed";
    ExecutionStatus["CANCELLED"] = "cancelled";
    ExecutionStatus["PAUSED"] = "paused";
})(ExecutionStatus || (exports.ExecutionStatus = ExecutionStatus = {}));
var StepStatus;
(function (StepStatus) {
    StepStatus["PENDING"] = "pending";
    StepStatus["RUNNING"] = "running";
    StepStatus["COMPLETED"] = "completed";
    StepStatus["FAILED"] = "failed";
    StepStatus["SKIPPED"] = "skipped";
})(StepStatus || (exports.StepStatus = StepStatus = {}));
let AutomationExecution = class AutomationExecution {
    automationId;
    automationName;
    automationVersion;
    companyId;
    status;
    context;
    steps;
    currentStepId;
    startedAt;
    completedAt;
    duration;
    error;
    retryCount;
    maxRetries;
    nextRetryAt;
    triggeredBy;
    triggerType;
    triggerData;
    results;
    totalSteps;
    completedSteps;
    failedSteps;
    skippedSteps;
    metadata;
    executionId;
    isTest;
    parentExecutionId;
    childExecutionIds;
};
exports.AutomationExecution = AutomationExecution;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Types.ObjectId, ref: 'Automation' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], AutomationExecution.prototype, "automationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "automationName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Number }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "automationVersion", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Types.ObjectId, ref: 'Company' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], AutomationExecution.prototype, "companyId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: ExecutionStatus, default: ExecutionStatus.PENDING }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Object }),
    __metadata("design:type", Object)
], AutomationExecution.prototype, "context", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Object], default: [] }),
    __metadata("design:type", Array)
], AutomationExecution.prototype, "steps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "currentStepId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], AutomationExecution.prototype, "startedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], AutomationExecution.prototype, "completedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "duration", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], AutomationExecution.prototype, "error", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "retryCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 3 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "maxRetries", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date }),
    __metadata("design:type", Date)
], AutomationExecution.prototype, "nextRetryAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "triggeredBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "triggerType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], AutomationExecution.prototype, "triggerData", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], AutomationExecution.prototype, "results", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "totalSteps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "completedSteps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "failedSteps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number, default: 0 }),
    __metadata("design:type", Number)
], AutomationExecution.prototype, "skippedSteps", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, default: {} }),
    __metadata("design:type", Object)
], AutomationExecution.prototype, "metadata", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "executionId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Boolean, default: false }),
    __metadata("design:type", Boolean)
], AutomationExecution.prototype, "isTest", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AutomationExecution.prototype, "parentExecutionId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], AutomationExecution.prototype, "childExecutionIds", void 0);
exports.AutomationExecution = AutomationExecution = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], AutomationExecution);
exports.AutomationExecutionSchema = mongoose_1.SchemaFactory.createForClass(AutomationExecution);
exports.AutomationExecutionSchema.index({ automationId: 1, status: 1 });
exports.AutomationExecutionSchema.index({ companyId: 1, status: 1 });
exports.AutomationExecutionSchema.index({ 'context.contact.phoneNumber': 1 });
exports.AutomationExecutionSchema.index({ 'context.connection.id': 1 });
exports.AutomationExecutionSchema.index({ startedAt: -1 });
exports.AutomationExecutionSchema.index({ completedAt: -1 });
exports.AutomationExecutionSchema.index({ executionId: 1 }, { unique: true });
exports.AutomationExecutionSchema.index({ triggerType: 1 });
exports.AutomationExecutionSchema.index({ triggeredBy: 1 });
exports.AutomationExecutionSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 });
exports.AutomationExecutionSchema.methods.addStep = function (step) {
    this.steps.push(step);
    this.totalSteps = this.steps.length;
};
exports.AutomationExecutionSchema.methods.updateStep = function (stepId, updates) {
    const stepIndex = this.steps.findIndex(step => step.stepId === stepId);
    if (stepIndex !== -1) {
        Object.assign(this.steps[stepIndex], updates);
        this.completedSteps = this.steps.filter(s => s.status === StepStatus.COMPLETED).length;
        this.failedSteps = this.steps.filter(s => s.status === StepStatus.FAILED).length;
        this.skippedSteps = this.steps.filter(s => s.status === StepStatus.SKIPPED).length;
    }
};
exports.AutomationExecutionSchema.methods.getCurrentStep = function () {
    if (!this.currentStepId)
        return null;
    return this.steps.find(step => step.stepId === this.currentStepId) || null;
};
exports.AutomationExecutionSchema.methods.getNextStep = function () {
    const currentStep = this.getCurrentStep();
    if (!currentStep || !currentStep.nextStepId)
        return null;
    return this.steps.find(step => step.stepId === currentStep.nextStepId) || null;
};
exports.AutomationExecutionSchema.methods.markAsCompleted = function () {
    this.status = ExecutionStatus.COMPLETED;
    this.completedAt = new Date();
    this.duration = this.completedAt.getTime() - (this.startedAt?.getTime() || 0);
};
exports.AutomationExecutionSchema.methods.markAsFailed = function (error) {
    this.status = ExecutionStatus.FAILED;
    this.completedAt = new Date();
    this.duration = this.completedAt.getTime() - (this.startedAt?.getTime() || 0);
    this.error = {
        code: error.code || 'EXECUTION_FAILED',
        message: error.message || 'Execution failed',
        stack: error.stack,
        stepId: this.currentStepId,
    };
};
exports.AutomationExecutionSchema.methods.canRetry = function () {
    return this.status === ExecutionStatus.FAILED &&
        this.retryCount < this.maxRetries &&
        (!this.nextRetryAt || new Date() >= this.nextRetryAt);
};
exports.AutomationExecutionSchema.methods.scheduleRetry = function (delayMinutes = 5) {
    this.retryCount += 1;
    this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
    this.status = ExecutionStatus.PENDING;
};
exports.AutomationExecutionSchema.methods.getProgress = function () {
    if (this.totalSteps === 0)
        return 0;
    return (this.completedSteps / this.totalSteps) * 100;
};
exports.AutomationExecutionSchema.methods.getDuration = function () {
    if (!this.startedAt)
        return 0;
    const endTime = this.completedAt || new Date();
    return endTime.getTime() - this.startedAt.getTime();
};
//# sourceMappingURL=automation-execution.schema.js.map