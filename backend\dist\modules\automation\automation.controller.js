"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const automation_service_1 = require("./services/automation.service");
const automation_executor_service_1 = require("./services/automation-executor.service");
const create_automation_dto_1 = require("./dto/create-automation.dto");
const update_automation_dto_1 = require("./dto/update-automation.dto");
const automation_response_dto_1 = require("./dto/automation-response.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const tenant_guard_1 = require("../../common/guards/tenant.guard");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const entities_1 = require("../../database/entities");
const automation_execution_schema_1 = require("../../database/entities/automation-execution.schema");
let AutomationController = class AutomationController {
    automationService;
    executorService;
    constructor(automationService, executorService) {
        this.automationService = automationService;
        this.executorService = executorService;
    }
    async create(createAutomationDto, currentUser) {
        const automation = await this.automationService.create(createAutomationDto, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async findAll(query, currentUser) {
        const result = await this.automationService.findAll(query, currentUser);
        return {
            ...result,
            automations: result.automations.map(automation => (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true })),
        };
    }
    async getStats(currentUser) {
        return this.automationService.getAutomationStats(currentUser);
    }
    async getTemplates(category, currentUser) {
        const query = {
            isTemplate: true,
            templateCategory: category,
        };
        const result = await this.automationService.findAll(query, currentUser);
        return result.automations.map(automation => (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true }));
    }
    async findOne(id, currentUser) {
        const automation = await this.automationService.findOne(id, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async update(id, updateAutomationDto, currentUser) {
        const automation = await this.automationService.update(id, updateAutomationDto, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async remove(id, currentUser) {
        return this.automationService.remove(id, currentUser);
    }
    async activate(id, currentUser) {
        const automation = await this.automationService.activate(id, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async deactivate(id, currentUser) {
        const automation = await this.automationService.deactivate(id, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async duplicate(id, currentUser) {
        const automation = await this.automationService.duplicate(id, currentUser);
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
    }
    async test(id, testData, currentUser) {
        const automation = await this.automationService.findOne(id, currentUser);
        const execution = await this.executorService.executeAutomation(automation, {
            type: entities_1.TriggerType.MANUAL,
            contactPhone: testData.contactPhone,
            connectionId: testData.connectionId,
            companyId: currentUser.companyId,
            userId: currentUser.id,
            metadata: { isTest: true },
        });
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true });
    }
    async getExecutions(id, query, currentUser) {
        query.automationId = id;
        const result = await this.automationService.getExecutions(query, currentUser);
        return {
            ...result,
            executions: result.executions.map(execution => (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true })),
        };
    }
    async getExecution(executionId, currentUser) {
        const result = await this.automationService.getExecutions({ page: 1, limit: 1 }, currentUser);
        const execution = result.executions.find(exec => exec.executionId === executionId);
        if (!execution) {
            throw new Error('Execution not found');
        }
        return (0, class_transformer_1.plainToClass)(automation_response_dto_1.AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true });
    }
    async retryExecution(executionId, currentUser) {
        return this.executorService.retryExecution(executionId);
    }
};
exports.AutomationController = AutomationController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_CREATE),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova automação' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Automação criada com sucesso',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Já existe uma automação com este nome',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_automation_dto_1.CreateAutomationDto, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar automações' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, enum: entities_1.AutomationType, description: 'Filtrar por tipo' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: entities_1.AutomationStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'triggerType', required: false, enum: entities_1.TriggerType, description: 'Filtrar por tipo de gatilho' }),
    (0, swagger_1.ApiQuery)({ name: 'isTemplate', required: false, type: Boolean, description: 'Filtrar por templates' }),
    (0, swagger_1.ApiQuery)({ name: 'templateCategory', required: false, description: 'Filtrar por categoria de template' }),
    (0, swagger_1.ApiQuery)({ name: 'createdBy', required: false, description: 'Filtrar por criador' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar por nome ou descrição' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Campo para ordenação' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de automações',
        type: [automation_response_dto_1.AutomationResponseDto],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter estatísticas de automações' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Estatísticas de automações',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                inactive: { type: 'number' },
                draft: { type: 'number' },
                totalExecutions: { type: 'number' },
                successRate: { type: 'number' },
            },
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('templates'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar templates de automação' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, description: 'Filtrar por categoria' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de templates',
        type: [automation_response_dto_1.AutomationResponseDto],
    }),
    __param(0, (0, common_1.Query)('category')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "getTemplates", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter automação por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da automação',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Automação não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Automação atualizada com sucesso',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Automação não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_automation_dto_1.UpdateAutomationDto, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_DELETE),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Automação deletada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Automação não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/activate'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Ativar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Automação ativada com sucesso',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "activate", null);
__decorate([
    (0, common_1.Post)(':id/deactivate'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Desativar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Automação desativada com sucesso',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "deactivate", null);
__decorate([
    (0, common_1.Post)(':id/duplicate'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_CREATE),
    (0, swagger_1.ApiOperation)({ summary: 'Duplicar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Automação duplicada com sucesso',
        type: automation_response_dto_1.AutomationResponseDto,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "duplicate", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Testar automação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Teste da automação iniciado',
        type: automation_response_dto_1.AutomationExecutionResponseDto,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "test", null);
__decorate([
    (0, common_1.Get)(':id/executions'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar execuções da automação' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: automation_execution_schema_1.ExecutionStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'contactPhone', required: false, description: 'Filtrar por telefone do contato' }),
    (0, swagger_1.ApiQuery)({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' }),
    (0, swagger_1.ApiQuery)({ name: 'dateTo', required: false, type: Date, description: 'Data final' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de execuções',
        type: [automation_response_dto_1.AutomationExecutionResponseDto],
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "getExecutions", null);
__decorate([
    (0, common_1.Get)('executions/:executionId'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter execução por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da execução',
        type: automation_response_dto_1.AutomationExecutionResponseDto,
    }),
    __param(0, (0, common_1.Param)('executionId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "getExecution", null);
__decorate([
    (0, common_1.Post)('executions/:executionId/retry'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.AUTOMATION_UPDATE),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Tentar novamente execução falhada' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Execução reagendada com sucesso',
    }),
    __param(0, (0, common_1.Param)('executionId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AutomationController.prototype, "retryExecution", null);
exports.AutomationController = AutomationController = __decorate([
    (0, swagger_1.ApiTags)('Automação'),
    (0, common_1.Controller)('automation'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [automation_service_1.AutomationService,
        automation_executor_service_1.AutomationExecutorService])
], AutomationController);
//# sourceMappingURL=automation.controller.js.map