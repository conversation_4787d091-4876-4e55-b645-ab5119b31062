import { SetMetadata } from '@nestjs/common';

export const PERMISSIONS_KEY = 'permissions';
export const RequirePermissions = (...permissions: string[]) => 
  SetMetadata(PERMISSIONS_KEY, permissions);

// Definição de permissões do sistema
export const PERMISSIONS = {
  // Usuários
  USERS_CREATE: 'users:create',
  USERS_READ: 'users:read',
  USERS_UPDATE: 'users:update',
  USERS_DELETE: 'users:delete',
  
  // Empresas
  COMPANIES_CREATE: 'companies:create',
  COMPANIES_READ: 'companies:read',
  COMPANIES_UPDATE: 'companies:update',
  COMPANIES_DELETE: 'companies:delete',
  
  // WhatsApp
  WHATSAPP_CONNECT: 'whatsapp:connect',
  WHATSAPP_DISCONNECT: 'whatsapp:disconnect',
  WHATSAPP_SEND_MESSAGE: 'whatsapp:send_message',
  WHATSAPP_READ_MESSAGES: 'whatsapp:read_messages',
  
  // Mensagens
  MESSAGES_READ: 'messages:read',
  MESSAGES_SEND: 'messages:send',
  MESSAGES_DELETE: 'messages:delete',
  
  // Analytics
  ANALYTICS_READ: 'analytics:read',
  ANALYTICS_EXPORT: 'analytics:export',
  
  // Automação
  AUTOMATION_CREATE: 'automation:create',
  AUTOMATION_READ: 'automation:read',
  AUTOMATION_UPDATE: 'automation:update',
  AUTOMATION_DELETE: 'automation:delete',
  
  // Faturamento
  BILLING_READ: 'billing:read',
  BILLING_MANAGE: 'billing:manage',
  
  // Integrações
  INTEGRATIONS_CREATE: 'integrations:create',
  INTEGRATIONS_READ: 'integrations:read',
  INTEGRATIONS_UPDATE: 'integrations:update',
  INTEGRATIONS_DELETE: 'integrations:delete',
  
  // Admin
  ADMIN_FULL_ACCESS: 'admin:full_access',
} as const;
