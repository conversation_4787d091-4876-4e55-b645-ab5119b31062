import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from '../../database/entities';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';

export interface FindUsersOptions {
  companyId?: string;
  role?: UserRole;
  status?: UserStatus;
  search?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto, currentUser: AuthenticatedUser): Promise<User> {
    // Verificar se o usuário atual pode criar usuários na empresa especificada
    if (currentUser.canAccessCompany && !currentUser.canAccessCompany(createUserDto.companyId)) {
      throw new ForbiddenException('Não é possível criar usuário nesta empresa');
    }

    // Verificar se email já existe
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email já está em uso');
    }

    // Verificar hierarquia de papéis
    this.validateRoleHierarchy(currentUser.role as UserRole, createUserDto.role);

    // Hash da senha
    const hashedPassword = await bcrypt.hash(createUserDto.password, 12);

    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
      status: UserStatus.ACTIVE,
      createdBy: currentUser.id,
    });

    return this.userRepository.save(user);
  }

  async findAll(options: FindUsersOptions, currentUser: AuthenticatedUser): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { companyId, role, status, search, page = 1, limit = 10 } = options;

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.company', 'company');

    // Filtro por empresa baseado no usuário atual
    if (currentUser.role === UserRole.SUPER_ADMIN) {
      // Super admin pode ver todos os usuários
      if (companyId) {
        queryBuilder.andWhere('user.companyId = :companyId', { companyId });
      }
    } else if (currentUser.role === UserRole.AGENCY_ADMIN) {
      // Agência pode ver usuários de seus clientes
      queryBuilder.andWhere(
        '(user.companyId = :currentCompanyId OR user.companyId IN (SELECT c.id FROM companies c WHERE c.agencyId = :currentCompanyId))',
        { currentCompanyId: currentUser.companyId }
      );
    } else {
      // Outros usuários só veem da própria empresa
      queryBuilder.andWhere('user.companyId = :companyId', { companyId: currentUser.companyId });
    }

    // Filtros adicionais
    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Paginação
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Ordenação
    queryBuilder.orderBy('user.createdAt', 'DESC');

    const [users, total] = await queryBuilder.getManyAndCount();

    return {
      users,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['company'],
    });

    if (!user) {
      throw new NotFoundException('Usuário não encontrado');
    }

    // Verificar se o usuário atual pode acessar este usuário
    if (currentUser.canAccessCompany && !currentUser.canAccessCompany(user.companyId)) {
      throw new ForbiddenException('Acesso negado a este usuário');
    }

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto, currentUser: AuthenticatedUser): Promise<User> {
    const user = await this.findOne(id, currentUser);

    // Verificar hierarquia de papéis se estiver alterando o papel
    if (updateUserDto.role) {
      this.validateRoleHierarchy(currentUser.role as UserRole, updateUserDto.role);
    }

    // Verificar se email já existe (se estiver sendo alterado)
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('Email já está em uso');
      }
    }

    Object.assign(user, updateUserDto);
    user.updatedBy = currentUser.id;

    return this.userRepository.save(user);
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const user = await this.findOne(id, currentUser);

    // Não permitir que o usuário delete a si mesmo
    if (user.id === currentUser.id) {
      throw new ForbiddenException('Não é possível deletar sua própria conta');
    }

    // Verificar hierarquia de papéis
    this.validateRoleHierarchy(currentUser.role as UserRole, user.role);

    await this.userRepository.softDelete(id);
  }

  async changePassword(id: string, newPassword: string, currentUser: AuthenticatedUser): Promise<void> {
    const user = await this.findOne(id, currentUser);

    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    await this.userRepository.update(id, {
      password: hashedPassword,
      updatedBy: currentUser.id,
    });
  }

  private validateRoleHierarchy(currentUserRole: UserRole, targetRole: UserRole): void {
    const roleHierarchy = {
      [UserRole.SUPER_ADMIN]: 4,
      [UserRole.AGENCY_ADMIN]: 3,
      [UserRole.COMPANY_ADMIN]: 2,
      [UserRole.SELLER]: 1,
    };

    const currentLevel = roleHierarchy[currentUserRole];
    const targetLevel = roleHierarchy[targetRole];

    if (currentLevel <= targetLevel) {
      throw new ForbiddenException('Não é possível gerenciar usuários com papel igual ou superior');
    }
  }
}
