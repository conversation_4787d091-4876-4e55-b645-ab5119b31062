import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Automation, AutomationSchema } from '../../database/entities/automation.schema';
import { AutomationExecution, AutomationExecutionSchema } from '../../database/entities/automation-execution.schema';
import { AutomationService } from './services/automation.service';
import { AutomationExecutorService } from './services/automation-executor.service';
import { AutomationController } from './automation.controller';
import { AnalyticsModule } from '../analytics/analytics.module';
import { WhatsAppModule } from '../whatsapp/whatsapp.module';
import { MessagesModule } from '../messages/messages.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Automation.name, schema: AutomationSchema },
      { name: AutomationExecution.name, schema: AutomationExecutionSchema },
    ]),
    AnalyticsModule,
    WhatsAppModule,
    MessagesModule,
  ],
  controllers: [AutomationController],
  providers: [AutomationService, AutomationExecutorService],
  exports: [AutomationService, AutomationExecutorService],
})
export class AutomationModule {}
