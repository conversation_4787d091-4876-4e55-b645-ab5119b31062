import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { 
  AutomationType, 
  AutomationStatus, 
  TriggerCondition, 
  ActionStep, 
  FlowStep 
} from '../../../database/entities/automation.schema';

export class AutomationResponseDto {
  @ApiProperty({
    description: 'ID da automação',
    example: 'automation-id',
  })
  @Expose()
  @Transform(({ obj }) => obj._id?.toString() || obj.id)
  id: string;

  @ApiProperty({
    description: 'Nome da automação',
    example: 'Boas-vindas para novos contatos',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição da automação',
    example: 'Envia mensagem de boas-vindas e coleta informações básicas',
  })
  @Expose()
  description?: string;

  @ApiProperty({
    description: 'Tipo da automação',
    enum: AutomationType,
  })
  @Expose()
  type: AutomationType;

  @ApiProperty({
    description: 'Status da automação',
    enum: AutomationStatus,
  })
  @Expose()
  status: AutomationStatus;

  @ApiProperty({
    description: 'ID da empresa',
    example: 'company-id',
  })
  @Expose()
  @Transform(({ obj }) => obj.companyId?.toString())
  companyId: string;

  @ApiProperty({
    description: 'Criado por',
    example: 'user-id',
  })
  @Expose()
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Atualizado por',
    example: 'user-id',
  })
  @Expose()
  updatedBy?: string;

  @ApiProperty({
    description: 'Configuração do gatilho',
    type: Object,
  })
  @Expose()
  trigger: TriggerCondition;

  @ApiPropertyOptional({
    description: 'Passos de ação',
    type: [Object],
  })
  @Expose()
  actions?: ActionStep[];

  @ApiPropertyOptional({
    description: 'Passos do fluxo',
    type: [Object],
  })
  @Expose()
  flowSteps?: FlowStep[];

  @ApiPropertyOptional({
    description: 'ID do passo inicial',
    example: 'step-1',
  })
  @Expose()
  startStepId?: string;

  @ApiProperty({
    description: 'Configurações da automação',
    type: Object,
  })
  @Expose()
  settings: Record<string, any>;

  @ApiProperty({
    description: 'Número de execuções',
    example: 150,
  })
  @Expose()
  executionCount: number;

  @ApiProperty({
    description: 'Número de sucessos',
    example: 142,
  })
  @Expose()
  successCount: number;

  @ApiProperty({
    description: 'Número de erros',
    example: 8,
  })
  @Expose()
  errorCount: number;

  @ApiProperty({
    description: 'Taxa de sucesso (%)',
    example: 94.67,
  })
  @Expose()
  @Transform(({ obj }) => {
    if (obj.executionCount === 0) return 0;
    return Math.round((obj.successCount / obj.executionCount) * 100 * 100) / 100;
  })
  successRate: number;

  @ApiPropertyOptional({
    description: 'Última execução',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastExecutedAt?: Date;

  @ApiPropertyOptional({
    description: 'Última modificação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastModifiedAt?: Date;

  @ApiProperty({
    description: 'Versão da automação',
    example: 1,
  })
  @Expose()
  version: number;

  @ApiProperty({
    description: 'Se é um template',
    example: false,
  })
  @Expose()
  isTemplate: boolean;

  @ApiPropertyOptional({
    description: 'Categoria do template',
    example: 'vendas',
  })
  @Expose()
  templateCategory?: string;

  @ApiProperty({
    description: 'Se está ativa',
    example: true,
  })
  @Expose()
  @Transform(({ obj }) => obj.status === AutomationStatus.ACTIVE)
  isActive: boolean;

  @ApiProperty({
    description: 'Número de passos',
    example: 5,
  })
  @Expose()
  @Transform(({ obj }) => (obj.actions?.length || 0) + (obj.flowSteps?.length || 0))
  stepCount: number;

  @ApiPropertyOptional({
    description: 'Metadados adicionais',
  })
  @Expose()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;
}

export class AutomationExecutionResponseDto {
  @ApiProperty({
    description: 'ID da execução',
    example: 'execution-id',
  })
  @Expose()
  @Transform(({ obj }) => obj._id?.toString() || obj.id)
  id: string;

  @ApiProperty({
    description: 'ID da automação',
    example: 'automation-id',
  })
  @Expose()
  @Transform(({ obj }) => obj.automationId?.toString())
  automationId: string;

  @ApiProperty({
    description: 'Nome da automação',
    example: 'Boas-vindas para novos contatos',
  })
  @Expose()
  automationName: string;

  @ApiProperty({
    description: 'Status da execução',
    example: 'completed',
  })
  @Expose()
  status: string;

  @ApiProperty({
    description: 'Contexto da execução',
    type: Object,
  })
  @Expose()
  context: Record<string, any>;

  @ApiProperty({
    description: 'Passos executados',
    type: [Object],
  })
  @Expose()
  steps: Array<{
    stepId: string;
    stepName: string;
    status: string;
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    error?: any;
  }>;

  @ApiPropertyOptional({
    description: 'Passo atual',
    example: 'step-2',
  })
  @Expose()
  currentStepId?: string;

  @ApiPropertyOptional({
    description: 'Data de início',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  startedAt?: Date;

  @ApiPropertyOptional({
    description: 'Data de conclusão',
    example: '2023-12-01T10:05:00Z',
  })
  @Expose()
  completedAt?: Date;

  @ApiProperty({
    description: 'Duração em milissegundos',
    example: 300000,
  })
  @Expose()
  duration: number;

  @ApiPropertyOptional({
    description: 'Erro da execução',
    type: Object,
  })
  @Expose()
  error?: Record<string, any>;

  @ApiProperty({
    description: 'Número de tentativas',
    example: 1,
  })
  @Expose()
  retryCount: number;

  @ApiProperty({
    description: 'Tipo do gatilho',
    example: 'message_received',
  })
  @Expose()
  triggerType: string;

  @ApiProperty({
    description: 'Resultados da execução',
    type: Object,
  })
  @Expose()
  results: Record<string, any>;

  @ApiProperty({
    description: 'Progresso (%)',
    example: 100,
  })
  @Expose()
  @Transform(({ obj }) => {
    if (obj.totalSteps === 0) return 0;
    return Math.round((obj.completedSteps / obj.totalSteps) * 100);
  })
  progress: number;

  @ApiProperty({
    description: 'Total de passos',
    example: 5,
  })
  @Expose()
  totalSteps: number;

  @ApiProperty({
    description: 'Passos concluídos',
    example: 5,
  })
  @Expose()
  completedSteps: number;

  @ApiProperty({
    description: 'Passos falhados',
    example: 0,
  })
  @Expose()
  failedSteps: number;

  @ApiProperty({
    description: 'ID único da execução',
    example: 'exec_123456789',
  })
  @Expose()
  executionId: string;

  @ApiProperty({
    description: 'Se é um teste',
    example: false,
  })
  @Expose()
  isTest: boolean;

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;
}
