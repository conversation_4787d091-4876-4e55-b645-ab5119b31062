import { WhatsAppService } from '../services/whatsapp.service'
import { WhatsAppConnectionRepository } from '../repositories/whatsapp-connection.repository'
import { MessageRepository } from '../repositories/message.repository'
import { ContactRepository } from '../repositories/contact.repository'
import { EvolutionApiService } from '../services/evolution-api.service'
import { ConnectionStatus, ConnectionType, MessageDirection, MessageStatus, MessageType } from '@prisma/client'

// Mock das dependências
jest.mock('../repositories/whatsapp-connection.repository')
jest.mock('../repositories/message.repository')
jest.mock('../repositories/contact.repository')
jest.mock('../services/evolution-api.service')

describe('WhatsAppService', () => {
  let whatsappService: WhatsAppService
  let mockConnectionRepository: jest.Mocked<WhatsAppConnectionRepository>
  let mockMessageRepository: jest.Mocked<MessageRepository>
  let mockContactRepository: jest.Mocked<ContactRepository>
  let mockEvolutionApi: jest.Mocked<EvolutionApiService>

  beforeEach(() => {
    mockConnectionRepository = new WhatsAppConnectionRepository() as jest.Mocked<WhatsAppConnectionRepository>
    mockMessageRepository = new MessageRepository() as jest.Mocked<MessageRepository>
    mockContactRepository = new ContactRepository() as jest.Mocked<ContactRepository>
    mockEvolutionApi = new EvolutionApiService() as jest.Mocked<EvolutionApiService>
    
    whatsappService = new WhatsAppService()
    
    // Injetar mocks
    ;(whatsappService as any).connectionRepository = mockConnectionRepository
    ;(whatsappService as any).messageRepository = mockMessageRepository
    ;(whatsappService as any).contactRepository = mockContactRepository
    ;(whatsappService as any).evolutionApi = mockEvolutionApi
  })

  describe('createConnection', () => {
    const createConnectionData = {
      name: 'Conexão Teste',
      phoneNumber: '5511999999999',
      companyId: 'company-1',
      type: ConnectionType.EVOLUTION_API
    }

    it('deve criar nova conexão', async () => {
      // Arrange
      mockConnectionRepository.findByPhoneNumber.mockResolvedValue(null)
      
      const mockConnection = {
        id: 'connection-1',
        name: 'Conexão Teste',
        phoneNumber: '5511999999999',
        status: ConnectionStatus.DISCONNECTED,
        companyId: 'company-1'
      }
      
      mockConnectionRepository.create.mockResolvedValue(mockConnection as any)

      // Act
      const result = await whatsappService.createConnection(createConnectionData)

      // Assert
      expect(mockConnectionRepository.findByPhoneNumber).toHaveBeenCalledWith(
        createConnectionData.phoneNumber,
        createConnectionData.companyId
      )
      expect(mockConnectionRepository.create).toHaveBeenCalled()
      expect(result).toEqual(mockConnection)
    })

    it('deve rejeitar criação de conexão com número já existente', async () => {
      // Arrange
      const existingConnection = {
        id: 'connection-1',
        phoneNumber: '5511999999999',
        companyId: 'company-1'
      }
      
      mockConnectionRepository.findByPhoneNumber.mockResolvedValue(existingConnection as any)

      // Act & Assert
      await expect(whatsappService.createConnection(createConnectionData))
        .rejects.toThrow('Já existe uma conexão com este número de telefone')
    })
  })

  describe('connectWhatsApp', () => {
    const mockConnection = {
      id: 'connection-1',
      name: 'Conexão Teste',
      phoneNumber: '5511999999999',
      status: ConnectionStatus.DISCONNECTED,
      companyId: 'company-1',
      instanceId: null
    }

    it('deve conectar WhatsApp com sucesso', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.updateStatus.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.update.mockResolvedValue(mockConnection as any)
      
      const mockInstance = {
        instanceName: 'instance_connection-1',
        status: 'close'
      }
      
      const mockQrData = {
        base64: 'mock-qr-code-base64'
      }
      
      mockEvolutionApi.createInstance.mockResolvedValue(mockInstance as any)
      mockEvolutionApi.getQrCode.mockResolvedValue(mockQrData as any)
      mockConnectionRepository.updateQrCode.mockResolvedValue(mockConnection as any)

      // Act
      const result = await whatsappService.connectWhatsApp('connection-1')

      // Assert
      expect(mockConnectionRepository.findById).toHaveBeenCalledWith('connection-1')
      expect(mockConnectionRepository.updateStatus).toHaveBeenCalledWith('connection-1', ConnectionStatus.CONNECTING)
      expect(mockEvolutionApi.createInstance).toHaveBeenCalled()
      expect(mockEvolutionApi.getQrCode).toHaveBeenCalled()
      expect(result).toHaveProperty('qrCode')
    })

    it('deve rejeitar conexão se já estiver conectada', async () => {
      // Arrange
      const connectedConnection = {
        ...mockConnection,
        status: ConnectionStatus.CONNECTED
      }
      
      mockConnectionRepository.findById.mockResolvedValue(connectedConnection as any)

      // Act & Assert
      await expect(whatsappService.connectWhatsApp('connection-1'))
        .rejects.toThrow('Conexão já está ativa')
    })

    it('deve rejeitar conexão se não encontrar a conexão', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(null)

      // Act & Assert
      await expect(whatsappService.connectWhatsApp('connection-1'))
        .rejects.toThrow('Conexão não encontrada')
    })
  })

  describe('sendMessage', () => {
    const mockConnection = {
      id: 'connection-1',
      status: ConnectionStatus.CONNECTED,
      companyId: 'company-1',
      instanceId: 'instance_connection-1'
    }

    const mockContact = {
      id: 'contact-1',
      phone: '5511999999999',
      whatsappConnectionId: 'connection-1',
      companyId: 'company-1'
    }

    const sendMessageData = {
      connectionId: 'connection-1',
      contactPhone: '5511999999999',
      content: 'Olá, teste!',
      type: MessageType.TEXT,
      userId: 'user-1'
    }

    it('deve enviar mensagem com sucesso', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(mockConnection as any)
      mockContactRepository.findOrCreate.mockResolvedValue(mockContact as any)
      
      const mockMessage = {
        id: 'message-1',
        messageId: 'msg_123',
        content: { text: 'Olá, teste!' },
        direction: MessageDirection.OUTBOUND,
        status: MessageStatus.PENDING
      }
      
      mockMessageRepository.create.mockResolvedValue(mockMessage as any)
      mockContactRepository.updateMessageStats.mockResolvedValue(mockContact as any)
      
      const mockEvolutionResult = {
        key: { id: 'evolution_msg_123' }
      }
      
      mockEvolutionApi.sendTextMessage.mockResolvedValue(mockEvolutionResult)
      mockEvolutionApi.formatPhoneNumber.mockReturnValue('<EMAIL>')
      mockMessageRepository.update.mockResolvedValue(mockMessage as any)
      mockMessageRepository.updateStatus.mockResolvedValue(mockMessage as any)

      // Act
      const result = await whatsappService.sendMessage(sendMessageData)

      // Assert
      expect(mockConnectionRepository.findById).toHaveBeenCalledWith('connection-1')
      expect(mockContactRepository.findOrCreate).toHaveBeenCalled()
      expect(mockMessageRepository.create).toHaveBeenCalled()
      expect(mockEvolutionApi.sendTextMessage).toHaveBeenCalled()
      expect(mockMessageRepository.updateStatus).toHaveBeenCalledWith(mockMessage.id, MessageStatus.SENT)
      expect(result).toEqual(mockMessage)
    })

    it('deve rejeitar envio se conexão não estiver ativa', async () => {
      // Arrange
      const disconnectedConnection = {
        ...mockConnection,
        status: ConnectionStatus.DISCONNECTED
      }
      
      mockConnectionRepository.findById.mockResolvedValue(disconnectedConnection as any)

      // Act & Assert
      await expect(whatsappService.sendMessage(sendMessageData))
        .rejects.toThrow('Conexão não está ativa')
    })

    it('deve rejeitar envio se conexão não for encontrada', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(null)

      // Act & Assert
      await expect(whatsappService.sendMessage(sendMessageData))
        .rejects.toThrow('Conexão não encontrada')
    })

    it('deve marcar mensagem como falha se Evolution API falhar', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(mockConnection as any)
      mockContactRepository.findOrCreate.mockResolvedValue(mockContact as any)
      
      const mockMessage = {
        id: 'message-1',
        messageId: 'msg_123'
      }
      
      mockMessageRepository.create.mockResolvedValue(mockMessage as any)
      mockContactRepository.updateMessageStats.mockResolvedValue(mockContact as any)
      mockEvolutionApi.formatPhoneNumber.mockReturnValue('<EMAIL>')
      mockEvolutionApi.sendTextMessage.mockRejectedValue(new Error('Evolution API Error'))
      mockMessageRepository.updateStatus.mockResolvedValue(mockMessage as any)

      // Act & Assert
      await expect(whatsappService.sendMessage(sendMessageData)).rejects.toThrow('Evolution API Error')
      expect(mockMessageRepository.updateStatus).toHaveBeenCalledWith(mockMessage.id, MessageStatus.FAILED)
    })
  })

  describe('disconnectWhatsApp', () => {
    const mockConnection = {
      id: 'connection-1',
      status: ConnectionStatus.CONNECTED,
      instanceId: 'instance_connection-1'
    }

    it('deve desconectar WhatsApp com sucesso', async () => {
      // Arrange
      mockConnectionRepository.findById.mockResolvedValue(mockConnection as any)
      mockEvolutionApi.logoutInstance.mockResolvedValue(undefined)
      mockConnectionRepository.updateStatus.mockResolvedValue(mockConnection as any)
      mockConnectionRepository.clearQrCode.mockResolvedValue(mockConnection as any)

      // Act
      await whatsappService.disconnectWhatsApp('connection-1')

      // Assert
      expect(mockConnectionRepository.findById).toHaveBeenCalledWith('connection-1')
      expect(mockEvolutionApi.logoutInstance).toHaveBeenCalledWith(mockConnection.instanceId)
      expect(mockConnectionRepository.updateStatus).toHaveBeenCalledWith('connection-1', ConnectionStatus.DISCONNECTED)
      expect(mockConnectionRepository.clearQrCode).toHaveBeenCalledWith('connection-1')
    })

    it('deve rejeitar desconexão se já estiver desconectada', async () => {
      // Arrange
      const disconnectedConnection = {
        ...mockConnection,
        status: ConnectionStatus.DISCONNECTED
      }
      
      mockConnectionRepository.findById.mockResolvedValue(disconnectedConnection as any)

      // Act & Assert
      await expect(whatsappService.disconnectWhatsApp('connection-1'))
        .rejects.toThrow('Conexão já está desconectada')
    })
  })
})
