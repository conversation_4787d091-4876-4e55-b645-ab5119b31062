import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { MessagesService } from '../services/messages.service';
import { ContactsService } from '../services/contacts.service';

interface AuthenticatedSocket extends Socket {
  user?: AuthenticatedUser;
}

@WebSocketGateway({
  cors: {
    origin: '*',
    credentials: true,
  },
  namespace: '/messages',
})
export class MessagesGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(MessagesGateway.name);
  private connectedUsers = new Map<string, Set<string>>(); // userId -> Set<socketId>

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private messagesService: MessagesService,
    private contactsService: ContactsService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extrair token do handshake
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Connection rejected: No token provided`);
        client.disconnect();
        return;
      }

      // Verificar token
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('jwt.secret'),
      });

      // Simular usuário autenticado (em produção, buscar do banco)
      const user: AuthenticatedUser = {
        id: payload.sub,
        email: payload.email,
        role: payload.role,
        companyId: payload.companyId,
      };

      client.user = user;

      // Adicionar à lista de usuários conectados
      if (!this.connectedUsers.has(user.id)) {
        this.connectedUsers.set(user.id, new Set());
      }
      this.connectedUsers.get(user.id)!.add(client.id);

      // Entrar na sala da empresa
      await client.join(`company:${user.companyId}`);

      this.logger.log(`User connected: ${user.email} (${client.id})`);

      // Enviar confirmação de conexão
      client.emit('connected', {
        message: 'Conectado ao sistema de mensagens',
        userId: user.id,
        companyId: user.companyId,
      });

    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.user) {
      const userSockets = this.connectedUsers.get(client.user.id);
      if (userSockets) {
        userSockets.delete(client.id);
        if (userSockets.size === 0) {
          this.connectedUsers.delete(client.user.id);
        }
      }

      this.logger.log(`User disconnected: ${client.user.email} (${client.id})`);
    }
  }

  @SubscribeMessage('join_conversation')
  async handleJoinConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { contactPhone: string; whatsappConnectionId: string },
  ) {
    if (!client.user) return;

    const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
    await client.join(conversationId);

    this.logger.log(`User ${client.user.email} joined conversation: ${conversationId}`);

    // Enviar histórico de mensagens
    try {
      const messages = await this.messagesService.getConversation(
        data.contactPhone,
        data.whatsappConnectionId,
        client.user,
        50,
      );

      client.emit('conversation_history', {
        conversationId,
        messages: messages.reverse(), // Ordem cronológica
      });
    } catch (error) {
      this.logger.error(`Error loading conversation history: ${error.message}`);
      client.emit('error', { message: 'Erro ao carregar histórico da conversa' });
    }
  }

  @SubscribeMessage('leave_conversation')
  async handleLeaveConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { contactPhone: string; whatsappConnectionId: string },
  ) {
    if (!client.user) return;

    const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
    await client.leave(conversationId);

    this.logger.log(`User ${client.user.email} left conversation: ${conversationId}`);
  }

  @SubscribeMessage('typing_start')
  async handleTypingStart(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { contactPhone: string; whatsappConnectionId: string },
  ) {
    if (!client.user) return;

    const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
    
    // Notificar outros usuários na conversa
    client.to(conversationId).emit('user_typing', {
      userId: client.user.id,
      userName: client.user.email,
      conversationId,
    });
  }

  @SubscribeMessage('typing_stop')
  async handleTypingStop(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { contactPhone: string; whatsappConnectionId: string },
  ) {
    if (!client.user) return;

    const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
    
    // Notificar outros usuários na conversa
    client.to(conversationId).emit('user_stopped_typing', {
      userId: client.user.id,
      userName: client.user.email,
      conversationId,
    });
  }

  @SubscribeMessage('mark_as_read')
  async handleMarkAsRead(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { messageId: string },
  ) {
    if (!client.user) return;

    try {
      // Aqui você implementaria a lógica para marcar como lida
      // Por enquanto, apenas log
      this.logger.log(`Message marked as read: ${data.messageId} by ${client.user.email}`);
      
      client.emit('message_read_confirmed', { messageId: data.messageId });
    } catch (error) {
      this.logger.error(`Error marking message as read: ${error.message}`);
      client.emit('error', { message: 'Erro ao marcar mensagem como lida' });
    }
  }

  // Métodos para emitir eventos do servidor

  async emitNewMessage(message: any, companyId: string) {
    const conversationId = `${message.whatsappConnectionId}:${message.contactPhone}`;
    
    // Emitir para todos na conversa
    this.server.to(conversationId).emit('new_message', message);
    
    // Emitir para todos da empresa (para notificações)
    this.server.to(`company:${companyId}`).emit('message_notification', {
      conversationId,
      message,
      type: 'new_message',
    });

    this.logger.log(`New message emitted to conversation: ${conversationId}`);
  }

  async emitMessageStatusUpdate(messageId: string, status: string, companyId: string) {
    this.server.to(`company:${companyId}`).emit('message_status_update', {
      messageId,
      status,
      timestamp: new Date(),
    });

    this.logger.log(`Message status update emitted: ${messageId} -> ${status}`);
  }

  async emitContactUpdate(contact: any, companyId: string) {
    this.server.to(`company:${companyId}`).emit('contact_update', contact);
    this.logger.log(`Contact update emitted for company: ${companyId}`);
  }

  async emitConnectionStatusUpdate(connectionId: string, status: string, companyId: string) {
    this.server.to(`company:${companyId}`).emit('connection_status_update', {
      connectionId,
      status,
      timestamp: new Date(),
    });

    this.logger.log(`Connection status update emitted: ${connectionId} -> ${status}`);
  }

  // Métodos utilitários

  getConnectedUsers(): Map<string, Set<string>> {
    return this.connectedUsers;
  }

  isUserOnline(userId: string): boolean {
    return this.connectedUsers.has(userId) && this.connectedUsers.get(userId)!.size > 0;
  }

  async sendNotificationToUser(userId: string, notification: any) {
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.forEach(socketId => {
        this.server.to(socketId).emit('notification', notification);
      });
    }
  }
}
