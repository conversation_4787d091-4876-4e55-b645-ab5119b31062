import { AnalyticsService } from './analytics.service';
export declare class TrackingService {
    private analyticsService;
    private readonly logger;
    constructor(analyticsService: AnalyticsService);
    trackUserLogin(userId: string, companyId: string, sessionId: string, metadata?: any): Promise<void>;
    trackUserLogout(userId: string, companyId: string, sessionId: string, sessionDuration?: number): Promise<void>;
    trackUserCreated(userId: string, companyId: string, createdBy: string, userRole: string): Promise<void>;
    trackWhatsAppConnected(connectionId: string, companyId: string, userId: string, phoneNumber: string): Promise<void>;
    trackWhatsAppDisconnected(connectionId: string, companyId: string, userId?: string, reason?: string): Promise<void>;
    trackQRCodeGenerated(connectionId: string, companyId: string, userId: string): Promise<void>;
    trackMessageSent(messageId: string, companyId: string, connectionId: string, contactPhone: string, messageType: string, isAutomated?: boolean, userId?: string, automationId?: string): Promise<void>;
    trackMessageReceived(messageId: string, companyId: string, connectionId: string, contactPhone: string, messageType: string, isFirstMessage?: boolean): Promise<void>;
    trackMessageDelivered(messageId: string, companyId: string, deliveryTime?: number): Promise<void>;
    trackMessageRead(messageId: string, companyId: string, readTime?: number): Promise<void>;
    trackMessageFailed(messageId: string, companyId: string, errorCode?: string, errorMessage?: string): Promise<void>;
    trackBulkMessageSent(companyId: string, connectionId: string, recipientCount: number, messageType: string, userId: string, campaignId?: string): Promise<void>;
    trackContactCreated(contactId: string, companyId: string, connectionId: string, contactPhone: string, source?: string, userId?: string): Promise<void>;
    trackContactUpdated(contactId: string, companyId: string, contactPhone: string, updatedFields: string[], userId: string): Promise<void>;
    trackContactTagged(contactId: string, companyId: string, contactPhone: string, tagName: string, userId: string): Promise<void>;
    trackLeadConverted(contactId: string, companyId: string, contactPhone: string, conversionType: string, conversionValue?: number, userId?: string): Promise<void>;
    trackLeadScored(contactId: string, companyId: string, contactPhone: string, leadScore: number, previousScore?: number, userId?: string): Promise<void>;
    trackConversationStarted(conversationId: string, companyId: string, connectionId: string, contactPhone: string, initiatedBy: 'contact' | 'user'): Promise<void>;
    trackConversationEnded(conversationId: string, companyId: string, connectionId: string, contactPhone: string, duration: number, messageCount: number): Promise<void>;
    trackFirstResponse(companyId: string, connectionId: string, contactPhone: string, responseTime: number, userId: string): Promise<void>;
    trackResponseTime(companyId: string, connectionId: string, contactPhone: string, responseTime: number, userId: string): Promise<void>;
    trackApiRequest(companyId: string, endpoint: string, method: string, responseTime: number, statusCode: number, userId?: string): Promise<void>;
    trackError(companyId: string, errorCode: string, errorMessage: string, context?: string, userId?: string): Promise<void>;
    trackPerformanceMetric(companyId: string, metricName: string, value: number, unit: string, context?: string): Promise<void>;
}
