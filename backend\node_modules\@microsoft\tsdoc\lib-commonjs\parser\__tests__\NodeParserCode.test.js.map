{"version": 3, "file": "NodeParserCode.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserCode.test.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,6CAA4C;AAE5C,IAAI,CAAC,8BAA8B,EAAE;IACnC,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,aAAa,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1G,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,8BAA8B,EAAE;IACnC,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAChG,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,yCAAyC;QACzC,sBAAsB;QACtB,uBAAuB;QACvB,WAAW;QACX,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,kEAAkE;QAClE,QAAQ;QACR,uBAAuB;QACvB,UAAU;KACX,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yBAAyB,EAAE;IAC9B,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,qCAAqC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9E,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,sCAAsC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5E,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,uCAAuC,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACxE,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,uCAAuC,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACxF,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,+BAA+B,EAAE,uBAAuB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpF,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,sCAAsC,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACtG,CAAC;IACF,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,qDAAqD;QACrD,QAAQ;QACR,SAAS;QACT,WAAW;QACX,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Code span basic, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * line `1`', ' * line ` 2` sdf', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * M`&`M', ' */'].join('\\n'));\r\n});\r\n\r\ntest('01 Code span basic, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * `multi', ' * line`', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * ``', ' */'].join('\\n'));\r\n});\r\n\r\ntest('03 Code fence, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * This is a code fence with all parts:',\r\n      ' * ```a language!   ',\r\n      ' *   some `code` here',\r\n      ' * ```   ',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * This is a code fence with no language or trailing whitespace:',\r\n      ' * ```',\r\n      ' *   some `code` here',\r\n      ' * ```*/'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 Code fence, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Code fence incorrectly indented:', ' *    ```', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Code fence not starting the line:', ' *a```', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Code fence not being terminated 1:', ' * ```*/'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Code fence not being terminated 2:', ' * ``` some stuff', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Language having backticks:', ' * ``` some stuff ```', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * Closing delimiter being indented:', ' * ```', ' * code', ' *      ```', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * Closing delimiter not being on a line by itself:',\r\n      ' * ```',\r\n      ' * code',\r\n      ' * ```  a',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n"]}