import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { ConnectionStatus } from '../../../database/entities';
export interface EvolutionInstance {
    instanceName: string;
    status: string;
    qrcode?: string;
    profilePictureUrl?: string;
    profileName?: string;
    number?: string;
}
export interface EvolutionMessage {
    number: string;
    text?: string;
    media?: {
        mediatype: string;
        media: string;
        fileName?: string;
        caption?: string;
    };
    location?: {
        latitude: number;
        longitude: number;
        address?: string;
        name?: string;
    };
    contact?: {
        fullName: string;
        wuid: string;
        phoneNumber: string;
    };
    quoted?: {
        messageId: string;
    };
}
export interface EvolutionWebhookData {
    instanceName: string;
    event: string;
    data: any;
}
export declare class EvolutionApiService {
    private readonly configService;
    private readonly httpService;
    private readonly logger;
    private readonly baseUrl;
    private readonly apiKey;
    constructor(configService: ConfigService, httpService: HttpService);
    private getHeaders;
    createInstance(instanceName: string, webhookUrl?: string): Promise<EvolutionInstance>;
    deleteInstance(instanceName: string): Promise<void>;
    getInstanceStatus(instanceName: string): Promise<EvolutionInstance>;
    getQRCode(instanceName: string): Promise<string>;
    sendMessage(instanceName: string, message: EvolutionMessage): Promise<any>;
    sendMediaMessage(instanceName: string, message: EvolutionMessage): Promise<any>;
    logout(instanceName: string): Promise<void>;
    restart(instanceName: string): Promise<void>;
    private getMediaEndpoint;
    mapEvolutionStatusToConnectionStatus(evolutionStatus: string): ConnectionStatus;
    generateInstanceName(companyId: string, phoneNumber: string): string;
}
