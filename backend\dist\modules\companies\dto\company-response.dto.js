"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompanyResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const entities_1 = require("../../../database/entities");
class CompanyResponseDto {
    id;
    name;
    cnpj;
    email;
    phone;
    address;
    website;
    status;
    logo;
    agencyId;
    isAgency;
    activeUsersCount;
    settings;
    createdAt;
    updatedAt;
}
exports.CompanyResponseDto = CompanyResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 'uuid-da-empresa',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da empresa',
        example: 'Empresa Exemplo Ltda',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ da empresa',
        example: '12.345.678/0001-90',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email da empresa',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da empresa',
        example: '+5511999999999',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Endereço da empresa',
        example: 'Rua Exemplo, 123 - São Paulo, SP',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Website da empresa',
        example: 'https://www.empresa.com',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da empresa',
        enum: entities_1.CompanyStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Logo da empresa',
        example: 'https://example.com/logo.jpg',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "logo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da agência',
        example: 'uuid-da-agencia',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], CompanyResponseDto.prototype, "agencyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'É uma agência',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.agencyId === null),
    __metadata("design:type", Boolean)
], CompanyResponseDto.prototype, "isAgency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de usuários ativos',
        example: 5,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.users?.filter(user => user.isActive).length || 0),
    __metadata("design:type", Number)
], CompanyResponseDto.prototype, "activeUsersCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configurações da empresa',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], CompanyResponseDto.prototype, "settings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CompanyResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de atualização',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], CompanyResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=company-response.dto.js.map