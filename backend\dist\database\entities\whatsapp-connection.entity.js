"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppConnection = exports.ConnectionStatus = exports.ConnectionType = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("./base.entity");
const company_entity_1 = require("./company.entity");
const user_entity_1 = require("./user.entity");
var ConnectionType;
(function (ConnectionType) {
    ConnectionType["EVOLUTION_API"] = "evolution_api";
    ConnectionType["META_OFFICIAL"] = "meta_official";
})(ConnectionType || (exports.ConnectionType = ConnectionType = {}));
var ConnectionStatus;
(function (ConnectionStatus) {
    ConnectionStatus["CONNECTED"] = "connected";
    ConnectionStatus["DISCONNECTED"] = "disconnected";
    ConnectionStatus["CONNECTING"] = "connecting";
    ConnectionStatus["ERROR"] = "error";
    ConnectionStatus["PENDING"] = "pending";
})(ConnectionStatus || (exports.ConnectionStatus = ConnectionStatus = {}));
let WhatsAppConnection = class WhatsAppConnection extends base_entity_1.BaseEntity {
    name;
    phoneNumber;
    type;
    status;
    qrCode;
    instanceId;
    connectionData;
    settings;
    lastConnectedAt;
    lastDisconnectedAt;
    errorMessage;
    isActive;
    companyId;
    company;
    assignedUserId;
    assignedUser;
    get isConnected() {
        return this.status === ConnectionStatus.CONNECTED;
    }
    get displayName() {
        return `${this.name} (${this.phoneNumber})`;
    }
    get formattedPhoneNumber() {
        const cleaned = this.phoneNumber.replace(/\D/g, '');
        if (cleaned.length === 13 && cleaned.startsWith('55')) {
            const ddd = cleaned.substring(2, 4);
            const number = cleaned.substring(4);
            return `+55 (${ddd}) ${number.substring(0, 5)}-${number.substring(5)}`;
        }
        return this.phoneNumber;
    }
    updateStatus(status, errorMessage) {
        this.status = status;
        if (errorMessage) {
            this.errorMessage = errorMessage;
        }
        if (status === ConnectionStatus.CONNECTED) {
            this.lastConnectedAt = new Date();
            this.errorMessage = undefined;
        }
        else if (status === ConnectionStatus.DISCONNECTED || status === ConnectionStatus.ERROR) {
            this.lastDisconnectedAt = new Date();
        }
    }
    canBeUsedBy(userId) {
        return this.assignedUserId === userId || this.assignedUserId === null;
    }
};
exports.WhatsAppConnection = WhatsAppConnection;
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20, unique: true }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ConnectionType,
        default: ConnectionType.EVOLUTION_API,
    }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ConnectionStatus,
        default: ConnectionStatus.PENDING,
    }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "qrCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "instanceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WhatsAppConnection.prototype, "connectionData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], WhatsAppConnection.prototype, "settings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], WhatsAppConnection.prototype, "lastConnectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], WhatsAppConnection.prototype, "lastDisconnectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], WhatsAppConnection.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company, (company) => company.whatsappConnections),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], WhatsAppConnection.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], WhatsAppConnection.prototype, "assignedUserId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.assignedWhatsAppConnections, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'assignedUserId' }),
    __metadata("design:type", user_entity_1.User)
], WhatsAppConnection.prototype, "assignedUser", void 0);
exports.WhatsAppConnection = WhatsAppConnection = __decorate([
    (0, typeorm_1.Entity)('whatsapp_connections'),
    (0, typeorm_1.Index)(['phoneNumber'], { unique: true })
], WhatsAppConnection);
//# sourceMappingURL=whatsapp-connection.entity.js.map