import { AutomationService, FindAutomationsOptions, FindExecutionsOptions } from './services/automation.service';
import { AutomationExecutorService } from './services/automation-executor.service';
import { CreateAutomationDto } from './dto/create-automation.dto';
import { UpdateAutomationDto } from './dto/update-automation.dto';
import { AutomationResponseDto, AutomationExecutionResponseDto } from './dto/automation-response.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
export declare class AutomationController {
    private readonly automationService;
    private readonly executorService;
    constructor(automationService: AutomationService, executorService: AutomationExecutorService);
    create(createAutomationDto: CreateAutomationDto, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    findAll(query: FindAutomationsOptions, currentUser: AuthenticatedUser): Promise<{
        automations: AutomationResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    getStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        inactive: number;
        draft: number;
        totalExecutions: number;
        successRate: number;
    }>;
    getTemplates(category?: string, currentUser?: AuthenticatedUser): Promise<AutomationResponseDto[]>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    update(id: string, updateAutomationDto: UpdateAutomationDto, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    activate(id: string, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    deactivate(id: string, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    duplicate(id: string, currentUser: AuthenticatedUser): Promise<AutomationResponseDto>;
    test(id: string, testData: {
        contactPhone: string;
        connectionId: string;
    }, currentUser: AuthenticatedUser): Promise<AutomationExecutionResponseDto>;
    getExecutions(id: string, query: FindExecutionsOptions, currentUser: AuthenticatedUser): Promise<{
        executions: AutomationExecutionResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    getExecution(executionId: string, currentUser: AuthenticatedUser): Promise<AutomationExecutionResponseDto>;
    retryExecution(executionId: string, currentUser: AuthenticatedUser): Promise<void>;
}
