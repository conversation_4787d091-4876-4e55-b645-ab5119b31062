"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportQueryDto = exports.DashboardQueryDto = exports.AnalyticsQueryDto = exports.MetricType = exports.GroupBy = exports.TimeRange = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const analytics_event_schema_1 = require("../../../database/entities/analytics-event.schema");
var TimeRange;
(function (TimeRange) {
    TimeRange["LAST_24_HOURS"] = "last_24_hours";
    TimeRange["LAST_7_DAYS"] = "last_7_days";
    TimeRange["LAST_30_DAYS"] = "last_30_days";
    TimeRange["LAST_90_DAYS"] = "last_90_days";
    TimeRange["LAST_YEAR"] = "last_year";
    TimeRange["CUSTOM"] = "custom";
})(TimeRange || (exports.TimeRange = TimeRange = {}));
var GroupBy;
(function (GroupBy) {
    GroupBy["HOUR"] = "hour";
    GroupBy["DAY"] = "day";
    GroupBy["WEEK"] = "week";
    GroupBy["MONTH"] = "month";
    GroupBy["YEAR"] = "year";
})(GroupBy || (exports.GroupBy = GroupBy = {}));
var MetricType;
(function (MetricType) {
    MetricType["COUNT"] = "count";
    MetricType["SUM"] = "sum";
    MetricType["AVERAGE"] = "average";
    MetricType["MIN"] = "min";
    MetricType["MAX"] = "max";
    MetricType["UNIQUE"] = "unique";
})(MetricType || (exports.MetricType = MetricType = {}));
class AnalyticsQueryDto {
    timeRange = TimeRange.LAST_7_DAYS;
    startDate;
    endDate;
    eventTypes;
    categories;
    userIds;
    connectionIds;
    contactPhones;
    groupBy = GroupBy.DAY;
    metricType = MetricType.COUNT;
    metricField;
    filters;
    compareWithPrevious = false;
    limit = 100;
    page = 1;
}
exports.AnalyticsQueryDto = AnalyticsQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Período de tempo',
        enum: TimeRange,
        default: TimeRange.LAST_7_DAYS,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(TimeRange),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "timeRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de início (para período customizado)',
        example: '2023-12-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de fim (para período customizado)',
        example: '2023-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tipos de eventos',
        enum: analytics_event_schema_1.EventType,
        isArray: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(analytics_event_schema_1.EventType, { each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "eventTypes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Categorias de eventos',
        enum: analytics_event_schema_1.EventCategory,
        isArray: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(analytics_event_schema_1.EventCategory, { each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'IDs de usuários',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "userIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'IDs de conexões WhatsApp',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "connectionIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Telefones de contatos',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "contactPhones", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Agrupar por',
        enum: GroupBy,
        default: GroupBy.DAY,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(GroupBy),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "groupBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tipo de métrica',
        enum: MetricType,
        default: MetricType.COUNT,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(MetricType),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "metricType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Campo para calcular métrica (para sum, average, etc.)',
        example: 'value',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "metricField", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filtros customizados (JSON)',
        example: '{"properties.messageType": "text"}',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        try {
            return typeof value === 'string' ? JSON.parse(value) : value;
        }
        catch {
            return {};
        }
    }),
    __metadata("design:type", Object)
], AnalyticsQueryDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Incluir comparação com período anterior',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AnalyticsQueryDto.prototype, "compareWithPrevious", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Limite de resultados',
        default: 100,
        minimum: 1,
        maximum: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(1000),
    __metadata("design:type", Number)
], AnalyticsQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Página',
        default: 1,
        minimum: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AnalyticsQueryDto.prototype, "page", void 0);
class DashboardQueryDto {
    timeRange = TimeRange.LAST_7_DAYS;
    startDate;
    endDate;
    connectionIds;
    compareWithPrevious = true;
}
exports.DashboardQueryDto = DashboardQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Período de tempo',
        enum: TimeRange,
        default: TimeRange.LAST_7_DAYS,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(TimeRange),
    __metadata("design:type", String)
], DashboardQueryDto.prototype, "timeRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de início (para período customizado)',
        example: '2023-12-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], DashboardQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de fim (para período customizado)',
        example: '2023-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], DashboardQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'IDs de conexões WhatsApp',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DashboardQueryDto.prototype, "connectionIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Incluir comparação com período anterior',
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], DashboardQueryDto.prototype, "compareWithPrevious", void 0);
class ReportQueryDto extends AnalyticsQueryDto {
    format = 'json';
    includeDetails = false;
    fields;
}
exports.ReportQueryDto = ReportQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Formato do relatório',
        enum: ['json', 'csv', 'excel'],
        default: 'json',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['json', 'csv', 'excel']),
    __metadata("design:type", String)
], ReportQueryDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Incluir dados detalhados',
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], ReportQueryDto.prototype, "includeDetails", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Campos para incluir no relatório',
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ReportQueryDto.prototype, "fields", void 0);
//# sourceMappingURL=analytics-query.dto.js.map