import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ContactDocument = Contact & Document;

export enum ContactStatus {
  ACTIVE = 'active',
  BLOCKED = 'blocked',
  ARCHIVED = 'archived',
}

export interface ContactTag {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
  createdBy: string;
}

export interface ContactSource {
  type: string; // 'google_ads', 'meta_ads', 'organic', 'direct', etc.
  campaign?: string;
  medium?: string;
  source?: string;
  utm_params?: Record<string, string>;
  referrer?: string;
  capturedAt: Date;
}

@Schema({ timestamps: true })
export class Contact {
  @Prop({ required: true })
  phoneNumber: string;

  @Prop()
  name?: string;

  @Prop()
  email?: string;

  @Prop()
  profilePicture?: string;

  @Prop({ type: String, enum: ContactStatus, default: ContactStatus.ACTIVE })
  status: ContactStatus;

  @Prop({ type: [Schema.Types.Mixed], default: [] })
  tags: ContactTag[];

  @Prop({ type: Schema.Types.Mixed })
  source?: ContactSource;

  @Prop({ type: Schema.Types.Mixed, default: {} })
  customFields: Record<string, any>;

  @Prop({ type: Schema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop()
  lastMessageAt?: Date;

  @Prop()
  lastInteractionAt?: Date;

  @Prop({ default: 0 })
  messageCount: number;

  @Prop({ default: false })
  isLead: boolean;

  @Prop()
  leadScore?: number;

  @Prop()
  leadStage?: string;

  @Prop()
  assignedTo?: string; // User ID

  @Prop()
  notes?: string;

  // Relacionamentos
  @Prop({ required: true, type: Types.ObjectId, ref: 'Company' })
  companyId: Types.ObjectId;

  @Prop({ required: true })
  whatsappConnectionId: string;

  @Prop()
  createdBy?: string;

  @Prop()
  updatedBy?: string;
}

export const ContactSchema = SchemaFactory.createForClass(Contact);

// Índices
ContactSchema.index({ phoneNumber: 1, companyId: 1 }, { unique: true });
ContactSchema.index({ companyId: 1, whatsappConnectionId: 1 });
ContactSchema.index({ companyId: 1, assignedTo: 1 });
ContactSchema.index({ companyId: 1, isLead: 1 });
ContactSchema.index({ companyId: 1, 'tags.name': 1 });
ContactSchema.index({ companyId: 1, 'source.type': 1 });
ContactSchema.index({ lastMessageAt: -1 });
ContactSchema.index({ createdAt: -1 });
