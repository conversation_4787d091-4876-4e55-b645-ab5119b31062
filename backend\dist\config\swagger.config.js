"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupSwagger = setupSwagger;
const swagger_1 = require("@nestjs/swagger");
function setupSwagger(app) {
    const config = new swagger_1.DocumentBuilder()
        .setTitle('WhatsApp Business Platform API')
        .setDescription(`
# WhatsApp Business Platform API

Uma plataforma completa para gestão de WhatsApp Business com recursos avançados de automação, analytics e integrações.

## Funcionalidades Principais

### 🔐 Autenticação e Autorização
- Sistema JWT com refresh tokens
- RBAC (Role-Based Access Control)
- Multi-tenant com isolamento completo
- Recuperação de senha por email

### 📱 Gestão de Conexões WhatsApp
- Integração com Evolution API V2
- Suporte para API Oficial da Meta (preparado)
- Múltiplas conexões por empresa
- Monitoramento de status em tempo real

### 💬 Sistema de Mensagens
- Envio e recebimento de mensagens
- Suporte a texto, imagem, áudio, vídeo, documento
- WebSocket para tempo real
- Histórico completo de conversas

### 👥 Gestão de Contatos
- Cadastro automático via WhatsApp
- Tags e campos customizados
- Segmentação avançada
- Importação/exportação

### 📊 Analytics e Relatórios
- Dashboard em tempo real
- Métricas de engajamento
- Relatórios customizáveis
- Tracking de eventos

### 🤖 Automação e Chatbot
- Construtor visual de fluxos
- Integração com IA (OpenAI/Google)
- Respostas automáticas
- Condições e ações avançadas

### 💳 Sistema de Faturamento
- Múltiplos planos e ciclos
- Integração com Stripe
- Gestão de assinaturas
- Comissões para agências

### 🔗 Integrações Externas
- Webhooks customizáveis
- Google Sheets, CRMs, Zapier
- APIs de terceiros
- Sistema de retry inteligente

## Autenticação

Todas as rotas protegidas requerem um token JWT no header:

\`\`\`
Authorization: Bearer <seu-token-jwt>
\`\`\`

## Rate Limiting

A API possui rate limiting configurado:
- 100 requisições por minuto para usuários autenticados
- 20 requisições por minuto para rotas públicas

## Códigos de Status

- \`200\` - Sucesso
- \`201\` - Criado com sucesso
- \`400\` - Erro de validação
- \`401\` - Não autorizado
- \`403\` - Acesso negado
- \`404\` - Não encontrado
- \`409\` - Conflito
- \`422\` - Entidade não processável
- \`429\` - Muitas requisições
- \`500\` - Erro interno do servidor

## Paginação

Rotas que retornam listas suportam paginação:

\`\`\`json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 10,
  "totalPages": 10
}
\`\`\`

## Filtros e Busca

Muitas rotas suportam filtros via query parameters:
- \`search\` - Busca textual
- \`dateFrom\` / \`dateTo\` - Filtro por data
- \`status\` - Filtro por status
- \`sortBy\` / \`sortOrder\` - Ordenação

## Webhooks

A plataforma pode enviar webhooks para eventos importantes:
- Mensagem recebida/enviada
- Contato criado/atualizado
- Automação disparada
- Mudança de status da conexão

Configure webhooks em \`/integrations\`.

## SDKs e Bibliotecas

### JavaScript/TypeScript
\`\`\`bash
npm install whatsapp-platform-sdk
\`\`\`

### Python
\`\`\`bash
pip install whatsapp-platform-sdk
\`\`\`

### PHP
\`\`\`bash
composer require whatsapp-platform/sdk
\`\`\`

## Suporte

- 📧 Email: <EMAIL>
- 📚 Documentação: https://docs.whatsappplatform.com
- 💬 Chat: https://whatsappplatform.com/chat
- 🐛 Issues: https://github.com/whatsapp-platform/api/issues

## Changelog

### v1.0.0 (2024-01-01)
- Lançamento inicial
- Todos os módulos implementados
- Cobertura de testes > 80%
- Documentação completa
    `)
        .setVersion('1.0.0')
        .setContact('WhatsApp Platform Support', 'https://whatsappplatform.com', '<EMAIL>')
        .setLicense('MIT License', 'https://opensource.org/licenses/MIT')
        .addServer('http://localhost:3000', 'Desenvolvimento')
        .addServer('https://api-staging.whatsappplatform.com', 'Staging')
        .addServer('https://api.whatsappplatform.com', 'Produção')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Token JWT para autenticação',
        in: 'header',
    }, 'JWT-auth')
        .addApiKey({
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'Chave da API para integrações',
    }, 'API-Key')
        .addTag('Autenticação', 'Endpoints para login, registro e gestão de sessão')
        .addTag('Usuários', 'Gestão de usuários e perfis')
        .addTag('Empresas', 'Gestão de empresas e configurações')
        .addTag('Conexões WhatsApp', 'Gestão de conexões com WhatsApp')
        .addTag('Mensagens', 'Envio e recebimento de mensagens')
        .addTag('Contatos', 'Gestão de contatos e leads')
        .addTag('Analytics', 'Relatórios e métricas')
        .addTag('Automação', 'Fluxos de automação e chatbot')
        .addTag('Faturamento', 'Planos, assinaturas e pagamentos')
        .addTag('Integrações', 'Webhooks e integrações externas')
        .addTag('Sistema', 'Endpoints de sistema e monitoramento')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config, {
        operationIdFactory: (controllerKey, methodKey) => methodKey,
        deepScanRoutes: true,
    });
    document.components = {
        ...document.components,
        examples: {
            UserExample: {
                summary: 'Exemplo de usuário',
                value: {
                    id: 'user-123',
                    name: 'João Silva',
                    email: '<EMAIL>',
                    role: 'ADMIN',
                    companyId: 'company-123',
                    isActive: true,
                    createdAt: '2024-01-01T00:00:00.000Z',
                    updatedAt: '2024-01-01T00:00:00.000Z',
                },
            },
            MessageExample: {
                summary: 'Exemplo de mensagem',
                value: {
                    id: 'msg-123',
                    messageId: 'whatsapp-msg-123',
                    contactId: 'contact-123',
                    content: 'Olá! Como posso ajudar?',
                    type: 'TEXT',
                    direction: 'OUTBOUND',
                    status: 'DELIVERED',
                    timestamp: '2024-01-01T12:00:00.000Z',
                },
            },
            ContactExample: {
                summary: 'Exemplo de contato',
                value: {
                    id: 'contact-123',
                    phoneNumber: '+5511999999999',
                    name: 'Maria Santos',
                    status: 'ACTIVE',
                    source: 'WHATSAPP',
                    tags: ['cliente', 'vip'],
                    customFields: {
                        empresa: 'Empresa XYZ',
                        cargo: 'Gerente',
                    },
                },
            },
        },
        schemas: {
            Error: {
                type: 'object',
                properties: {
                    statusCode: {
                        type: 'number',
                        example: 400,
                    },
                    message: {
                        type: 'string',
                        example: 'Erro de validação',
                    },
                    error: {
                        type: 'string',
                        example: 'Bad Request',
                    },
                    timestamp: {
                        type: 'string',
                        format: 'date-time',
                        example: '2024-01-01T12:00:00.000Z',
                    },
                    path: {
                        type: 'string',
                        example: '/api/v1/users',
                    },
                },
            },
            PaginatedResponse: {
                type: 'object',
                properties: {
                    data: {
                        type: 'array',
                        items: {},
                    },
                    total: {
                        type: 'number',
                        example: 100,
                    },
                    page: {
                        type: 'number',
                        example: 1,
                    },
                    limit: {
                        type: 'number',
                        example: 10,
                    },
                    totalPages: {
                        type: 'number',
                        example: 10,
                    },
                },
            },
        },
    };
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        customSiteTitle: 'WhatsApp Platform API Documentation',
        customfavIcon: '/favicon.ico',
        customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #25D366 }
      .swagger-ui .scheme-container { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    `,
        swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
            docExpansion: 'none',
            filter: true,
            showExtensions: true,
            showCommonExtensions: true,
            tryItOutEnabled: true,
        },
    });
    swagger_1.SwaggerModule.setup('api/docs-json', app, document);
}
//# sourceMappingURL=swagger.config.js.map