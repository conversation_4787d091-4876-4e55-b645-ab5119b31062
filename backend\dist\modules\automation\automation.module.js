"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const automation_schema_1 = require("../../database/entities/automation.schema");
const automation_execution_schema_1 = require("../../database/entities/automation-execution.schema");
const automation_service_1 = require("./services/automation.service");
const automation_executor_service_1 = require("./services/automation-executor.service");
const automation_controller_1 = require("./automation.controller");
const analytics_module_1 = require("../analytics/analytics.module");
const whatsapp_module_1 = require("../whatsapp/whatsapp.module");
const messages_module_1 = require("../messages/messages.module");
let AutomationModule = class AutomationModule {
};
exports.AutomationModule = AutomationModule;
exports.AutomationModule = AutomationModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: automation_schema_1.Automation.name, schema: automation_schema_1.AutomationSchema },
                { name: automation_execution_schema_1.AutomationExecution.name, schema: automation_execution_schema_1.AutomationExecutionSchema },
            ]),
            analytics_module_1.AnalyticsModule,
            whatsapp_module_1.WhatsAppModule,
            messages_module_1.MessagesModule,
        ],
        controllers: [automation_controller_1.AutomationController],
        providers: [automation_service_1.AutomationService, automation_executor_service_1.AutomationExecutorService],
        exports: [automation_service_1.AutomationService, automation_executor_service_1.AutomationExecutorService],
    })
], AutomationModule);
//# sourceMappingURL=automation.module.js.map