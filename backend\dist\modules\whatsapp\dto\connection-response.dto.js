"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const entities_1 = require("../../../database/entities");
class ConnectionResponseDto {
    id;
    name;
    phoneNumber;
    formattedPhoneNumber;
    type;
    status;
    isConnected;
    qrCode;
    instanceId;
    lastConnectedAt;
    lastDisconnectedAt;
    errorMessage;
    isActive;
    companyId;
    assignedUserId;
    settings;
    createdAt;
    updatedAt;
}
exports.ConnectionResponseDto = ConnectionResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da conexão',
        example: 'uuid-da-conexao',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da conexão',
        example: 'WhatsApp Vendas',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de telefone',
        example: '+5511999999999',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número formatado',
        example: '+55 (11) 99999-9999',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.formattedPhoneNumber),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "formattedPhoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo de conexão',
        enum: entities_1.ConnectionType,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da conexão',
        enum: entities_1.ConnectionStatus,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se está conectado',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.isConnected),
    __metadata("design:type", Boolean)
], ConnectionResponseDto.prototype, "isConnected", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'QR Code para conexão',
        example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "qrCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da instância',
        example: 'instance-123',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "instanceId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Último login',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ConnectionResponseDto.prototype, "lastConnectedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Última desconexão',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ConnectionResponseDto.prototype, "lastDisconnectedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mensagem de erro',
        example: 'Falha na autenticação',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Se a conexão está ativa',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Boolean)
], ConnectionResponseDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 'uuid-da-empresa',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do usuário responsável',
        example: 'uuid-do-usuario',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], ConnectionResponseDto.prototype, "assignedUserId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Configurações da conexão',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], ConnectionResponseDto.prototype, "settings", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ConnectionResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de atualização',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], ConnectionResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=connection-response.dto.js.map