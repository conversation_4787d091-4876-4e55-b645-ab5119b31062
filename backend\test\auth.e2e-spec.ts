import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { TypeOrmEntities } from '../src/database/entities';
import { ValidationPipe } from '@nestjs/common';

describe('AuthController (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AppModule,
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: TypeOrmEntities,
          synchronize: true,
          logging: false,
        }),
        MongooseModule.forRoot('mongodb://localhost:27017/test-e2e'),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));
    
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/auth/register (POST)', () => {
    it('should register a new user successfully', () => {
      const registerDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123!',
        companyName: 'Test Company',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511999999999',
        companyDocument: '12345678901',
      };

      return request(app.getHttpServer())
        .post('/auth/register')
        .send(registerDto)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('access_token');
          expect(res.body).toHaveProperty('user');
          expect(res.body.user.email).toBe(registerDto.email);
          expect(res.body.user.name).toBe(registerDto.name);
          authToken = res.body.access_token;
        });
    });

    it('should fail with invalid email format', () => {
      const registerDto = {
        name: 'Test User',
        email: 'invalid-email',
        password: 'Password123!',
        companyName: 'Test Company',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511999999999',
        companyDocument: '12345678901',
      };

      return request(app.getHttpServer())
        .post('/auth/register')
        .send(registerDto)
        .expect(400);
    });

    it('should fail with weak password', () => {
      const registerDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: '123',
        companyName: 'Test Company',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511999999999',
        companyDocument: '12345678901',
      };

      return request(app.getHttpServer())
        .post('/auth/register')
        .send(registerDto)
        .expect(400);
    });

    it('should fail when email already exists', () => {
      const registerDto = {
        name: 'Test User 2',
        email: '<EMAIL>', // Same email as first test
        password: 'Password123!',
        companyName: 'Test Company 2',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511888888888',
        companyDocument: '98765432109',
      };

      return request(app.getHttpServer())
        .post('/auth/register')
        .send(registerDto)
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('já está em uso');
        });
    });
  });

  describe('/auth/login (POST)', () => {
    it('should login successfully with valid credentials', () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      return request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('access_token');
          expect(res.body).toHaveProperty('user');
          expect(res.body.user.email).toBe(loginDto.email);
        });
    });

    it('should fail with invalid credentials', () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      return request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(401);
    });

    it('should fail with nonexistent email', () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'Password123!',
      };

      return request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(401);
    });

    it('should fail with missing fields', () => {
      return request(app.getHttpServer())
        .post('/auth/login')
        .send({ email: '<EMAIL>' })
        .expect(400);
    });
  });

  describe('/auth/profile (GET)', () => {
    it('should return user profile when authenticated', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('id');
          expect(res.body).toHaveProperty('email');
          expect(res.body).toHaveProperty('name');
          expect(res.body).toHaveProperty('role');
          expect(res.body).toHaveProperty('companyId');
        });
    });

    it('should fail without authentication token', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .expect(401);
    });

    it('should fail with invalid token', () => {
      return request(app.getHttpServer())
        .get('/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('/auth/forgot-password (POST)', () => {
    it('should send password reset email for existing user', () => {
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send(forgotPasswordDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toContain('enviado com sucesso');
        });
    });

    it('should return success even for nonexistent email', () => {
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send(forgotPasswordDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toContain('enviado com sucesso');
        });
    });

    it('should fail with invalid email format', () => {
      const forgotPasswordDto = {
        email: 'invalid-email',
      };

      return request(app.getHttpServer())
        .post('/auth/forgot-password')
        .send(forgotPasswordDto)
        .expect(400);
    });
  });

  describe('/auth/change-password (POST)', () => {
    it('should change password successfully when authenticated', () => {
      const changePasswordDto = {
        currentPassword: 'Password123!',
        newPassword: 'NewPassword123!',
      };

      return request(app.getHttpServer())
        .post('/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(changePasswordDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.message).toContain('alterada com sucesso');
        });
    });

    it('should fail with wrong current password', () => {
      const changePasswordDto = {
        currentPassword: 'wrongpassword',
        newPassword: 'NewPassword123!',
      };

      return request(app.getHttpServer())
        .post('/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(changePasswordDto)
        .expect(400);
    });

    it('should fail without authentication', () => {
      const changePasswordDto = {
        currentPassword: 'NewPassword123!',
        newPassword: 'AnotherPassword123!',
      };

      return request(app.getHttpServer())
        .post('/auth/change-password')
        .send(changePasswordDto)
        .expect(401);
    });

    it('should fail with weak new password', () => {
      const changePasswordDto = {
        currentPassword: 'NewPassword123!',
        newPassword: '123',
      };

      return request(app.getHttpServer())
        .post('/auth/change-password')
        .set('Authorization', `Bearer ${authToken}`)
        .send(changePasswordDto)
        .expect(400);
    });
  });
});
