{"version": 3, "file": "MongoInstance.d.ts", "sourceRoot": "", "sources": ["../../src/util/MongoInstance.ts"], "names": [], "mappings": ";;;AAAA,OAAO,EAAE,YAAY,EAAe,YAAY,EAAE,MAAM,eAAe,CAAC;AAExE,OAAO,EAAe,eAAe,EAAE,MAAM,eAAe,CAAC;AAE7D,OAAO,EAKL,WAAW,EAGZ,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EAAe,kBAAkB,EAAqB,MAAM,SAAS,CAAC;AAiB7E,MAAM,MAAM,aAAa,GAAG,kBAAkB,GAAG,YAAY,CAAC;AAE9D;;;;;;;;;;;;;GAaG;AACH,MAAM,WAAW,mBAAmB;IAClC;;;OAGG;IACH,WAAW,CAAC,EAAE,OAAO,CAAC;IAEtB;;;;OAIG;IACH,YAAY,CAAC,EAAE,OAAO,CAAC;IAEvB;;;OAGG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;;;;;;OAOG;IACH,IAAI,CAAC,EAAE,GAAG,CAAC;IAEX;;;;;OAKG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,2BAA2B;IAC1C;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB;;;;OAIG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;;OAIG;IACH,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B;;;OAGG;IACH,mBAAmB,CAAC,EAAE,mBAAmB,CAAC;IAC1C;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,uBAAwB,SAAQ,2BAA2B;IAC1E;;;;;OAKG;IACH,IAAI,CAAC,EAAE,OAAO,CAAC;IACf;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;;OAIG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;;OAKG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B;AAED,oBAAY,mBAAmB;IAC7B,iBAAiB,sBAAsB;IACvC,eAAe,oBAAoB;IACnC,aAAa,kBAAkB;IAC/B,cAAc,mBAAmB;IACjC,cAAc,mBAAmB;IACjC,cAAc,mBAAmB;IACjC,gDAAgD;IAChD,gBAAgB,qBAAqB;IACrC,mCAAmC;IACnC,aAAa,kBAAkB;IAC/B,cAAc,mBAAmB;IACjC,gBAAgB,qBAAqB;IACrC,eAAe,oBAAoB;CACpC;AAED,MAAM,WAAW,UAAU;IACzB,uBAAuB;IACvB,QAAQ,EAAE,uBAAuB,CAAC;IAClC,2BAA2B;IAC3B,MAAM,EAAE,eAAe,CAAC;IACxB,kCAAkC;IAClC,KAAK,EAAE,YAAY,CAAC;CACrB;AAGD,MAAM,WAAW,aAAc,SAAQ,YAAY;IAEjD,IAAI,CAAC,KAAK,EAAE,mBAAmB,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC;IAC1D,EAAE,CAAC,KAAK,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IACzE,IAAI,CAAC,KAAK,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CAC5E;AAED;;;GAGG;AAEH,qBAAa,aAAc,SAAQ,YAAa,YAAW,WAAW;IAGpE,YAAY,EAAE,uBAAuB,CAAC;IACtC,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,eAAe,CAAC,CAAC;IAC/C,QAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;IAE3C;;;OAGG;IACH,sBAAsB,CAAC,EAAE,kBAAkB,CAAC;IAE5C;;OAEG;IACH,aAAa,CAAC,EAAE,YAAY,CAAC;IAC7B;;OAEG;IACH,aAAa,CAAC,EAAE,YAAY,CAAC;IAC7B;;OAEG;IACH,iBAAiB,EAAE,OAAO,CAAS;IACnC;;OAEG;IACH,eAAe,EAAE,OAAO,CAAS;IACjC;;OAEG;IACH,SAAS,EAAE,OAAO,CAAS;IAE3B;;;;OAIG;IAEH,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBAEnB,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;IAoBrC;;;OAGG;IACH,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI;IAKvD;;;OAGG;WACU,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC;IAQtE;;OAEG;IACH,kBAAkB,IAAI,MAAM,EAAE;IA6C9B;;;OAGG;IACG,KAAK,IAAI,OAAO,CAAC,IAAI,CAAC;IAyD5B;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC;IAwF9B;;;;OAIG;IACH,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAG,YAAY;IAsB7C;;;;;OAKG;IACH,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,YAAY;IAsBhE;;;;;OAKG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAK/B;;;;;OAKG;IACH,YAAY,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;IAiB9D;;;;OAIG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAQ7C;;;;;;;;OAQG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IA4B7C;;;OAGG;IACH,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM;IAyFjC,CAAC,MAAM,CAAC,YAAY,CAAC;CAG5B;AAED,eAAe,aAAa,CAAC"}