Plataforma de Gestão e Automação de WhatsApp

1. Visão Geral e Objetivo do Produto

O objetivo deste projeto é desenvolver uma plataforma SaaS (Software as a Service) robusta, escalável e segura para a gestão centralizada e automação de contas de WhatsApp. A aplicação atenderá empresas de diversos segmentos, com um foco especial em otimizar o trabalho de equipes de vendas e permitir que agências de tráfego pago mensurem com precisão o retorno sobre o investimento (ROI) de suas campanhas.

A plataforma se diferenciará por sua arquitetura multi-tenant, múltiplos níveis de acesso hierárquico, integrações flexíveis com APIs do WhatsApp (Oficial e não oficial), e um poderoso conjunto de ferramentas de automação, rastreamento de leads e relatórios.

Não quero trabalhar com rock. Já vamos trabalhar com ela em produção e com o código completo, funcional, robusto e seguro, dividido em back end e front end; E iniciando o desenvolvimento pelo Back end, para só depois que ele estiver 100% completo e funcionando, possamos fazer o front end

2. Arquitetura e Níveis de Acesso

A aplicação será construída sobre uma arquitetura multi-tenant, onde cada "Tenant" é uma Conta de Empresa. A estrutura hierárquica de acesso é fundamental para o modelo de negócio e será dividida em quatro níveis:

Nível 1: Administrador Principal (Desenvolvedor)

Visão: Controle total sobre a plataforma. Gerencia a infraestrutura, as contas globais, as configurações mestras e o faturamento geral.

Acessos: Possui todas as funcionalidades disponíveis para os demais níveis, além de painéis exclusivos de administração.

Funções Principais:
Dashboard de Super Admin com métricas globais da plataforma (total de contas, usuários, MRR, etc.).
Gestão de todas as contas de Agências, Empresas e Vendedores.
Configurações globais de integrações (chaves de API para OpenAI, Google AI, CRMs, etc.).
Painel de faturamento completo com relatórios financeiros.
Logs de sistema e monitoramento de performance.
Gerenciamento do gateway de pagamento.

Nível 2: Agência de Tráfego (Revendedor)
Visão: Gerenciar as contas de seus clientes (Empresas), acompanhar o desempenho dos leads gerados e receber comissionamento recorrente.
Acessos: Possui todas as funções de uma Conta Empresa, com adição de funcionalidades de revenda.
Funções Principais:
Dashboard de Revendedor: visualização do faturamento recorrente (comissão de 25%), número de clientes ativos, total de usuários dos clientes.
Área de Gestão de Clientes: listar, visualizar e acessar (com permissão) o painel de seus clientes indicados.
Criação de Contas para Clientes: pode criar uma conta diretamente e enviar um link de convite para o cliente finalizar o cadastro e o pagamento.
Geração de Links de Revenda: criação de links de indicação únicos que atribuem o comissionamento automaticamente.
Acesso consolidado aos dashboards de rastreamento de leads de todos os seus clientes.

Nível 3: Cliente Empresa (Administrador)
Visão: Gerenciar a operação de WhatsApp de sua empresa, incluindo vendedores, números e pagamentos.
Acessos: Gerencia seus próprios usuários e configurações, sem visibilidade sobre outras empresas.
Funções Principais:
Gestão de Pagamentos: Área para gerenciar a assinatura da plataforma, visualizar faturas e realizar pagamentos. O custo será de R$ 150/mês (base) + R$ 100/mês por Vendedor ativo.
Gestão de Usuários (Vendedores): Criar, editar e desativar contas para seus vendedores.
Gestão de Conexões WhatsApp:
Conectar múltiplos números de WhatsApp (via QR Code ou API Oficial).
Atribuir um ou mais números a cada vendedor.
Painel de status das conexões.
Configuração de Rastreamento de Leads: Definir e configurar as fontes de tráfego (Google, Meta, Site, etc.) para rastreamento.
Dashboard de Administrador: Visão geral das conversas, desempenho dos vendedores, volume de leads por fonte e por etiqueta.
Relatórios: Gerar, exportar e agendar relatórios consolidados da equipe.
Geração de Link Público para Dashboard: Criar um link compartilhável (sem necessidade de login) para visualização do dashboard de leads, ideal para compartilhar com a agência.
Todas as funcionalidades do nível Vendedor.

Nível 4: Vendedor (Usuário)
Visão: O usuário final que interage com os leads e clientes no dia a dia.
Acessos: Limitado às suas próprias conversas e aos números de WhatsApp que lhe foram atribuídos.
Funções Principais:
Interface de Chat Unificada: Semelhante ao WhatsApp Web, permite visualizar e responder conversas de múltiplos números em uma única tela.
Etiquetas (Tags) de Funil:
Criar, editar e atribuir etiquetas personalizáveis aos contatos (ex: "Novo Lead", "Negociando", "Venda Ganhada").
As etiquetas devem ser coloridas e funcionar como um funil visual para organização.
Disparo de Mensagens em Massa:
Importar listas de contatos (CSV, Planilhas Google).
Configurar múltiplas variações de mensagens (sintaxe de spin) para evitar bloqueios.
Definir intervalos de tempo randômicos e customizáveis entre os envios.
Criar sequências de mensagens (cadências).
Agendamento de Mensagens: Programar o envio de mensagens individuais ou em massa para datas e horários futuros.
Respostas Rápidas: Cadastrar templates de mensagens para agilizar o atendimento.

3. Módulos e Funcionalidades Detalhadas
3.1. Engine de Conexão WhatsApp
Integração Dual: A plataforma deve suportar de forma nativa e estável duas formas de conexão:
API Oficial da Meta (WABA): Para empresas que necessitam de escalabilidade e conformidade. O processo de onboarding deve ser guiado dentro da plataforma.
API Não Oficial (Evolution API V2): Conexão simplificada via leitura de QR Code. A plataforma deve gerenciar a instância do Evolution API para cada conexão, garantindo estabilidade. A comunicação com a API deve seguir a documentação oficial (https://doc.evolution-api.com/v2/).

Gerenciador de Status: Um painel deve exibir em tempo real o status de cada número conectado (Conectado, Desconectado, Requer Atenção).

3.2. Módulo de Rastreamento e Analytics de Leads
Captura de Fonte:
O sistema deve ser capaz de identificar a origem do lead através de parâmetros em URLs (UTMs) ou via integração com formulários de site.
Fontes configuráveis: Meta Ads, Google Ads, Site Orgânico, TikTok Ads, Link da Bio, Direto, etc.
Dashboard Interativo:
Visualização de dados com filtros por período, fonte de tráfego, cliente (para agências), número de WhatsApp e etiqueta.
Gráficos para ilustrar o volume de leads, taxa de conversão (baseada em etiquetas como "Venda Ganhada"), e tempo médio de resposta.
Relatórios Automatizados:
O administrador (Empresa ou Agência) pode configurar a geração de relatórios (PDF, CSV).
Agendamento de envio por e-mail (diário, semanal, mensal).
Seleção de métricas a serem incluídas no relatório.

3.3. Módulo de Automação
Chatbot Simples:
Construtor visual de fluxos (if/else) baseado em palavras-chave.
Menu de opções (ex: "Digite 1 para Financeiro, 2 para Suporte").
Bot com Inteligência Artificial:
Integração com APIs do Google (Gemini) e OpenAI (GPT).
Treinamento: Possibilidade de treinar o bot com informações da empresa (documentos, URLs de site, FAQs) para que ele responda perguntas de forma contextualizada.

Transbordo Humano: Regras configuráveis para transferir a conversa para um vendedor humano.

Integrações Externas:

Planilhas Google: Enviar dados de novos leads (nome, telefone, etiqueta, fonte) para uma planilha específica em tempo real.

CRMs (via Webhooks): Configurar webhooks para enviar dados de leads para qualquer CRM que aceite esse tipo de integração (ex: RD Station, PipeDrive, Hubspot).

3.4. Sistema de Faturamento e Assinaturas

Gateway de Pagamento: Integrar com um gateway de pagamento robusto (ex: Stripe, Pagar.me).

Lógica de Cobrança:

A cobrança é sempre feita no nível da Conta Empresa.

O sistema calcula automaticamente o valor mensal: R$ 150 + (Nº de Vendedores Ativos * R$ 100).

A fatura é recorrente e gerada automaticamente.

Gestão de Comissões (Agências):

O sistema deve rastrear todas as assinaturas vinculadas a uma agência.

Calcular 25% de comissão sobre o valor total pago pelo cliente.

Disponibilizar um painel para a agência visualizar o saldo a receber e solicitar o saque (ou receber de forma automática, a definir).

4. Requisitos Técnicos e de Segurança

Backend: Node.js com TypeScript, utilizando um framework como NestJS para organização e escalabilidade, ou Go pela sua performance em aplicações concorrentes. A arquitetura de microsserviços pode ser considerada para isolar funcionalidades críticas (ex: serviço de conexão, serviço de mensagens, serviço de billing).

Frontend: React (com Next.js para SSR e otimizações) ou Vue.js. Utilizar uma biblioteca de componentes como Material-UI ou Ant Design e customizá-la para criar um design clean, moderno e atrativo. Tailwind CSS para agilidade na estilização.

Banco de Dados:
Relacional (PostgreSQL): Para dados estruturados como usuários, empresas, assinaturas, permissões.
NoSQL (MongoDB ou DynamoDB): Para armazenar dados de alta volumetria e flexíveis, como logs de mensagens e contatos.
Comunicação em Tempo Real: WebSockets (Socket.IO) para sincronizar a interface de chat instantaneamente.

Infraestrutura/DevOps:
Containerização com Docker.
Orquestração com Kubernetes.
Cloud Provider: AWS, Google Cloud ou Azure.
CI/CD para automação de deploys.

Segurança (Fundamental):
Autenticação: JWT (JSON Web Tokens) com refresh tokens.

Autorização: Implementação rigorosa de RBAC (Role-Based Access Control) em todas as rotas da API.

Criptografia: HTTPS/TLS para todos os dados em trânsito. Criptografia de dados sensíveis no banco de dados (chaves de API, tokens).

LGPD: A aplicação deve ser desenvolvida em conformidade com a Lei Geral de Proteção de Dados.

Proteção contra Ataques: Prevenção contra XSS, CSRF, SQL Injection. Limitação de taxa (Rate Limiting) em APIs críticas.

5. Sugestões Adicionais (Valor Agregado)

Funil de Vendas Kanban: Além das etiquetas, criar uma visualização em colunas (estilo Trello/Kanban) onde os vendedores podem arrastar e soltar os leads entre as etapas do funil.

Análise de Sentimento por IA: Utilizar IA para analisar as mensagens dos clientes e classificar o "humor" da conversa (Positivo, Neutro, Negativo), fornecendo insights para os gestores.

Gamificação para Vendedores: Criar um ranking de vendedores baseado em métricas de performance (nº de vendas, tempo de resposta), com medalhas e pontuações para incentivar a equipe.

App Mobile (Roadmap futuro): Planejar o desenvolvimento de um aplicativo mobile (React Native) para que os vendedores possam gerenciar suas conversas em qualquer lugar.