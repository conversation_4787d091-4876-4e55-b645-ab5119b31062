import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>, Controller, Get } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

// Entidades básicas apenas
import { User } from './database/entities/user.entity';
import { Organization } from './database/entities/organization.entity';
import { Subscription } from './database/entities/subscription.entity';
import { Payment } from './database/entities/payment.entity';
import { Integration } from './database/entities/integration.entity';

@Controller()
class HealthController {
  @Get()
  getHealth() {
    return {
      status: 'ok',
      message: 'WhatsApp Platform API está funcionando!',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  }

  @Get('health')
  getHealthCheck() {
    return {
      status: 'healthy',
      database: 'connected',
      services: {
        auth: 'active',
        whatsapp: 'ready',
        billing: 'active'
      }
    };
  }
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    TypeOrmModule.forRoot({
      type: 'sqlite',
      database: ':memory:',
      entities: [User, Organization, Subscription, Payment, Integration],
      synchronize: true,
      logging: true,
    }),
  ],
  controllers: [HealthController],
})
class TestAppModule {}

async function bootstrap() {
  const app = await NestFactory.create(TestAppModule);

  // Configurar CORS
  app.enableCors({
    origin: true,
    credentials: true,
  });

  // Configurar validação global
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Configurar prefixo da API
  app.setGlobalPrefix('api');

  // Configurar Swagger
  const config = new DocumentBuilder()
    .setTitle('WhatsApp Platform API - Test')
    .setDescription('API de teste para plataforma de WhatsApp Business')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = 3000;
  await app.listen(port);
  
  console.log(`🚀 Servidor de teste rodando em: http://localhost:${port}`);
  console.log(`📚 Documentação da API: http://localhost:${port}/api/docs`);
  console.log(`💚 Health check: http://localhost:${port}/api/health`);
}

bootstrap().catch(console.error);
