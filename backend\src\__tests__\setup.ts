// Setup para testes
import { config } from 'dotenv'

// Carregar variáveis de ambiente de teste
config({ path: '.env.test' })

// Mock do Prisma para testes
jest.mock('../lib/prisma', () => ({
  __esModule: true,
  default: {
    user: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    company: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    whatsAppConnection: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    message: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    contact: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    $queryRaw: jest.fn(),
  },
  connectDatabase: jest.fn(),
  disconnectDatabase: jest.fn(),
  checkDatabaseHealth: jest.fn(),
}))

// Mock do WebSocket para testes
jest.mock('../services/websocket.service', () => ({
  initializeWebSocket: jest.fn(),
  getWebSocketService: jest.fn(() => ({
    emitNewMessage: jest.fn(),
    emitMessageUpdate: jest.fn(),
    emitConnectionUpdate: jest.fn(),
    emitQrCodeUpdate: jest.fn(),
    emitNotification: jest.fn(),
  })),
}))

// Mock da Evolution API para testes
jest.mock('../services/evolution-api.service', () => ({
  EvolutionApiService: jest.fn().mockImplementation(() => ({
    createInstance: jest.fn(),
    getInstance: jest.fn(),
    deleteInstance: jest.fn(),
    getConnectionState: jest.fn(),
    getQrCode: jest.fn(),
    sendTextMessage: jest.fn(),
    sendMediaMessage: jest.fn(),
    formatPhoneNumber: jest.fn(),
    extractPhoneFromJid: jest.fn(),
    healthCheck: jest.fn(),
  })),
}))

// Configurações globais para testes
beforeAll(async () => {
  // Setup global antes de todos os testes
})

afterAll(async () => {
  // Cleanup global após todos os testes
})

beforeEach(() => {
  // Reset de mocks antes de cada teste
  jest.clearAllMocks()
})

afterEach(() => {
  // Cleanup após cada teste
})
