import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { 
  Automation, 
  AutomationDocument, 
  AutomationType, 
  AutomationStatus,
  TriggerType 
} from '../../../database/entities/automation.schema';
import { 
  AutomationExecution, 
  AutomationExecutionDocument, 
  ExecutionStatus 
} from '../../../database/entities/automation-execution.schema';
import { CreateAutomationDto } from '../dto/create-automation.dto';
import { UpdateAutomationDto } from '../dto/update-automation.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { UserRole } from '../../../database/entities';

export interface FindAutomationsOptions {
  companyId?: string;
  type?: AutomationType;
  status?: AutomationStatus;
  triggerType?: TriggerType;
  isTemplate?: boolean;
  templateCategory?: string;
  createdBy?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FindExecutionsOptions {
  automationId?: string;
  companyId?: string;
  status?: ExecutionStatus;
  contactPhone?: string;
  connectionId?: string;
  triggeredBy?: string;
  dateFrom?: Date;
  dateTo?: Date;
  page?: number;
  limit?: number;
}

@Injectable()
export class AutomationService {
  private readonly logger = new Logger(AutomationService.name);

  constructor(
    @InjectModel(Automation.name)
    private automationModel: Model<AutomationDocument>,
    @InjectModel(AutomationExecution.name)
    private executionModel: Model<AutomationExecutionDocument>,
  ) {}

  async create(
    createAutomationDto: CreateAutomationDto,
    currentUser: AuthenticatedUser,
  ): Promise<AutomationDocument> {
    // Verificar se já existe automação com o mesmo nome
    const existingAutomation = await this.automationModel.findOne({
      name: createAutomationDto.name,
      companyId: new Types.ObjectId(currentUser.companyId),
    });

    if (existingAutomation) {
      throw new ConflictException('Já existe uma automação com este nome');
    }

    // Validar configuração do gatilho
    this.validateTriggerConfiguration(createAutomationDto.trigger);

    // Validar passos da automação
    if (createAutomationDto.actions) {
      this.validateActionSteps(createAutomationDto.actions);
    }

    if (createAutomationDto.flowSteps) {
      this.validateFlowSteps(createAutomationDto.flowSteps);
    }

    const automation = new this.automationModel({
      ...createAutomationDto,
      companyId: new Types.ObjectId(currentUser.companyId),
      createdBy: currentUser.id,
      status: AutomationStatus.DRAFT,
      executionCount: 0,
      successCount: 0,
      errorCount: 0,
      version: 1,
      lastModifiedAt: new Date(),
    });

    const savedAutomation = await automation.save();
    this.logger.log(`Automation created: ${savedAutomation._id} by ${currentUser.email}`);

    return savedAutomation;
  }

  async findAll(
    options: FindAutomationsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    automations: AutomationDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      companyId,
      type,
      status,
      triggerType,
      isTemplate,
      templateCategory,
      createdBy,
      search,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = options;

    // Filtro base por empresa
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
    };

    // Filtros adicionais
    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      filter.companyId = new Types.ObjectId(companyId);
    }

    if (type) {
      filter.type = type;
    }

    if (status) {
      filter.status = status;
    }

    if (triggerType) {
      filter['trigger.type'] = triggerType;
    }

    if (typeof isTemplate === 'boolean') {
      filter.isTemplate = isTemplate;
    }

    if (templateCategory) {
      filter.templateCategory = templateCategory;
    }

    if (createdBy) {
      filter.createdBy = createdBy;
    }

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ];
    }

    // Paginação
    const skip = (page - 1) * limit;

    // Ordenação
    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const [automations, total] = await Promise.all([
      this.automationModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.automationModel.countDocuments(filter),
    ]);

    return {
      automations,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument> {
    const automation = await this.automationModel.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(currentUser.companyId),
    });

    if (!automation) {
      throw new NotFoundException('Automação não encontrada');
    }

    return automation;
  }

  async update(
    id: string,
    updateAutomationDto: UpdateAutomationDto,
    currentUser: AuthenticatedUser,
  ): Promise<AutomationDocument> {
    const automation = await this.findOne(id, currentUser);

    // Verificar se pode editar
    if (automation.createdBy !== currentUser.id && currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Não é possível editar esta automação');
    }

    // Verificar se nome já existe (se estiver sendo alterado)
    if (updateAutomationDto.name && updateAutomationDto.name !== automation.name) {
      const existingAutomation = await this.automationModel.findOne({
        name: updateAutomationDto.name,
        companyId: new Types.ObjectId(currentUser.companyId),
        _id: { $ne: automation._id },
      });

      if (existingAutomation) {
        throw new ConflictException('Já existe uma automação com este nome');
      }
    }

    // Validar configurações se fornecidas
    if (updateAutomationDto.trigger) {
      this.validateTriggerConfiguration(updateAutomationDto.trigger);
    }

    if (updateAutomationDto.actions) {
      this.validateActionSteps(updateAutomationDto.actions);
    }

    if (updateAutomationDto.flowSteps) {
      this.validateFlowSteps(updateAutomationDto.flowSteps);
    }

    // Incrementar versão se houver mudanças significativas
    if (updateAutomationDto.trigger || updateAutomationDto.actions || updateAutomationDto.flowSteps) {
      automation.version = automation.version + 1;
    }

    Object.assign(automation, updateAutomationDto);
    automation.updatedBy = currentUser.id;
    automation.lastModifiedAt = new Date();

    const updatedAutomation = await automation.save();
    this.logger.log(`Automation updated: ${id} by ${currentUser.email}`);

    return updatedAutomation;
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const automation = await this.findOne(id, currentUser);

    // Verificar se pode deletar
    if (automation.createdBy !== currentUser.id && currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('Não é possível deletar esta automação');
    }

    // Verificar se há execuções em andamento
    const runningExecutions = await this.executionModel.countDocuments({
      automationId: automation._id,
      status: { $in: [ExecutionStatus.PENDING, ExecutionStatus.RUNNING] },
    });

    if (runningExecutions > 0) {
      throw new ConflictException('Não é possível deletar automação com execuções em andamento');
    }

    await this.automationModel.findByIdAndDelete(automation._id);
    this.logger.log(`Automation deleted: ${id} by ${currentUser.email}`);
  }

  async activate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument> {
    const automation = await this.findOne(id, currentUser);

    if (automation.status === AutomationStatus.ACTIVE) {
      throw new ConflictException('Automação já está ativa');
    }

    // Validar se a automação está completa
    this.validateAutomationCompleteness(automation);

    automation.status = AutomationStatus.ACTIVE;
    automation.updatedBy = currentUser.id;
    automation.lastModifiedAt = new Date();

    const updatedAutomation = await automation.save();
    this.logger.log(`Automation activated: ${id} by ${currentUser.email}`);

    return updatedAutomation;
  }

  async deactivate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument> {
    const automation = await this.findOne(id, currentUser);

    if (automation.status !== AutomationStatus.ACTIVE) {
      throw new ConflictException('Automação não está ativa');
    }

    automation.status = AutomationStatus.INACTIVE;
    automation.updatedBy = currentUser.id;
    automation.lastModifiedAt = new Date();

    const updatedAutomation = await automation.save();
    this.logger.log(`Automation deactivated: ${id} by ${currentUser.email}`);

    return updatedAutomation;
  }

  async duplicate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument> {
    const originalAutomation = await this.findOne(id, currentUser);

    const duplicatedData = {
      name: `${originalAutomation.name} (Cópia)`,
      description: originalAutomation.description,
      type: originalAutomation.type,
      trigger: originalAutomation.trigger,
      actions: originalAutomation.actions,
      flowSteps: originalAutomation.flowSteps,
      startStepId: originalAutomation.startStepId,
      settings: originalAutomation.settings,
      metadata: originalAutomation.metadata,
    };

    return this.create(duplicatedData, currentUser);
  }

  async getExecutions(
    options: FindExecutionsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    executions: AutomationExecutionDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      automationId,
      companyId,
      status,
      contactPhone,
      connectionId,
      triggeredBy,
      dateFrom,
      dateTo,
      page = 1,
      limit = 10,
    } = options;

    // Filtro base por empresa
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
    };

    // Filtros adicionais
    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      filter.companyId = new Types.ObjectId(companyId);
    }

    if (automationId) {
      filter.automationId = new Types.ObjectId(automationId);
    }

    if (status) {
      filter.status = status;
    }

    if (contactPhone) {
      filter['context.contact.phoneNumber'] = contactPhone;
    }

    if (connectionId) {
      filter['context.connection.id'] = connectionId;
    }

    if (triggeredBy) {
      filter.triggeredBy = triggeredBy;
    }

    if (dateFrom || dateTo) {
      filter.startedAt = {};
      if (dateFrom) filter.startedAt.$gte = dateFrom;
      if (dateTo) filter.startedAt.$lte = dateTo;
    }

    // Paginação
    const skip = (page - 1) * limit;

    const [executions, total] = await Promise.all([
      this.executionModel
        .find(filter)
        .sort({ startedAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.executionModel.countDocuments(filter),
    ]);

    return {
      executions,
      total,
      page,
      limit,
    };
  }

  async getAutomationStats(currentUser: AuthenticatedUser): Promise<{
    total: number;
    active: number;
    inactive: number;
    draft: number;
    totalExecutions: number;
    successRate: number;
  }> {
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };

    const [total, active, inactive, draft, executionStats] = await Promise.all([
      this.automationModel.countDocuments(companyFilter),
      this.automationModel.countDocuments({ ...companyFilter, status: AutomationStatus.ACTIVE }),
      this.automationModel.countDocuments({ ...companyFilter, status: AutomationStatus.INACTIVE }),
      this.automationModel.countDocuments({ ...companyFilter, status: AutomationStatus.DRAFT }),
      this.automationModel.aggregate([
        { $match: companyFilter },
        {
          $group: {
            _id: null,
            totalExecutions: { $sum: '$executionCount' },
            totalSuccess: { $sum: '$successCount' },
          },
        },
      ]),
    ]);

    const stats = executionStats[0] || { totalExecutions: 0, totalSuccess: 0 };
    const successRate = stats.totalExecutions > 0 ? (stats.totalSuccess / stats.totalExecutions) * 100 : 0;

    return {
      total,
      active,
      inactive,
      draft,
      totalExecutions: stats.totalExecutions,
      successRate: Math.round(successRate * 100) / 100,
    };
  }

  // Métodos de validação privados

  private validateTriggerConfiguration(trigger: any): void {
    if (!trigger.type) {
      throw new ConflictException('Tipo de gatilho é obrigatório');
    }

    switch (trigger.type) {
      case TriggerType.KEYWORD_MATCH:
        if (!trigger.keywords || trigger.keywords.length === 0) {
          throw new ConflictException('Palavras-chave são obrigatórias para gatilho de palavra-chave');
        }
        break;
      case TriggerType.SCHEDULE:
        if (!trigger.schedule) {
          throw new ConflictException('Configuração de agendamento é obrigatória');
        }
        break;
      case TriggerType.INACTIVITY:
        if (!trigger.inactivityMinutes || trigger.inactivityMinutes < 1) {
          throw new ConflictException('Minutos de inatividade devem ser maior que 0');
        }
        break;
      case TriggerType.WEBHOOK:
        if (!trigger.webhookUrl) {
          throw new ConflictException('URL do webhook é obrigatória');
        }
        break;
    }
  }

  private validateActionSteps(actions: any[]): void {
    if (actions.length === 0) {
      throw new ConflictException('Pelo menos uma ação é obrigatória');
    }

    const stepIds = new Set();
    for (const action of actions) {
      if (!action.id) {
        throw new ConflictException('ID do passo é obrigatório');
      }
      if (stepIds.has(action.id)) {
        throw new ConflictException(`ID do passo duplicado: ${action.id}`);
      }
      stepIds.add(action.id);

      if (!action.type) {
        throw new ConflictException('Tipo da ação é obrigatório');
      }
      if (!action.config) {
        throw new ConflictException('Configuração da ação é obrigatória');
      }
    }
  }

  private validateFlowSteps(flowSteps: any[]): void {
    if (flowSteps.length === 0) {
      throw new ConflictException('Pelo menos um passo do fluxo é obrigatório');
    }

    const stepIds = new Set();
    for (const step of flowSteps) {
      if (!step.id) {
        throw new ConflictException('ID do passo é obrigatório');
      }
      if (stepIds.has(step.id)) {
        throw new ConflictException(`ID do passo duplicado: ${step.id}`);
      }
      stepIds.add(step.id);

      if (!step.type) {
        throw new ConflictException('Tipo do passo é obrigatório');
      }
      if (!step.config) {
        throw new ConflictException('Configuração do passo é obrigatória');
      }
    }
  }

  private validateAutomationCompleteness(automation: AutomationDocument): void {
    if (!automation.trigger) {
      throw new ConflictException('Gatilho não configurado');
    }

    const hasActions = automation.actions && automation.actions.length > 0;
    const hasFlowSteps = automation.flowSteps && automation.flowSteps.length > 0;

    if (!hasActions && !hasFlowSteps) {
      throw new ConflictException('Automação deve ter pelo menos uma ação ou passo de fluxo');
    }

    if (hasFlowSteps && !automation.startStepId) {
      throw new ConflictException('Passo inicial é obrigatório para fluxos');
    }
  }
}
