{"version": 3, "file": "DocNodeManager.js", "sourceRoot": "", "sources": ["../../src/configuration/DocNodeManager.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAG3D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAiBtD;;;;;;;GAOG;AACH;IAAA;QAMmB,8BAAyB,GAA8C,IAAI,GAAG,EAG5F,CAAC;QACa,qCAAgC,GAC/C,IAAI,GAAG,EAAoD,CAAC;IA8FhE,CAAC;IA5FC;;;OAGG;IACI,yCAAgB,GAAvB,UAAwB,WAAmB,EAAE,WAA8C;QACzF,IAAM,gBAAgB,GAAuB,YAAY,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;QACnG,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,gBAAgB,CAAC,CAAC;QACnE,CAAC;QAED,KAAyB,UAAW,EAAX,2BAAW,EAAX,yBAAW,EAAX,IAAW,EAAE,CAAC;YAAlC,IAAM,UAAU,oBAAA;YACnB,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CACb,2BAAoB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,gCAA6B;oBACrF,oGAAoG,CACvG,CAAC;YACJ,CAAC;YAED,IAAI,kBAAkB,GAA6C,IAAI,CAAC,yBAAyB,CAAC,GAAG,CACnG,UAAU,CAAC,WAAW,CACvB,CAAC;YAEF,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CACb,6BAAqB,UAAU,CAAC,WAAW,8BAA0B;oBACnE,cAAO,kBAAkB,CAAC,WAAW,CAAE,CAC1C,CAAC;YACJ,CAAC;YAED,kBAAkB,GAAG,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YACvF,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CACb,6DAAsD,kBAAkB,CAAC,WAAW,CAAE;oBACpF,cAAO,kBAAkB,CAAC,WAAW,CAAE,CAC1C,CAAC;YACJ,CAAC;YAED,IAAM,aAAa,GAAiC;gBAClD,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,WAAW,aAAA;gBACX,iBAAiB,EAAE,IAAI,GAAG,EAAU;aACrC,CAAC;YAEF,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YAC1E,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,UAAU,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAED;;OAEG;IACI,iDAAwB,GAA/B,UAAgC,WAAmB;QACjD,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,6BAAqB,WAAW,uDAAmD,CAAC,CAAC;QACvG,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACI,kDAAyB,GAAhC,UAAiC,UAAkB,EAAE,UAAiC;QACpF,IAAM,gBAAgB,GAAiC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAEvF,KAAwB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE,CAAC;YAAhC,IAAM,SAAS,mBAAA;YAClB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAC/B,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,uCAAc,GAArB,UAAsB,UAAkB,EAAE,SAAiB;QACzD,IAAM,gBAAgB,GAAiC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACvF,OAAO,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC3D,CAAC;IAEO,uCAAc,GAAtB,UAAuB,WAAmB;QACxC,IAAM,UAAU,GACd,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAClD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,6BAAqB,WAAW,uDAAmD,CAAC,CAAC;QACvG,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAvGD,gDAAgD;IAChD,EAAE;IACF,6BAA6B;IACL,8BAAe,GAAW,qBAAqB,AAAhC,CAAiC;IAqG1E,qBAAC;CAAA,AAzGD,IAyGC;SAzGY,cAAc", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\r\n\r\nimport type { DocNode } from '../nodes/DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport type DocNodeConstructor = new (...args: any[]) => DocNode;\r\n\r\nexport interface IDocNodeDefinition {\r\n  docNodeKind: string;\r\n  constructor: DocNodeConstructor;\r\n}\r\n\r\ninterface IRegisteredDocNodeDefinition {\r\n  docNodeKind: string;\r\n  constructor: DocNodeConstructor;\r\n  packageName: string;\r\n  allowedChildKinds: Set<string>;\r\n}\r\n\r\n/**\r\n * Part of the {@link TSDocConfiguration} object.\r\n *\r\n * @remarks\r\n * If you define your own custom subclasses of `DocNode`, they must be registered with the `DocNodeManager`.\r\n * Use {@link DocNodeManager.registerAllowableChildren} to specify which {@link DocNodeContainer} subclasses\r\n * are allowed to contain your nodes.\r\n */\r\nexport class DocNodeManager {\r\n  // Matches an ASCII TypeScript-style identifier.\r\n  //\r\n  // Example: \"_myIdentifier99\"\r\n  private static readonly _nodeKindRegExp: RegExp = /^[_a-z][_a-z0-9]*$/i;\r\n\r\n  private readonly _docNodeDefinitionsByKind: Map<string, IRegisteredDocNodeDefinition> = new Map<\r\n    string,\r\n    IRegisteredDocNodeDefinition\r\n  >();\r\n  private readonly _docNodeDefinitionsByConstructor: Map<DocNodeConstructor, IRegisteredDocNodeDefinition> =\r\n    new Map<DocNodeConstructor, IRegisteredDocNodeDefinition>();\r\n\r\n  /**\r\n   * Registers a list of {@link IDocNodeDefinition} objects to be used with the associated\r\n   * {@link TSDocConfiguration} object.\r\n   */\r\n  public registerDocNodes(packageName: string, definitions: ReadonlyArray<IDocNodeDefinition>): void {\r\n    const packageNameError: string | undefined = StringChecks.explainIfInvalidPackageName(packageName);\r\n    if (packageNameError) {\r\n      throw new Error('Invalid NPM package name: ' + packageNameError);\r\n    }\r\n\r\n    for (const definition of definitions) {\r\n      if (!DocNodeManager._nodeKindRegExp.test(definition.docNodeKind)) {\r\n        throw new Error(\r\n          `The DocNode kind ${JSON.stringify(definition.docNodeKind)} is not a valid identifier.` +\r\n            ` It must start with an underscore or letter, and be comprised of letters, numbers, and underscores`\r\n        );\r\n      }\r\n\r\n      let existingDefinition: IRegisteredDocNodeDefinition | undefined = this._docNodeDefinitionsByKind.get(\r\n        definition.docNodeKind\r\n      );\r\n\r\n      if (existingDefinition !== undefined) {\r\n        throw new Error(\r\n          `The DocNode kind \"${definition.docNodeKind}\" was already registered` +\r\n            ` by ${existingDefinition.packageName}`\r\n        );\r\n      }\r\n\r\n      existingDefinition = this._docNodeDefinitionsByConstructor.get(definition.constructor);\r\n      if (existingDefinition !== undefined) {\r\n        throw new Error(\r\n          `This DocNode constructor was already registered by ${existingDefinition.packageName}` +\r\n            ` as ${existingDefinition.docNodeKind}`\r\n        );\r\n      }\r\n\r\n      const newDefinition: IRegisteredDocNodeDefinition = {\r\n        docNodeKind: definition.docNodeKind,\r\n        constructor: definition.constructor,\r\n        packageName,\r\n        allowedChildKinds: new Set<string>()\r\n      };\r\n\r\n      this._docNodeDefinitionsByKind.set(definition.docNodeKind, newDefinition);\r\n      this._docNodeDefinitionsByConstructor.set(definition.constructor, newDefinition);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reports an error if the specified DocNode kind has not been registered.\r\n   */\r\n  public throwIfNotRegisteredKind(docNodeKind: string): void {\r\n    if (!this._docNodeDefinitionsByKind.has(docNodeKind)) {\r\n      throw new Error(`The DocNode kind \"${docNodeKind}\" was not registered with this TSDocConfiguration`);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * For the given parent DocNode kind, registers the specified DocNode kinds as being allowable children of\r\n   * the parent.\r\n   *\r\n   * @remarks\r\n   * To prevent mistakes, `DocNodeContainer` will report an error if you try to add node that was not registered\r\n   * as an allowable child of the container.\r\n   */\r\n  public registerAllowableChildren(parentKind: string, childKinds: ReadonlyArray<string>): void {\r\n    const parentDefinition: IRegisteredDocNodeDefinition = this._getDefinition(parentKind);\r\n\r\n    for (const childKind of childKinds) {\r\n      this._getDefinition(childKind);\r\n      parentDefinition.allowedChildKinds.add(childKind);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns true if the specified DocNode kind has been registered as an allowable child of the specified\r\n   * parent DocNode kind.\r\n   */\r\n  public isAllowedChild(parentKind: string, childKind: string): boolean {\r\n    const parentDefinition: IRegisteredDocNodeDefinition = this._getDefinition(parentKind);\r\n    return parentDefinition.allowedChildKinds.has(childKind);\r\n  }\r\n\r\n  private _getDefinition(docNodeKind: string): IRegisteredDocNodeDefinition {\r\n    const definition: IRegisteredDocNodeDefinition | undefined =\r\n      this._docNodeDefinitionsByKind.get(docNodeKind);\r\n    if (definition === undefined) {\r\n      throw new Error(`The DocNode kind \"${docNodeKind}\" was not registered with this TSDocConfiguration`);\r\n    }\r\n    return definition;\r\n  }\r\n}\r\n"]}