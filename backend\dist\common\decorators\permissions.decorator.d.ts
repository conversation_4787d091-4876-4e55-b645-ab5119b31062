export declare const PERMISSIONS_KEY = "permissions";
export declare const RequirePermissions: (...permissions: string[]) => import("@nestjs/common").CustomDecorator<string>;
export declare const PERMISSIONS: {
    readonly USERS_CREATE: "users:create";
    readonly USERS_READ: "users:read";
    readonly USERS_UPDATE: "users:update";
    readonly USERS_DELETE: "users:delete";
    readonly COMPANIES_CREATE: "companies:create";
    readonly COMPANIES_READ: "companies:read";
    readonly COMPANIES_UPDATE: "companies:update";
    readonly COMPANIES_DELETE: "companies:delete";
    readonly WHATSAPP_CONNECT: "whatsapp:connect";
    readonly WHATSAPP_DISCONNECT: "whatsapp:disconnect";
    readonly WHATSAPP_SEND_MESSAGE: "whatsapp:send_message";
    readonly WHATSAPP_READ_MESSAGES: "whatsapp:read_messages";
    readonly MESSAGES_READ: "messages:read";
    readonly MESSAGES_SEND: "messages:send";
    readonly MESSAGES_DELETE: "messages:delete";
    readonly ANALYTICS_READ: "analytics:read";
    readonly ANALYTICS_EXPORT: "analytics:export";
    readonly AUTOMATION_CREATE: "automation:create";
    readonly AUTOMATION_READ: "automation:read";
    readonly AUTOMATION_UPDATE: "automation:update";
    readonly AUTOMATION_DELETE: "automation:delete";
    readonly BILLING_READ: "billing:read";
    readonly BILLING_MANAGE: "billing:manage";
    readonly INTEGRATIONS_CREATE: "integrations:create";
    readonly INTEGRATIONS_READ: "integrations:read";
    readonly INTEGRATIONS_UPDATE: "integrations:update";
    readonly INTEGRATIONS_DELETE: "integrations:delete";
    readonly ADMIN_FULL_ACCESS: "admin:full_access";
};
