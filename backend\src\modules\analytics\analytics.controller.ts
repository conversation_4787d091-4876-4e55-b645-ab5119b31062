import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  Res,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { plainToClass } from 'class-transformer';
import { AnalyticsService } from './services/analytics.service';
import { TrackingService } from './services/tracking.service';
import { 
  AnalyticsQueryDto, 
  DashboardQueryDto, 
  ReportQueryDto 
} from './dto/analytics-query.dto';
import { 
  AnalyticsReportResponse,
  DashboardMetrics,
  ConversationAnalytics,
  UserActivityAnalytics,
  PerformanceAnalytics,
  AnalyticsMetric 
} from './dto/analytics-response.dto';
import { CurrentUser, AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { RequirePermissions, PERMISSIONS } from '../../common/decorators/permissions.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole } from '../../database/entities';
import { EventType, EventCategory } from '../../database/entities/analytics-event.schema';

@ApiTags('Analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly trackingService: TrackingService,
  ) {}

  @Get('dashboard')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter métricas do dashboard' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Período de tempo' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Data de início' })
  @ApiQuery({ name: 'endDate', required: false, description: 'Data de fim' })
  @ApiQuery({ name: 'connectionIds', required: false, type: [String], description: 'IDs das conexões' })
  @ApiQuery({ name: 'compareWithPrevious', required: false, type: Boolean, description: 'Comparar com período anterior' })
  @ApiResponse({
    status: 200,
    description: 'Métricas do dashboard',
    type: DashboardMetrics,
  })
  async getDashboard(
    @Query() query: DashboardQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<DashboardMetrics> {
    const metrics = await this.analyticsService.getDashboardMetrics(query, currentUser);
    return plainToClass(DashboardMetrics, metrics, { excludeExtraneousValues: true });
  }

  @Get('conversations')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter analytics de conversas' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Período de tempo' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Data de início' })
  @ApiQuery({ name: 'endDate', required: false, description: 'Data de fim' })
  @ApiResponse({
    status: 200,
    description: 'Analytics de conversas',
    type: ConversationAnalytics,
  })
  async getConversationAnalytics(
    @Query() query: DashboardQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ConversationAnalytics> {
    const analytics = await this.analyticsService.getConversationAnalytics(query, currentUser);
    return plainToClass(ConversationAnalytics, analytics, { excludeExtraneousValues: true });
  }

  @Get('user-activity')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter analytics de atividade de usuários' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Período de tempo' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Data de início' })
  @ApiQuery({ name: 'endDate', required: false, description: 'Data de fim' })
  @ApiResponse({
    status: 200,
    description: 'Analytics de atividade de usuários',
    type: UserActivityAnalytics,
  })
  async getUserActivityAnalytics(
    @Query() query: DashboardQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<UserActivityAnalytics> {
    const analytics = await this.analyticsService.getUserActivityAnalytics(query, currentUser);
    return plainToClass(UserActivityAnalytics, analytics, { excludeExtraneousValues: true });
  }

  @Get('performance')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter analytics de performance' })
  @ApiQuery({ name: 'timeRange', required: false, description: 'Período de tempo' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Data de início' })
  @ApiQuery({ name: 'endDate', required: false, description: 'Data de fim' })
  @ApiResponse({
    status: 200,
    description: 'Analytics de performance',
    type: PerformanceAnalytics,
  })
  async getPerformanceAnalytics(
    @Query() query: DashboardQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<PerformanceAnalytics> {
    const analytics = await this.analyticsService.getPerformanceAnalytics(query, currentUser);
    return plainToClass(PerformanceAnalytics, analytics, { excludeExtraneousValues: true });
  }

  @Get('custom')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter analytics customizados' })
  @ApiResponse({
    status: 200,
    description: 'Métricas customizadas',
    type: [AnalyticsMetric],
  })
  async getCustomAnalytics(
    @Query() query: AnalyticsQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AnalyticsMetric[]> {
    const metrics = await this.analyticsService.getCustomAnalytics(query, currentUser);
    return metrics.map(metric => 
      plainToClass(AnalyticsMetric, metric, { excludeExtraneousValues: true })
    );
  }

  @Get('report')
  @RequirePermissions(PERMISSIONS.ANALYTICS_EXPORT)
  @ApiOperation({ summary: 'Gerar relatório completo' })
  @ApiResponse({
    status: 200,
    description: 'Relatório completo de analytics',
    type: AnalyticsReportResponse,
  })
  async getFullReport(
    @Query() query: DashboardQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AnalyticsReportResponse> {
    const [dashboard, conversations, userActivity, performance] = await Promise.all([
      this.analyticsService.getDashboardMetrics(query, currentUser),
      this.analyticsService.getConversationAnalytics(query, currentUser),
      this.analyticsService.getUserActivityAnalytics(query, currentUser),
      this.analyticsService.getPerformanceAnalytics(query, currentUser),
    ]);

    const report: AnalyticsReportResponse = {
      dashboard,
      conversations,
      userActivity,
      performance,
      period: {
        start: query.startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        end: query.endDate || new Date().toISOString(),
      },
      generatedAt: new Date().toISOString(),
    };

    return plainToClass(AnalyticsReportResponse, report, { excludeExtraneousValues: true });
  }

  @Get('export')
  @RequirePermissions(PERMISSIONS.ANALYTICS_EXPORT)
  @ApiOperation({ summary: 'Exportar relatório em diferentes formatos' })
  @ApiQuery({ name: 'format', required: false, enum: ['json', 'csv', 'excel'], description: 'Formato do export' })
  @ApiResponse({
    status: 200,
    description: 'Arquivo de relatório',
  })
  async exportReport(
    @Query() query: ReportQueryDto,
    @CurrentUser() currentUser: AuthenticatedUser,
    @Res() res: Response,
  ): Promise<void> {
    const format = query.format || 'json';
    
    // Obter dados do relatório
    const report = await this.getFullReport(query, currentUser);

    // Configurar headers baseado no formato
    switch (format) {
      case 'csv':
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.csv');
        // Implementar conversão para CSV
        res.send(this.convertToCSV(report));
        break;
      
      case 'excel':
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.xlsx');
        // Implementar conversão para Excel
        res.send(this.convertToExcel(report));
        break;
      
      default:
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.json');
        res.json(report);
    }
  }

  @Post('track')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Rastrear evento customizado' })
  @ApiResponse({
    status: 204,
    description: 'Evento rastreado com sucesso',
  })
  async trackCustomEvent(
    @Body() eventData: {
      type: EventType;
      category: EventCategory;
      properties?: Record<string, any>;
    },
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      eventData.type,
      eventData.category,
      currentUser.companyId,
      eventData.properties || {},
      currentUser.id,
    );
  }

  @Get('events')
  @Roles(UserRole.SUPER_ADMIN, UserRole.AGENCY_ADMIN)
  @RequirePermissions(PERMISSIONS.ADMIN_FULL_ACCESS)
  @ApiOperation({ summary: 'Listar eventos disponíveis (Admin)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de tipos de eventos e categorias',
    schema: {
      type: 'object',
      properties: {
        eventTypes: { type: 'array', items: { type: 'string' } },
        categories: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async getAvailableEvents() {
    return {
      eventTypes: Object.values(EventType),
      categories: Object.values(EventCategory),
    };
  }

  // Métodos auxiliares privados para conversão de formato

  private convertToCSV(data: any): string {
    // Implementação simplificada - em produção, usar biblioteca como csv-writer
    const headers = ['Metric', 'Value', 'Previous Value', 'Change %'];
    const rows = [
      ['Messages Sent', data.dashboard.messagesSent.value, data.dashboard.messagesSent.previousValue || 0, data.dashboard.messagesSent.changePercent || 0],
      ['Messages Received', data.dashboard.messagesReceived.value, data.dashboard.messagesReceived.previousValue || 0, data.dashboard.messagesReceived.changePercent || 0],
      // Adicionar mais métricas conforme necessário
    ];

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  }

  private convertToExcel(data: any): Buffer {
    // Implementação simplificada - em produção, usar biblioteca como exceljs
    // Por enquanto, retornar JSON como buffer
    return Buffer.from(JSON.stringify(data, null, 2));
  }
}
