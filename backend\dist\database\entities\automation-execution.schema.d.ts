import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type AutomationExecutionDocument = AutomationExecution & Document;
export declare enum ExecutionStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
    PAUSED = "paused"
}
export declare enum StepStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    SKIPPED = "skipped"
}
export interface ExecutionStep {
    stepId: string;
    stepName: string;
    status: StepStatus;
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    input?: Record<string, any>;
    output?: Record<string, any>;
    error?: {
        code: string;
        message: string;
        stack?: string;
    };
    retryCount?: number;
    nextStepId?: string;
}
export interface ExecutionContext {
    contact: {
        id: string;
        phoneNumber: string;
        name?: string;
        tags?: string[];
        customFields?: Record<string, any>;
    };
    message?: {
        id: string;
        content?: string;
        type: string;
        timestamp: Date;
    };
    user?: {
        id: string;
        name: string;
        email: string;
    };
    connection: {
        id: string;
        phoneNumber: string;
        name: string;
    };
    variables: Record<string, any>;
    session: Record<string, any>;
}
export declare class AutomationExecution {
    automationId: Types.ObjectId;
    automationName: string;
    automationVersion: number;
    companyId: Types.ObjectId;
    status: ExecutionStatus;
    context: ExecutionContext;
    steps: ExecutionStep[];
    currentStepId?: string;
    startedAt?: Date;
    completedAt?: Date;
    duration?: number;
    error?: {
        code: string;
        message: string;
        stack?: string;
        stepId?: string;
    };
    retryCount: number;
    maxRetries: number;
    nextRetryAt?: Date;
    triggeredBy?: string;
    triggerType: string;
    triggerData: Record<string, any>;
    results: {
        messagesSent?: number;
        tagsAdded?: string[];
        tagsRemoved?: string[];
        contactUpdated?: boolean;
        leadCreated?: boolean;
        webhooksCalled?: number;
        aiInteractions?: number;
        humanHandoff?: boolean;
    };
    totalSteps: number;
    completedSteps: number;
    failedSteps: number;
    skippedSteps: number;
    metadata: Record<string, any>;
    executionId: string;
    isTest: boolean;
    parentExecutionId?: string;
    childExecutionIds: string[];
}
export declare const AutomationExecutionSchema: mongoose.Schema<AutomationExecution, mongoose.Model<AutomationExecution, any, any, any, Document<unknown, any, AutomationExecution, any> & AutomationExecution & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, AutomationExecution, Document<unknown, {}, mongoose.FlatRecord<AutomationExecution>, {}> & mongoose.FlatRecord<AutomationExecution> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
