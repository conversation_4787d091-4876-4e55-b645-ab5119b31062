import { ConfigService } from '@nestjs/config';
import { MongooseModuleOptions } from '@nestjs/mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer: MongoMemoryServer;

export const getMongoConfig = async (configService: ConfigService): Promise<MongooseModuleOptions> => {
  const nodeEnv = configService.get('nodeEnv');
  const mongoUri = configService.get('mongo.uri');

  // Para desenvolvimento, usar MongoDB Memory Server se MongoDB local não estiver disponível
  if (nodeEnv === 'development') {
    // Tentar MongoDB local primeiro
    if (mongoUri && !mongoUri.includes('memory')) {
      return {
        uri: mongoUri,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      };
    }

    // Fallback para MongoDB Memory Server
    try {
      if (!mongoServer) {
        mongoServer = await MongoMemoryServer.create({
          instance: {
            dbName: 'whatsapp_platform_dev',
          },
        });
      }

      const uri = mongoServer.getUri();
      console.log('🍃 Using MongoDB Memory Server:', uri);

      return {
        uri,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      };
    } catch (error) {
      console.error('Failed to start MongoDB Memory Server:', error);
      // Fallback para URI padrão
      return {
        uri: 'mongodb://localhost:27017/whatsapp_platform_dev',
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      };
    }
  }

  // Para produção
  return {
    uri: mongoUri,
  };
};
