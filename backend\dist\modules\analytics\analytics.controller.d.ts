import { Response } from 'express';
import { AnalyticsService } from './services/analytics.service';
import { TrackingService } from './services/tracking.service';
import { AnalyticsQueryDto, DashboardQueryDto, ReportQueryDto } from './dto/analytics-query.dto';
import { AnalyticsReportResponse, DashboardMetrics, ConversationAnalytics, UserActivityAnalytics, PerformanceAnalytics, AnalyticsMetric } from './dto/analytics-response.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { EventType, EventCategory } from '../../database/entities/analytics-event.schema';
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly trackingService;
    constructor(analyticsService: AnalyticsService, trackingService: TrackingService);
    getDashboard(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<DashboardMetrics>;
    getConversationAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<ConversationAnalytics>;
    getUserActivityAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<UserActivityAnalytics>;
    getPerformanceAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<PerformanceAnalytics>;
    getCustomAnalytics(query: AnalyticsQueryDto, currentUser: AuthenticatedUser): Promise<AnalyticsMetric[]>;
    getFullReport(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<AnalyticsReportResponse>;
    exportReport(query: ReportQueryDto, currentUser: AuthenticatedUser, res: Response): Promise<void>;
    trackCustomEvent(eventData: {
        type: EventType;
        category: EventCategory;
        properties?: Record<string, any>;
    }, currentUser: AuthenticatedUser): Promise<void>;
    getAvailableEvents(): Promise<{
        eventTypes: EventType[];
        categories: EventCategory[];
    }>;
    private convertToCSV;
    private convertToExcel;
}
