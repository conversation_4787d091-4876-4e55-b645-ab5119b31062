"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Integration = exports.TriggerEvent = exports.IntegrationStatus = exports.IntegrationType = void 0;
const typeorm_1 = require("typeorm");
const company_entity_1 = require("./company.entity");
const user_entity_1 = require("./user.entity");
const webhook_log_entity_1 = require("./webhook-log.entity");
var IntegrationType;
(function (IntegrationType) {
    IntegrationType["WEBHOOK"] = "webhook";
    IntegrationType["GOOGLE_SHEETS"] = "google_sheets";
    IntegrationType["ZAPIER"] = "zapier";
    IntegrationType["CRM_HUBSPOT"] = "crm_hubspot";
    IntegrationType["CRM_SALESFORCE"] = "crm_salesforce";
    IntegrationType["CRM_PIPEDRIVE"] = "crm_pipedrive";
    IntegrationType["EMAIL_MAILCHIMP"] = "email_mailchimp";
    IntegrationType["EMAIL_SENDGRID"] = "email_sendgrid";
    IntegrationType["SMS_TWILIO"] = "sms_twilio";
    IntegrationType["SLACK"] = "slack";
    IntegrationType["DISCORD"] = "discord";
    IntegrationType["TELEGRAM"] = "telegram";
    IntegrationType["CUSTOM_API"] = "custom_api";
})(IntegrationType || (exports.IntegrationType = IntegrationType = {}));
var IntegrationStatus;
(function (IntegrationStatus) {
    IntegrationStatus["ACTIVE"] = "active";
    IntegrationStatus["INACTIVE"] = "inactive";
    IntegrationStatus["ERROR"] = "error";
    IntegrationStatus["PENDING"] = "pending";
    IntegrationStatus["EXPIRED"] = "expired";
})(IntegrationStatus || (exports.IntegrationStatus = IntegrationStatus = {}));
var TriggerEvent;
(function (TriggerEvent) {
    TriggerEvent["MESSAGE_RECEIVED"] = "message_received";
    TriggerEvent["MESSAGE_SENT"] = "message_sent";
    TriggerEvent["CONTACT_CREATED"] = "contact_created";
    TriggerEvent["CONTACT_UPDATED"] = "contact_updated";
    TriggerEvent["LEAD_CONVERTED"] = "lead_converted";
    TriggerEvent["AUTOMATION_TRIGGERED"] = "automation_triggered";
    TriggerEvent["AUTOMATION_COMPLETED"] = "automation_completed";
    TriggerEvent["CONNECTION_STATUS_CHANGED"] = "connection_status_changed";
    TriggerEvent["USER_CREATED"] = "user_created";
    TriggerEvent["SUBSCRIPTION_CHANGED"] = "subscription_changed";
    TriggerEvent["PAYMENT_RECEIVED"] = "payment_received";
    TriggerEvent["CUSTOM_EVENT"] = "custom_event";
})(TriggerEvent || (exports.TriggerEvent = TriggerEvent = {}));
let Integration = class Integration {
    id;
    name;
    description;
    type;
    status;
    companyId;
    company;
    createdBy;
    creator;
    triggerEvents;
    config;
    active;
    executionCount;
    successCount;
    errorCount;
    lastExecutedAt;
    lastSuccessAt;
    lastErrorAt;
    lastError;
    rateLimit;
    currentRateCount;
    rateLimitResetAt;
    maxRetries;
    retryDelay;
    timeout;
    metadata;
    tags;
    createdAt;
    updatedAt;
    logs;
    isActive() {
        return this.status === IntegrationStatus.ACTIVE && this.active;
    }
    canExecute() {
        if (!this.isActive())
            return false;
        if (this.rateLimit > 0) {
            const now = new Date();
            if (this.rateLimitResetAt && now < this.rateLimitResetAt) {
                return this.currentRateCount < this.rateLimit;
            }
            else {
                this.currentRateCount = 0;
                this.rateLimitResetAt = new Date(now.getTime() + 60000);
            }
        }
        return true;
    }
    incrementExecution(success) {
        this.executionCount += 1;
        this.lastExecutedAt = new Date();
        if (success) {
            this.successCount += 1;
            this.lastSuccessAt = new Date();
            this.lastError = null;
        }
        else {
            this.errorCount += 1;
            this.lastErrorAt = new Date();
        }
        if (this.rateLimit > 0) {
            this.currentRateCount += 1;
        }
    }
    setError(error) {
        this.lastError = error;
        this.lastErrorAt = new Date();
        if (this.errorCount > 10 && this.successCount === 0) {
            this.status = IntegrationStatus.ERROR;
        }
    }
    getSuccessRate() {
        if (this.executionCount === 0)
            return 0;
        return (this.successCount / this.executionCount) * 100;
    }
    hasEvent(event) {
        return this.triggerEvents.includes(event);
    }
    shouldTrigger(event, data) {
        if (!this.hasEvent(event))
            return false;
        if (!this.canExecute())
            return false;
        const filters = this.config.filters;
        if (!filters)
            return true;
        if (filters.contactTags && data.contact?.tags) {
            const hasMatchingTag = filters.contactTags.some(tag => data.contact.tags.includes(tag));
            if (!hasMatchingTag)
                return false;
        }
        if (filters.messageTypes && data.message?.type) {
            if (!filters.messageTypes.includes(data.message.type))
                return false;
        }
        if (filters.connectionIds && data.connectionId) {
            if (!filters.connectionIds.includes(data.connectionId))
                return false;
        }
        if (filters.userIds && data.userId) {
            if (!filters.userIds.includes(data.userId))
                return false;
        }
        return true;
    }
    transformData(data) {
        const mapping = this.config.dataMapping;
        if (!mapping)
            return data;
        const result = { ...data };
        if (mapping.staticFields) {
            Object.assign(result, mapping.staticFields);
        }
        if (mapping.dynamicFields) {
            for (const [targetField, sourceField] of Object.entries(mapping.dynamicFields)) {
                const value = this.getNestedValue(data, sourceField);
                if (value !== undefined) {
                    this.setNestedValue(result, targetField, value);
                }
            }
        }
        if (mapping.transformations) {
            for (const transformation of mapping.transformations) {
                const value = this.getNestedValue(result, transformation.field);
                if (value !== undefined) {
                    const transformedValue = this.applyTransformation(value, transformation);
                    this.setNestedValue(result, transformation.field, transformedValue);
                }
            }
        }
        return result;
    }
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key])
                current[key] = {};
            return current[key];
        }, obj);
        target[lastKey] = value;
    }
    applyTransformation(value, transformation) {
        switch (transformation.type) {
            case 'uppercase':
                return String(value).toUpperCase();
            case 'lowercase':
                return String(value).toLowerCase();
            case 'date_format':
                return new Date(value).toISOString();
            case 'number_format':
                return Number(value);
            default:
                return value;
        }
    }
};
exports.Integration = Integration;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Integration.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Integration.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Integration.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IntegrationType,
    }),
    __metadata("design:type", String)
], Integration.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: IntegrationStatus,
        default: IntegrationStatus.PENDING,
    }),
    __metadata("design:type", String)
], Integration.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Integration.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], Integration.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Integration.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], Integration.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array' }),
    __metadata("design:type", Array)
], Integration.prototype, "triggerEvents", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], Integration.prototype, "config", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Integration.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "executionCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "successCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "errorCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Integration.prototype, "lastExecutedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Integration.prototype, "lastSuccessAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Integration.prototype, "lastErrorAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "lastError", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "rateLimit", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Integration.prototype, "currentRateCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Integration.prototype, "rateLimitResetAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 3 }),
    __metadata("design:type", Number)
], Integration.prototype, "maxRetries", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 5000 }),
    __metadata("design:type", Number)
], Integration.prototype, "retryDelay", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 30000 }),
    __metadata("design:type", Number)
], Integration.prototype, "timeout", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Integration.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Integration.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Integration.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Integration.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => webhook_log_entity_1.WebhookLog, log => log.integration),
    __metadata("design:type", Array)
], Integration.prototype, "logs", void 0);
exports.Integration = Integration = __decorate([
    (0, typeorm_1.Entity)('integrations')
], Integration);
//# sourceMappingURL=integration.entity.js.map