import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Transform } from 'class-transformer';
import { UserRole, UserStatus } from '../../../database/entities';

export class UserResponseDto {
  @ApiProperty({
    description: 'ID do usuário',
    example: 'uuid-do-usuario',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Primeiro nome',
    example: 'João',
  })
  @Expose()
  firstName: string;

  @ApiProperty({
    description: 'Último nome',
    example: 'Silva',
  })
  @Expose()
  lastName: string;

  @ApiProperty({
    description: 'Nome completo',
    example: '<PERSON>',
  })
  @Expose()
  @Transform(({ obj }) => `${obj.firstName} ${obj.lastName}`)
  fullName: string;

  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiPropertyOptional({
    description: 'Telefone do usuário',
    example: '+5511999999999',
  })
  @Expose()
  phone?: string;

  @ApiProperty({
    description: 'Papel do usuário',
    enum: UserRole,
  })
  @Expose()
  role: UserRole;

  @ApiProperty({
    description: 'Status do usuário',
    enum: UserStatus,
  })
  @Expose()
  status: UserStatus;

  @ApiPropertyOptional({
    description: 'Avatar do usuário',
    example: 'https://example.com/avatar.jpg',
  })
  @Expose()
  avatar?: string;

  @ApiPropertyOptional({
    description: 'Último login',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  lastLoginAt?: Date;

  @ApiPropertyOptional({
    description: 'Email verificado em',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  emailVerifiedAt?: Date;

  @ApiProperty({
    description: 'ID da empresa',
    example: 'uuid-da-empresa',
  })
  @Expose()
  companyId: string;

  @ApiPropertyOptional({
    description: 'Permissões do usuário',
    type: [String],
  })
  @Expose()
  permissions?: string[];

  @ApiProperty({
    description: 'Data de criação',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Data de atualização',
    example: '2023-12-01T10:00:00Z',
  })
  @Expose()
  updatedAt: Date;

  // Campos excluídos
  @Exclude()
  password: string;

  @Exclude()
  emailVerificationToken?: string;

  @Exclude()
  passwordResetToken?: string;

  @Exclude()
  passwordResetExpiresAt?: Date;
}
