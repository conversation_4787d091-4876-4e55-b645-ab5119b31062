import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';

export type AutomationDocument = Automation & Document;

export enum AutomationType {
  WELCOME_MESSAGE = 'welcome_message',
  KEYWORD_RESPONSE = 'keyword_response',
  CHATBOT_FLOW = 'chatbot_flow',
  SCHEDULED_MESSAGE = 'scheduled_message',
  FOLLOW_UP = 'follow_up',
  LEAD_QUALIFICATION = 'lead_qualification',
  AWAY_MESSAGE = 'away_message',
  BUSINESS_HOURS = 'business_hours',
}

export enum AutomationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  PAUSED = 'paused',
}

export enum TriggerType {
  MESSAGE_RECEIVED = 'message_received',
  KEYWORD_MATCH = 'keyword_match',
  FIRST_MESSAGE = 'first_message',
  SCHEDULE = 'schedule',
  WEBHOOK = 'webhook',
  MANUAL = 'manual',
  CONTACT_CREATED = 'contact_created',
  TAG_ADDED = 'tag_added',
  INACTIVITY = 'inactivity',
}

export enum ActionType {
  SEND_MESSAGE = 'send_message',
  ADD_TAG = 'add_tag',
  REMOVE_TAG = 'remove_tag',
  ASSIGN_USER = 'assign_user',
  CREATE_LEAD = 'create_lead',
  UPDATE_CONTACT = 'update_contact',
  WEBHOOK_CALL = 'webhook_call',
  WAIT = 'wait',
  CONDITION = 'condition',
  AI_RESPONSE = 'ai_response',
  TRANSFER_TO_HUMAN = 'transfer_to_human',
}

export interface TriggerCondition {
  type: TriggerType;
  keywords?: string[];
  schedule?: {
    time: string; // HH:MM
    days: number[]; // 0-6 (Sunday-Saturday)
    timezone: string;
  };
  inactivityMinutes?: number;
  webhookUrl?: string;
  customConditions?: Record<string, any>;
}

export interface ActionStep {
  id: string;
  type: ActionType;
  name: string;
  description?: string;
  config: Record<string, any>;
  nextStepId?: string;
  conditions?: {
    field: string;
    operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
    value: any;
    nextStepId: string;
  }[];
  delay?: number; // Delay in seconds before executing this step
}

export interface FlowStep {
  id: string;
  name: string;
  type: 'message' | 'condition' | 'action' | 'ai' | 'human_handoff';
  position: { x: number; y: number };
  config: {
    message?: {
      type: 'text' | 'image' | 'audio' | 'video' | 'document';
      content?: string;
      mediaUrl?: string;
      buttons?: Array<{
        id: string;
        text: string;
        nextStepId?: string;
      }>;
    };
    condition?: {
      field: string;
      operator: string;
      value: any;
      trueStepId?: string;
      falseStepId?: string;
    };
    action?: {
      type: ActionType;
      config: Record<string, any>;
    };
    ai?: {
      provider: 'openai' | 'google' | 'anthropic';
      model: string;
      prompt: string;
      maxTokens?: number;
      temperature?: number;
    };
  };
  nextSteps?: string[];
}

@Schema({ timestamps: true })
export class Automation {
  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({ required: true, type: String, enum: AutomationType })
  type: AutomationType;

  @Prop({ required: true, type: String, enum: AutomationStatus, default: AutomationStatus.DRAFT })
  status: AutomationStatus;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Company' })
  companyId: Types.ObjectId;

  @Prop({ type: String })
  createdBy: string;

  @Prop({ type: String })
  updatedBy?: string;

  @Prop({ type: mongoose.Schema.Types.Mixed, required: true })
  trigger: TriggerCondition;

  @Prop({ type: [mongoose.Schema.Types.Mixed], default: [] })
  actions: ActionStep[];

  @Prop({ type: [mongoose.Schema.Types.Mixed], default: [] })
  flowSteps: FlowStep[];

  @Prop({ type: String })
  startStepId?: string;

  // Configurações específicas
  @Prop({ type: Object, default: {} })
  settings: {
    isActive?: boolean;
    priority?: number;
    maxExecutionsPerContact?: number;
    cooldownMinutes?: number;
    businessHoursOnly?: boolean;
    businessHours?: {
      start: string; // HH:MM
      end: string; // HH:MM
      timezone: string;
      days: number[]; // 0-6
    };
    allowedConnections?: string[];
    excludedContacts?: string[];
    tags?: string[];
  };

  // Estatísticas
  @Prop({ type: Number, default: 0 })
  executionCount: number;

  @Prop({ type: Number, default: 0 })
  successCount: number;

  @Prop({ type: Number, default: 0 })
  errorCount: number;

  @Prop({ type: Date })
  lastExecutedAt?: Date;

  @Prop({ type: Date })
  lastModifiedAt?: Date;

  // Versionamento
  @Prop({ type: Number, default: 1 })
  version: number;

  @Prop({ type: Boolean, default: false })
  isTemplate: boolean;

  @Prop({ type: String })
  templateCategory?: string;

  // Metadados
  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  metadata: Record<string, any>;
}

export const AutomationSchema = SchemaFactory.createForClass(Automation);

// Índices para performance
AutomationSchema.index({ companyId: 1, status: 1 });
AutomationSchema.index({ companyId: 1, type: 1 });
AutomationSchema.index({ 'trigger.type': 1, status: 1 });
AutomationSchema.index({ 'trigger.keywords': 1 });
AutomationSchema.index({ createdBy: 1 });
AutomationSchema.index({ lastExecutedAt: -1 });
AutomationSchema.index({ executionCount: -1 });

// Métodos do schema
AutomationSchema.methods.canExecute = function(contact: any, message?: any): boolean {
  if (this.status !== AutomationStatus.ACTIVE) {
    return false;
  }

  // Verificar horário comercial
  if (this.settings.businessHoursOnly) {
    const now = new Date();
    const businessHours = this.settings.businessHours;
    if (businessHours) {
      const currentHour = now.getHours();
      const currentDay = now.getDay();
      const startHour = parseInt(businessHours.start.split(':')[0]);
      const endHour = parseInt(businessHours.end.split(':')[0]);
      
      if (!businessHours.days.includes(currentDay) || 
          currentHour < startHour || 
          currentHour >= endHour) {
        return false;
      }
    }
  }

  // Verificar cooldown
  if (this.settings.cooldownMinutes && this.lastExecutedAt) {
    const cooldownMs = this.settings.cooldownMinutes * 60 * 1000;
    if (Date.now() - this.lastExecutedAt.getTime() < cooldownMs) {
      return false;
    }
  }

  // Verificar contatos excluídos
  if (this.settings.excludedContacts?.includes(contact.phoneNumber)) {
    return false;
  }

  return true;
};

AutomationSchema.methods.incrementExecution = function(success: boolean = true): void {
  this.executionCount += 1;
  if (success) {
    this.successCount += 1;
  } else {
    this.errorCount += 1;
  }
  this.lastExecutedAt = new Date();
};

AutomationSchema.methods.getSuccessRate = function(): number {
  if (this.executionCount === 0) return 0;
  return (this.successCount / this.executionCount) * 100;
};
