import { <PERSON><PERSON><PERSON>, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Company } from './company.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Exclude } from 'class-transformer';

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  AGENCY_ADMIN = 'agency_admin',
  COMPANY_ADMIN = 'company_admin',
  SELLER = 'seller',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

@Entity('users')
@Index(['email'], { unique: true })
@Index(['companyId', 'email'], { unique: true })
export class User extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  firstName: string;

  @Column({ type: 'varchar', length: 255 })
  lastName: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 255 })
  @Exclude()
  password: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: UserRole.SELLER,
  })
  role: UserRole;

  @Column({
    type: 'varchar',
    length: 50,
    default: UserStatus.PENDING,
  })
  status: UserStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar: string;

  @Column({ type: 'datetime', nullable: true })
  lastLoginAt: Date;

  @Column({ type: 'datetime', nullable: true })
  emailVerifiedAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  emailVerificationToken: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  passwordResetToken: string;

  @Column({ type: 'datetime', nullable: true })
  passwordResetExpiresAt: Date;

  @Column({ type: 'jsonb', nullable: true })
  preferences: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  permissions: string[];

  // Relacionamento com empresa
  @Column({ type: 'uuid' })
  companyId: string;

  @ManyToOne(() => Company, (company) => company.users)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  // Relacionamentos
  @OneToMany(() => WhatsAppConnection, (connection) => connection.assignedUser)
  assignedWhatsAppConnections: WhatsAppConnection[];

  // Campos calculados
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }

  get isSuperAdmin(): boolean {
    return this.role === UserRole.SUPER_ADMIN;
  }

  get isAgencyAdmin(): boolean {
    return this.role === UserRole.AGENCY_ADMIN;
  }

  get isCompanyAdmin(): boolean {
    return this.role === UserRole.COMPANY_ADMIN;
  }

  get isSeller(): boolean {
    return this.role === UserRole.SELLER;
  }

  // Métodos de verificação de permissão
  hasPermission(permission: string): boolean {
    return this.permissions?.includes(permission) || false;
  }

  canAccessCompany(companyId: string): boolean {
    if (this.isSuperAdmin) return true;
    if (this.isAgencyAdmin && this.company.isAgency) {
      // Agência pode acessar seus clientes
      return this.company.clients?.some(client => client.id === companyId) || false;
    }
    return this.companyId === companyId;
  }
}
