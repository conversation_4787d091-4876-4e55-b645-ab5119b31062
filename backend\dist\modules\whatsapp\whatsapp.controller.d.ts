import { WhatsAppService, FindConnectionsOptions } from './whatsapp.service';
import { CreateConnectionDto } from './dto/create-connection.dto';
import { UpdateConnectionDto } from './dto/update-connection.dto';
import { SendMessageDto, BulkMessageDto } from './dto/send-message.dto';
import { ConnectionResponseDto } from './dto/connection-response.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
export declare class WhatsAppController {
    private readonly whatsappService;
    constructor(whatsappService: WhatsAppService);
    createConnection(createConnectionDto: CreateConnectionDto, currentUser: AuthenticatedUser): Promise<ConnectionResponseDto>;
    findAllConnections(query: FindConnectionsOptions, currentUser: AuthenticatedUser): Promise<{
        connections: ConnectionResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOneConnection(id: string, currentUser: AuthenticatedUser): Promise<ConnectionResponseDto>;
    updateConnection(id: string, updateConnectionDto: UpdateConnectionDto, currentUser: AuthenticatedUser): Promise<ConnectionResponseDto>;
    removeConnection(id: string, currentUser: AuthenticatedUser): Promise<void>;
    connectWhatsApp(id: string, currentUser: AuthenticatedUser): Promise<{
        qrCode?: string;
    }>;
    disconnectWhatsApp(id: string, currentUser: AuthenticatedUser): Promise<void>;
    sendMessage(id: string, sendMessageDto: SendMessageDto, currentUser: AuthenticatedUser): Promise<any>;
    sendBulkMessage(id: string, bulkMessageDto: BulkMessageDto, currentUser: AuthenticatedUser): Promise<{
        phoneNumber: string;
        success: boolean;
        result?: any;
        error?: string;
    }[]>;
    handleWebhook(data: any): Promise<void>;
}
