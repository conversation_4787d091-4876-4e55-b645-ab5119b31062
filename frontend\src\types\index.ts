// Auth Types
export interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone?: string
  role: UserRole
  status: UserStatus
  avatar?: string
  lastLoginAt?: Date
  emailVerifiedAt?: Date
  preferences?: Record<string, any>
  permissions?: string[]
  companyId: string
  company?: Company
  createdAt: Date
  updatedAt: Date
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  SELLER = 'seller',
  SUPPORT = 'support'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended'
}

export interface AuthResponse {
  user: User
  accessToken: string
  refreshToken: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  companyName: string
  cnpj: string
}

// Company Types
export interface Company {
  id: string
  name: string
  cnpj: string
  email: string
  phone: string
  address?: string
  website?: string
  status: CompanyStatus
  settings?: Record<string, any>
  logo?: string
  agencyId?: string
  createdAt: Date
  updatedAt: Date
}

export enum CompanyStatus {
  TRIAL = 'trial',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled'
}

// WhatsApp Types
export interface WhatsAppConnection {
  id: string
  name: string
  phoneNumber: string
  type: ConnectionType
  status: ConnectionStatus
  qrCode?: string
  instanceId?: string
  connectionData?: Record<string, any>
  settings?: Record<string, any>
  lastConnectedAt?: Date
  lastDisconnectedAt?: Date
  errorMessage?: string
  isActive: boolean
  companyId: string
  assignedUserId?: string
  assignedUser?: User
  createdAt: Date
  updatedAt: Date
}

export enum ConnectionType {
  EVOLUTION_API = 'evolution_api',
  BAILEYS = 'baileys',
  WEB_WHATSAPP = 'web_whatsapp'
}

export enum ConnectionStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  PENDING = 'pending',
  ERROR = 'error',
  CONNECTING = 'connecting'
}

// Message Types
export interface Message {
  id: string
  messageId: string
  conversationId: string
  whatsappConnectionId: string
  contactPhone: string
  contactName?: string
  content: MessageContent
  type: MessageType
  direction: MessageDirection
  status: MessageStatus
  timestamp: Date
  readAt?: Date
  deliveredAt?: Date
  metadata?: Record<string, any>
  companyId: string
  userId?: string
  createdAt: Date
}

export interface MessageContent {
  text?: string
  media?: {
    url: string
    type: 'image' | 'video' | 'audio' | 'document'
    filename?: string
    caption?: string
  }
  location?: {
    latitude: number
    longitude: number
    address?: string
  }
  contact?: {
    name: string
    phone: string
  }
}

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  LOCATION = 'location',
  CONTACT = 'contact',
  STICKER = 'sticker'
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed'
}

// Contact Types
export interface Contact {
  id: string
  phone: string
  name?: string
  email?: string
  avatar?: string
  tags: string[]
  notes?: string
  customFields?: Record<string, any>
  lastMessageAt?: Date
  messageCount: number
  companyId: string
  whatsappConnectionId: string
  isBlocked: boolean
  createdAt: Date
  updatedAt: Date
}

// Conversation Types
export interface Conversation {
  id: string
  contactPhone: string
  contact?: Contact
  whatsappConnectionId: string
  whatsappConnection?: WhatsAppConnection
  lastMessage?: Message
  lastMessageAt?: Date
  unreadCount: number
  isArchived: boolean
  assignedUserId?: string
  assignedUser?: User
  tags: string[]
  notes?: string
  companyId: string
  createdAt: Date
  updatedAt: Date
}

// API Response Types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ApiError {
  message: string
  error: string
  statusCode: number
}

// Form Types
export interface SendMessageForm {
  contactPhone: string
  content: string
  type: MessageType
  whatsappConnectionId: string
}

export interface CreateConnectionForm {
  name: string
  phoneNumber: string
  type: ConnectionType
}

// Dashboard Types
export interface DashboardStats {
  totalConnections: number
  activeConnections: number
  totalMessages: number
  todayMessages: number
  totalContacts: number
  newContacts: number
  responseTime: number
  satisfactionRate: number
}

// Notification Types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  read: boolean
  createdAt: Date
}

// WebSocket Types
export interface SocketMessage {
  event: string
  data: any
  timestamp: Date
}

// Filter Types
export interface MessageFilters {
  contactPhone?: string
  whatsappConnectionId?: string
  type?: MessageType
  direction?: MessageDirection
  status?: MessageStatus
  startDate?: Date
  endDate?: Date
  search?: string
}

export interface ContactFilters {
  whatsappConnectionId?: string
  tags?: string[]
  hasMessages?: boolean
  search?: string
}

export interface ConnectionFilters {
  status?: ConnectionStatus
  type?: ConnectionType
  assignedUserId?: string
  search?: string
}
