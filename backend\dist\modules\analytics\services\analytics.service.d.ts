import { Model } from 'mongoose';
import { AnalyticsEventDocument, EventType, EventCategory, EventProperties } from '../../../database/entities/analytics-event.schema';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { AnalyticsQueryDto, DashboardQueryDto } from '../dto/analytics-query.dto';
import { AnalyticsMetric, DashboardMetrics, ConversationAnalytics, UserActivityAnalytics, PerformanceAnalytics } from '../dto/analytics-response.dto';
export declare class AnalyticsService {
    private analyticsEventModel;
    private readonly logger;
    constructor(analyticsEventModel: Model<AnalyticsEventDocument>);
    trackEvent(type: EventType, category: EventCategory, companyId: string, properties?: EventProperties, userId?: string, sessionId?: string): Promise<void>;
    getDashboardMetrics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<DashboardMetrics>;
    getConversationAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<ConversationAnalytics>;
    getUserActivityAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<UserActivityAnalytics>;
    getPerformanceAnalytics(query: DashboardQueryDto, currentUser: AuthenticatedUser): Promise<PerformanceAnalytics>;
    getCustomAnalytics(query: AnalyticsQueryDto, currentUser: AuthenticatedUser): Promise<AnalyticsMetric[]>;
    private getDateRange;
    private getMetricTimeSeries;
    private getGroupField;
    private formatMetric;
    private calculateRate;
    private calculateConversionRate;
    private getAverageResponseTime;
    private getActiveConnections;
    private getActiveUsers;
    private getTotalConversations;
    private getActiveConversations;
    private getConversationMetrics;
    private getFirstResponseMetrics;
    private getActiveUsersInPeriod;
    private getSessionMetrics;
    private getActivityByHour;
    private getActivityByDayOfWeek;
    private getApiMetrics;
    private getErrorMetrics;
    private getSystemMetrics;
    private executeAggregation;
}
