import { Server as SocketIOServer, Socket } from 'socket.io'
import { Server as HttpServer } from 'http'
import jwt from 'jsonwebtoken'
import { UserRepository } from '../repositories/user.repository'

export interface SocketUser {
  userId: string
  companyId: string
  socketId: string
  connectedAt: Date
}

export interface MessageEvent {
  type: 'new_message' | 'message_update' | 'message_status'
  data: any
  companyId: string
  connectionId?: string
  contactPhone?: string
}

export interface ConnectionEvent {
  type: 'connection_status' | 'qr_code_update'
  data: any
  companyId: string
  connectionId: string
}

export interface NotificationEvent {
  type: 'notification'
  data: {
    title: string
    message: string
    type: 'info' | 'success' | 'warning' | 'error'
    userId?: string
    companyId: string
  }
}

export class WebSocketService {
  private io: SocketIOServer
  private connectedUsers: Map<string, SocketUser> = new Map()
  private userRepository: UserRepository

  constructor(server: HttpServer) {
    this.userRepository = new UserRepository()
    
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3001",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupMiddleware()
    this.setupEventHandlers()
  }

  private setupMiddleware() {
    // Middleware de autenticação
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '')
        
        if (!token) {
          return next(new Error('Token de autenticação necessário'))
        }

        // Verificar token JWT
        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }
        
        // Buscar usuário
        const user = await this.userRepository.findById(decoded.userId)
        if (!user) {
          return next(new Error('Usuário não encontrado'))
        }

        // Adicionar dados do usuário ao socket
        socket.data.user = {
          id: user.id,
          companyId: user.companyId,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          role: user.role
        }

        next()
      } catch (error) {
        console.error('[WebSocket] Erro na autenticação:', error)
        next(new Error('Token inválido'))
      }
    })
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: Socket) => {
      this.handleConnection(socket)
    })
  }

  private handleConnection(socket: Socket) {
    const user = socket.data.user
    console.log(`[WebSocket] Usuário conectado: ${user.email} (${socket.id})`)

    // Adicionar usuário à lista de conectados
    const socketUser: SocketUser = {
      userId: user.id,
      companyId: user.companyId,
      socketId: socket.id,
      connectedAt: new Date()
    }
    this.connectedUsers.set(socket.id, socketUser)

    // Entrar na sala da empresa
    socket.join(`company:${user.companyId}`)
    
    // Entrar na sala do usuário
    socket.join(`user:${user.id}`)

    // Enviar status de conexão
    socket.emit('connected', {
      message: 'Conectado com sucesso',
      timestamp: new Date().toISOString(),
      connectedUsers: this.getCompanyConnectedUsers(user.companyId)
    })

    // Notificar outros usuários da empresa sobre a conexão
    socket.to(`company:${user.companyId}`).emit('user_connected', {
      userId: user.id,
      name: `${user.firstName} ${user.lastName}`,
      timestamp: new Date().toISOString()
    })

    // Event handlers
    socket.on('join_conversation', (data) => {
      this.handleJoinConversation(socket, data)
    })

    socket.on('leave_conversation', (data) => {
      this.handleLeaveConversation(socket, data)
    })

    socket.on('typing_start', (data) => {
      this.handleTypingStart(socket, data)
    })

    socket.on('typing_stop', (data) => {
      this.handleTypingStop(socket, data)
    })

    socket.on('mark_as_read', (data) => {
      this.handleMarkAsRead(socket, data)
    })

    socket.on('disconnect', () => {
      this.handleDisconnection(socket)
    })
  }

  private handleJoinConversation(socket: Socket, data: { contactPhone: string; connectionId: string }) {
    const conversationRoom = `conversation:${data.connectionId}:${data.contactPhone}`
    socket.join(conversationRoom)
    
    console.log(`[WebSocket] Usuário ${socket.data.user.email} entrou na conversa: ${conversationRoom}`)
    
    socket.emit('conversation_joined', {
      contactPhone: data.contactPhone,
      connectionId: data.connectionId,
      timestamp: new Date().toISOString()
    })
  }

  private handleLeaveConversation(socket: Socket, data: { contactPhone: string; connectionId: string }) {
    const conversationRoom = `conversation:${data.connectionId}:${data.contactPhone}`
    socket.leave(conversationRoom)
    
    console.log(`[WebSocket] Usuário ${socket.data.user.email} saiu da conversa: ${conversationRoom}`)
  }

  private handleTypingStart(socket: Socket, data: { contactPhone: string; connectionId: string }) {
    const conversationRoom = `conversation:${data.connectionId}:${data.contactPhone}`
    
    socket.to(conversationRoom).emit('user_typing', {
      userId: socket.data.user.id,
      name: `${socket.data.user.firstName} ${socket.data.user.lastName}`,
      contactPhone: data.contactPhone,
      connectionId: data.connectionId,
      timestamp: new Date().toISOString()
    })
  }

  private handleTypingStop(socket: Socket, data: { contactPhone: string; connectionId: string }) {
    const conversationRoom = `conversation:${data.connectionId}:${data.contactPhone}`
    
    socket.to(conversationRoom).emit('user_stopped_typing', {
      userId: socket.data.user.id,
      contactPhone: data.contactPhone,
      connectionId: data.connectionId,
      timestamp: new Date().toISOString()
    })
  }

  private handleMarkAsRead(socket: Socket, data: { messageIds: string[] }) {
    const user = socket.data.user
    
    // Notificar outros usuários da empresa sobre as mensagens lidas
    socket.to(`company:${user.companyId}`).emit('messages_read', {
      messageIds: data.messageIds,
      readBy: {
        userId: user.id,
        name: `${user.firstName} ${user.lastName}`
      },
      timestamp: new Date().toISOString()
    })
  }

  private handleDisconnection(socket: Socket) {
    const socketUser = this.connectedUsers.get(socket.id)
    
    if (socketUser) {
      console.log(`[WebSocket] Usuário desconectado: ${socket.data.user?.email} (${socket.id})`)
      
      // Notificar outros usuários da empresa sobre a desconexão
      socket.to(`company:${socketUser.companyId}`).emit('user_disconnected', {
        userId: socketUser.userId,
        timestamp: new Date().toISOString()
      })
      
      // Remover da lista de conectados
      this.connectedUsers.delete(socket.id)
    }
  }

  // Métodos públicos para emitir eventos

  public emitNewMessage(event: MessageEvent) {
    const { companyId, connectionId, contactPhone } = event
    
    // Emitir para todos os usuários da empresa
    this.io.to(`company:${companyId}`).emit('new_message', event.data)
    
    // Emitir para usuários na conversa específica
    if (connectionId && contactPhone) {
      const conversationRoom = `conversation:${connectionId}:${contactPhone}`
      this.io.to(conversationRoom).emit('conversation_message', event.data)
    }
  }

  public emitMessageUpdate(event: MessageEvent) {
    const { companyId, connectionId, contactPhone } = event
    
    // Emitir para todos os usuários da empresa
    this.io.to(`company:${companyId}`).emit('message_update', event.data)
    
    // Emitir para usuários na conversa específica
    if (connectionId && contactPhone) {
      const conversationRoom = `conversation:${connectionId}:${contactPhone}`
      this.io.to(conversationRoom).emit('conversation_message_update', event.data)
    }
  }

  public emitConnectionUpdate(event: ConnectionEvent) {
    const { companyId, connectionId } = event
    
    // Emitir para todos os usuários da empresa
    this.io.to(`company:${companyId}`).emit('connection_update', {
      connectionId,
      ...event.data
    })
  }

  public emitQrCodeUpdate(event: ConnectionEvent) {
    const { companyId, connectionId } = event
    
    // Emitir para todos os usuários da empresa
    this.io.to(`company:${companyId}`).emit('qr_code_update', {
      connectionId,
      ...event.data
    })
  }

  public emitNotification(event: NotificationEvent) {
    const { data } = event
    
    if (data.userId) {
      // Notificação para usuário específico
      this.io.to(`user:${data.userId}`).emit('notification', data)
    } else {
      // Notificação para toda a empresa
      this.io.to(`company:${data.companyId}`).emit('notification', data)
    }
  }

  // Métodos utilitários

  public getConnectedUsers(): SocketUser[] {
    return Array.from(this.connectedUsers.values())
  }

  public getCompanyConnectedUsers(companyId: string): SocketUser[] {
    return Array.from(this.connectedUsers.values()).filter(user => user.companyId === companyId)
  }

  public getUserConnections(userId: string): SocketUser[] {
    return Array.from(this.connectedUsers.values()).filter(user => user.userId === userId)
  }

  public isUserOnline(userId: string): boolean {
    return Array.from(this.connectedUsers.values()).some(user => user.userId === userId)
  }

  public getConnectionStats() {
    const totalConnections = this.connectedUsers.size
    const companiesWithUsers = new Set(Array.from(this.connectedUsers.values()).map(user => user.companyId)).size
    
    return {
      totalConnections,
      companiesWithUsers,
      connectedUsers: this.getConnectedUsers()
    }
  }
}

// Singleton instance
let websocketService: WebSocketService | null = null

export function initializeWebSocket(server: HttpServer): WebSocketService {
  if (!websocketService) {
    websocketService = new WebSocketService(server)
    console.log('✅ WebSocket service initialized')
  }
  return websocketService
}

export function getWebSocketService(): WebSocketService | null {
  return websocketService
}
