import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type MessageDocument = Message & Document;
export declare enum MessageType {
    TEXT = "text",
    IMAGE = "image",
    AUDIO = "audio",
    VIDEO = "video",
    DOCUMENT = "document",
    LOCATION = "location",
    CONTACT = "contact",
    STICKER = "sticker",
    SYSTEM = "system"
}
export declare enum MessageDirection {
    INBOUND = "inbound",
    OUTBOUND = "outbound"
}
export declare enum MessageStatus {
    PENDING = "pending",
    SENT = "sent",
    DELIVERED = "delivered",
    READ = "read",
    FAILED = "failed"
}
export interface MessageMedia {
    type: string;
    url?: string;
    filename?: string;
    mimetype?: string;
    size?: number;
    caption?: string;
    thumbnail?: string;
}
export interface MessageLocation {
    latitude: number;
    longitude: number;
    address?: string;
    name?: string;
}
export interface MessageContact {
    name: string;
    phone: string;
    email?: string;
}
export declare class Message {
    messageId: string;
    type: MessageType;
    direction: MessageDirection;
    status: MessageStatus;
    content?: string;
    media?: MessageMedia;
    location?: MessageLocation;
    contact?: MessageContact;
    quotedMessageId?: string;
    metadata: Record<string, any>;
    timestamp: Date;
    sentAt?: Date;
    deliveredAt?: Date;
    readAt?: Date;
    errorMessage?: string;
    isAutomated: boolean;
    automationId?: string;
    isTemplate: boolean;
    templateName?: string;
    companyId: Types.ObjectId;
    whatsappConnectionId: string;
    contactPhone: string;
    sentBy?: string;
    conversationId?: string;
    isFirstMessage: boolean;
    responseTime?: number;
    tags: string[];
}
export declare const MessageSchema: mongoose.Schema<Message, mongoose.Model<Message, any, any, any, Document<unknown, any, Message, any> & Message & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Message, Document<unknown, {}, mongoose.FlatRecord<Message>, {}> & mongoose.FlatRecord<Message> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
