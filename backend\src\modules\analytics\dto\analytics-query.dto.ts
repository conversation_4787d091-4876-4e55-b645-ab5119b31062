import {
  IsOptional,
  IsEnum,
  IsDateString,
  IsString,
  Is<PERSON><PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Max,
  IsBoolean,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { EventType, EventCategory } from '../../../database/entities/analytics-event.schema';

export enum TimeRange {
  LAST_24_HOURS = 'last_24_hours',
  LAST_7_DAYS = 'last_7_days',
  LAST_30_DAYS = 'last_30_days',
  LAST_90_DAYS = 'last_90_days',
  LAST_YEAR = 'last_year',
  CUSTOM = 'custom',
}

export enum GroupBy {
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
}

export enum MetricType {
  COUNT = 'count',
  SUM = 'sum',
  AVERAGE = 'average',
  MIN = 'min',
  MAX = 'max',
  UNIQUE = 'unique',
}

export class AnalyticsQueryDto {
  @ApiPropertyOptional({
    description: 'Período de tempo',
    enum: TimeRange,
    default: TimeRange.LAST_7_DAYS,
  })
  @IsOptional()
  @IsEnum(TimeRange)
  timeRange?: TimeRange = TimeRange.LAST_7_DAYS;

  @ApiPropertyOptional({
    description: 'Data de início (para período customizado)',
    example: '2023-12-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de fim (para período customizado)',
    example: '2023-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'Tipos de eventos',
    enum: EventType,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EventType, { each: true })
  eventTypes?: EventType[];

  @ApiPropertyOptional({
    description: 'Categorias de eventos',
    enum: EventCategory,
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EventCategory, { each: true })
  categories?: EventCategory[];

  @ApiPropertyOptional({
    description: 'IDs de usuários',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  userIds?: string[];

  @ApiPropertyOptional({
    description: 'IDs de conexões WhatsApp',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  connectionIds?: string[];

  @ApiPropertyOptional({
    description: 'Telefones de contatos',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contactPhones?: string[];

  @ApiPropertyOptional({
    description: 'Agrupar por',
    enum: GroupBy,
    default: GroupBy.DAY,
  })
  @IsOptional()
  @IsEnum(GroupBy)
  groupBy?: GroupBy = GroupBy.DAY;

  @ApiPropertyOptional({
    description: 'Tipo de métrica',
    enum: MetricType,
    default: MetricType.COUNT,
  })
  @IsOptional()
  @IsEnum(MetricType)
  metricType?: MetricType = MetricType.COUNT;

  @ApiPropertyOptional({
    description: 'Campo para calcular métrica (para sum, average, etc.)',
    example: 'value',
  })
  @IsOptional()
  @IsString()
  metricField?: string;

  @ApiPropertyOptional({
    description: 'Filtros customizados (JSON)',
    example: '{"properties.messageType": "text"}',
  })
  @IsOptional()
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return {};
    }
  })
  filters?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Incluir comparação com período anterior',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  compareWithPrevious?: boolean = false;

  @ApiPropertyOptional({
    description: 'Limite de resultados',
    default: 100,
    minimum: 1,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(1000)
  limit?: number = 100;

  @ApiPropertyOptional({
    description: 'Página',
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  page?: number = 1;
}

export class DashboardQueryDto {
  @ApiPropertyOptional({
    description: 'Período de tempo',
    enum: TimeRange,
    default: TimeRange.LAST_7_DAYS,
  })
  @IsOptional()
  @IsEnum(TimeRange)
  timeRange?: TimeRange = TimeRange.LAST_7_DAYS;

  @ApiPropertyOptional({
    description: 'Data de início (para período customizado)',
    example: '2023-12-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Data de fim (para período customizado)',
    example: '2023-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    description: 'IDs de conexões WhatsApp',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  connectionIds?: string[];

  @ApiPropertyOptional({
    description: 'Incluir comparação com período anterior',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  compareWithPrevious?: boolean = true;
}

export class ReportQueryDto extends AnalyticsQueryDto {
  @ApiPropertyOptional({
    description: 'Formato do relatório',
    enum: ['json', 'csv', 'excel'],
    default: 'json',
  })
  @IsOptional()
  @IsEnum(['json', 'csv', 'excel'])
  format?: string = 'json';

  @ApiPropertyOptional({
    description: 'Incluir dados detalhados',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDetails?: boolean = false;

  @ApiPropertyOptional({
    description: 'Campos para incluir no relatório',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fields?: string[];
}
