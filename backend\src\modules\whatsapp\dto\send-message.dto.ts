import {
  IsString,
  IsOptional,
  IsPhoneNumber,
  IsEnum,
  IsObject,
  IsArray,
  ValidateNested,
  IsUrl,
  IsNumber,
  Min,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
  DOCUMENT = 'document',
  LOCATION = 'location',
  CONTACT = 'contact',
}

export class MediaDto {
  @ApiProperty({
    description: 'URL do arquivo de mídia',
    example: 'https://example.com/image.jpg',
  })
  @IsUrl({}, { message: 'URL deve ser válida' })
  url: string;

  @ApiPropertyOptional({
    description: 'Nome do arquivo',
    example: 'imagem.jpg',
  })
  @IsOptional()
  @IsString({ message: 'Nome do arquivo deve ser uma string' })
  filename?: string;

  @ApiPropertyOptional({
    description: '<PERSON>a da mídia',
    example: 'Confira nossa promoção!',
  })
  @IsOptional()
  @IsString({ message: 'Legenda deve ser uma string' })
  caption?: string;
}

export class LocationDto {
  @ApiProperty({
    description: 'Latitude',
    example: -23.5505,
  })
  @IsNumber({}, { message: 'Latitude deve ser um número' })
  @Min(-90, { message: 'Latitude deve ser maior que -90' })
  @Max(90, { message: 'Latitude deve ser menor que 90' })
  latitude: number;

  @ApiProperty({
    description: 'Longitude',
    example: -46.6333,
  })
  @IsNumber({}, { message: 'Longitude deve ser um número' })
  @Min(-180, { message: 'Longitude deve ser maior que -180' })
  @Max(180, { message: 'Longitude deve ser menor que 180' })
  longitude: number;

  @ApiPropertyOptional({
    description: 'Endereço',
    example: 'Av. Paulista, 1000 - São Paulo, SP',
  })
  @IsOptional()
  @IsString({ message: 'Endereço deve ser uma string' })
  address?: string;

  @ApiPropertyOptional({
    description: 'Nome do local',
    example: 'Shopping Center',
  })
  @IsOptional()
  @IsString({ message: 'Nome do local deve ser uma string' })
  name?: string;
}

export class ContactDto {
  @ApiProperty({
    description: 'Nome do contato',
    example: 'João Silva',
  })
  @IsString({ message: 'Nome deve ser uma string' })
  name: string;

  @ApiProperty({
    description: 'Telefone do contato',
    example: '+5511999999999',
  })
  @IsPhoneNumber('BR', { message: 'Telefone deve ser válido' })
  phone: string;

  @ApiPropertyOptional({
    description: 'Email do contato',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString({ message: 'Email deve ser uma string' })
  email?: string;
}

export class SendMessageDto {
  @ApiProperty({
    description: 'Número de telefone do destinatário',
    example: '+5511999999999',
  })
  @IsPhoneNumber('BR', { message: 'Número de telefone deve ser válido' })
  to: string;

  @ApiProperty({
    description: 'Tipo da mensagem',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  @IsEnum(MessageType, { message: 'Tipo de mensagem deve ser válido' })
  type: MessageType;

  @ApiPropertyOptional({
    description: 'Conteúdo da mensagem (para mensagens de texto)',
    example: 'Olá! Como posso ajudá-lo?',
  })
  @IsOptional()
  @IsString({ message: 'Conteúdo deve ser uma string' })
  content?: string;

  @ApiPropertyOptional({
    description: 'Dados de mídia',
    type: MediaDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MediaDto)
  media?: MediaDto;

  @ApiPropertyOptional({
    description: 'Dados de localização',
    type: LocationDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => LocationDto)
  location?: LocationDto;

  @ApiPropertyOptional({
    description: 'Dados de contato',
    type: ContactDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ContactDto)
  contact?: ContactDto;

  @ApiPropertyOptional({
    description: 'ID da mensagem sendo respondida',
    example: 'msg-123',
  })
  @IsOptional()
  @IsString({ message: 'ID da mensagem deve ser uma string' })
  quotedMessageId?: string;

  @ApiPropertyOptional({
    description: 'Metadados adicionais',
    example: { campaign: 'promo-natal', source: 'website' },
  })
  @IsOptional()
  @IsObject({ message: 'Metadados devem ser um objeto' })
  metadata?: Record<string, any>;
}

export class BulkMessageDto {
  @ApiProperty({
    description: 'Lista de números de telefone',
    example: ['+5511999999999', '+5511888888888'],
  })
  @IsArray({ message: 'Números devem ser um array' })
  @IsPhoneNumber('BR', { each: true, message: 'Cada número deve ser válido' })
  to: string[];

  @ApiProperty({
    description: 'Tipo da mensagem',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  @IsEnum(MessageType, { message: 'Tipo de mensagem deve ser válido' })
  type: MessageType;

  @ApiPropertyOptional({
    description: 'Conteúdo da mensagem (para mensagens de texto)',
    example: 'Olá! Confira nossa promoção especial!',
  })
  @IsOptional()
  @IsString({ message: 'Conteúdo deve ser uma string' })
  content?: string;

  @ApiPropertyOptional({
    description: 'Dados de mídia',
    type: MediaDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MediaDto)
  media?: MediaDto;

  @ApiPropertyOptional({
    description: 'Intervalo mínimo entre envios (segundos)',
    example: 5,
    default: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Intervalo mínimo deve ser um número' })
  @Min(1, { message: 'Intervalo mínimo deve ser pelo menos 1 segundo' })
  minInterval?: number;

  @ApiPropertyOptional({
    description: 'Intervalo máximo entre envios (segundos)',
    example: 15,
    default: 5,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Intervalo máximo deve ser um número' })
  @Min(1, { message: 'Intervalo máximo deve ser pelo menos 1 segundo' })
  maxInterval?: number;

  @ApiPropertyOptional({
    description: 'Metadados adicionais',
    example: { campaign: 'promo-natal', source: 'website' },
  })
  @IsOptional()
  @IsObject({ message: 'Metadados devem ser um objeto' })
  metadata?: Record<string, any>;
}
