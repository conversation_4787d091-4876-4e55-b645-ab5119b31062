import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { WhatsAppConnection, ConnectionStatus, ConnectionType, UserRole } from '../../database/entities';
import { CreateConnectionDto } from './dto/create-connection.dto';
import { UpdateConnectionDto } from './dto/update-connection.dto';
import { SendMessageDto, BulkMessageDto } from './dto/send-message.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { EvolutionApiService } from './services/evolution-api.service';

export interface FindConnectionsOptions {
  companyId?: string;
  status?: ConnectionStatus;
  type?: ConnectionType;
  assignedUserId?: string;
  search?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class WhatsAppService {
  private readonly logger = new Logger(WhatsAppService.name);

  constructor(
    @InjectRepository(WhatsAppConnection)
    private connectionRepository: Repository<WhatsAppConnection>,
    private evolutionApiService: EvolutionApiService,
    private configService: ConfigService,
  ) {}

  async createConnection(
    createConnectionDto: CreateConnectionDto,
    currentUser: AuthenticatedUser,
  ): Promise<WhatsAppConnection> {
    // Verificar se o usuário pode criar conexões na empresa especificada
    if (currentUser.canAccessCompany && !currentUser.canAccessCompany(createConnectionDto.companyId)) {
      throw new ForbiddenException('Não é possível criar conexão nesta empresa');
    }

    // Verificar se o número já existe
    const existingConnection = await this.connectionRepository.findOne({
      where: { phoneNumber: createConnectionDto.phoneNumber },
    });

    if (existingConnection) {
      throw new ConflictException('Número de telefone já está em uso');
    }

    // Criar conexão no banco
    const connection = this.connectionRepository.create({
      ...createConnectionDto,
      status: ConnectionStatus.PENDING,
      createdBy: currentUser.id,
    });

    const savedConnection = await this.connectionRepository.save(connection);

    // Criar instância na Evolution API (se for esse tipo)
    if (createConnectionDto.type === ConnectionType.EVOLUTION_API) {
      try {
        await this.createEvolutionInstance(savedConnection);
      } catch (error) {
        // Se falhar, deletar a conexão do banco
        await this.connectionRepository.delete(savedConnection.id);
        throw error;
      }
    }

    return savedConnection;
  }

  async findAll(
    options: FindConnectionsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    connections: WhatsAppConnection[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { companyId, status, type, assignedUserId, search, page = 1, limit = 10 } = options;

    const queryBuilder = this.connectionRepository.createQueryBuilder('connection')
      .leftJoinAndSelect('connection.company', 'company')
      .leftJoinAndSelect('connection.assignedUser', 'assignedUser');

    // Filtro por empresa baseado no usuário atual
    if (currentUser.role === UserRole.SUPER_ADMIN) {
      if (companyId) {
        queryBuilder.andWhere('connection.companyId = :companyId', { companyId });
      }
    } else if (currentUser.role === UserRole.AGENCY_ADMIN) {
      queryBuilder.andWhere(
        '(connection.companyId = :currentCompanyId OR connection.companyId IN (SELECT c.id FROM companies c WHERE c.agencyId = :currentCompanyId))',
        { currentCompanyId: currentUser.companyId }
      );
    } else {
      queryBuilder.andWhere('connection.companyId = :companyId', { companyId: currentUser.companyId });
    }

    // Filtros adicionais
    if (status) {
      queryBuilder.andWhere('connection.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('connection.type = :type', { type });
    }

    if (assignedUserId) {
      queryBuilder.andWhere('connection.assignedUserId = :assignedUserId', { assignedUserId });
    }

    if (search) {
      queryBuilder.andWhere(
        '(connection.name ILIKE :search OR connection.phoneNumber ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Paginação
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Ordenação
    queryBuilder.orderBy('connection.createdAt', 'DESC');

    const [connections, total] = await queryBuilder.getManyAndCount();

    return {
      connections,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<WhatsAppConnection> {
    const connection = await this.connectionRepository.findOne({
      where: { id },
      relations: ['company', 'assignedUser'],
    });

    if (!connection) {
      throw new NotFoundException('Conexão não encontrada');
    }

    // Verificar se o usuário atual pode acessar esta conexão
    if (currentUser.canAccessCompany && !currentUser.canAccessCompany(connection.companyId)) {
      throw new ForbiddenException('Acesso negado a esta conexão');
    }

    return connection;
  }

  async update(
    id: string,
    updateConnectionDto: UpdateConnectionDto,
    currentUser: AuthenticatedUser,
  ): Promise<WhatsAppConnection> {
    const connection = await this.findOne(id, currentUser);

    Object.assign(connection, updateConnectionDto);
    connection.updatedBy = currentUser.id;

    return this.connectionRepository.save(connection);
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const connection = await this.findOne(id, currentUser);

    // Desconectar da Evolution API se necessário
    if (connection.type === ConnectionType.EVOLUTION_API && connection.instanceId) {
      try {
        await this.evolutionApiService.deleteInstance(connection.instanceId);
      } catch (error) {
        this.logger.warn(`Failed to delete Evolution instance: ${error.message}`);
      }
    }

    await this.connectionRepository.softDelete(id);
  }

  async connect(id: string, currentUser: AuthenticatedUser): Promise<{ qrCode?: string }> {
    const connection = await this.findOne(id, currentUser);

    if (connection.status === ConnectionStatus.CONNECTED) {
      throw new BadRequestException('Conexão já está ativa');
    }

    if (connection.type === ConnectionType.EVOLUTION_API) {
      try {
        // Obter QR Code
        const qrCode = await this.evolutionApiService.getQRCode(connection.instanceId!);
        
        // Atualizar status
        connection.updateStatus(ConnectionStatus.CONNECTING);
        connection.qrCode = qrCode;
        await this.connectionRepository.save(connection);

        return { qrCode };
      } catch (error) {
        connection.updateStatus(ConnectionStatus.ERROR, error.message);
        await this.connectionRepository.save(connection);
        throw error;
      }
    }

    throw new BadRequestException('Tipo de conexão não suportado');
  }

  async disconnect(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const connection = await this.findOne(id, currentUser);

    if (connection.type === ConnectionType.EVOLUTION_API && connection.instanceId) {
      try {
        await this.evolutionApiService.logout(connection.instanceId);
        connection.updateStatus(ConnectionStatus.DISCONNECTED);
        await this.connectionRepository.save(connection);
      } catch (error) {
        connection.updateStatus(ConnectionStatus.ERROR, error.message);
        await this.connectionRepository.save(connection);
        throw error;
      }
    }
  }

  async sendMessage(
    connectionId: string,
    sendMessageDto: SendMessageDto,
    currentUser: AuthenticatedUser,
  ): Promise<any> {
    const connection = await this.findOne(connectionId, currentUser);

    if (!connection.isConnected) {
      throw new BadRequestException('Conexão não está ativa');
    }

    if (connection.type === ConnectionType.EVOLUTION_API) {
      const evolutionMessage = this.mapToEvolutionMessage(sendMessageDto);
      
      if (sendMessageDto.media) {
        return this.evolutionApiService.sendMediaMessage(connection.instanceId!, evolutionMessage);
      } else {
        return this.evolutionApiService.sendMessage(connection.instanceId!, evolutionMessage);
      }
    }

    throw new BadRequestException('Tipo de conexão não suportado');
  }

  async sendBulkMessage(
    connectionId: string,
    bulkMessageDto: BulkMessageDto,
    currentUser: AuthenticatedUser,
  ): Promise<Array<{ phoneNumber: string; success: boolean; result?: any; error?: string }>> {
    const connection = await this.findOne(connectionId, currentUser);

    if (!connection.isConnected) {
      throw new BadRequestException('Conexão não está ativa');
    }

    const results: Array<{ phoneNumber: string; success: boolean; result?: any; error?: string }> = [];
    const { minInterval = 1, maxInterval = 5 } = bulkMessageDto;

    for (const phoneNumber of bulkMessageDto.to) {
      try {
        const messageDto: SendMessageDto = {
          to: phoneNumber,
          type: bulkMessageDto.type,
          content: bulkMessageDto.content,
          media: bulkMessageDto.media,
          metadata: bulkMessageDto.metadata,
        };

        const result = await this.sendMessage(connectionId, messageDto, currentUser);
        results.push({ phoneNumber, success: true, result });

        // Aguardar intervalo aleatório entre envios
        if (bulkMessageDto.to.indexOf(phoneNumber) < bulkMessageDto.to.length - 1) {
          const delay = Math.random() * (maxInterval - minInterval) + minInterval;
          await new Promise(resolve => setTimeout(resolve, delay * 1000));
        }
      } catch (error) {
        results.push({ phoneNumber, success: false, error: error.message });
      }
    }

    return results;
  }

  async handleWebhook(data: any): Promise<void> {
    this.logger.log('Webhook received:', JSON.stringify(data, null, 2));

    // Processar diferentes tipos de eventos
    switch (data.event) {
      case 'CONNECTION_UPDATE':
        await this.handleConnectionUpdate(data);
        break;
      case 'QRCODE_UPDATED':
        await this.handleQRCodeUpdate(data);
        break;
      case 'MESSAGES_UPSERT':
        await this.handleMessageReceived(data);
        break;
      default:
        this.logger.log(`Unhandled webhook event: ${data.event}`);
    }
  }

  private async createEvolutionInstance(connection: WhatsAppConnection): Promise<void> {
    const instanceName = this.evolutionApiService.generateInstanceName(
      connection.companyId,
      connection.phoneNumber
    );

    const webhookUrl = `${this.configService.get('cors.origin')}/api/v1/whatsapp/webhook`;
    
    await this.evolutionApiService.createInstance(instanceName, webhookUrl);
    
    connection.instanceId = instanceName;
    await this.connectionRepository.save(connection);
  }

  private mapToEvolutionMessage(sendMessageDto: SendMessageDto): any {
    const message: any = {
      number: sendMessageDto.to.replace(/\D/g, ''), // Remove caracteres não numéricos
    };

    if (sendMessageDto.content) {
      message.text = sendMessageDto.content;
    }

    if (sendMessageDto.media) {
      message.media = {
        mediatype: sendMessageDto.type,
        media: sendMessageDto.media.url,
        fileName: sendMessageDto.media.filename,
        caption: sendMessageDto.media.caption,
      };
    }

    if (sendMessageDto.location) {
      message.location = sendMessageDto.location;
    }

    if (sendMessageDto.contact) {
      message.contact = {
        fullName: sendMessageDto.contact.name,
        wuid: sendMessageDto.contact.phone,
        phoneNumber: sendMessageDto.contact.phone,
      };
    }

    if (sendMessageDto.quotedMessageId) {
      message.quoted = {
        messageId: sendMessageDto.quotedMessageId,
      };
    }

    return message;
  }

  private async handleConnectionUpdate(data: any): Promise<void> {
    const connection = await this.connectionRepository.findOne({
      where: { instanceId: data.instanceName },
    });

    if (connection) {
      const newStatus = this.evolutionApiService.mapEvolutionStatusToConnectionStatus(data.data.state);
      connection.updateStatus(newStatus);
      await this.connectionRepository.save(connection);
    }
  }

  private async handleQRCodeUpdate(data: any): Promise<void> {
    const connection = await this.connectionRepository.findOne({
      where: { instanceId: data.instanceName },
    });

    if (connection) {
      connection.qrCode = data.data.qrcode;
      await this.connectionRepository.save(connection);
    }
  }

  private async handleMessageReceived(data: any): Promise<void> {
    // Aqui seria implementada a lógica para processar mensagens recebidas
    // Por enquanto, apenas log
    this.logger.log(`Message received for instance ${data.instanceName}`);
  }
}
