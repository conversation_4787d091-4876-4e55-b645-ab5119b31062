import { <PERSON><PERSON>ty, Column, ManyToOne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Company } from './company.entity';
import { User } from './user.entity';

export enum ConnectionType {
  EVOLUTION_API = 'evolution_api',
  META_OFFICIAL = 'meta_official',
}

export enum ConnectionStatus {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  ERROR = 'error',
  PENDING = 'pending',
}

@Entity('whatsapp_connections')
@Index(['phoneNumber'], { unique: true })
export class WhatsAppConnection extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 20, unique: true })
  phoneNumber: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: ConnectionType.EVOLUTION_API,
  })
  type: ConnectionType;

  @Column({
    type: 'varchar',
    length: 50,
    default: ConnectionStatus.PENDING,
  })
  status: ConnectionStatus;

  @Column({ type: 'text', nullable: true })
  qrCode: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  instanceId: string;

  @Column({ type: 'jsonb', nullable: true })
  connectionData: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  settings: Record<string, any>;

  @Column({ type: 'datetime', nullable: true })
  lastConnectedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  lastDisconnectedAt: Date;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Relacionamento com empresa
  @Column({ type: 'uuid' })
  companyId: string;

  @ManyToOne(() => Company, (company) => company.whatsappConnections)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  // Relacionamento com usuário responsável
  @Column({ type: 'uuid', nullable: true })
  assignedUserId: string;

  @ManyToOne(() => User, (user) => user.assignedWhatsAppConnections, { nullable: true })
  @JoinColumn({ name: 'assignedUserId' })
  assignedUser: User;

  // Campos calculados
  get isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED;
  }

  get displayName(): string {
    return `${this.name} (${this.phoneNumber})`;
  }

  get formattedPhoneNumber(): string {
    // Formatar número brasileiro: +55 (11) 99999-9999
    const cleaned = this.phoneNumber.replace(/\D/g, '');
    if (cleaned.length === 13 && cleaned.startsWith('55')) {
      const ddd = cleaned.substring(2, 4);
      const number = cleaned.substring(4);
      return `+55 (${ddd}) ${number.substring(0, 5)}-${number.substring(5)}`;
    }
    return this.phoneNumber;
  }

  // Métodos de controle
  updateStatus(status: ConnectionStatus, errorMessage?: string): void {
    this.status = status;
    if (errorMessage) {
      this.errorMessage = errorMessage;
    }
    
    if (status === ConnectionStatus.CONNECTED) {
      this.lastConnectedAt = new Date();
      this.errorMessage = undefined;
    } else if (status === ConnectionStatus.DISCONNECTED || status === ConnectionStatus.ERROR) {
      this.lastDisconnectedAt = new Date();
    }
  }

  canBeUsedBy(userId: string): boolean {
    return this.assignedUserId === userId || this.assignedUserId === null;
  }
}
