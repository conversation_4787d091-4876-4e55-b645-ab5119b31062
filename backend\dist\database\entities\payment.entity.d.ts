import { Company } from './company.entity';
import { Subscription } from './subscription.entity';
import { Invoice } from './invoice.entity';
export declare enum PaymentStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    SUCCEEDED = "succeeded",
    FAILED = "failed",
    CANCELLED = "cancelled",
    REFUNDED = "refunded",
    PARTIALLY_REFUNDED = "partially_refunded"
}
export declare enum PaymentMethod {
    CREDIT_CARD = "credit_card",
    DEBIT_CARD = "debit_card",
    BANK_TRANSFER = "bank_transfer",
    PIX = "pix",
    BOLETO = "boleto",
    PAYPAL = "paypal",
    WALLET = "wallet",
    CRYPTO = "crypto"
}
export declare enum PaymentGateway {
    STRIPE = "stripe",
    MERCADO_PAGO = "mercado_pago",
    PAGSEGURO = "pagseguro",
    PAYPAL = "paypal",
    ASAAS = "asaas",
    GERENCIANET = "gerencianet",
    MANUAL = "manual"
}
export interface PaymentMethodDetails {
    type: PaymentMethod;
    brand?: string;
    last4?: string;
    expiryMonth?: number;
    expiryYear?: number;
    holderName?: string;
    bankName?: string;
    accountType?: string;
    pixKey?: string;
    walletProvider?: string;
}
export interface RefundDetails {
    refundId: string;
    amount: number;
    reason: string;
    refundedAt: Date;
    gatewayRefundId?: string;
}
export declare class Payment {
    id: string;
    paymentNumber: string;
    companyId: string;
    company: Company;
    subscriptionId: string;
    subscription: Subscription;
    invoiceId: string;
    invoice: Invoice;
    status: PaymentStatus;
    paymentMethod: PaymentMethod;
    gateway: PaymentGateway;
    amount: number;
    feeAmount: number;
    netAmount: number;
    refundedAmount: number;
    currency: string;
    paymentMethodDetails: PaymentMethodDetails;
    processedAt: Date;
    failedAt: Date;
    refundedAt: Date;
    dueDate: Date;
    gatewayTransactionId: string;
    gatewayPaymentId: string;
    gatewayCustomerId: string;
    gatewayPaymentMethodId: string;
    failureCode: string | null;
    failureMessage: string | null;
    attemptCount: number;
    nextAttemptDate: Date | null;
    refunds: RefundDetails[];
    description: string;
    notes: string;
    metadata: Record<string, any>;
    emailSent: boolean;
    emailSentAt: Date;
    isReconciled: boolean;
    reconciledAt: Date;
    reconciledBy: string;
    taxDocumentNumber: string;
    taxDocumentUrl: string;
    createdAt: Date;
    updatedAt: Date;
    isSucceeded(): boolean;
    isFailed(): boolean;
    isPending(): boolean;
    isProcessing(): boolean;
    isRefunded(): boolean;
    canBeRefunded(): boolean;
    canBeRetried(): boolean;
    getRemainingRefundableAmount(): number;
    getRefundProgress(): number;
    markAsSucceeded(gatewayTransactionId?: string): void;
    markAsFailed(failureCode?: string, failureMessage?: string): void;
    addRefund(amount: number, reason: string, gatewayRefundId?: string): void;
    retry(): void;
    cancel(): void;
    reconcile(reconciledBy: string): void;
    generatePaymentNumber(): string;
    getPaymentMethodDisplay(): string;
    getDaysOverdue(): number;
}
