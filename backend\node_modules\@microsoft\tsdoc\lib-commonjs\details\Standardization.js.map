{"version": 3, "file": "Standardization.js", "sourceRoot": "", "sources": ["../../src/details/Standardization.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;GAGG;AACH,IAAY,eA8BX;AA9BD,WAAY,eAAe;IACzB;;;;;OAKG;IACH,gCAAa,CAAA;IAEb;;;;OAIG;IACH,wCAAqB,CAAA;IAErB;;;;;;;OAOG;IACH,kDAA+B,CAAA;IAE/B;;OAEG;IACH,gCAAa,CAAA;AACf,CAAC,EA9BW,eAAe,+BAAf,eAAe,QA8B1B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\r\n\r\n/**\r\n * Used to group the {@link StandardTags} definitions according to the level of support\r\n * expected from documentation tools that implement the standard.\r\n */\r\nexport enum Standardization {\r\n  /**\r\n   * TSDoc tags in the \"Core\" standardization group are considered essential.\r\n   * Their meaning is standardized, and every documentation tool is expected\r\n   * to recognize them.  The TSDoc parser library typically provides dedicated APIs\r\n   * for accessing these tags.\r\n   */\r\n  Core = 'Core',\r\n\r\n  /**\r\n   * TSDoc tags in the \"Extended\" standardization group are optional.  Documentation tools\r\n   * may or may not support them.  If they do, the syntax and semantics should conform to\r\n   * the TSDoc standard definitions.\r\n   */\r\n  Extended = 'Extended',\r\n\r\n  /**\r\n   * TSDoc tags in the \"Discretionary\" standardization group are optional.  Although the\r\n   * syntax is specified, the semantics for these tags are implementation-specific\r\n   * (and sometimes difficult to describe completely without referring to a specific\r\n   * implementation).  Discretionary tags are included in the TSDoc standard to ensure that\r\n   * if two different popular tools use the same tag name, developers can expect the syntax\r\n   * to be the same, and the semantics to be somewhat similar.\r\n   */\r\n  Discretionary = 'Discretionary',\r\n\r\n  /**\r\n   * The tag is not part of the TSDoc standard.  All used-defined tags are assigned to this group.\r\n   */\r\n  None = 'None'\r\n}\r\n"]}