import { BillingService, FindPlansOptions, FindSubscriptionsOptions } from './services/billing.service';
import { CreatePlanDto } from './dto/create-plan.dto';
import { CreateSubscriptionDto, UpdateSubscriptionDto, ChangePlanDto, CancelSubscriptionDto, SubscriptionUsageDto } from './dto/subscription-dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
export declare class BillingController {
    private readonly billingService;
    constructor(billingService: BillingService);
    createPlan(createPlanDto: CreatePlanDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Plan>;
    findAllPlans(query: FindPlansOptions): Promise<{
        plans: import("../../database/entities").Plan[];
        total: number;
        page: number;
        limit: number;
    }>;
    findPlan(id: string): Promise<import("../../database/entities").Plan>;
    updatePlan(id: string, updateData: Partial<CreatePlanDto>, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Plan>;
    deletePlan(id: string, currentUser: AuthenticatedUser): Promise<void>;
    createSubscription(createSubscriptionDto: CreateSubscriptionDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    findAllSubscriptions(query: FindSubscriptionsOptions, currentUser: AuthenticatedUser): Promise<{
        subscriptions: import("../../database/entities").Subscription[];
        total: number;
        page: number;
        limit: number;
    }>;
    getSubscriptionStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        trial: number;
        cancelled: number;
        expired: number;
        totalRevenue: number;
        monthlyRevenue: number;
    }>;
    findSubscription(id: string, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    updateSubscription(id: string, updateSubscriptionDto: UpdateSubscriptionDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    changePlan(id: string, changePlanDto: ChangePlanDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    cancelSubscription(id: string, cancelSubscriptionDto: CancelSubscriptionDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    updateUsage(id: string, usageDto: SubscriptionUsageDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities").Subscription>;
    getUsage(id: string, currentUser: AuthenticatedUser): Promise<{
        currentUsage: import("../../database/entities").UsageMetrics;
        usageHistory: Record<string, import("../../database/entities").UsageMetrics>;
        planLimits: import("../../database/entities").PlanFeatures;
    }>;
    checkUsageLimits(id: string, currentUser: AuthenticatedUser): Promise<{
        status: string;
        limits: {};
        usage: {};
        lastUpdated?: undefined;
    } | {
        status: string;
        limits: {
            connections: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
            contacts: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
            messages: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
            users: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
            automations: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
            storage: {
                current: number;
                limit: number;
                percentage: number;
                exceeded: boolean;
            };
        };
        lastUpdated: Date;
        usage?: undefined;
    }>;
}
