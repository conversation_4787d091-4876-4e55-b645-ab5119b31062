import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { 
  Automation, 
  AutomationDocument, 
  AutomationStatus,
  TriggerType,
  ActionType 
} from '../../../database/entities/automation.schema';
import { 
  AutomationExecution, 
  AutomationExecutionDocument, 
  ExecutionStatus,
  StepStatus,
  ExecutionContext,
  ExecutionStep 
} from '../../../database/entities/automation-execution.schema';
import { TrackingService } from '../../analytics/services/tracking.service';
import { WhatsAppService } from '../../whatsapp/whatsapp.service';
import { ContactsService } from '../../messages/services/contacts.service';
import { MessagesService } from '../../messages/services/messages.service';

export interface TriggerContext {
  type: TriggerType;
  contactPhone: string;
  connectionId: string;
  companyId: string;
  messageId?: string;
  messageContent?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class AutomationExecutorService {
  private readonly logger = new Logger(AutomationExecutorService.name);

  constructor(
    @InjectModel(Automation.name)
    private automationModel: Model<AutomationDocument>,
    @InjectModel(AutomationExecution.name)
    private executionModel: Model<AutomationExecutionDocument>,
    private trackingService: TrackingService,
    private whatsappService: WhatsAppService,
    private contactsService: ContactsService,
    private messagesService: MessagesService,
  ) {}

  async triggerAutomations(context: TriggerContext): Promise<void> {
    try {
      // Buscar automações ativas que podem ser disparadas
      const automations = await this.findTriggeredAutomations(context);

      this.logger.log(`Found ${automations.length} automations to trigger for ${context.contactPhone}`);

      // Executar cada automação
      for (const automation of automations) {
        try {
          await this.executeAutomation(automation, context);
        } catch (error) {
          this.logger.error(`Failed to execute automation ${automation._id}: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to trigger automations: ${error.message}`);
    }
  }

  async executeAutomation(
    automation: AutomationDocument,
    triggerContext: TriggerContext,
  ): Promise<AutomationExecutionDocument> {
    // Verificar se a automação pode ser executada
    if (automation.status !== AutomationStatus.ACTIVE) {
      throw new Error('Automation is not active');
    }

    // Buscar dados do contato
    const contact = await this.contactsService.findByPhone(
      triggerContext.contactPhone,
      triggerContext.connectionId,
      { companyId: triggerContext.companyId } as any,
    );

    if (!contact) {
      throw new Error('Contact not found');
    }

    // Criar contexto de execução
    const executionContext: ExecutionContext = {
      contact: {
        id: (contact as any)._id.toString(),
        phoneNumber: contact.phoneNumber,
        name: contact.name,
        tags: contact.tags?.map(tag => tag.name) || [],
        customFields: contact.customFields || {},
      },
      connection: {
        id: triggerContext.connectionId,
        phoneNumber: '', // Será preenchido pelo serviço WhatsApp
        name: '',
      },
      variables: {},
      session: {},
    };

    if (triggerContext.messageId) {
      const message = await this.messagesService.findByMessageId(triggerContext.messageId);
      if (message) {
        executionContext.message = {
          id: (message as any)._id.toString(),
          content: message.content,
          type: message.type,
          timestamp: message.timestamp,
        };
      }
    }

    // Criar execução
    const execution = new this.executionModel({
      automationId: automation._id,
      automationName: automation.name,
      automationVersion: automation.version,
      companyId: new Types.ObjectId(triggerContext.companyId),
      status: ExecutionStatus.PENDING,
      context: executionContext,
      triggerType: triggerContext.type,
      triggerData: triggerContext.metadata || {},
      executionId: this.generateExecutionId(),
      startedAt: new Date(),
    });

    await execution.save();

    // Rastrear início da automação (implementação simplificada)
    this.logger.log(`Tracking automation triggered: ${automation._id}`);

    // Executar passos
    try {
      await this.executeSteps(automation, execution);
      
      execution.status = ExecutionStatus.COMPLETED;
      execution.completedAt = new Date();
      execution.duration = execution.completedAt.getTime() - (execution.startedAt?.getTime() || 0);

      automation.executionCount += 1;
      automation.successCount += 1;
      automation.lastExecutedAt = new Date();
      
      await Promise.all([execution.save(), automation.save()]);

      // Rastrear conclusão (implementação simplificada)
      this.logger.log(`Tracking automation completed: ${automation._id}`);

      this.logger.log(`Automation executed successfully: ${automation._id}`);
    } catch (error) {
      execution.status = ExecutionStatus.FAILED;
      execution.completedAt = new Date();
      execution.duration = execution.completedAt.getTime() - (execution.startedAt?.getTime() || 0);
      execution.error = {
        code: error.code || 'EXECUTION_FAILED',
        message: error.message || 'Execution failed',
        stack: error.stack,
        stepId: execution.currentStepId,
      };

      automation.executionCount += 1;
      automation.errorCount += 1;
      automation.lastExecutedAt = new Date();
      
      await Promise.all([execution.save(), automation.save()]);

      this.logger.error(`Automation execution failed: ${automation._id} - ${error.message}`);
      throw error;
    }

    return execution;
  }

  async retryExecution(executionId: string): Promise<void> {
    const execution = await this.executionModel.findOne({ executionId });
    
    if (!execution) {
      throw new Error('Execution not found');
    }

    if (execution.status !== ExecutionStatus.FAILED ||
        execution.retryCount >= execution.maxRetries ||
        (execution.nextRetryAt && new Date() < execution.nextRetryAt)) {
      throw new Error('Execution cannot be retried');
    }

    const automation = await this.automationModel.findById(execution.automationId);
    
    if (!automation) {
      throw new Error('Automation not found');
    }

    execution.retryCount += 1;
    execution.nextRetryAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes
    execution.status = ExecutionStatus.PENDING;
    await execution.save();

    // Re-executar
    try {
      await this.executeSteps(automation, execution);
      execution.status = ExecutionStatus.COMPLETED;
      execution.completedAt = new Date();
    } catch (error) {
      execution.status = ExecutionStatus.FAILED;
      execution.completedAt = new Date();
      execution.error = {
        code: error.code || 'RETRY_FAILED',
        message: error.message,
        stack: error.stack,
      };
    }

    await execution.save();
  }

  private async findTriggeredAutomations(context: TriggerContext): Promise<AutomationDocument[]> {
    const filter: any = {
      companyId: new Types.ObjectId(context.companyId),
      status: AutomationStatus.ACTIVE,
      'trigger.type': context.type,
    };

    // Filtros específicos por tipo de gatilho
    switch (context.type) {
      case TriggerType.KEYWORD_MATCH:
        if (context.messageContent) {
          const keywords = await this.extractKeywords(context.messageContent);
          filter['trigger.keywords'] = { $in: keywords };
        }
        break;
      case TriggerType.FIRST_MESSAGE:
        // Verificar se é realmente a primeira mensagem
        const messageCount = await this.messagesService.findAll(
          { contactPhone: context.contactPhone, whatsappConnectionId: context.connectionId },
          { companyId: context.companyId } as any,
        );
        if (messageCount.total > 1) {
          return [];
        }
        break;
    }

    const automations = await this.automationModel.find(filter);

    // Filtrar automações que podem ser executadas
    return automations.filter(automation => {
      // Verificar configurações específicas
      if (automation.settings?.allowedConnections && automation.settings.allowedConnections.length > 0) {
        return automation.settings.allowedConnections.includes(context.connectionId);
      }
      return true;
    });
  }

  private async executeSteps(
    automation: AutomationDocument,
    execution: AutomationExecutionDocument,
  ): Promise<void> {
    execution.status = ExecutionStatus.RUNNING;
    await execution.save();

    if (automation.actions && automation.actions.length > 0) {
      // Executar ações sequenciais
      await this.executeActionSteps(automation.actions, execution);
    } else if (automation.flowSteps && automation.flowSteps.length > 0) {
      // Executar fluxo
      await this.executeFlowSteps(automation.flowSteps, automation.startStepId!, execution);
    }
  }

  private async executeActionSteps(
    actions: any[],
    execution: AutomationExecutionDocument,
  ): Promise<void> {
    for (const action of actions) {
      const step: ExecutionStep = {
        stepId: action.id,
        stepName: action.name,
        status: StepStatus.PENDING,
        startedAt: new Date(),
      };

      execution.steps.push(step);
      execution.totalSteps = execution.steps.length;
      execution.currentStepId = action.id;
      await execution.save();

      try {
        const result = await this.executeAction(action, execution);
        
        step.status = StepStatus.COMPLETED;
        step.completedAt = new Date();
        step.duration = step.completedAt.getTime() - step.startedAt!.getTime();
        step.output = result;

        const stepIndex = execution.steps.findIndex(s => s.stepId === action.id);
        if (stepIndex !== -1) {
          Object.assign(execution.steps[stepIndex], step);
          execution.completedSteps = execution.steps.filter(s => s.status === StepStatus.COMPLETED).length;
        }
        await execution.save();

        // Delay se configurado
        if (action.delay && action.delay > 0) {
          await this.delay(action.delay * 1000);
        }
      } catch (error) {
        step.status = StepStatus.FAILED;
        step.completedAt = new Date();
        step.error = {
          code: error.code || 'ACTION_FAILED',
          message: error.message,
          stack: error.stack,
        };

        const stepIndex = execution.steps.findIndex(s => s.stepId === action.id);
        if (stepIndex !== -1) {
          Object.assign(execution.steps[stepIndex], step);
          execution.failedSteps = execution.steps.filter(s => s.status === StepStatus.FAILED).length;
        }
        await execution.save();
        
        throw error;
      }
    }
  }

  private async executeFlowSteps(
    flowSteps: any[],
    startStepId: string,
    execution: AutomationExecutionDocument,
  ): Promise<void> {
    let currentStepId = startStepId;
    const stepMap = new Map(flowSteps.map(step => [step.id, step]));

    while (currentStepId) {
      const flowStep = stepMap.get(currentStepId);
      if (!flowStep) break;

      const step: ExecutionStep = {
        stepId: flowStep.id,
        stepName: flowStep.name,
        status: StepStatus.PENDING,
        startedAt: new Date(),
      };

      execution.steps.push(step);
      execution.totalSteps = execution.steps.length;
      execution.currentStepId = flowStep.id;
      await execution.save();

      try {
        const result = await this.executeFlowStep(flowStep, execution);
        
        step.status = StepStatus.COMPLETED;
        step.completedAt = new Date();
        step.duration = step.completedAt.getTime() - step.startedAt!.getTime();
        step.output = result;
        step.nextStepId = result.nextStepId;

        const stepIndex = execution.steps.findIndex(s => s.stepId === flowStep.id);
        if (stepIndex !== -1) {
          Object.assign(execution.steps[stepIndex], step);
          execution.completedSteps = execution.steps.filter(s => s.status === StepStatus.COMPLETED).length;
        }
        await execution.save();

        currentStepId = result.nextStepId;
      } catch (error) {
        step.status = StepStatus.FAILED;
        step.completedAt = new Date();
        step.error = {
          code: error.code || 'FLOW_STEP_FAILED',
          message: error.message,
          stack: error.stack,
        };

        const stepIndex = execution.steps.findIndex(s => s.stepId === flowStep.id);
        if (stepIndex !== -1) {
          Object.assign(execution.steps[stepIndex], step);
          execution.failedSteps = execution.steps.filter(s => s.status === StepStatus.FAILED).length;
        }
        await execution.save();
        
        throw error;
      }
    }
  }

  private async executeAction(action: any, execution: AutomationExecutionDocument): Promise<any> {
    switch (action.type) {
      case ActionType.SEND_MESSAGE:
        return this.executeSendMessage(action.config, execution);
      case ActionType.ADD_TAG:
        return this.executeAddTag(action.config, execution);
      case ActionType.REMOVE_TAG:
        return this.executeRemoveTag(action.config, execution);
      case ActionType.UPDATE_CONTACT:
        return this.executeUpdateContact(action.config, execution);
      case ActionType.WAIT:
        return this.executeWait(action.config, execution);
      default:
        throw new Error(`Unsupported action type: ${action.type}`);
    }
  }

  private async executeFlowStep(flowStep: any, execution: AutomationExecutionDocument): Promise<any> {
    switch (flowStep.type) {
      case 'message':
        await this.executeSendMessage(flowStep.config.message, execution);
        return { nextStepId: flowStep.nextSteps?.[0] };
      case 'condition':
        const conditionResult = await this.evaluateCondition(flowStep.config.condition, execution);
        return { 
          nextStepId: conditionResult ? 
            flowStep.config.condition.trueStepId : 
            flowStep.config.condition.falseStepId 
        };
      case 'action':
        await this.executeAction(flowStep.config.action, execution);
        return { nextStepId: flowStep.nextSteps?.[0] };
      case 'ai':
        const aiResponse = await this.executeAIInteraction(flowStep.config.ai, execution);
        return { nextStepId: flowStep.nextSteps?.[0], aiResponse };
      case 'human_handoff':
        await this.executeHumanHandoff(execution);
        return { nextStepId: null }; // Fim do fluxo
      default:
        throw new Error(`Unsupported flow step type: ${flowStep.type}`);
    }
  }

  // Implementações específicas dos executores

  private async executeSendMessage(config: any, execution: AutomationExecutionDocument): Promise<any> {
    // Implementação simplificada - em produção, usar WhatsAppService
    this.logger.log(`Sending message: ${config.content} to ${execution.context.contact.phoneNumber}`);
    
    // Simular envio
    await this.delay(1000);
    
    return { messageId: `msg_${Date.now()}`, sent: true };
  }

  private async executeAddTag(config: any, execution: AutomationExecutionDocument): Promise<any> {
    // Implementação simplificada
    this.logger.log(`Adding tag: ${config.tagName} to ${execution.context.contact.phoneNumber}`);
    return { tagAdded: config.tagName };
  }

  private async executeRemoveTag(config: any, execution: AutomationExecutionDocument): Promise<any> {
    // Implementação simplificada
    this.logger.log(`Removing tag: ${config.tagName} from ${execution.context.contact.phoneNumber}`);
    return { tagRemoved: config.tagName };
  }

  private async executeUpdateContact(config: any, execution: AutomationExecutionDocument): Promise<any> {
    // Implementação simplificada
    this.logger.log(`Updating contact: ${execution.context.contact.phoneNumber}`);
    return { contactUpdated: true, fields: config.fields };
  }

  private async executeWait(config: any, execution: AutomationExecutionDocument): Promise<any> {
    const waitTime = config.seconds * 1000;
    await this.delay(waitTime);
    return { waited: config.seconds };
  }

  private async evaluateCondition(condition: any, execution: AutomationExecutionDocument): Promise<boolean> {
    // Implementação simplificada de avaliação de condições
    const fieldValue = this.getFieldValue(condition.field, execution);
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'contains':
        return String(fieldValue).includes(condition.value);
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      default:
        return false;
    }
  }

  private async executeAIInteraction(config: any, execution: AutomationExecutionDocument): Promise<string> {
    // Implementação simplificada - em produção, integrar com OpenAI/Google/Anthropic
    this.logger.log(`AI interaction with ${config.provider} model ${config.model}`);
    
    // Simular resposta da IA
    await this.delay(2000);
    
    return `AI response from ${config.provider}: This is a simulated response to the prompt.`;
  }

  private async executeHumanHandoff(execution: AutomationExecutionDocument): Promise<void> {
    // Implementação simplificada - em produção, notificar agentes humanos
    this.logger.log(`Human handoff for ${execution.context.contact.phoneNumber}`);
    
    execution.results.humanHandoff = true;
  }

  // Métodos auxiliares

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async extractKeywords(message: string): Promise<string[]> {
    // Implementação simplificada - em produção, usar NLP
    return message.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  }

  private getFieldValue(field: string, execution: AutomationExecutionDocument): any {
    const parts = field.split('.');
    let value: any = execution.context;
    
    for (const part of parts) {
      value = value?.[part];
    }
    
    return value;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
