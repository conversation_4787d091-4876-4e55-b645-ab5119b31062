"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Subscription = exports.SubscriptionPlan = exports.SubscriptionStatus = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("./base.entity");
const company_entity_1 = require("./company.entity");
var SubscriptionStatus;
(function (SubscriptionStatus) {
    SubscriptionStatus["ACTIVE"] = "active";
    SubscriptionStatus["INACTIVE"] = "inactive";
    SubscriptionStatus["PAST_DUE"] = "past_due";
    SubscriptionStatus["CANCELED"] = "canceled";
    SubscriptionStatus["TRIALING"] = "trialing";
})(SubscriptionStatus || (exports.SubscriptionStatus = SubscriptionStatus = {}));
var SubscriptionPlan;
(function (SubscriptionPlan) {
    SubscriptionPlan["BASIC"] = "basic";
    SubscriptionPlan["PROFESSIONAL"] = "professional";
    SubscriptionPlan["ENTERPRISE"] = "enterprise";
})(SubscriptionPlan || (exports.SubscriptionPlan = SubscriptionPlan = {}));
let Subscription = class Subscription extends base_entity_1.BaseEntity {
    plan;
    status;
    basePrice;
    pricePerUser;
    includedUsers;
    additionalUsers;
    totalAmount;
    currentPeriodStart;
    currentPeriodEnd;
    trialStart;
    trialEnd;
    canceledAt;
    cancelAtPeriodEnd;
    stripeSubscriptionId;
    stripeCustomerId;
    metadata;
    companyId;
    company;
    get isActive() {
        return this.status === SubscriptionStatus.ACTIVE;
    }
    get isTrialing() {
        return this.status === SubscriptionStatus.TRIALING;
    }
    get isPastDue() {
        return this.status === SubscriptionStatus.PAST_DUE;
    }
    get isCanceled() {
        return this.status === SubscriptionStatus.CANCELED;
    }
    get daysUntilExpiry() {
        const now = new Date();
        const expiry = this.isTrialing ? this.trialEnd : this.currentPeriodEnd;
        if (!expiry)
            return 0;
        const diffTime = expiry.getTime() - now.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    get isExpired() {
        const now = new Date();
        const expiry = this.isTrialing ? this.trialEnd : this.currentPeriodEnd;
        return expiry ? now > expiry : false;
    }
    calculateTotalAmount(activeUsers) {
        const additionalUsers = Math.max(0, activeUsers - this.includedUsers);
        return this.basePrice + (additionalUsers * this.pricePerUser);
    }
    updatePricing(activeUsers) {
        this.additionalUsers = Math.max(0, activeUsers - this.includedUsers);
        this.totalAmount = this.calculateTotalAmount(activeUsers);
    }
    startTrial(days = 14) {
        const now = new Date();
        this.trialStart = now;
        this.trialEnd = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));
        this.status = SubscriptionStatus.TRIALING;
    }
    activate() {
        this.status = SubscriptionStatus.ACTIVE;
        if (this.isTrialing) {
            this.currentPeriodStart = new Date();
            this.currentPeriodEnd = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000));
        }
    }
    cancel(immediately = false) {
        this.canceledAt = new Date();
        if (immediately) {
            this.status = SubscriptionStatus.CANCELED;
        }
        else {
            this.cancelAtPeriodEnd = true;
        }
    }
};
exports.Subscription = Subscription;
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SubscriptionPlan,
        default: SubscriptionPlan.BASIC,
    }),
    __metadata("design:type", String)
], Subscription.prototype, "plan", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SubscriptionStatus,
        default: SubscriptionStatus.TRIALING,
    }),
    __metadata("design:type", String)
], Subscription.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Subscription.prototype, "basePrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Subscription.prototype, "pricePerUser", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], Subscription.prototype, "includedUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', default: 0 }),
    __metadata("design:type", Number)
], Subscription.prototype, "additionalUsers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Subscription.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    __metadata("design:type", Date)
], Subscription.prototype, "currentPeriodStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone' }),
    __metadata("design:type", Date)
], Subscription.prototype, "currentPeriodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", Date)
], Subscription.prototype, "trialStart", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", Date)
], Subscription.prototype, "trialEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", Date)
], Subscription.prototype, "canceledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp with time zone', nullable: true }),
    __metadata("design:type", Boolean)
], Subscription.prototype, "cancelAtPeriodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Subscription.prototype, "stripeSubscriptionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Subscription.prototype, "stripeCustomerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Subscription.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], Subscription.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company, (company) => company.subscriptions),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], Subscription.prototype, "company", void 0);
exports.Subscription = Subscription = __decorate([
    (0, typeorm_1.Entity)('subscriptions')
], Subscription);
//# sourceMappingURL=subscription.entity.js.map