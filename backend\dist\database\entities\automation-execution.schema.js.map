{"version": 3, "file": "automation-execution.schema.js", "sourceRoot": "", "sources": ["../../../src/database/entities/automation-execution.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAI3C,IAAY,eAOX;AAPD,WAAY,eAAe;IACzB,sCAAmB,CAAA;IACnB,sCAAmB,CAAA;IACnB,0CAAuB,CAAA;IACvB,oCAAiB,CAAA;IACjB,0CAAuB,CAAA;IACvB,oCAAiB,CAAA;AACnB,CAAC,EAPW,eAAe,+BAAf,eAAe,QAO1B;AAED,IAAY,UAMX;AAND,WAAY,UAAU;IACpB,iCAAmB,CAAA;IACnB,iCAAmB,CAAA;IACnB,qCAAuB,CAAA;IACvB,+BAAiB,CAAA;IACjB,iCAAmB,CAAA;AACrB,CAAC,EANW,UAAU,0BAAV,UAAU,QAMrB;AAiDM,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAE9B,YAAY,CAAiB;IAG7B,cAAc,CAAS;IAGvB,iBAAiB,CAAS;IAG1B,SAAS,CAAiB;IAG1B,MAAM,CAAkB;IAGxB,OAAO,CAAmB;IAG1B,KAAK,CAAkB;IAGvB,aAAa,CAAU;IAGvB,SAAS,CAAQ;IAGjB,WAAW,CAAQ;IAGnB,QAAQ,CAAU;IAGlB,KAAK,CAKH;IAGF,UAAU,CAAS;IAGnB,UAAU,CAAS;IAGnB,WAAW,CAAQ;IAGnB,WAAW,CAAU;IAGrB,WAAW,CAAS;IAGpB,WAAW,CAAsB;IAIjC,OAAO,CASL;IAIF,UAAU,CAAS;IAGnB,cAAc,CAAS;IAGvB,WAAW,CAAS;IAGpB,YAAY,CAAS;IAIrB,QAAQ,CAAsB;IAG9B,WAAW,CAAS;IAGpB,MAAM,CAAU;IAGhB,iBAAiB,CAAU;IAG3B,iBAAiB,CAAW;CAC7B,CAAA;AArGY,kDAAmB;AAE9B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;8BACpD,gBAAK,CAAC,QAAQ;yDAAC;AAG7B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2DACF;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;8DACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;8BACpD,gBAAK,CAAC,QAAQ;sDAAC;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,eAAe,CAAC,OAAO,EAAE,CAAC;;mDACxE;AAGxB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;kDACf;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;0DACA;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACT,IAAI;sDAAC;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACP,IAAI;wDAAC;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;qDACL;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;kDAMrB;AAGF;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uDAChB;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uDAChB;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACP,IAAI;wDAAC;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDACF;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDACH;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;wDACH;AAIjC;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;oDAUlC;AAIF;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uDAChB;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2DACZ;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;wDACf;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;yDACd;AAIrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;qDACN;AAG9B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;wDACH;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;mDACxB;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;8DACI;AAG3B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;8DACV;8BApGjB,mBAAmB;IAD/B,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;GAChB,mBAAmB,CAqG/B;AAEY,QAAA,yBAAyB,GAAG,wBAAa,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;AAG3F,iCAAyB,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AAChE,iCAAyB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7D,iCAAyB,CAAC,KAAK,CAAC,EAAE,6BAA6B,EAAE,CAAC,EAAE,CAAC,CAAC;AACtE,iCAAyB,CAAC,KAAK,CAAC,EAAE,uBAAuB,EAAE,CAAC,EAAE,CAAC,CAAC;AAChE,iCAAyB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnD,iCAAyB,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACrD,iCAAyB,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACtE,iCAAyB,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AACpD,iCAAyB,CAAC,KAAK,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AAGpD,iCAAyB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,kBAAkB,EAAE,OAAO,EAAE,CAAC,CAAC;AAGnF,iCAAyB,CAAC,OAAO,CAAC,OAAO,GAAG,UAAS,IAAmB;IACtE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACtC,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,UAAU,GAAG,UAAS,MAAc,EAAE,OAA+B;IACrG,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;IACvE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;QACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;QAG9C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QACvF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;QACjF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IACrF,CAAC;AACH,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,cAAc,GAAG;IACjD,IAAI,CAAC,IAAI,CAAC,aAAa;QAAE,OAAO,IAAI,CAAC;IACrC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;AAC7E,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,WAAW,GAAG;IAC9C,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;IAC1C,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU;QAAE,OAAO,IAAI,CAAC;IACzD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;AACjF,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,eAAe,GAAG;IAClD,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC;IACxC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AAChF,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,YAAY,GAAG,UAAS,KAAU;IAClE,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;IACrC,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9E,IAAI,CAAC,KAAK,GAAG;QACX,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,kBAAkB;QACtC,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,kBAAkB;QAC5C,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,MAAM,EAAE,IAAI,CAAC,aAAa;KAC3B,CAAC;AACJ,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,QAAQ,GAAG;IAC3C,OAAO,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM;QACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;QACjC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/D,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,aAAa,GAAG,UAAS,eAAuB,CAAC;IACjF,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;IACrB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACnE,IAAI,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC;AACxC,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,WAAW,GAAG;IAC9C,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC;IACpC,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;AACvD,CAAC,CAAC;AAEF,iCAAyB,CAAC,OAAO,CAAC,WAAW,GAAG;IAC9C,IAAI,CAAC,IAAI,CAAC,SAAS;QAAE,OAAO,CAAC,CAAC;IAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,EAAE,CAAC;IAC/C,OAAO,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;AACtD,CAAC,CAAC"}