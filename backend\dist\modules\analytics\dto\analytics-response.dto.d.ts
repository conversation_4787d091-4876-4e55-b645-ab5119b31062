export declare class MetricDataPoint {
    timestamp: string;
    value: number;
    previousValue?: number;
    changePercent?: number;
    metadata?: Record<string, any>;
}
export declare class AnalyticsMetric {
    name: string;
    value: number;
    previousValue?: number;
    changePercent?: number;
    trend?: string;
    timeSeries: MetricDataPoint[];
    unit?: string;
    format?: string;
}
export declare class DashboardMetrics {
    messagesSent: AnalyticsMetric;
    messagesReceived: AnalyticsMetric;
    deliveryRate: AnalyticsMetric;
    readRate: AnalyticsMetric;
    averageResponseTime: AnalyticsMetric;
    newContacts: AnalyticsMetric;
    leadsConverted: AnalyticsMetric;
    conversionRate: AnalyticsMetric;
    activeConnections: AnalyticsMetric;
    activeUsers: AnalyticsMetric;
}
export declare class ConversationAnalytics {
    totalConversations: number;
    activeConversations: number;
    averageConversationDuration: number;
    averageMessagesPerConversation: number;
    firstResponseRate: number;
    averageFirstResponseTime: number;
}
export declare class UserActivityAnalytics {
    activeToday: number;
    activeThisWeek: number;
    activeThisMonth: number;
    averageSessionDuration: number;
    activityByHour: MetricDataPoint[];
    activityByDayOfWeek: MetricDataPoint[];
}
export declare class PerformanceAnalytics {
    averageApiResponseTime: number;
    errorRate: number;
    uptime: number;
    requestsPerMinute: number;
    activeWebSocketConnections: number;
}
export declare class AnalyticsReportResponse {
    dashboard: DashboardMetrics;
    conversations: ConversationAnalytics;
    userActivity: UserActivityAnalytics;
    performance: PerformanceAnalytics;
    period: {
        start: string;
        end: string;
    };
    generatedAt: string;
}
