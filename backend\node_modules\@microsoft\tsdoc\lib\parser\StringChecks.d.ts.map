{"version": 3, "file": "StringChecks.d.ts", "sourceRoot": "", "sources": ["../../src/parser/StringChecks.ts"], "names": [], "mappings": "AAGA;;GAEG;AACH,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAgC;IAE3E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAoC;IAC5E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAqC;IASlF,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAkC;IAMzE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAA0B;IAG1E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,4BAA4B,CAAoB;IAKxE,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,uBAAuB,CAAqD;IAEpG,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAcrC;IAEH;;;;OAIG;WACW,4BAA4B,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAY/E;;;;OAIG;WACW,oBAAoB,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAOzD;;;OAGG;WACW,uBAAuB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAiBtE;;OAEG;WACW,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAQ5E;;OAEG;WACW,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAOtD;;OAEG;WACW,2BAA2B,CAAC,WAAW,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAYlF;;OAEG;WACW,0BAA0B,CACtC,UAAU,EAAE,MAAM,EAClB,qBAAqB,EAAE,OAAO,GAC7B,MAAM,GAAG,SAAS;IAmBrB;;OAEG;WACW,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAIzD;;;;OAIG;WACW,kCAAkC,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAgBxF;;;OAGG;WACW,wCAAwC,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;CAe/F"}