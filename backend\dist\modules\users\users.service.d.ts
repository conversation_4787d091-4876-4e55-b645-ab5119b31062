import { Repository } from 'typeorm';
import { User, UserRole, UserStatus } from '../../database/entities';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
export interface FindUsersOptions {
    companyId?: string;
    role?: UserRole;
    status?: UserStatus;
    search?: string;
    page?: number;
    limit?: number;
}
export declare class UsersService {
    private userRepository;
    constructor(userRepository: Repository<User>);
    create(createUserDto: CreateUserDto, currentUser: AuthenticatedUser): Promise<User>;
    findAll(options: FindUsersOptions, currentUser: AuthenticatedUser): Promise<{
        users: User[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<User>;
    update(id: string, updateUserDto: UpdateUserDto, currentUser: AuthenticatedUser): Promise<User>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    changePassword(id: string, newPassword: string, currentUser: AuthenticatedUser): Promise<void>;
    private validateRoleHierarchy;
}
