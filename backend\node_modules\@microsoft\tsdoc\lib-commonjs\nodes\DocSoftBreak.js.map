{"version": 3, "file": "DocSoftBreak.js", "sourceRoot": "", "sources": ["../../src/nodes/DocSoftBreak.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;AAE3D,qCAAyG;AAEzG,2CAAuD;AAcvD;;;;;;;;;;;;GAYG;AACH;IAAkC,gCAAO;IAGvC;;;OAGG;IACH,sBAAmB,UAAmE;QACpF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,yFAAyF;YACzF,uGAAuG;YACvG,0EAA0E;YAC1E,IAAM,gBAAgB,GAAkC,UAA2C,CAAC;YACpG,KAAI,CAAC,iBAAiB,GAAG,IAAI,uBAAU,CAAC;gBACtC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,SAAS;gBAClC,OAAO,EAAE,gBAAgB,CAAC,gBAAgB;aAC3C,CAAC,CAAC;QACL,CAAC;;IACH,CAAC;IAGD,sBAAW,8BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;;;OAAA;IAED,gBAAgB;IACN,sCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAClC,CAAC;IACH,mBAAC;AAAD,CAAC,AAhCD,CAAkC,iBAAO,GAgCxC;AAhCY,oCAAY", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, type IDocNodeParameters, DocNode, type IDocNodeParsedParameters } from './DocNode';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocSoftBreak}.\r\n */\r\nexport interface IDocSoftBreakParameters extends IDocNodeParameters {}\r\n\r\n/**\r\n * Constructor parameters for {@link DocSoftBreak}.\r\n */\r\nexport interface IDocSoftBreakParsedParameters extends IDocNodeParsedParameters {\r\n  softBreakExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Instructs a renderer to insert an explicit newline in the output.\r\n * (Normally the renderer uses a formatting rule to determine where\r\n * lines should wrap.)\r\n *\r\n * @remarks\r\n * In HTML, a soft break is represented as an ASCII newline character (which does not\r\n * affect the web browser's view), whereas the hard break is the `<br />` element\r\n * (which starts a new line in the web browser's view).\r\n *\r\n * TSDoc follows the same conventions, except the renderer avoids emitting\r\n * two empty lines (because that could start a new CommonMark paragraph).\r\n */\r\nexport class DocSoftBreak extends DocNode {\r\n  private readonly _softBreakExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocSoftBreakParameters | IDocSoftBreakParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      // The type is IDocNodeParsedParameters, which is a base of IDocSoftBreakParsedParameters\r\n      // but not a base of IDocSoftBreakParameters. Therefore the type must be IDocSoftBreakParsedParameters.\r\n      // TypeScript 4 could infer this, but for some reason TypeScript 5 cannot.\r\n      const parsedParameters: IDocSoftBreakParsedParameters = parameters as IDocSoftBreakParsedParameters;\r\n      this._softBreakExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.SoftBreak,\r\n        content: parsedParameters.softBreakExcerpt\r\n      });\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.SoftBreak;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._softBreakExcerpt];\r\n  }\r\n}\r\n"]}