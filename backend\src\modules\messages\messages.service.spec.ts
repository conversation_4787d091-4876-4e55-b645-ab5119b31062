import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { MessagesService } from './services/messages.service';
import { WhatsAppService } from '../whatsapp/services/whatsapp.service';
import { AnalyticsService } from '../analytics/analytics.service';
import { Message } from '../../database/entities/message.schema';
import { Contact } from '../../database/entities/contact.schema';
import { 
  createMockModel, 
  mockMessage, 
  mockContact, 
  mockUser 
} from '../../../test/setup';

describe('MessagesService', () => {
  let service: MessagesService;
  let messageModel: any;
  let contactModel: any;
  let whatsappService: WhatsAppService;
  let analyticsService: AnalyticsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MessagesService,
        {
          provide: getModelToken(Message.name),
          useValue: createMockModel(),
        },
        {
          provide: getModelToken(Contact.name),
          useValue: createMockModel(),
        },
        {
          provide: WhatsAppService,
          useValue: {
            sendMessage: jest.fn(),
            sendMediaMessage: jest.fn(),
            getConnectionStatus: jest.fn(),
          },
        },
        {
          provide: AnalyticsService,
          useValue: {
            trackEvent: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MessagesService>(MessagesService);
    messageModel = module.get(getModelToken(Message.name));
    contactModel = module.get(getModelToken(Contact.name));
    whatsappService = module.get<WhatsAppService>(WhatsAppService);
    analyticsService = module.get<AnalyticsService>(AnalyticsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendMessage', () => {
    it('should send text message successfully', async () => {
      const sendMessageDto = {
        contactId: 'contact-123',
        content: 'Hello, World!',
        type: 'TEXT' as any,
      };

      const contact = mockContact;
      const sentMessage = { ...mockMessage, direction: 'OUTBOUND' };

      contactModel.findById.mockResolvedValue(contact);
      jest.spyOn(whatsappService, 'sendMessage').mockResolvedValue({
        messageId: 'msg-123',
        status: 'SENT',
      });
      messageModel.create.mockResolvedValue(sentMessage);

      const result = await service.sendMessage(sendMessageDto, mockUser);

      expect(result).toEqual(sentMessage);
      expect(whatsappService.sendMessage).toHaveBeenCalledWith(
        contact.whatsappConnectionId,
        contact.phoneNumber,
        sendMessageDto.content,
        sendMessageDto.type,
      );
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should throw error when contact is not found', async () => {
      const sendMessageDto = {
        contactId: 'nonexistent-contact',
        content: 'Hello, World!',
        type: 'TEXT' as any,
      };

      contactModel.findById.mockResolvedValue(null);

      await expect(service.sendMessage(sendMessageDto, mockUser))
        .rejects.toThrow('Contato não encontrado');
    });

    it('should send media message successfully', async () => {
      const sendMessageDto = {
        contactId: 'contact-123',
        content: 'Check this image!',
        type: 'IMAGE' as any,
        mediaUrl: 'https://example.com/image.jpg',
      };

      const contact = mockContact;
      const sentMessage = { ...mockMessage, direction: 'OUTBOUND', type: 'IMAGE' };

      contactModel.findById.mockResolvedValue(contact);
      jest.spyOn(whatsappService, 'sendMediaMessage').mockResolvedValue({
        messageId: 'msg-123',
        status: 'SENT',
      });
      messageModel.create.mockResolvedValue(sentMessage);

      const result = await service.sendMessage(sendMessageDto, mockUser);

      expect(result).toEqual(sentMessage);
      expect(whatsappService.sendMediaMessage).toHaveBeenCalledWith(
        contact.whatsappConnectionId,
        contact.phoneNumber,
        sendMessageDto.mediaUrl,
        sendMessageDto.type,
        sendMessageDto.content,
      );
    });
  });

  describe('receiveMessage', () => {
    it('should process received message and create contact if not exists', async () => {
      const webhookData = {
        messageId: 'msg-123',
        from: '+5511888888888',
        content: 'Hello from customer',
        type: 'TEXT',
        timestamp: new Date(),
        connectionId: 'connection-123',
        companyId: 'company-123',
      };

      const newContact = { ...mockContact, phoneNumber: webhookData.from };
      const receivedMessage = { ...mockMessage, direction: 'INBOUND' };

      contactModel.findOne.mockResolvedValue(null);
      contactModel.create.mockResolvedValue(newContact);
      messageModel.create.mockResolvedValue(receivedMessage);

      const result = await service.receiveMessage(webhookData);

      expect(result).toEqual(receivedMessage);
      expect(contactModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          phoneNumber: webhookData.from,
          companyId: webhookData.companyId,
          whatsappConnectionId: webhookData.connectionId,
        })
      );
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should process received message with existing contact', async () => {
      const webhookData = {
        messageId: 'msg-123',
        from: '+5511888888888',
        content: 'Hello again',
        type: 'TEXT',
        timestamp: new Date(),
        connectionId: 'connection-123',
        companyId: 'company-123',
      };

      const existingContact = mockContact;
      const receivedMessage = { ...mockMessage, direction: 'INBOUND' };

      contactModel.findOne.mockResolvedValue(existingContact);
      messageModel.create.mockResolvedValue(receivedMessage);

      const result = await service.receiveMessage(webhookData);

      expect(result).toEqual(receivedMessage);
      expect(contactModel.create).not.toHaveBeenCalled();
    });
  });

  describe('findMessages', () => {
    it('should return paginated messages with filters', async () => {
      const options = {
        contactId: 'contact-123',
        page: 1,
        limit: 10,
        sortBy: 'timestamp',
        sortOrder: 'DESC' as any,
      };

      const messages = [mockMessage];
      const total = 1;

      messageModel.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(messages),
            }),
          }),
        }),
      });
      messageModel.countDocuments.mockResolvedValue(total);

      const result = await service.findMessages(options, mockUser);

      expect(result).toEqual({
        messages,
        total,
        page: options.page,
        limit: options.limit,
      });
    });

    it('should search messages by content', async () => {
      const options = {
        search: 'hello',
        page: 1,
        limit: 10,
      };

      const messages = [mockMessage];
      const total = 1;

      messageModel.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            skip: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(messages),
            }),
          }),
        }),
      });
      messageModel.countDocuments.mockResolvedValue(total);

      const result = await service.findMessages(options, mockUser);

      expect(result.messages).toEqual(messages);
      expect(messageModel.find).toHaveBeenCalledWith(
        expect.objectContaining({
          content: { $regex: 'hello', $options: 'i' },
        })
      );
    });
  });

  describe('getMessageById', () => {
    it('should return message when found', async () => {
      const messageId = 'message-123';
      const message = mockMessage;

      messageModel.findById.mockReturnValue({
        populate: jest.fn().mockResolvedValue(message),
      });

      const result = await service.getMessageById(messageId, mockUser);

      expect(result).toEqual(message);
    });

    it('should throw error when message not found', async () => {
      const messageId = 'nonexistent-message';

      messageModel.findById.mockReturnValue({
        populate: jest.fn().mockResolvedValue(null),
      });

      await expect(service.getMessageById(messageId, mockUser))
        .rejects.toThrow('Mensagem não encontrada');
    });
  });

  describe('markAsRead', () => {
    it('should mark message as read successfully', async () => {
      const messageId = 'message-123';
      const message = { ...mockMessage, isRead: false };
      const updatedMessage = { ...message, isRead: true };

      messageModel.findById.mockResolvedValue(message);
      messageModel.findByIdAndUpdate.mockResolvedValue(updatedMessage);

      const result = await service.markAsRead(messageId, mockUser);

      expect(result).toEqual(updatedMessage);
      expect(messageModel.findByIdAndUpdate).toHaveBeenCalledWith(
        messageId,
        { isRead: true, readAt: expect.any(Date) },
        { new: true }
      );
    });
  });

  describe('deleteMessage', () => {
    it('should delete message successfully', async () => {
      const messageId = 'message-123';
      const message = mockMessage;

      messageModel.findById.mockResolvedValue(message);
      messageModel.findByIdAndDelete.mockResolvedValue(message);

      await service.deleteMessage(messageId, mockUser);

      expect(messageModel.findByIdAndDelete).toHaveBeenCalledWith(messageId);
      expect(analyticsService.trackEvent).toHaveBeenCalled();
    });

    it('should throw error when trying to delete message from different company', async () => {
      const messageId = 'message-123';
      const message = { ...mockMessage, companyId: 'different-company' };

      messageModel.findById.mockResolvedValue(message);

      await expect(service.deleteMessage(messageId, mockUser))
        .rejects.toThrow('Mensagem não encontrada');
    });
  });

  describe('getMessageStats', () => {
    it('should return message statistics', async () => {
      const stats = {
        total: 100,
        sent: 60,
        received: 40,
        read: 80,
        unread: 20,
      };

      messageModel.countDocuments
        .mockResolvedValueOnce(stats.total)
        .mockResolvedValueOnce(stats.sent)
        .mockResolvedValueOnce(stats.received)
        .mockResolvedValueOnce(stats.read)
        .mockResolvedValueOnce(stats.unread);

      const result = await service.getMessageStats(mockUser);

      expect(result).toEqual(stats);
    });
  });
});
