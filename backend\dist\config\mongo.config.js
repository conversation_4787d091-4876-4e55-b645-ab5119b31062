"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMongoConfig = void 0;
const mongodb_memory_server_1 = require("mongodb-memory-server");
let mongoServer;
const getMongoConfig = async (configService) => {
    const nodeEnv = configService.get('nodeEnv');
    const mongoUri = configService.get('mongo.uri');
    if (nodeEnv === 'development') {
        if (mongoUri && !mongoUri.includes('memory')) {
            return {
                uri: mongoUri,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
            };
        }
        try {
            if (!mongoServer) {
                mongoServer = await mongodb_memory_server_1.MongoMemoryServer.create({
                    instance: {
                        dbName: 'whatsapp_platform_dev',
                    },
                });
            }
            const uri = mongoServer.getUri();
            console.log('🍃 Using MongoDB Memory Server:', uri);
            return {
                uri,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
            };
        }
        catch (error) {
            console.error('Failed to start MongoDB Memory Server:', error);
            return {
                uri: 'mongodb://localhost:27017/whatsapp_platform_dev',
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
            };
        }
    }
    return {
        uri: mongoUri,
    };
};
exports.getMongoConfig = getMongoConfig;
//# sourceMappingURL=mongo.config.js.map