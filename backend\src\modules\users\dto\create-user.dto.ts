import {
  IsEmail,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>y,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '../../../database/entities';

export class CreateUserDto {
  @ApiProperty({
    description: 'Primeiro nome do usuário',
    example: '<PERSON>',
  })
  @IsString({ message: 'Primeiro nome deve ser uma string' })
  @MinLength(2, { message: 'Primeiro nome deve ter pelo menos 2 caracteres' })
  firstName: string;

  @ApiProperty({
    description: 'Último nome do usuário',
    example: 'Silva',
  })
  @IsString({ message: 'Último nome deve ser uma string' })
  @MinLength(2, { message: 'Último nome deve ter pelo menos 2 caracteres' })
  lastName: string;

  @ApiProperty({
    description: 'Email do usuário',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  email: string;

  @ApiPropertyOptional({
    description: 'Telefone do usuário',
    example: '+5511999999999',
  })
  @IsOptional()
  @IsString({ message: 'Telefone deve ser uma string' })
  phone?: string;

  @ApiProperty({
    description: 'Senha do usuário',
    example: 'senha123',
    minLength: 6,
  })
  @IsString({ message: 'Senha deve ser uma string' })
  @MinLength(6, { message: 'Senha deve ter pelo menos 6 caracteres' })
  password: string;

  @ApiProperty({
    description: 'Papel do usuário',
    enum: UserRole,
    default: UserRole.SELLER,
  })
  @IsEnum(UserRole, { message: 'Papel deve ser um valor válido' })
  role: UserRole;

  @ApiProperty({
    description: 'ID da empresa',
    example: 'uuid-da-empresa',
  })
  @IsUUID(4, { message: 'ID da empresa deve ser um UUID válido' })
  companyId: string;

  @ApiPropertyOptional({
    description: 'Permissões específicas do usuário',
    type: [String],
    example: ['users:read', 'messages:send'],
  })
  @IsOptional()
  @IsArray({ message: 'Permissões devem ser um array' })
  @IsString({ each: true, message: 'Cada permissão deve ser uma string' })
  permissions?: string[];
}
