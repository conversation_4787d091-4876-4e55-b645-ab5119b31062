{"version": 3, "file": "analytics.interceptor.js", "sourceRoot": "", "sources": ["../../../../src/modules/analytics/interceptors/analytics.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAMwB;AAExB,8CAAiD;AACjD,mEAA+D;AAIxD,IAAM,oBAAoB,4BAA1B,MAAM,oBAAoB;IAGX;IAFH,MAAM,GAAG,IAAI,eAAM,CAAC,sBAAoB,CAAC,IAAI,CAAC,CAAC;IAEhE,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAExD,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,WAAW,EAAE,CAAC;QACtD,MAAM,IAAI,GAAsB,OAAO,CAAC,IAAI,CAAC;QAE7C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC;QAEjE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,GAAG,EAAE;YACP,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;YACzC,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;YAGvC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,eAAe,CAClC,IAAI,CAAC,SAAS,EACd,GAAG,EACH,MAAM,EACN,YAAY,EACZ,UAAU,EACV,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACrE,CAAC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,UAAU,MAAM,YAAY,IAAI,CAAC,CAAC;QAC5E,CAAC,CAAC,EACF,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YACnB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;YACzC,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC;YAGvC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC3B,IAAI,CAAC,eAAe,CAAC,UAAU,CAC7B,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,IAAI,IAAI,eAAe,EAC7B,KAAK,CAAC,OAAO,IAAI,wBAAwB,EACzC,GAAG,MAAM,IAAI,GAAG,EAAE,EAClB,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;gBAGH,IAAI,CAAC,eAAe,CAAC,eAAe,CAClC,IAAI,CAAC,SAAS,EACd,GAAG,EACH,MAAM,EACN,YAAY,EACZ,UAAU,EACV,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;oBACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxF,CAAC,CAAC,CAAC;YACL,CAAC;YAGD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,MAAM,IAAI,GAAG,MAAM,UAAU,MAAM,YAAY,eAAe,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAEpG,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AA5EY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;qCAI0B,kCAAe;GAHzC,oBAAoB,CA4EhC;AAGM,IAAM,wBAAwB,gCAA9B,MAAM,wBAAwB;IAGf;IAFH,MAAM,GAAG,IAAI,eAAM,CAAC,0BAAwB,CAAC,IAAI,CAAC,CAAC;IAEpE,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAExD,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,MAAM,EAAE,EAAE;YAEb,IAAI,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,EAAE,IAAI,EAAE,CAAC;gBACrE,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAE3C,IAAI,CAAC,eAAe,CAAC,cAAc,CACjC,MAAM,CAAC,IAAI,CAAC,EAAE,EACd,MAAM,CAAC,IAAI,CAAC,SAAS,EACrB,SAAS,EACT;oBACE,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;oBACxC,SAAS,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,CAAC,aAAa;iBAC1D,CACF,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACtD,MAAM,IAAI,GAAsB,OAAO,CAAC,IAAI,CAAC;gBAC7C,IAAI,IAAI,EAAE,CAAC;oBACT,IAAI,CAAC,eAAe,CAAC,eAAe,CAClC,IAAI,CAAC,EAAE,EACP,IAAI,CAAC,SAAS,EACd,OAAO,CAAC,SAAS,IAAI,SAAS,CAC/B,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACrE,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,EAAE,IAAI,EAAE,CAAC;gBACxE,IAAI,CAAC,eAAe,CAAC,gBAAgB,CACnC,MAAM,CAAC,IAAI,CAAC,EAAE,EACd,MAAM,CAAC,IAAI,CAAC,SAAS,EACrB,MAAM,CAAC,IAAI,CAAC,EAAE,EACd,MAAM,CAAC,IAAI,CAAC,IAAI,CACjB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACvE,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,iBAAiB;QACvB,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5E,CAAC;CACF,CAAA;AA3DY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAI0B,kCAAe;GAHzC,wBAAwB,CA2DpC;AAGM,IAAM,4BAA4B,oCAAlC,MAAM,4BAA4B;IAGnB;IAFH,MAAM,GAAG,IAAI,eAAM,CAAC,8BAA4B,CAAC,IAAI,CAAC,CAAC;IAExE,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAExD,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAsB,OAAO,CAAC,IAAI,CAAC;QAC7C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,MAAM,EAAE,EAAE;YACb,IAAI,CAAC,IAAI;gBAAE,OAAO;YAGlB,IAAI,GAAG,CAAC,QAAQ,CAAC,uBAAuB,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC;gBAC7E,IAAI,CAAC,eAAe,CAAC,sBAAsB,CACzC,MAAM,CAAC,EAAE,EACT,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,EAAE,EACP,MAAM,CAAC,WAAW,CACnB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC7E,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,EAAE,MAAM,EAAE,CAAC;gBACpE,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBACnD,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,oBAAoB,CACvC,YAAY,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC5E,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACrD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBACnD,IAAI,YAAY,EAAE,CAAC;oBACjB,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAC5C,YAAY,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,EAAE,EACP,mBAAmB,CACpB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAChF,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBACvD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBACnD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;gBAEjC,IAAI,YAAY,IAAI,WAAW,EAAE,CAAC;oBAChC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CACnC,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE,EACnB,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,WAAW,CAAC,EAAE,EACd,WAAW,CAAC,IAAI,IAAI,MAAM,EAC1B,KAAK,EACL,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACtE,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;gBACnD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;gBAEjC,IAAI,YAAY,IAAI,WAAW,EAAE,EAAE,EAAE,CAAC;oBACpC,IAAI,CAAC,eAAe,CAAC,oBAAoB,CACvC,IAAI,CAAC,SAAS,EACd,YAAY,EACZ,WAAW,CAAC,EAAE,CAAC,MAAM,EACrB,WAAW,CAAC,IAAI,IAAI,MAAM,EAC1B,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBAC3E,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,GAAW;QACrC,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;QACrD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC,CAAC;CACF,CAAA;AAjGY,oEAA4B;uCAA5B,4BAA4B;IADxC,IAAA,mBAAU,GAAE;qCAI0B,kCAAe;GAHzC,4BAA4B,CAiGxC;AAGM,IAAM,2BAA2B,mCAAjC,MAAM,2BAA2B;IAGlB;IAFH,MAAM,GAAG,IAAI,eAAM,CAAC,6BAA2B,CAAC,IAAI,CAAC,CAAC;IAEvE,YAAoB,eAAgC;QAAhC,oBAAe,GAAf,eAAe,CAAiB;IAAG,CAAC;IAExD,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,IAAI,GAAsB,OAAO,CAAC,IAAI,CAAC;QAC7C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,MAAM,EAAE,EAAE;YACb,IAAI,CAAC,IAAI;gBAAE,OAAO;YAGlB,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC;gBACjE,IAAI,CAAC,eAAe,CAAC,mBAAmB,CACtC,MAAM,CAAC,EAAE,EACT,IAAI,CAAC,SAAS,EACd,MAAM,CAAC,oBAAoB,EAC3B,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,MAAM,EAAE,IAAI,EACnB,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1E,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,EAAE,EAAE,EAAE,CAAC;gBAClE,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAEhD,IAAI,CAAC,eAAe,CAAC,mBAAmB,CACtC,MAAM,CAAC,EAAE,EACT,IAAI,CAAC,SAAS,EACd,MAAM,CAAC,WAAW,EAClB,aAAa,EACb,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;oBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxE,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;gBAC7C,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;gBAE7B,IAAI,SAAS,IAAI,OAAO,EAAE,IAAI,EAAE,CAAC;oBAC/B,IAAI,CAAC,eAAe,CAAC,kBAAkB,CACrC,SAAS,EACT,IAAI,CAAC,SAAS,EACd,SAAS,EACT,OAAO,CAAC,IAAI,EACZ,IAAI,CAAC,EAAE,CACR,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACzE,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,GAAW;QAClC,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAClD,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC,CAAC;CACF,CAAA;AAnEY,kEAA2B;sCAA3B,2BAA2B;IADvC,IAAA,mBAAU,GAAE;qCAI0B,kCAAe;GAHzC,2BAA2B,CAmEvC"}