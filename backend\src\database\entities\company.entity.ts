import { <PERSON><PERSON>ty, Column, OneToMany, ManyToOne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';

export enum CompanyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  TRIAL = 'trial',
}

@Entity('companies')
export class Company extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'varchar', length: 14, unique: true })
  cnpj: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  email: string;

  @Column({ type: 'varchar', length: 20 })
  phone: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website: string;

  @Column({
    type: 'varchar',
    length: 50,
    default: CompanyStatus.TRIAL,
  })
  status: CompanyStatus;

  @Column({ type: 'json', nullable: true })
  settings: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  logo: string;

  // Relacionamento com agência (se for cliente de uma agência)
  @Column({ type: 'uuid', nullable: true })
  agencyId: string;

  @ManyToOne(() => Company, { nullable: true })
  @JoinColumn({ name: 'agencyId' })
  agency: Company;

  // Relacionamentos
  @OneToMany(() => User, (user) => user.company)
  users: User[];

  @OneToMany(() => WhatsAppConnection, (connection) => connection.company)
  whatsappConnections: WhatsAppConnection[];

  @OneToMany(() => Subscription, (subscription) => subscription.company)
  subscriptions: Subscription[];

  @OneToMany(() => Company, (company) => company.agency)
  clients: Company[];

  // Campos calculados
  get isAgency(): boolean {
    return this.agencyId === null;
  }

  get activeUsersCount(): number {
    return this.users?.filter(user => user.isActive).length || 0;
  }
}
