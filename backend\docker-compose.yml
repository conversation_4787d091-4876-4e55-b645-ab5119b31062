version: '3.8'

services:
  # Aplicação principal
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: whatsapp-platform-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=********************************************/whatsapp_platform
      - MONGODB_URI=mongodb://mongo:27017/whatsapp_platform
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - WHATSAPP_API_URL=${WHATSAPP_API_URL}
      - WHATSAPP_API_KEY=${WHATSAPP_API_KEY}
    depends_on:
      - postgres
      - mongo
      - redis
    networks:
      - whatsapp-network
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Banco PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: whatsapp-platform-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=whatsapp_platform
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Banco MongoDB
  mongo:
    image: mongo:6.0
    container_name: whatsapp-platform-mongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=whatsapp_platform
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
      - ./backups:/backups
    networks:
      - whatsapp-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: whatsapp-platform-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx como proxy reverso
  nginx:
    image: nginx:alpine
    container_name: whatsapp-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api
    networks:
      - whatsapp-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus para monitoramento
  prometheus:
    image: prom/prometheus:latest
    container_name: whatsapp-platform-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - whatsapp-network

  # Grafana para dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: whatsapp-platform-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - whatsapp-network

  # Evolution API para WhatsApp
  evolution-api:
    image: atendai/evolution-api:latest
    container_name: whatsapp-evolution-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - SERVER_TYPE=http
      - SERVER_PORT=8080
      - CORS_ORIGIN=*
      - CORS_METHODS=GET,POST,PUT,DELETE
      - CORS_CREDENTIALS=true
      - LOG_LEVEL=ERROR
      - LOG_COLOR=true
      - DEL_INSTANCE=false
      - DATABASE_ENABLED=true
      - DATABASE_CONNECTION_URI=***************************************************************
      - DATABASE_CONNECTION_DB_PREFIX_NAME=evolution
      - REDIS_ENABLED=true
      - REDIS_URI=redis://redis:6379
      - REDIS_PREFIX_KEY=evolution
      - RABBITMQ_ENABLED=false
      - WEBSOCKET_ENABLED=true
      - WEBSOCKET_GLOBAL_EVENTS=false
      - WA_BUSINESS_TOKEN_WEBHOOK=evolution
      - WA_BUSINESS_URL=https://graph.facebook.com
      - WA_BUSINESS_VERSION=v18.0
      - WA_BUSINESS_LANGUAGE=pt_BR
      - WEBHOOK_GLOBAL_URL=http://api:3000/webhooks/whatsapp
      - WEBHOOK_GLOBAL_ENABLED=true
      - WEBHOOK_GLOBAL_WEBHOOK_BY_EVENTS=false
      - CONFIG_SESSION_PHONE_CLIENT=WhatsApp Platform
      - CONFIG_SESSION_PHONE_NAME=Chrome
      - QRCODE_LIMIT=30
      - AUTHENTICATION_TYPE=apikey
      - AUTHENTICATION_API_KEY=B6D711FCDE4D4FD5936544120E713976
      - AUTHENTICATION_EXPOSE_IN_FETCH_INSTANCES=true
      - LANGUAGE=pt-BR
    volumes:
      - evolution_instances:/evolution/instances
      - evolution_store:/evolution/store
    depends_on:
      - mongo
      - redis
    networks:
      - whatsapp-network

networks:
  whatsapp-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  mongo_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  evolution_instances:
    driver: local
  evolution_store:
    driver: local
