"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactSchema = exports.Contact = exports.ContactStatus = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoose = require("mongoose");
var ContactStatus;
(function (ContactStatus) {
    ContactStatus["ACTIVE"] = "active";
    ContactStatus["BLOCKED"] = "blocked";
    ContactStatus["ARCHIVED"] = "archived";
})(ContactStatus || (exports.ContactStatus = ContactStatus = {}));
let Contact = class Contact {
    phoneNumber;
    name;
    email;
    profilePicture;
    status;
    tags;
    source;
    customFields;
    metadata;
    lastMessageAt;
    lastInteractionAt;
    messageCount;
    isLead;
    leadScore;
    leadStage;
    assignedTo;
    notes;
    companyId;
    whatsappConnectionId;
    createdBy;
    updatedBy;
};
exports.Contact = Contact;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Contact.prototype, "phoneNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "profilePicture", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String, enum: ContactStatus, default: ContactStatus.ACTIVE }),
    __metadata("design:type", String)
], Contact.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [mongoose.Schema.Types.Mixed], default: [] }),
    __metadata("design:type", Array)
], Contact.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed }),
    __metadata("design:type", Object)
], Contact.prototype, "source", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, default: {} }),
    __metadata("design:type", Object)
], Contact.prototype, "customFields", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, default: {} }),
    __metadata("design:type", Object)
], Contact.prototype, "metadata", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Contact.prototype, "lastMessageAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Contact.prototype, "lastInteractionAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Contact.prototype, "messageCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Contact.prototype, "isLead", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Contact.prototype, "leadScore", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "leadStage", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "assignedTo", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "notes", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Types.ObjectId, ref: 'Company' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Contact.prototype, "companyId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Contact.prototype, "whatsappConnectionId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "createdBy", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Contact.prototype, "updatedBy", void 0);
exports.Contact = Contact = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Contact);
exports.ContactSchema = mongoose_1.SchemaFactory.createForClass(Contact);
exports.ContactSchema.index({ phoneNumber: 1, companyId: 1 }, { unique: true });
exports.ContactSchema.index({ companyId: 1, whatsappConnectionId: 1 });
exports.ContactSchema.index({ companyId: 1, assignedTo: 1 });
exports.ContactSchema.index({ companyId: 1, isLead: 1 });
exports.ContactSchema.index({ companyId: 1, 'tags.name': 1 });
exports.ContactSchema.index({ companyId: 1, 'source.type': 1 });
exports.ContactSchema.index({ lastMessageAt: -1 });
exports.ContactSchema.index({ createdAt: -1 });
//# sourceMappingURL=contact.schema.js.map