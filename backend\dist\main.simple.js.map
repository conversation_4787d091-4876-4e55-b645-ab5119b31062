{"version": 3, "file": "main.simple.js", "sourceRoot": "", "sources": ["../src/main.simple.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,2CAAgD;AAChD,2CAA+C;AAC/C,6CAAiE;AACjE,2DAAsD;AAEtD,KAAK,UAAU,SAAS;IACtB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;IACtD,MAAM,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAG7C,GAAG,CAAC,UAAU,CAAC;QACb,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;QACxC,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC;IAGH,GAAG,CAAC,cAAc,CAChB,IAAI,uBAAc,CAAC;QACjB,SAAS,EAAE,IAAI;QACf,oBAAoB,EAAE,IAAI;QAC1B,SAAS,EAAE,IAAI;KAChB,CAAC,CACH,CAAC;IAGF,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAClD,IAAI,SAAS,EAAE,CAAC;QACd,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAGD,MAAM,MAAM,GAAG,IAAI,yBAAe,EAAE;SACjC,QAAQ,CAAC,uBAAuB,CAAC;SACjC,cAAc,CAAC,0CAA0C,CAAC;SAC1D,UAAU,CAAC,KAAK,CAAC;SACjB,aAAa,EAAE;SACf,KAAK,EAAE,CAAC;IAEX,MAAM,QAAQ,GAAG,uBAAa,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC3D,uBAAa,CAAC,KAAK,CAAC,UAAU,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IAE/C,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAC/C,MAAM,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEvB,OAAO,CAAC,GAAG,CAAC,6CAA6C,IAAI,EAAE,CAAC,CAAC;IACjE,OAAO,CAAC,GAAG,CAAC,4CAA4C,IAAI,WAAW,CAAC,CAAC;AAC3E,CAAC;AAED,SAAS,EAAE,CAAC"}