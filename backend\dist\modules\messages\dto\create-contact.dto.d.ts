export declare enum ContactStatus {
    ACTIVE = "active",
    BLOCKED = "blocked",
    ARCHIVED = "archived"
}
export declare class ContactTagDto {
    id?: string;
    name: string;
    color: string;
    createdAt?: Date;
    createdBy?: string;
}
export declare class ContactSourceDto {
    type: string;
    campaign?: string;
    medium?: string;
    source?: string;
    utm_params?: Record<string, string>;
    referrer?: string;
}
export declare class CreateContactDto {
    phoneNumber: string;
    name?: string;
    email?: string;
    profilePicture?: string;
    status: ContactStatus;
    tags?: ContactTagDto[];
    source?: ContactSourceDto;
    customFields?: Record<string, any>;
    isLead?: boolean;
    leadScore?: number;
    leadStage?: string;
    assignedTo?: string;
    notes?: string;
    whatsappConnectionId: string;
}
