"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsEventSchema = exports.AnalyticsEvent = exports.EventCategory = exports.EventType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoose = require("mongoose");
var EventType;
(function (EventType) {
    EventType["USER_LOGIN"] = "user_login";
    EventType["USER_LOGOUT"] = "user_logout";
    EventType["USER_CREATED"] = "user_created";
    EventType["WHATSAPP_CONNECTED"] = "whatsapp_connected";
    EventType["WHATSAPP_DISCONNECTED"] = "whatsapp_disconnected";
    EventType["WHATSAPP_QR_GENERATED"] = "whatsapp_qr_generated";
    EventType["MESSAGE_SENT"] = "message_sent";
    EventType["MESSAGE_RECEIVED"] = "message_received";
    EventType["MESSAGE_DELIVERED"] = "message_delivered";
    EventType["MESSAGE_READ"] = "message_read";
    EventType["MESSAGE_FAILED"] = "message_failed";
    EventType["BULK_MESSAGE_SENT"] = "bulk_message_sent";
    EventType["CONTACT_CREATED"] = "contact_created";
    EventType["CONTACT_UPDATED"] = "contact_updated";
    EventType["CONTACT_TAGGED"] = "contact_tagged";
    EventType["LEAD_CONVERTED"] = "lead_converted";
    EventType["LEAD_SCORED"] = "lead_scored";
    EventType["AUTOMATION_TRIGGERED"] = "automation_triggered";
    EventType["AUTOMATION_COMPLETED"] = "automation_completed";
    EventType["CHATBOT_INTERACTION"] = "chatbot_interaction";
    EventType["CONVERSATION_STARTED"] = "conversation_started";
    EventType["CONVERSATION_ENDED"] = "conversation_ended";
    EventType["FIRST_RESPONSE"] = "first_response";
    EventType["RESPONSE_TIME_MEASURED"] = "response_time_measured";
    EventType["SUBSCRIPTION_CREATED"] = "subscription_created";
    EventType["SUBSCRIPTION_UPDATED"] = "subscription_updated";
    EventType["PAYMENT_PROCESSED"] = "payment_processed";
    EventType["PAYMENT_FAILED"] = "payment_failed";
    EventType["API_REQUEST"] = "api_request";
    EventType["ERROR_OCCURRED"] = "error_occurred";
    EventType["PERFORMANCE_METRIC"] = "performance_metric";
})(EventType || (exports.EventType = EventType = {}));
var EventCategory;
(function (EventCategory) {
    EventCategory["USER"] = "user";
    EventCategory["WHATSAPP"] = "whatsapp";
    EventCategory["MESSAGE"] = "message";
    EventCategory["CONTACT"] = "contact";
    EventCategory["AUTOMATION"] = "automation";
    EventCategory["CONVERSION"] = "conversion";
    EventCategory["BILLING"] = "billing";
    EventCategory["SYSTEM"] = "system";
})(EventCategory || (exports.EventCategory = EventCategory = {}));
let AnalyticsEvent = class AnalyticsEvent {
    type;
    category;
    timestamp;
    companyId;
    userId;
    sessionId;
    properties;
    metadata;
    date;
    hour;
    month;
    year;
    dayOfWeek;
    hourOfDay;
    value;
    duration;
    country;
    region;
    city;
    timezone;
    userAgent;
    ipAddress;
};
exports.AnalyticsEvent = AnalyticsEvent;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: EventType }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String, enum: EventCategory }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], AnalyticsEvent.prototype, "timestamp", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Types.ObjectId, ref: 'Company' }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], AnalyticsEvent.prototype, "companyId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "sessionId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, default: {} }),
    __metadata("design:type", Object)
], AnalyticsEvent.prototype, "properties", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Schema.Types.Mixed, default: {} }),
    __metadata("design:type", Object)
], AnalyticsEvent.prototype, "metadata", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "date", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "hour", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "month", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "year", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], AnalyticsEvent.prototype, "dayOfWeek", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], AnalyticsEvent.prototype, "hourOfDay", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], AnalyticsEvent.prototype, "value", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Number }),
    __metadata("design:type", Number)
], AnalyticsEvent.prototype, "duration", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "country", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "region", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "city", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "timezone", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "userAgent", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: String }),
    __metadata("design:type", String)
], AnalyticsEvent.prototype, "ipAddress", void 0);
exports.AnalyticsEvent = AnalyticsEvent = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], AnalyticsEvent);
exports.AnalyticsEventSchema = mongoose_1.SchemaFactory.createForClass(AnalyticsEvent);
exports.AnalyticsEventSchema.index({ companyId: 1, type: 1, timestamp: -1 });
exports.AnalyticsEventSchema.index({ companyId: 1, category: 1, timestamp: -1 });
exports.AnalyticsEventSchema.index({ companyId: 1, date: 1 });
exports.AnalyticsEventSchema.index({ companyId: 1, month: 1 });
exports.AnalyticsEventSchema.index({ companyId: 1, userId: 1, timestamp: -1 });
exports.AnalyticsEventSchema.index({ timestamp: -1 });
exports.AnalyticsEventSchema.index({ 'properties.connectionId': 1, timestamp: -1 });
exports.AnalyticsEventSchema.index({ 'properties.contactPhone': 1, timestamp: -1 });
exports.AnalyticsEventSchema.index({ timestamp: 1 }, { expireAfterSeconds: 63072000 });
//# sourceMappingURL=analytics-event.schema.js.map