{"version": 3, "file": "MongoBinaryDownload.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinaryDownload.ts"], "names": [], "mappings": ";;;;AAAA,oDAAoB;AACpB,6BAA0B;AAC1B,wDAAwB;AACxB,2BAA4F;AAC5F,uDAAyC;AACzC,+BAAmC;AACnC,oEAA6B;AAC7B,0DAA0B;AAC1B,8FAA8D;AAC9D,yDAAoD;AACpD,yEAAmF;AACnF,0DAA0B;AAC1B,mCAAoE;AACpE,qDAAkD;AAElD,uCAAqC;AACrC,qCAA+E;AAG/E,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,6BAA6B,CAAC,CAAC;AASjD;;GAEG;AACH,MAAa,mBAAmB;IAO9B,YAAY,IAAqB;QAC/B,IAAA,iBAAS,EAAC,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC,CAAC;QAChG,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAA,uBAAa,EAAC,sCAAsB,CAAC,OAAO,CAAC,CAAC;QAC9E,IAAA,iBAAS,EACP,OAAO,OAAO,KAAK,QAAQ,EAC3B,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAC1D,CAAC;QAEF,wEAAwE;QACxE,IAAI,CAAC,UAAU,GAAG;YAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,YAAE,CAAC,QAAQ,EAAE;YACxC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,YAAE,CAAC,IAAI,EAAE;YAC5B,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,SAAS,CAAC,CAAC;YACrF,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE;YACrC,EAAE,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE;SACjC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG;YAChB,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,CAAC;YACV,aAAa,EAAE,CAAC;SACjB,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,OAAO;QACrB,MAAM,IAAI,GAAG,MAAM,+BAAc,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEnE,OAAO,+BAAc,CAAC,iBAAiB,CACrC,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B,MAAM,+BAAc,CAAC,aAAa,CAAC,IAAI,CAAC,CACzC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,GAAG,CAAC,eAAe,CAAC,CAAC;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QAExC,IAAI,MAAM,IAAA,kBAAU,EAAC,UAAU,CAAC,EAAE,CAAC;YACjC,GAAG,CAAC,+BAA+B,UAAU,8BAA8B,CAAC,CAAC;YAE7E,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAClD,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACnC,MAAM,aAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAExC,IAAI,MAAM,IAAA,kBAAU,EAAC,UAAU,CAAC,EAAE,CAAC;YACjC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,iDAAiD,UAAU,GAAG,CAAC,CAAC;IAClF,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,aAAa;QACjB,GAAG,CAAC,eAAe,CAAC,CAAC;QACrB,MAAM,MAAM,GAAG,IAAI,gCAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE3D,MAAM,IAAA,aAAK,EAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAEzC,IAAI,CAAC;YACH,MAAM,aAAU,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,cAAS,CAAC,IAAI,GAAG,cAAS,CAAC,IAAI,CAAC,CAAC,CAAC,sGAAsG;QAC/L,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CACX,0BAA0B,IAAI,CAAC,UAAU,CAAC,WAAW,qEAAqE;gBACxH,6CAA6C,CAChD,CAAC;YACF,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,cAAc,EAAE,CAAC;QAElD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAExD,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,WAAW,MAAM,EAAE,cAAc,CAAC,CAAC;QAE9D,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,YAAY,CAChB,kBAA0B,EAC1B,cAAsB;QAEtB,GAAG,CAAC,oDAAoD,CAAC,CAAC;QAE1D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC9B,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAE1C,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAC/D,MAAM,gBAAgB,GAAG,CAAC,MAAM,aAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACvF,MAAM,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvD,MAAM,WAAW,GAAG,MAAM,IAAA,mBAAW,EAAC,cAAc,CAAC,CAAC;QACtD,GAAG,CAAC,4BAA4B,WAAW,iBAAiB,YAAY,EAAE,CAAC,CAAC;QAE5E,IAAI,YAAY,KAAK,WAAW,EAAE,CAAC;YACjC,MAAM,IAAI,4BAAmB,CAAC,WAAW,EAAE,YAAY,IAAI,SAAS,CAAC,CAAC;QACxE,CAAC;QAED,MAAM,aAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAExC,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,QAAQ,CAAC,WAAmB;QAChC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChB,MAAM,KAAK,GACT,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,UAAU;YACtB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,gBAAgB;YAC5B,OAAO,CAAC,GAAG,CAAC,WAAW;YACvB,OAAO,CAAC,GAAG,CAAC,UAAU;YACtB,OAAO,CAAC,GAAG,CAAC,WAAW;YACvB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QAEzB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,CAAC;QAE/D,MAAM,SAAS,GAAG,IAAI,SAAG,CAAC,WAAW,CAAC,CAAC;QACvC,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,IAAI,KAAK,CAAC;QAEzC,MAAM,cAAc,GAAmB;YACrC,MAAM,EAAE,KAAK;YACb,kBAAkB,EAAE,SAAS;YAC7B,QAAQ,EAAE,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;YACxF,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,mCAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SACtD,CAAC;QAEF,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAErD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,kDAAkD,WAAW,GAAG,CAAC,CAAC;QACpF,CAAC;QAED,MAAM,gBAAgB,GAAG,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7E,MAAM,oBAAoB,GAAG,cAAI,CAAC,OAAO,CACvC,IAAI,CAAC,UAAU,CAAC,WAAW,EAC3B,GAAG,QAAQ,cAAc,CAC1B,CAAC;QACF,GAAG,CAAC,wBAAwB,KAAK,CAAC,CAAC,CAAC,eAAe,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,MAAM,WAAW,GAAG,CAAC,CAAC;QAEtF,IAAI,MAAM,IAAA,kBAAU,EAAC,gBAAgB,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,+DAA+D,CAAC,CAAC;YAErE,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;QAErC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,YAAY,CAC5C,SAAS,EACT,cAAc,EACd,gBAAgB,EAChB,oBAAoB,CACrB,CAAC;QAEF,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CAAC,cAAsB;QAClC,GAAG,CAAC,SAAS,CAAC,CAAC;QACf,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7C,GAAG,CAAC,sBAAsB,cAAc,aAAa,eAAe,GAAG,CAAC,CAAC;QAEzE,MAAM,IAAA,aAAK,EAAC,cAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;QAE3C,MAAM,MAAM,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,iCAAiC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE9E,IAAI,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YAC3C,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,6CAA6C,cAAc,uBACzD,IAAI,CAAC,eAAe,IAAI,SAC1B,2CAA2C,CAC5C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,CAAC,MAAM,IAAA,kBAAU,EAAC,eAAe,CAAC,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CACb,kDAAkD,cAAc,uBAC9D,IAAI,CAAC,eAAe,IAAI,SAC1B,2CAA2C,CAC5C,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAChB,cAAsB,EACtB,WAAmB,EACnB,MAAiC;QAEjC,GAAG,CAAC,cAAc,CAAC,CAAC;QACpB,MAAM,OAAO,GAAG,oBAAG,CAAC,OAAO,EAAE,CAAC;QAC9B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;YAC3C,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CACT,IAAA,sBAAiB,EAAC,WAAW,EAAE;oBAC7B,IAAI,EAAE,KAAK;iBACZ,CAAC,CACH,CAAC;YACJ,CAAC;YAED,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC/B,MAAM,CAAC,MAAM,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC9B,IAAA,qBAAgB,EAAC,cAAc,CAAC;iBAC7B,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC,CAAC;iBACD,IAAI,CAAC,IAAA,kBAAW,GAAE,CAAC;iBACnB,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC;iBACb,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;gBACnB,GAAG,CAAC,IAAI,wBAAe,CAAC,yBAAyB,GAAG,cAAc,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;YACpF,CAAC,CAAC;iBACD,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,UAAU,CACd,cAAsB,EACtB,WAAmB,EACnB,MAAiC;QAEjC,GAAG,CAAC,YAAY,CAAC,CAAC;QAElB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,eAAK,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;gBACjE,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACpB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBAED,OAAO,CAAC,SAAS,EAAE,CAAC;gBAEpB,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBAEnC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC;oBAC7B,CAAC;oBAED,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;wBACxC,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;4BACf,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;wBACtB,CAAC;wBAED,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;wBACvC,CAAC,CAAC,IAAI,CACJ,IAAA,sBAAiB,EAAC,WAAW,EAAE;4BAC7B,IAAI,EAAE,KAAK;yBACZ,CAAC,CACH,CAAC;oBACJ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,YAAY,CAChB,GAAQ,EACR,WAA2B,EAC3B,gBAAwB,EACxB,oBAA4B;QAE5B,GAAG,CAAC,cAAc,CAAC,CAAC;QACpB,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAEnD,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;QACzF,MAAM,eAAe,GAAoC;YACvD,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAC3D,GAAG,WAAW;SACf,CAAC;QAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,GAAG,CAAC,qCAAqC,WAAW,GAAG,CAAC,CAAC;YACzD,wBAAK;iBACF,GAAG,CAAC,GAAG,EAAE,eAAe,EAAE,CAAC,QAAQ,EAAE,EAAE;gBACtC,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;oBAC/B,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;wBAChC,MAAM,CACJ,IAAI,sBAAa,CACf,WAAW,EACX,sCAAsC;4BACpC,4EAA4E;4BAC5E,0FAA0F;4BAC1F,gDAAgD;4BAChD,oEAAoE,CACvE,CACF,CAAC;wBAEF,OAAO;oBACT,CAAC;oBAED,MAAM,CACJ,IAAI,sBAAa,CAAC,WAAW,EAAE,gCAAgC,QAAQ,CAAC,UAAU,GAAG,CAAC,CACvF,CAAC;oBAEF,OAAO;gBACT,CAAC;gBAED,8BAA8B;gBAC9B,IAAI,aAAqB,CAAC;gBAE1B,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,QAAQ,EAAE,CAAC;oBAC1D,GAAG,CAAC,4CAA4C,CAAC,CAAC;oBAElD,aAAa,GAAG,CAAC,CAAC;gBACpB,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,EAAE,CAAC,CAAC;oBAEjE,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;wBAChC,GAAG,CAAC,mDAAmD,CAAC,CAAC;wBAEzD,aAAa,GAAG,CAAC,CAAC;oBACpB,CAAC;gBACH,CAAC;gBAED,+HAA+H;gBAC/H,IACE,CAAC,IAAA,yBAAS,EAAC,IAAA,uBAAa,EAAC,sCAAsB,CAAC,8BAA8B,CAAC,CAAC;oBAChF,aAAa,IAAI,CAAC,EAClB,CAAC;oBACD,MAAM,CACJ,IAAI,sBAAa,CACf,WAAW,EACX,oEAAoE,CACrE,CACF,CAAC;oBAEF,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;gBAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;gBACvC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;gBAEnF,MAAM,UAAU,GAAG,IAAA,sBAAiB,EAAC,oBAAoB,CAAC,CAAC;gBAE3D,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAE1B,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;oBACjC,IACE,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM;wBAChD,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EACnC,CAAC;wBACD,MAAM,CACJ,IAAI,sBAAa,CACf,WAAW,EACX,cAAc,IAAI,CAAC,UAAU,CAAC,OAAO,mCAAmC,CACzE,CACF,CAAC;wBAEF,OAAO;oBACT,CAAC;oBAED,IAAI,CAAC,qBAAqB,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;oBAEhD,UAAU,CAAC,KAAK,EAAE,CAAC;oBACnB,MAAM,aAAU,CAAC,MAAM,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;oBAChE,GAAG,CAAC,wBAAwB,oBAAoB,SAAS,gBAAgB,GAAG,CAAC,CAAC;oBAE9E,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAU,EAAE,EAAE;oBACjC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;gBAC1B,sCAAsC;gBACtC,OAAO,CAAC,KAAK,CAAC,qBAAqB,WAAW,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;gBACjE,MAAM,CAAC,IAAI,sBAAa,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,qBAAqB,CAAC,KAAyB,EAAE,aAAsB,KAAK;QAC1E,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC;QAExC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC9D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,aAAa,GAAG,GAAG,CAAC;QAEpC,MAAM,eAAe,GACnB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QACrF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QAE7E,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,MAAM,OAAO,GAAG,wBAAwB,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,eAAe,MAAM,UAAU,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,QAAQ,EAAE,CAAC;QAEpJ,IAAI,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACzB,sGAAsG;YACtG,IAAA,oBAAS,EAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,oEAAoE;YAClG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,GAAQ;QAC3B,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC;QAEhC,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;CACF;AAteD,kDAseC;AAED,kBAAe,mBAAmB,CAAC"}