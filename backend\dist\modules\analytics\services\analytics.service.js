"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const analytics_event_schema_1 = require("../../../database/entities/analytics-event.schema");
const analytics_query_dto_1 = require("../dto/analytics-query.dto");
let AnalyticsService = AnalyticsService_1 = class AnalyticsService {
    analyticsEventModel;
    logger = new common_1.Logger(AnalyticsService_1.name);
    constructor(analyticsEventModel) {
        this.analyticsEventModel = analyticsEventModel;
    }
    async trackEvent(type, category, companyId, properties = {}, userId, sessionId) {
        try {
            const timestamp = new Date();
            const date = timestamp.toISOString().split('T')[0];
            const hour = timestamp.toISOString().substring(0, 13);
            const month = timestamp.toISOString().substring(0, 7);
            const year = timestamp.getFullYear().toString();
            const dayOfWeek = timestamp.getDay();
            const hourOfDay = timestamp.getHours();
            const event = new this.analyticsEventModel({
                type,
                category,
                timestamp,
                companyId: new mongoose_2.Types.ObjectId(companyId),
                userId,
                sessionId,
                properties,
                date,
                hour,
                month,
                year,
                dayOfWeek,
                hourOfDay,
                value: properties.value || 1,
                duration: properties.duration,
            });
            await event.save();
            this.logger.debug(`Event tracked: ${type} for company ${companyId}`);
        }
        catch (error) {
            this.logger.error(`Failed to track event: ${error.message}`, error.stack);
        }
    }
    async getDashboardMetrics(query, currentUser) {
        const { startDate, endDate } = this.getDateRange(query.timeRange || analytics_query_dto_1.TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const baseFilter = {
            ...companyFilter,
            timestamp: { $gte: startDate, $lte: endDate },
        };
        if (query.connectionIds && query.connectionIds.length > 0) {
            baseFilter['properties.connectionId'] = { $in: query.connectionIds };
        }
        const [messagesSent, messagesReceived, messagesDelivered, messagesRead, responseTimes, newContacts, leadsConverted, activeConnections, activeUsers,] = await Promise.all([
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.MESSAGE_SENT, analytics_query_dto_1.GroupBy.DAY),
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.MESSAGE_RECEIVED, analytics_query_dto_1.GroupBy.DAY),
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.MESSAGE_DELIVERED, analytics_query_dto_1.GroupBy.DAY),
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.MESSAGE_READ, analytics_query_dto_1.GroupBy.DAY),
            this.getAverageResponseTime(baseFilter),
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.CONTACT_CREATED, analytics_query_dto_1.GroupBy.DAY),
            this.getMetricTimeSeries(baseFilter, analytics_event_schema_1.EventType.LEAD_CONVERTED, analytics_query_dto_1.GroupBy.DAY),
            this.getActiveConnections(companyFilter),
            this.getActiveUsers(baseFilter),
        ]);
        const deliveryRate = this.calculateRate(messagesDelivered, messagesSent);
        const readRate = this.calculateRate(messagesRead, messagesDelivered);
        const conversionRate = this.calculateConversionRate(leadsConverted, newContacts);
        return {
            messagesSent: this.formatMetric('messages_sent', messagesSent),
            messagesReceived: this.formatMetric('messages_received', messagesReceived),
            deliveryRate: this.formatMetric('delivery_rate', deliveryRate, '%'),
            readRate: this.formatMetric('read_rate', readRate, '%'),
            averageResponseTime: this.formatMetric('avg_response_time', responseTimes, 'seconds'),
            newContacts: this.formatMetric('new_contacts', newContacts),
            leadsConverted: this.formatMetric('leads_converted', leadsConverted),
            conversionRate: this.formatMetric('conversion_rate', conversionRate, '%'),
            activeConnections: this.formatMetric('active_connections', activeConnections),
            activeUsers: this.formatMetric('active_users', activeUsers),
        };
    }
    async getConversationAnalytics(query, currentUser) {
        const { startDate, endDate } = this.getDateRange(query.timeRange || analytics_query_dto_1.TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const baseFilter = {
            ...companyFilter,
            timestamp: { $gte: startDate, $lte: endDate },
        };
        const [totalConversations, activeConversations, conversationMetrics, firstResponseMetrics,] = await Promise.all([
            this.getTotalConversations(baseFilter),
            this.getActiveConversations(companyFilter),
            this.getConversationMetrics(baseFilter),
            this.getFirstResponseMetrics(baseFilter),
        ]);
        return {
            totalConversations,
            activeConversations,
            averageConversationDuration: conversationMetrics.avgDuration,
            averageMessagesPerConversation: conversationMetrics.avgMessages,
            firstResponseRate: firstResponseMetrics.rate,
            averageFirstResponseTime: firstResponseMetrics.avgTime,
        };
    }
    async getUserActivityAnalytics(query, currentUser) {
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const now = new Date();
        const [activeToday, activeThisWeek, activeThisMonth, sessionMetrics, activityByHour, activityByDayOfWeek,] = await Promise.all([
            this.getActiveUsersInPeriod(companyFilter, 1),
            this.getActiveUsersInPeriod(companyFilter, 7),
            this.getActiveUsersInPeriod(companyFilter, 30),
            this.getSessionMetrics(companyFilter),
            this.getActivityByHour(companyFilter),
            this.getActivityByDayOfWeek(companyFilter),
        ]);
        return {
            activeToday,
            activeThisWeek,
            activeThisMonth,
            averageSessionDuration: sessionMetrics.avgDuration,
            activityByHour,
            activityByDayOfWeek,
        };
    }
    async getPerformanceAnalytics(query, currentUser) {
        const { startDate, endDate } = this.getDateRange(query.timeRange || analytics_query_dto_1.TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const baseFilter = {
            ...companyFilter,
            timestamp: { $gte: startDate, $lte: endDate },
        };
        const [apiMetrics, errorMetrics, systemMetrics,] = await Promise.all([
            this.getApiMetrics(baseFilter),
            this.getErrorMetrics(baseFilter),
            this.getSystemMetrics(companyFilter),
        ]);
        return {
            averageApiResponseTime: apiMetrics.avgResponseTime,
            errorRate: errorMetrics.rate,
            uptime: systemMetrics.uptime,
            requestsPerMinute: apiMetrics.requestsPerMinute,
            activeWebSocketConnections: systemMetrics.activeWebSocketConnections,
        };
    }
    async getCustomAnalytics(query, currentUser) {
        const { startDate, endDate } = this.getDateRange(query.timeRange || analytics_query_dto_1.TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
            timestamp: { $gte: startDate, $lte: endDate },
        };
        if (query.eventTypes && query.eventTypes.length > 0) {
            filter.type = { $in: query.eventTypes };
        }
        if (query.categories && query.categories.length > 0) {
            filter.category = { $in: query.categories };
        }
        if (query.userIds && query.userIds.length > 0) {
            filter.userId = { $in: query.userIds };
        }
        if (query.connectionIds && query.connectionIds.length > 0) {
            filter['properties.connectionId'] = { $in: query.connectionIds };
        }
        if (query.contactPhones && query.contactPhones.length > 0) {
            filter['properties.contactPhone'] = { $in: query.contactPhones };
        }
        if (query.filters) {
            Object.assign(filter, query.filters);
        }
        const results = await this.executeAggregation(filter, query);
        return results.map(result => this.formatMetric(result._id || 'metric', result.timeSeries || [], query.metricField ? 'custom' : 'count'));
    }
    getDateRange(timeRange, startDate, endDate) {
        const now = new Date();
        let start;
        let end = now;
        switch (timeRange) {
            case analytics_query_dto_1.TimeRange.LAST_24_HOURS:
                start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case analytics_query_dto_1.TimeRange.LAST_7_DAYS:
                start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case analytics_query_dto_1.TimeRange.LAST_30_DAYS:
                start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case analytics_query_dto_1.TimeRange.LAST_90_DAYS:
                start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            case analytics_query_dto_1.TimeRange.LAST_YEAR:
                start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                break;
            case analytics_query_dto_1.TimeRange.CUSTOM:
                start = startDate ? new Date(startDate) : new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                end = endDate ? new Date(endDate) : now;
                break;
            default:
                start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        }
        return { startDate: start, endDate: end };
    }
    async getMetricTimeSeries(baseFilter, eventType, groupBy) {
        const filter = { ...baseFilter, type: eventType };
        const groupField = this.getGroupField(groupBy);
        const results = await this.analyticsEventModel.aggregate([
            { $match: filter },
            {
                $group: {
                    _id: `$${groupField}`,
                    value: { $sum: 1 },
                    timestamp: { $first: '$timestamp' },
                },
            },
            { $sort: { _id: 1 } },
        ]);
        return results.map(result => ({
            timestamp: result._id,
            value: result.value,
        }));
    }
    getGroupField(groupBy) {
        switch (groupBy) {
            case analytics_query_dto_1.GroupBy.HOUR:
                return 'hour';
            case analytics_query_dto_1.GroupBy.DAY:
                return 'date';
            case analytics_query_dto_1.GroupBy.WEEK:
                return 'date';
            case analytics_query_dto_1.GroupBy.MONTH:
                return 'month';
            case analytics_query_dto_1.GroupBy.YEAR:
                return 'year';
            default:
                return 'date';
        }
    }
    formatMetric(name, timeSeries, unit = 'count') {
        const totalValue = timeSeries.reduce((sum, point) => sum + point.value, 0);
        return {
            name,
            value: totalValue,
            timeSeries,
            unit,
            format: unit === '%' ? 'percentage' : 'number',
        };
    }
    calculateRate(numerator, denominator) {
        const numeratorMap = new Map(numerator.map(p => [p.timestamp, p.value]));
        return denominator.map(point => ({
            timestamp: point.timestamp,
            value: point.value > 0 ? ((numeratorMap.get(point.timestamp) || 0) / point.value) * 100 : 0,
        }));
    }
    calculateConversionRate(converted, total) {
        return this.calculateRate(converted, total);
    }
    async getAverageResponseTime(filter) {
        const results = await this.analyticsEventModel.aggregate([
            { $match: { ...filter, type: analytics_event_schema_1.EventType.RESPONSE_TIME_MEASURED } },
            {
                $group: {
                    _id: '$date',
                    value: { $avg: '$properties.responseTime' },
                },
            },
            { $sort: { _id: 1 } },
        ]);
        return results.map(result => ({
            timestamp: result._id,
            value: Math.round(result.value || 0),
        }));
    }
    async getActiveConnections(filter) {
        return [{ timestamp: new Date().toISOString(), value: 5 }];
    }
    async getActiveUsers(filter) {
        return [{ timestamp: new Date().toISOString(), value: 10 }];
    }
    async getTotalConversations(filter) {
        return this.analyticsEventModel.countDocuments({
            ...filter,
            type: analytics_event_schema_1.EventType.CONVERSATION_STARTED,
        });
    }
    async getActiveConversations(filter) {
        return 25;
    }
    async getConversationMetrics(filter) {
        return { avgDuration: 15.5, avgMessages: 8.2 };
    }
    async getFirstResponseMetrics(filter) {
        return { rate: 85.5, avgTime: 12.3 };
    }
    async getActiveUsersInPeriod(filter, days) {
        const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        const result = await this.analyticsEventModel.distinct('userId', {
            ...filter,
            timestamp: { $gte: startDate },
            type: analytics_event_schema_1.EventType.USER_LOGIN,
        });
        return result.length;
    }
    async getSessionMetrics(filter) {
        return { avgDuration: 45.2 };
    }
    async getActivityByHour(filter) {
        const results = await this.analyticsEventModel.aggregate([
            { $match: filter },
            {
                $group: {
                    _id: '$hourOfDay',
                    value: { $sum: 1 },
                },
            },
            { $sort: { _id: 1 } },
        ]);
        return results.map(result => ({
            timestamp: `${result._id}:00`,
            value: result.value,
        }));
    }
    async getActivityByDayOfWeek(filter) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const results = await this.analyticsEventModel.aggregate([
            { $match: filter },
            {
                $group: {
                    _id: '$dayOfWeek',
                    value: { $sum: 1 },
                },
            },
            { $sort: { _id: 1 } },
        ]);
        return results.map(result => ({
            timestamp: days[result._id],
            value: result.value,
        }));
    }
    async getApiMetrics(filter) {
        return { avgResponseTime: 150, requestsPerMinute: 1250 };
    }
    async getErrorMetrics(filter) {
        return { rate: 0.5 };
    }
    async getSystemMetrics(filter) {
        return { uptime: 99.9, activeWebSocketConnections: 85 };
    }
    async executeAggregation(filter, query) {
        const results = await this.analyticsEventModel.aggregate([
            { $match: filter },
            {
                $group: {
                    _id: query.groupBy === analytics_query_dto_1.GroupBy.DAY ? '$date' : '$hour',
                    value: query.metricType === analytics_query_dto_1.MetricType.COUNT ? { $sum: 1 } : { $avg: '$value' },
                },
            },
            { $sort: { _id: 1 } },
            { $limit: query.limit || 100 },
        ]);
        return results.map(result => ({
            _id: result._id,
            timeSeries: [{ timestamp: result._id, value: result.value }],
        }));
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = AnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(analytics_event_schema_1.AnalyticsEvent.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map