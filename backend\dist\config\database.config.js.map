{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../src/config/database.config.ts"], "names": [], "mappings": ";;;AAGO,MAAM,iBAAiB,GAAG,CAAC,aAA4B,EAAwB,EAAE,CAAC,CAAC;IACxF,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;IACxC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;IACxC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;IAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;IAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;IAC5C,QAAQ,EAAE,CAAC,SAAS,GAAG,yCAAyC,CAAC;IACjE,UAAU,EAAE,CAAC,SAAS,GAAG,oCAAoC,CAAC;IAC9D,WAAW,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,aAAa;IAC3D,OAAO,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,aAAa;IACvD,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;CAC3F,CAAC,CAAC;AAZU,QAAA,iBAAiB,qBAY3B;AAEI,MAAM,cAAc,GAAG,CAAC,aAA4B,EAAE,EAAE,CAAC,CAAC;IAC/D,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;IACrC,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;CACzB,CAAC,CAAC;AAJU,QAAA,cAAc,kBAIxB"}