{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../src/config/database.config.ts"], "names": [], "mappings": ";;;AAGO,MAAM,iBAAiB,GAAG,CAAC,aAA4B,EAAwB,EAAE;IACtF,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAG7C,MAAM,UAAU,GAAG;QACjB,QAAQ,EAAE,CAAC,SAAS,GAAG,yCAAyC,CAAC;QACjE,UAAU,EAAE,CAAC,SAAS,GAAG,oCAAoC,CAAC;QAC9D,WAAW,EAAE,OAAO,KAAK,aAAa;QACtC,OAAO,EAAE,OAAO,KAAK,aAAa;KACnC,CAAC;IAGF,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEtD,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YACtD,OAAO;gBACL,GAAG,UAAU;gBACb,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,WAAW;gBACvD,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,IAAI;gBAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,UAAU;gBAC9D,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,UAAU;gBAC9D,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,mBAAmB;aACpE,CAAC;QACJ,CAAC;QAGD,OAAO;YACL,GAAG,UAAU;YACb,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,uBAAuB;YACjC,UAAU,EAAE,KAAK;SAClB,CAAC;IACJ,CAAC;IAGD,OAAO;QACL,GAAG,UAAU;QACb,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QAC5C,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE;KACnC,CAAC;AACJ,CAAC,CAAC;AAjDW,QAAA,iBAAiB,qBAiD5B;AAEK,MAAM,cAAc,GAAG,CAAC,aAA4B,EAAE,EAAE,CAAC,CAAC;IAC/D,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;IACrC,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;CACzB,CAAC,CAAC;AAJU,QAAA,cAAc,kBAIxB"}