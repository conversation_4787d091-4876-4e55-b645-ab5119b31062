{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../src/config/database.config.ts"], "names": [], "mappings": ";;;AAGO,MAAM,iBAAiB,GAAG,CAAC,aAA4B,EAAwB,EAAE;IACtF,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAG7C,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;QAC9B,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,CAAC,SAAS,GAAG,yCAAyC,CAAC;YACjE,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;SACjB,CAAC;IACJ,CAAC;IAGD,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QACxC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;QAChD,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,eAAe,CAAC;QAC5C,QAAQ,EAAE,CAAC,SAAS,GAAG,yCAAyC,CAAC;QACjE,UAAU,EAAE,CAAC,SAAS,GAAG,oCAAoC,CAAC;QAC9D,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,EAAE,kBAAkB,EAAE,KAAK,EAAE;KACnC,CAAC;AACJ,CAAC,CAAC;AA7BW,QAAA,iBAAiB,qBA6B5B;AAEK,MAAM,cAAc,GAAG,CAAC,aAA4B,EAAE,EAAE,CAAC,CAAC;IAC/D,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC;IACrC,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;CACzB,CAAC,CAAC;AAJU,QAAA,cAAc,kBAIxB"}