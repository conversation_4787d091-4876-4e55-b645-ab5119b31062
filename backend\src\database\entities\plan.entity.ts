import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Subscription } from './subscription.entity';

export enum PlanType {
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
  AGENCY = 'agency',
  CUSTOM = 'custom',
}

export enum PlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
}

export enum BillingCycle {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  SEMI_ANNUAL = 'semi_annual',
  ANNUAL = 'annual',
}

export interface PlanFeatures {
  // Limites de uso
  maxConnections: number;
  maxContacts: number;
  maxMessages: number; // Por mês
  maxUsers: number;
  maxAutomations: number;
  maxChatbots: number;
  
  // Funcionalidades
  hasAnalytics: boolean;
  hasAdvancedReports: boolean;
  hasAPI: boolean;
  hasWebhooks: boolean;
  hasIntegrations: boolean;
  hasWhiteLabel: boolean;
  hasCustomDomain: boolean;
  hasPrioritySupport: boolean;
  hasCustomFields: boolean;
  hasBulkMessages: boolean;
  hasScheduledMessages: boolean;
  hasMessageTemplates: boolean;
  hasContactSegmentation: boolean;
  hasLeadScoring: boolean;
  hasConversationRouting: boolean;
  hasMultiAgent: boolean;
  hasFileSharing: boolean;
  hasVideoCall: boolean;
  hasVoiceMessage: boolean;
  
  // Integrações específicas
  hasGoogleSheets: boolean;
  hasCRMIntegration: boolean;
  hasZapierIntegration: boolean;
  hasEmailMarketing: boolean;
  hasSMSIntegration: boolean;
  
  // Recursos avançados
  hasAI: boolean;
  hasCustomAI: boolean;
  hasAdvancedAutomation: boolean;
  hasWorkflowBuilder: boolean;
  hasCustomReports: boolean;
  hasDataExport: boolean;
  hasBackupRestore: boolean;
  hasSSO: boolean;
  hasAuditLog: boolean;
  hasRoleManagement: boolean;
  
  // Limites de armazenamento
  storageGB: number;
  messageRetentionDays: number;
  
  // Suporte
  supportLevel: 'basic' | 'standard' | 'premium' | 'enterprise';
  supportChannels: string[]; // ['email', 'chat', 'phone', 'dedicated']
  responseTimeSLA: number; // Em horas
}

@Entity('plans')
export class Plan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PlanType,
    default: PlanType.STARTER,
  })
  type: PlanType;

  @Column({
    type: 'enum',
    enum: PlanStatus,
    default: PlanStatus.ACTIVE,
  })
  status: PlanStatus;

  @Column({
    type: 'enum',
    enum: BillingCycle,
    default: BillingCycle.MONTHLY,
  })
  billingCycle: BillingCycle;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  setupFee: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  discountPercentage: number;

  @Column({ type: 'json' })
  features: PlanFeatures;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Configurações de trial
  @Column({ default: false })
  hasTrialPeriod: boolean;

  @Column({ default: 0 })
  trialDays: number;

  // Configurações de agência
  @Column({ default: false })
  isAgencyPlan: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  agencyCommissionPercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  agencyFixedCommission: number;

  // Configurações de cobrança
  @Column({ default: true })
  isRecurring: boolean;

  @Column({ nullable: true })
  stripeProductId: string;

  @Column({ nullable: true })
  stripePriceId: string;

  // Configurações de upgrade/downgrade
  @Column({ default: true })
  allowUpgrade: boolean;

  @Column({ default: true })
  allowDowngrade: boolean;

  @Column({ type: 'simple-array', nullable: true })
  upgradeableTo: string[]; // IDs dos planos para upgrade

  @Column({ type: 'simple-array', nullable: true })
  downgradeableTo: string[]; // IDs dos planos para downgrade

  // Configurações de visibilidade
  @Column({ default: true })
  isPublic: boolean;

  @Column({ default: false })
  isCustom: boolean;

  @Column({ nullable: true })
  targetAudience: string; // 'small_business', 'enterprise', 'agency', etc.

  @Column({ default: 0 })
  sortOrder: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Subscription, subscription => subscription.plan)
  subscriptions: Subscription[];

  // Métodos auxiliares
  getMonthlyPrice(): number {
    switch (this.billingCycle) {
      case BillingCycle.MONTHLY:
        return this.price;
      case BillingCycle.QUARTERLY:
        return this.price / 3;
      case BillingCycle.SEMI_ANNUAL:
        return this.price / 6;
      case BillingCycle.ANNUAL:
        return this.price / 12;
      default:
        return this.price;
    }
  }

  getDiscountedPrice(): number {
    return this.price * (1 - this.discountPercentage / 100);
  }

  hasFeature(feature: keyof PlanFeatures): boolean {
    return Boolean(this.features[feature]);
  }

  getFeatureLimit(feature: keyof PlanFeatures): number {
    const value = this.features[feature];
    return typeof value === 'number' ? value : 0;
  }

  canUpgradeTo(planId: string): boolean {
    return this.allowUpgrade && (this.upgradeableTo?.includes(planId) ?? false);
  }

  canDowngradeTo(planId: string): boolean {
    return this.allowDowngrade && (this.downgradeableTo?.includes(planId) ?? false);
  }

  isEnterprise(): boolean {
    return this.type === PlanType.ENTERPRISE || this.type === PlanType.CUSTOM;
  }

  isAgency(): boolean {
    return this.type === PlanType.AGENCY || this.isAgencyPlan;
  }

  getCommissionAmount(subscriptionPrice: number): number {
    if (!this.isAgency()) return 0;
    
    const percentageCommission = (subscriptionPrice * this.agencyCommissionPercentage) / 100;
    return percentageCommission + this.agencyFixedCommission;
  }
}
