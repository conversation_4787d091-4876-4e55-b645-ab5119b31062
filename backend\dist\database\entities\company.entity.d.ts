import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
export declare enum CompanyStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    TRIAL = "trial"
}
export declare class Company extends BaseEntity {
    name: string;
    cnpj: string;
    email: string;
    phone: string;
    address: string;
    website: string;
    status: CompanyStatus;
    settings: Record<string, any>;
    logo: string;
    agencyId: string;
    agency: Company;
    users: User[];
    whatsappConnections: WhatsAppConnection[];
    subscriptions: Subscription[];
    clients: Company[];
    get isAgency(): boolean;
    get activeUsersCount(): number;
}
