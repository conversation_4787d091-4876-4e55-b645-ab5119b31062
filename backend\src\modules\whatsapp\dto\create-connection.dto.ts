import {
  IsString,
  IsEnum,
  IsOptional,
  IsUUID,
  IsPhoneNumber,
  Length,
  IsObject,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ConnectionType } from '../../../database/entities';

export class CreateConnectionDto {
  @ApiProperty({
    description: 'Nome da conexão',
    example: 'WhatsApp Vendas',
  })
  @IsString({ message: 'Nome deve ser uma string' })
  @Length(2, 255, { message: 'Nome deve ter entre 2 e 255 caracteres' })
  name: string;

  @ApiProperty({
    description: 'Número de telefone',
    example: '+5511999999999',
  })
  @IsPhoneNumber('BR', { message: 'Número de telefone deve ser válido' })
  phoneNumber: string;

  @ApiProperty({
    description: 'Tipo de conexão',
    enum: ConnectionType,
    default: ConnectionType.EVOLUTION_API,
  })
  @IsEnum(ConnectionType, { message: 'Tipo de conexão deve ser válido' })
  type: ConnectionType;

  @ApiProperty({
    description: 'ID da empresa',
    example: 'uuid-da-empresa',
  })
  @IsUUID(4, { message: 'ID da empresa deve ser um UUID válido' })
  companyId: string;

  @ApiPropertyOptional({
    description: 'ID do usuário responsável',
    example: 'uuid-do-usuario',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ID do usuário deve ser um UUID válido' })
  assignedUserId?: string;

  @ApiPropertyOptional({
    description: 'Configurações específicas da conexão',
    example: { webhook: 'https://example.com/webhook', autoReconnect: true },
  })
  @IsOptional()
  @IsObject({ message: 'Configurações devem ser um objeto' })
  settings?: Record<string, any>;
}
