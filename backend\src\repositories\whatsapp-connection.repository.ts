import { WhatsAppConnection, ConnectionType, ConnectionStatus, Prisma } from '@prisma/client'
import prisma from '../lib/prisma'

export interface CreateWhatsAppConnectionData {
  name: string
  phoneNumber: string
  type?: ConnectionType
  status?: ConnectionStatus
  qrCode?: string
  instanceId?: string
  connectionData?: any
  settings?: any
  companyId: string
  assignedUserId?: string
}

export interface UpdateWhatsAppConnectionData {
  name?: string
  phoneNumber?: string
  type?: ConnectionType
  status?: ConnectionStatus
  qrCode?: string
  instanceId?: string
  connectionData?: any
  settings?: any
  lastConnectedAt?: Date
  lastDisconnectedAt?: Date
  errorMessage?: string
  isActive?: boolean
  assignedUserId?: string
}

export interface WhatsAppConnectionFilters {
  companyId?: string
  status?: ConnectionStatus
  type?: ConnectionType
  assignedUserId?: string
  isActive?: boolean
  search?: string
}

export class WhatsAppConnectionRepository {
  async create(data: CreateWhatsAppConnectionData): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.create({
      data,
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async findById(id: string): Promise<WhatsAppConnection | null> {
    return prisma.whatsAppConnection.findUnique({
      where: { id },
      include: {
        company: true,
        assignedUser: true,
        messages: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        },
        contacts: {
          take: 10,
          orderBy: { createdAt: 'desc' }
        }
      }
    })
  }

  async findByInstanceId(instanceId: string): Promise<WhatsAppConnection | null> {
    return prisma.whatsAppConnection.findFirst({
      where: { instanceId },
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async findByPhoneNumber(phoneNumber: string, companyId: string): Promise<WhatsAppConnection | null> {
    return prisma.whatsAppConnection.findFirst({
      where: { 
        phoneNumber,
        companyId
      },
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async update(id: string, data: UpdateWhatsAppConnectionData): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.update({
      where: { id },
      data,
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async delete(id: string): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.delete({
      where: { id }
    })
  }

  async findMany(filters: WhatsAppConnectionFilters = {}, page = 1, limit = 10): Promise<{
    connections: WhatsAppConnection[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.WhatsAppConnectionWhereInput = {}

    if (filters.companyId) {
      where.companyId = filters.companyId
    }

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.type) {
      where.type = filters.type
    }

    if (filters.assignedUserId) {
      where.assignedUserId = filters.assignedUserId
    }

    if (filters.isActive !== undefined) {
      where.isActive = filters.isActive
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { phoneNumber: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    const [connections, total] = await Promise.all([
      prisma.whatsAppConnection.findMany({
        where,
        include: {
          company: true,
          assignedUser: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.whatsAppConnection.count({ where })
    ])

    return {
      connections,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async updateStatus(id: string, status: ConnectionStatus, errorMessage?: string): Promise<WhatsAppConnection> {
    const updateData: UpdateWhatsAppConnectionData = { status }

    if (status === ConnectionStatus.CONNECTED) {
      updateData.lastConnectedAt = new Date()
      updateData.errorMessage = null
    } else if (status === ConnectionStatus.DISCONNECTED) {
      updateData.lastDisconnectedAt = new Date()
    }

    if (errorMessage) {
      updateData.errorMessage = errorMessage
    }

    return prisma.whatsAppConnection.update({
      where: { id },
      data: updateData,
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async updateQrCode(id: string, qrCode: string): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.update({
      where: { id },
      data: { qrCode },
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async clearQrCode(id: string): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.update({
      where: { id },
      data: { qrCode: null },
      include: {
        company: true,
        assignedUser: true
      }
    })
  }

  async findByCompany(companyId: string): Promise<WhatsAppConnection[]> {
    return prisma.whatsAppConnection.findMany({
      where: { companyId },
      include: {
        company: true,
        assignedUser: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }

  async findConnected(companyId?: string): Promise<WhatsAppConnection[]> {
    const where: Prisma.WhatsAppConnectionWhereInput = {
      status: ConnectionStatus.CONNECTED,
      isActive: true
    }

    if (companyId) {
      where.companyId = companyId
    }

    return prisma.whatsAppConnection.findMany({
      where,
      include: {
        company: true,
        assignedUser: true
      },
      orderBy: {
        lastConnectedAt: 'desc'
      }
    })
  }

  async countByStatus(companyId: string): Promise<{
    connected: number
    disconnected: number
    pending: number
    error: number
    total: number
  }> {
    const [connected, disconnected, pending, error, total] = await Promise.all([
      prisma.whatsAppConnection.count({ 
        where: { companyId, status: ConnectionStatus.CONNECTED } 
      }),
      prisma.whatsAppConnection.count({ 
        where: { companyId, status: ConnectionStatus.DISCONNECTED } 
      }),
      prisma.whatsAppConnection.count({ 
        where: { companyId, status: ConnectionStatus.PENDING } 
      }),
      prisma.whatsAppConnection.count({ 
        where: { companyId, status: ConnectionStatus.ERROR } 
      }),
      prisma.whatsAppConnection.count({ where: { companyId } })
    ])

    return {
      connected,
      disconnected,
      pending,
      error,
      total
    }
  }

  async updateConnectionData(id: string, connectionData: any): Promise<WhatsAppConnection> {
    return prisma.whatsAppConnection.update({
      where: { id },
      data: { connectionData },
      include: {
        company: true,
        assignedUser: true
      }
    })
  }
}
