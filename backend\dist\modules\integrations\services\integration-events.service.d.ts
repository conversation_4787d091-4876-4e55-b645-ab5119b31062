import { IntegrationsService } from './integrations.service';
export declare class IntegrationEventsService {
    private integrationsService;
    private readonly logger;
    constructor(integrationsService: IntegrationsService);
    onMessageReceived(data: {
        messageId: string;
        contactId: string;
        contactPhone: string;
        contactName?: string;
        contactTags?: string[];
        connectionId: string;
        content: string;
        type: string;
        timestamp: Date;
        companyId: string;
        userId?: string;
    }): Promise<void>;
    onMessageSent(data: {
        messageId: string;
        contactId: string;
        contactPhone: string;
        contactName?: string;
        contactTags?: string[];
        connectionId: string;
        content: string;
        type: string;
        timestamp: Date;
        companyId: string;
        userId: string;
    }): Promise<void>;
    onContactCreated(data: {
        contactId: string;
        contactPhone: string;
        contactName?: string;
        contactTags?: string[];
        connectionId: string;
        companyId: string;
        userId?: string;
        source?: string;
        customFields?: Record<string, any>;
    }): Promise<void>;
    onContactUpdated(data: {
        contactId: string;
        contactPhone: string;
        contactName?: string;
        contactTags?: string[];
        connectionId: string;
        companyId: string;
        userId: string;
        changes: Record<string, {
            old: any;
            new: any;
        }>;
        customFields?: Record<string, any>;
    }): Promise<void>;
    onLeadConverted(data: {
        leadId: string;
        contactId: string;
        contactPhone: string;
        contactName?: string;
        contactTags?: string[];
        connectionId: string;
        companyId: string;
        userId: string;
        conversionData?: Record<string, any>;
    }): Promise<void>;
    onAutomationTriggered(data: {
        automationId: string;
        automationName: string;
        executionId: string;
        contactId: string;
        contactPhone: string;
        contactName?: string;
        connectionId: string;
        companyId: string;
        triggerType: string;
        userId?: string;
    }): Promise<void>;
    onAutomationCompleted(data: {
        automationId: string;
        automationName: string;
        executionId: string;
        contactId: string;
        contactPhone: string;
        contactName?: string;
        connectionId: string;
        companyId: string;
        status: string;
        duration: number;
        results: Record<string, any>;
        userId?: string;
    }): Promise<void>;
    onConnectionStatusChanged(data: {
        connectionId: string;
        connectionName: string;
        phoneNumber: string;
        oldStatus: string;
        newStatus: string;
        companyId: string;
        userId?: string;
    }): Promise<void>;
    onUserCreated(data: {
        userId: string;
        userName: string;
        userEmail: string;
        userRole: string;
        companyId: string;
        createdBy: string;
    }): Promise<void>;
    onSubscriptionChanged(data: {
        subscriptionId: string;
        planName: string;
        oldStatus?: string;
        newStatus: string;
        companyId: string;
        userId?: string;
        changeType: 'created' | 'updated' | 'cancelled' | 'expired';
    }): Promise<void>;
    onPaymentReceived(data: {
        paymentId: string;
        subscriptionId?: string;
        amount: number;
        currency: string;
        paymentMethod: string;
        status: string;
        companyId: string;
    }): Promise<void>;
    onCustomEvent(data: {
        eventName: string;
        eventData: Record<string, any>;
        companyId: string;
        userId?: string;
        contactId?: string;
        connectionId?: string;
    }): Promise<void>;
}
