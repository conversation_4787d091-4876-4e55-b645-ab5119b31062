{"version": 3, "file": "ParsingBasics.test.js", "sourceRoot": "", "sources": ["../../src/__tests__/ParsingBasics.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAIL,kBAAkB,EAClB,kBAAkB,EAClB,kBAAkB,EACnB,MAAM,UAAU,CAAC;AAClB,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAC;AAE9D,IAAI,CAAC,0CAA0C,EAAE;IAC/C,IAAM,aAAa,GAAkB,WAAW,CAAC,+BAA+B,CAC9E,CAAC,KAAK,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,4BAA4B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5F,CAAC;IAEF,IAAM,UAAU,GAAe,aAAa,CAAC,UAAU,CAAC;IACxD,IAAM,cAAc,GAA2B,UAAU,CAAC,cAAc,CAAC;IAEzE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAChD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iDAAiD,EAAE;IACtD,IAAM,aAAa,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IACnE,aAAa,CAAC,iBAAiB,CAAC;QAC9B,IAAI,kBAAkB,CAAC;YACrB,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,kBAAkB,CAAC,QAAQ;SACxC,CAAC;QACF,IAAI,kBAAkB,CAAC;YACrB,OAAO,EAAE,iBAAiB;YAC1B,UAAU,EAAE,kBAAkB,CAAC,WAAW;SAC3C,CAAC;KACH,CAAC,CAAC;IAEH,IAAM,aAAa,GAAkB,WAAW,CAAC,+BAA+B,CAC9E;QACE,KAAK;QACL,wCAAwC;QACxC,IAAI;QACJ,aAAa;QACb,uFAAuF;QACvF,IAAI;QACJ,iBAAiB;QACjB,4DAA4D;QAC5D,IAAI;QACJ,sCAAsC;QACtC,yCAAyC;QACzC,kDAAkD;QAClD,IAAI;QACJ,0BAA0B;QAC1B,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,aAAa,CACd,CAAC;IAEF,IAAM,UAAU,GAAe,aAAa,CAAC,UAAU,CAAC;IACxD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kBAAkB,EAAE;IACvB,IAAM,aAAa,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IACnE,aAAa,CAAC,iBAAiB,CAAC;QAC9B,IAAI,kBAAkB,CAAC;YACrB,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,kBAAkB,CAAC,QAAQ;SACxC,CAAC;QACF,IAAI,kBAAkB,CAAC;YACrB,OAAO,EAAE,iBAAiB;YAC1B,UAAU,EAAE,kBAAkB,CAAC,WAAW;SAC3C,CAAC;KACH,CAAC,CAAC;IAEH,IAAM,aAAa,GAAkB,WAAW,CAAC,+BAA+B,CAC9E;QACE,KAAK;QACL,4EAA4E;QAC5E,4DAA4D;QAC5D,0BAA0B;QAC1B,gDAAgD;QAChD,yEAAyE;QACzE,iBAAiB;QACjB,4DAA4D;QAC5D,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ,aAAa,CACd,CAAC;IAEF,IAAM,UAAU,GAAe,aAAa,CAAC,UAAU,CAAC;IACxD,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,6BAA6B,EAAE;IAClC,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,oCAAoC;QACpC,qCAAqC;QACrC,gDAAgD;QAChD,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,qBAAqB,EAAE;IAC1B,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,8CAA8C;QAC9C,IAAI;QACJ,sEAAsE;QACtE,yCAAyC;QACzC,wEAAwE;QACxE,qBAAqB;QACrB,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE;IAC/C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,kCAAkC;QAClC,gCAAgC;QAChC,iCAAiC;QACjC,mCAAmC;QACnC,kCAAkC;QAClC,gCAAgC;QAChC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,iCAAiC;QACjC,6BAA6B;QAC7B,8BAA8B;QAC9B,gCAAgC;QAChC,iCAAiC;QACjC,mCAAmC;QACnC,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gCAAgC,EAAE;IACrC,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,cAAc;QACd,IAAI;QACJ,wBAAwB;QACxB,oBAAoB;QACpB,IAAI;QACJ,YAAY;QACZ,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uBAAuB,EAAE;IAC5B,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,cAAc,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACxF,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport {\r\n  type StandardModifierTagSet,\r\n  type DocComment,\r\n  type ParserContext,\r\n  TSDocConfiguration,\r\n  TSDocTagDefinition,\r\n  TSDocTagSyntaxKind\r\n} from '../index';\r\nimport { TestHelpers } from '../parser/__tests__/TestHelpers';\r\n\r\ntest('01 Simple @beta and @internal extraction', () => {\r\n  const parserContext: ParserContext = TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    ['/**', ' * START @beta', ' * @unknownTag', ' * @internal @internal END', ' */'].join('\\n')\r\n  );\r\n\r\n  const docComment: DocComment = parserContext.docComment;\r\n  const modifierTagSet: StandardModifierTagSet = docComment.modifierTagSet;\r\n\r\n  expect(modifierTagSet.isAlpha()).toEqual(false);\r\n  expect(modifierTagSet.isBeta()).toEqual(true);\r\n  expect(modifierTagSet.isInternal()).toEqual(true);\r\n});\r\n\r\ntest('02 A basic TSDoc comment with common components', () => {\r\n  const configuration: TSDocConfiguration = new TSDocConfiguration();\r\n  configuration.addTagDefinitions([\r\n    new TSDocTagDefinition({\r\n      tagName: '@customBlock',\r\n      syntaxKind: TSDocTagSyntaxKind.BlockTag\r\n    }),\r\n    new TSDocTagDefinition({\r\n      tagName: '@customModifier',\r\n      syntaxKind: TSDocTagSyntaxKind.ModifierTag\r\n    })\r\n  ]);\r\n\r\n  const parserContext: ParserContext = TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * Returns the average of two numbers.',\r\n      ' *',\r\n      ' * @remarks',\r\n      ' * This method is part of the {@link core-library#Statistics | Statistics subsystem}.',\r\n      ' *',\r\n      ' * @customBlock',\r\n      ' * This is a custom block containing an @undefinedBlockTag',\r\n      ' *',\r\n      ' * @param x - The first input number',\r\n      ' * @param y$_ - The second input number',\r\n      ' * @returns The arithmetic mean of `x` and `y$_`',\r\n      ' *',\r\n      ' * @beta @customModifier',\r\n      ' */'\r\n    ].join('\\n'),\r\n    configuration\r\n  );\r\n\r\n  const docComment: DocComment = parserContext.docComment;\r\n  expect(docComment.modifierTagSet.hasTagName('@customModifier')).toEqual(true);\r\n});\r\n\r\ntest('03 Jumbled order', () => {\r\n  const configuration: TSDocConfiguration = new TSDocConfiguration();\r\n  configuration.addTagDefinitions([\r\n    new TSDocTagDefinition({\r\n      tagName: '@customBlock',\r\n      syntaxKind: TSDocTagSyntaxKind.BlockTag\r\n    }),\r\n    new TSDocTagDefinition({\r\n      tagName: '@customModifier',\r\n      syntaxKind: TSDocTagSyntaxKind.ModifierTag\r\n    })\r\n  ]);\r\n\r\n  const parserContext: ParserContext = TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * Returns the average of two numbers. @remarks This method is part of the',\r\n      ' * {@link core-library#Statistics | Statistics subsystem}.',\r\n      ' * @beta @customModifier',\r\n      ' * @returns The arithmetic mean of `x` and `y`',\r\n      ' * @param x - The first input number @param y - The second input number',\r\n      ' * @customBlock',\r\n      ' * This is a custom block containing an @undefinedBlockTag',\r\n      ' */'\r\n    ].join('\\n'),\r\n    configuration\r\n  );\r\n\r\n  const docComment: DocComment = parserContext.docComment;\r\n  expect(docComment.modifierTagSet.hasTagName('@customModifier')).toEqual(true);\r\n});\r\n\r\ntest('03 Incomplete @param blocks', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * @param - The first input number',\r\n      ' * @param y The second input number',\r\n      ' * @returns The arithmetic mean of `x` and `y`',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 typeParam blocks', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * Constructs a map from a JavaScript object',\r\n      ' *',\r\n      ' * @typeParam K - The generic type parameter indicating the key type',\r\n      ' * @param jsonObject - The input object',\r\n      ' * @typeParam V - The generic type parameter indicating the value type',\r\n      ' * @returns The map',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('05 Invalid JSDoc syntax in @param blocks', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * @param {type} a - description',\r\n      ' * @param {{}} b - description',\r\n      ' * @param {\"{\"} c - description',\r\n      ' * @param {\"\\\\\"\"} d - description',\r\n      ' * @param e {type} - description',\r\n      ' * @param f {{}} - description',\r\n      ' * @param g {\"{\"} - description',\r\n      ' * @param h - {type} description',\r\n      ' * @param i - {{}} description',\r\n      ' * @param j - {\"{\"} description',\r\n      ' * @param [k] - description',\r\n      ' * @param [l=] - description',\r\n      ' * @param [m=[]] - description',\r\n      ' * @param [n=\"[\"] - description',\r\n      ' * @param [o=\"\\\\\"\"] - description',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('06 Invalid JSDoc optional name', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    [\r\n      '/**',\r\n      ' * Example 1',\r\n      ' *',\r\n      ' * @param [n - this is',\r\n      ' * the description',\r\n      ' *',\r\n      ' * @public',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('07 Invalid JSDoc type', () => {\r\n  TestHelpers.parseAndMatchDocCommentSnapshot(\r\n    ['/**', ' * Example 1', ' *', ' * @param { test', ' *', ' * @public', ' */'].join('\\n')\r\n  );\r\n});\r\n"]}