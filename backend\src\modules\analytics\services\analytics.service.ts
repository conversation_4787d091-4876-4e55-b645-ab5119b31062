import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { 
  AnalyticsEvent, 
  AnalyticsEventDocument, 
  EventType, 
  EventCategory,
  EventProperties 
} from '../../../database/entities/analytics-event.schema';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { 
  AnalyticsQueryDto, 
  DashboardQueryDto, 
  TimeRange, 
  GroupBy, 
  MetricType 
} from '../dto/analytics-query.dto';
import { 
  AnalyticsMetric, 
  DashboardMetrics, 
  MetricDataPoint,
  ConversationAnalytics,
  UserActivityAnalytics,
  PerformanceAnalytics 
} from '../dto/analytics-response.dto';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    @InjectModel(AnalyticsEvent.name)
    private analyticsEventModel: Model<AnalyticsEventDocument>,
  ) {}

  async trackEvent(
    type: EventType,
    category: EventCategory,
    companyId: string,
    properties: EventProperties = {},
    userId?: string,
    sessionId?: string,
  ): Promise<void> {
    try {
      const timestamp = new Date();
      
      // Preparar campos para agregação
      const date = timestamp.toISOString().split('T')[0]; // YYYY-MM-DD
      const hour = timestamp.toISOString().substring(0, 13); // YYYY-MM-DD HH
      const month = timestamp.toISOString().substring(0, 7); // YYYY-MM
      const year = timestamp.getFullYear().toString();
      const dayOfWeek = timestamp.getDay();
      const hourOfDay = timestamp.getHours();

      const event = new this.analyticsEventModel({
        type,
        category,
        timestamp,
        companyId: new Types.ObjectId(companyId),
        userId,
        sessionId,
        properties,
        date,
        hour,
        month,
        year,
        dayOfWeek,
        hourOfDay,
        value: properties.value || 1,
        duration: properties.duration,
      });

      await event.save();
      this.logger.debug(`Event tracked: ${type} for company ${companyId}`);
    } catch (error) {
      this.logger.error(`Failed to track event: ${error.message}`, error.stack);
    }
  }

  async getDashboardMetrics(
    query: DashboardQueryDto,
    currentUser: AuthenticatedUser,
  ): Promise<DashboardMetrics> {
    const { startDate, endDate } = this.getDateRange(query.timeRange || TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };

    // Filtros base
    const baseFilter = {
      ...companyFilter,
      timestamp: { $gte: startDate, $lte: endDate },
    };

    if (query.connectionIds && query.connectionIds.length > 0) {
      baseFilter['properties.connectionId'] = { $in: query.connectionIds };
    }

    // Buscar métricas em paralelo
    const [
      messagesSent,
      messagesReceived,
      messagesDelivered,
      messagesRead,
      responseTimes,
      newContacts,
      leadsConverted,
      activeConnections,
      activeUsers,
    ] = await Promise.all([
      this.getMetricTimeSeries(baseFilter, EventType.MESSAGE_SENT, GroupBy.DAY),
      this.getMetricTimeSeries(baseFilter, EventType.MESSAGE_RECEIVED, GroupBy.DAY),
      this.getMetricTimeSeries(baseFilter, EventType.MESSAGE_DELIVERED, GroupBy.DAY),
      this.getMetricTimeSeries(baseFilter, EventType.MESSAGE_READ, GroupBy.DAY),
      this.getAverageResponseTime(baseFilter),
      this.getMetricTimeSeries(baseFilter, EventType.CONTACT_CREATED, GroupBy.DAY),
      this.getMetricTimeSeries(baseFilter, EventType.LEAD_CONVERTED, GroupBy.DAY),
      this.getActiveConnections(companyFilter),
      this.getActiveUsers(baseFilter),
    ]);

    // Calcular taxas
    const deliveryRate = this.calculateRate(messagesDelivered, messagesSent);
    const readRate = this.calculateRate(messagesRead, messagesDelivered);
    const conversionRate = this.calculateConversionRate(leadsConverted, newContacts);

    return {
      messagesSent: this.formatMetric('messages_sent', messagesSent),
      messagesReceived: this.formatMetric('messages_received', messagesReceived),
      deliveryRate: this.formatMetric('delivery_rate', deliveryRate, '%'),
      readRate: this.formatMetric('read_rate', readRate, '%'),
      averageResponseTime: this.formatMetric('avg_response_time', responseTimes, 'seconds'),
      newContacts: this.formatMetric('new_contacts', newContacts),
      leadsConverted: this.formatMetric('leads_converted', leadsConverted),
      conversionRate: this.formatMetric('conversion_rate', conversionRate, '%'),
      activeConnections: this.formatMetric('active_connections', activeConnections),
      activeUsers: this.formatMetric('active_users', activeUsers),
    };
  }

  async getConversationAnalytics(
    query: DashboardQueryDto,
    currentUser: AuthenticatedUser,
  ): Promise<ConversationAnalytics> {
    const { startDate, endDate } = this.getDateRange(query.timeRange || TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };

    const baseFilter = {
      ...companyFilter,
      timestamp: { $gte: startDate, $lte: endDate },
    };

    const [
      totalConversations,
      activeConversations,
      conversationMetrics,
      firstResponseMetrics,
    ] = await Promise.all([
      this.getTotalConversations(baseFilter),
      this.getActiveConversations(companyFilter),
      this.getConversationMetrics(baseFilter),
      this.getFirstResponseMetrics(baseFilter),
    ]);

    return {
      totalConversations,
      activeConversations,
      averageConversationDuration: conversationMetrics.avgDuration,
      averageMessagesPerConversation: conversationMetrics.avgMessages,
      firstResponseRate: firstResponseMetrics.rate,
      averageFirstResponseTime: firstResponseMetrics.avgTime,
    };
  }

  async getUserActivityAnalytics(
    query: DashboardQueryDto,
    currentUser: AuthenticatedUser,
  ): Promise<UserActivityAnalytics> {
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };
    const now = new Date();

    const [
      activeToday,
      activeThisWeek,
      activeThisMonth,
      sessionMetrics,
      activityByHour,
      activityByDayOfWeek,
    ] = await Promise.all([
      this.getActiveUsersInPeriod(companyFilter, 1),
      this.getActiveUsersInPeriod(companyFilter, 7),
      this.getActiveUsersInPeriod(companyFilter, 30),
      this.getSessionMetrics(companyFilter),
      this.getActivityByHour(companyFilter),
      this.getActivityByDayOfWeek(companyFilter),
    ]);

    return {
      activeToday,
      activeThisWeek,
      activeThisMonth,
      averageSessionDuration: sessionMetrics.avgDuration,
      activityByHour,
      activityByDayOfWeek,
    };
  }

  async getPerformanceAnalytics(
    query: DashboardQueryDto,
    currentUser: AuthenticatedUser,
  ): Promise<PerformanceAnalytics> {
    const { startDate, endDate } = this.getDateRange(query.timeRange || TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
    const companyFilter = { companyId: new Types.ObjectId(currentUser.companyId) };

    const baseFilter = {
      ...companyFilter,
      timestamp: { $gte: startDate, $lte: endDate },
    };

    const [
      apiMetrics,
      errorMetrics,
      systemMetrics,
    ] = await Promise.all([
      this.getApiMetrics(baseFilter),
      this.getErrorMetrics(baseFilter),
      this.getSystemMetrics(companyFilter),
    ]);

    return {
      averageApiResponseTime: apiMetrics.avgResponseTime,
      errorRate: errorMetrics.rate,
      uptime: systemMetrics.uptime,
      requestsPerMinute: apiMetrics.requestsPerMinute,
      activeWebSocketConnections: systemMetrics.activeWebSocketConnections,
    };
  }

  async getCustomAnalytics(
    query: AnalyticsQueryDto,
    currentUser: AuthenticatedUser,
  ): Promise<AnalyticsMetric[]> {
    const { startDate, endDate } = this.getDateRange(query.timeRange || TimeRange.LAST_7_DAYS, query.startDate, query.endDate);
    
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
      timestamp: { $gte: startDate, $lte: endDate },
    };

    // Aplicar filtros
    if (query.eventTypes && query.eventTypes.length > 0) {
      filter.type = { $in: query.eventTypes };
    }

    if (query.categories && query.categories.length > 0) {
      filter.category = { $in: query.categories };
    }

    if (query.userIds && query.userIds.length > 0) {
      filter.userId = { $in: query.userIds };
    }

    if (query.connectionIds && query.connectionIds.length > 0) {
      filter['properties.connectionId'] = { $in: query.connectionIds };
    }

    if (query.contactPhones && query.contactPhones.length > 0) {
      filter['properties.contactPhone'] = { $in: query.contactPhones };
    }

    if (query.filters) {
      Object.assign(filter, query.filters);
    }

    // Executar agregação
    const results = await this.executeAggregation(filter, query);
    
    return results.map(result => this.formatMetric(
      result._id || 'metric',
      result.timeSeries || [],
      query.metricField ? 'custom' : 'count'
    ));
  }

  // Métodos auxiliares privados

  private getDateRange(timeRange: TimeRange, startDate?: string, endDate?: string) {
    const now = new Date();
    let start: Date;
    let end: Date = now;

    switch (timeRange) {
      case TimeRange.LAST_24_HOURS:
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case TimeRange.LAST_7_DAYS:
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case TimeRange.LAST_30_DAYS:
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case TimeRange.LAST_90_DAYS:
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case TimeRange.LAST_YEAR:
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      case TimeRange.CUSTOM:
        start = startDate ? new Date(startDate) : new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        end = endDate ? new Date(endDate) : now;
        break;
      default:
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return { startDate: start, endDate: end };
  }

  private async getMetricTimeSeries(
    baseFilter: any,
    eventType: EventType,
    groupBy: GroupBy,
  ): Promise<MetricDataPoint[]> {
    const filter = { ...baseFilter, type: eventType };
    
    const groupField = this.getGroupField(groupBy);
    
    const results = await this.analyticsEventModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: `$${groupField}`,
          value: { $sum: 1 },
          timestamp: { $first: '$timestamp' },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return results.map(result => ({
      timestamp: result._id,
      value: result.value,
    }));
  }

  private getGroupField(groupBy: GroupBy): string {
    switch (groupBy) {
      case GroupBy.HOUR:
        return 'hour';
      case GroupBy.DAY:
        return 'date';
      case GroupBy.WEEK:
        return 'date'; // Será agrupado por semana no frontend
      case GroupBy.MONTH:
        return 'month';
      case GroupBy.YEAR:
        return 'year';
      default:
        return 'date';
    }
  }

  private formatMetric(
    name: string,
    timeSeries: MetricDataPoint[],
    unit: string = 'count',
  ): AnalyticsMetric {
    const totalValue = timeSeries.reduce((sum, point) => sum + point.value, 0);
    
    return {
      name,
      value: totalValue,
      timeSeries,
      unit,
      format: unit === '%' ? 'percentage' : 'number',
    };
  }

  private calculateRate(numerator: MetricDataPoint[], denominator: MetricDataPoint[]): MetricDataPoint[] {
    const numeratorMap = new Map(numerator.map(p => [p.timestamp, p.value]));
    
    return denominator.map(point => ({
      timestamp: point.timestamp,
      value: point.value > 0 ? ((numeratorMap.get(point.timestamp) || 0) / point.value) * 100 : 0,
    }));
  }

  private calculateConversionRate(converted: MetricDataPoint[], total: MetricDataPoint[]): MetricDataPoint[] {
    return this.calculateRate(converted, total);
  }

  // Implementações simplificadas dos métodos auxiliares
  private async getAverageResponseTime(filter: any): Promise<MetricDataPoint[]> {
    const results = await this.analyticsEventModel.aggregate([
      { $match: { ...filter, type: EventType.RESPONSE_TIME_MEASURED } },
      {
        $group: {
          _id: '$date',
          value: { $avg: '$properties.responseTime' },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return results.map(result => ({
      timestamp: result._id,
      value: Math.round(result.value || 0),
    }));
  }

  private async getActiveConnections(filter: any): Promise<MetricDataPoint[]> {
    // Implementação simplificada - em produção, buscar do banco de conexões
    return [{ timestamp: new Date().toISOString(), value: 5 }];
  }

  private async getActiveUsers(filter: any): Promise<MetricDataPoint[]> {
    // Implementação simplificada - em produção, buscar usuários únicos ativos
    return [{ timestamp: new Date().toISOString(), value: 10 }];
  }

  private async getTotalConversations(filter: any): Promise<number> {
    return this.analyticsEventModel.countDocuments({
      ...filter,
      type: EventType.CONVERSATION_STARTED,
    });
  }

  private async getActiveConversations(filter: any): Promise<number> {
    // Implementação simplificada
    return 25;
  }

  private async getConversationMetrics(filter: any): Promise<{ avgDuration: number; avgMessages: number }> {
    // Implementação simplificada
    return { avgDuration: 15.5, avgMessages: 8.2 };
  }

  private async getFirstResponseMetrics(filter: any): Promise<{ rate: number; avgTime: number }> {
    // Implementação simplificada
    return { rate: 85.5, avgTime: 12.3 };
  }

  private async getActiveUsersInPeriod(filter: any, days: number): Promise<number> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    const result = await this.analyticsEventModel.distinct('userId', {
      ...filter,
      timestamp: { $gte: startDate },
      type: EventType.USER_LOGIN,
    });

    return result.length;
  }

  private async getSessionMetrics(filter: any): Promise<{ avgDuration: number }> {
    // Implementação simplificada
    return { avgDuration: 45.2 };
  }

  private async getActivityByHour(filter: any): Promise<MetricDataPoint[]> {
    const results = await this.analyticsEventModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$hourOfDay',
          value: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return results.map(result => ({
      timestamp: `${result._id}:00`,
      value: result.value,
    }));
  }

  private async getActivityByDayOfWeek(filter: any): Promise<MetricDataPoint[]> {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    const results = await this.analyticsEventModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$dayOfWeek',
          value: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    return results.map(result => ({
      timestamp: days[result._id],
      value: result.value,
    }));
  }

  private async getApiMetrics(filter: any): Promise<{ avgResponseTime: number; requestsPerMinute: number }> {
    // Implementação simplificada
    return { avgResponseTime: 150, requestsPerMinute: 1250 };
  }

  private async getErrorMetrics(filter: any): Promise<{ rate: number }> {
    // Implementação simplificada
    return { rate: 0.5 };
  }

  private async getSystemMetrics(filter: any): Promise<{ uptime: number; activeWebSocketConnections: number }> {
    // Implementação simplificada
    return { uptime: 99.9, activeWebSocketConnections: 85 };
  }

  private async executeAggregation(filter: any, query: AnalyticsQueryDto): Promise<any[]> {
    // Implementação simplificada da agregação customizada
    const results = await this.analyticsEventModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: query.groupBy === GroupBy.DAY ? '$date' : '$hour',
          value: query.metricType === MetricType.COUNT ? { $sum: 1 } : { $avg: '$value' },
        },
      },
      { $sort: { _id: 1 } },
      { $limit: query.limit || 100 },
    ]);

    return results.map(result => ({
      _id: result._id,
      timeSeries: [{ timestamp: result._id, value: result.value }],
    }));
  }
}
