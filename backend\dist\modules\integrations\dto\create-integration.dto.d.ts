import { IntegrationType, TriggerEvent, IntegrationConfig } from '../../../database/entities/integration.entity';
export declare class AuthConfigDto {
    authType: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';
    apiKey?: string;
    bearerToken?: string;
    basicAuth?: {
        username: string;
        password: string;
    };
    oauth2?: {
        clientId: string;
        clientSecret: string;
        accessToken: string;
        refreshToken: string;
        tokenExpiry?: Date;
        scope?: string[];
    };
}
export declare class GoogleSheetsConfigDto {
    spreadsheetId: string;
    sheetName: string;
    range?: string;
    serviceAccountKey?: string;
}
export declare class CrmConfigDto {
    apiUrl: string;
    objectType: string;
    fieldMapping: Record<string, string>;
    customFields?: Record<string, any>;
}
export declare class EmailConfigDto {
    listId?: string;
    templateId?: string;
    fromEmail?: string;
    fromName?: string;
}
export declare class SlackConfigDto {
    channelId: string;
    botToken: string;
    messageFormat?: string;
}
export declare class FiltersDto {
    contactTags?: string[];
    messageTypes?: string[];
    connectionIds?: string[];
    userIds?: string[];
    customConditions?: Record<string, any>;
}
export declare class DataMappingDto {
    staticFields?: Record<string, any>;
    dynamicFields?: Record<string, string>;
    transformations?: Array<{
        field: string;
        type: 'uppercase' | 'lowercase' | 'date_format' | 'number_format' | 'custom';
        config?: any;
    }>;
}
export declare class IntegrationConfigDto implements IntegrationConfig {
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    headers?: Record<string, string>;
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    authType?: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';
    apiKey?: string;
    bearerToken?: string;
    googleSheets?: GoogleSheetsConfigDto;
    crm?: CrmConfigDto;
    email?: EmailConfigDto;
    slack?: SlackConfigDto;
    filters?: FiltersDto;
    dataMapping?: DataMappingDto;
}
export declare class CreateIntegrationDto {
    name: string;
    description?: string;
    type: IntegrationType;
    triggerEvents: TriggerEvent[];
    config: IntegrationConfigDto;
    active?: boolean;
    rateLimit?: number;
    maxRetries?: number;
    retryDelay?: number;
    timeout?: number;
    metadata?: Record<string, any>;
    tags?: string[];
}
