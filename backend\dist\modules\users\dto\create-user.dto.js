"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const entities_1 = require("../../../database/entities");
class CreateUserDto {
    firstName;
    lastName;
    email;
    phone;
    password;
    role;
    companyId;
    permissions;
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Primeiro nome do usuário',
        example: 'João',
    }),
    (0, class_validator_1.IsString)({ message: 'Primeiro nome deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Primeiro nome deve ter pelo menos 2 caracteres' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Último nome do usuário',
        example: 'Silva',
    }),
    (0, class_validator_1.IsString)({ message: 'Último nome deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Último nome deve ter pelo menos 2 caracteres' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email do usuário',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsEmail)({}, { message: 'Email deve ter um formato válido' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Telefone do usuário',
        example: '+5511999999999',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone deve ser uma string' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Senha do usuário',
        example: 'senha123',
        minLength: 6,
    }),
    (0, class_validator_1.IsString)({ message: 'Senha deve ser uma string' }),
    (0, class_validator_1.MinLength)(6, { message: 'Senha deve ter pelo menos 6 caracteres' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Papel do usuário',
        enum: entities_1.UserRole,
        default: entities_1.UserRole.SELLER,
    }),
    (0, class_validator_1.IsEnum)(entities_1.UserRole, { message: 'Papel deve ser um valor válido' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 'uuid-da-empresa',
    }),
    (0, class_validator_1.IsUUID)(4, { message: 'ID da empresa deve ser um UUID válido' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "companyId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Permissões específicas do usuário',
        type: [String],
        example: ['users:read', 'messages:send'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Permissões devem ser um array' }),
    (0, class_validator_1.IsString)({ each: true, message: 'Cada permissão deve ser uma string' }),
    __metadata("design:type", Array)
], CreateUserDto.prototype, "permissions", void 0);
//# sourceMappingURL=create-user.dto.js.map