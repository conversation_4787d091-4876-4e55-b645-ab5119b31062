import {
  IsString,
  IsEmail,
  IsOptional,
  IsUUID,
  IsUrl,
  Length,
  Matches,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateCompanyDto {
  @ApiProperty({
    description: 'Nome da empresa',
    example: 'Empresa Exemplo Ltda',
  })
  @IsString({ message: 'Nome deve ser uma string' })
  @Length(2, 255, { message: 'Nome deve ter entre 2 e 255 caracteres' })
  name: string;

  @ApiProperty({
    description: 'CNPJ da empresa',
    example: '12.345.678/0001-90',
  })
  @IsString({ message: 'CNPJ deve ser uma string' })
  @Matches(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, {
    message: 'CNPJ deve ter o formato XX.XXX.XXX/XXXX-XX',
  })
  cnpj: string;

  @ApiProperty({
    description: 'Email da empresa',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email deve ter um formato válido' })
  email: string;

  @ApiProperty({
    description: 'Telefone da empresa',
    example: '+5511999999999',
  })
  @IsString({ message: 'Telefone deve ser uma string' })
  @Length(10, 20, { message: 'Telefone deve ter entre 10 e 20 caracteres' })
  phone: string;

  @ApiPropertyOptional({
    description: 'Endereço da empresa',
    example: 'Rua Exemplo, 123 - São Paulo, SP',
  })
  @IsOptional()
  @IsString({ message: 'Endereço deve ser uma string' })
  address?: string;

  @ApiPropertyOptional({
    description: 'Website da empresa',
    example: 'https://www.empresa.com',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Website deve ser uma URL válida' })
  website?: string;

  @ApiPropertyOptional({
    description: 'ID da agência (se for cliente de uma agência)',
    example: 'uuid-da-agencia',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ID da agência deve ser um UUID válido' })
  agencyId?: string;

  @ApiPropertyOptional({
    description: 'Configurações específicas da empresa',
    example: { theme: 'dark', notifications: true },
  })
  @IsOptional()
  settings?: Record<string, any>;
}
