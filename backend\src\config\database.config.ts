import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';

export const getDatabaseConfig = (configService: ConfigService): TypeOrmModuleOptions => {
  const nodeEnv = configService.get('nodeEnv');

  // Configuração base
  const baseConfig = {
    entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
    synchronize: nodeEnv === 'development',
    logging: nodeEnv === 'development',
  };

  // Para desenvolvimento, tentar PostgreSQL primeiro, depois SQLite
  if (nodeEnv === 'development') {
    const databaseUrl = configService.get('DATABASE_URL');

    if (databaseUrl && databaseUrl.includes('postgresql')) {
      return {
        ...baseConfig,
        type: 'postgres',
        host: configService.get('database.host') || 'localhost',
        port: configService.get('database.port') || 5432,
        username: configService.get('database.username') || 'postgres',
        password: configService.get('database.password') || 'password',
        database: configService.get('database.name') || 'whatsapp_platform',
      };
    }

    // Fallback para SQLite em desenvolvimento
    return {
      ...baseConfig,
      type: 'sqlite',
      database: './dev-database.sqlite',
      dropSchema: false, // Manter dados entre reinicializações
      // Configurações específicas para SQLite
      enableWAL: true,
      synchronize: true, // Para desenvolvimento, recriar schema automaticamente
    };
  }

  // Para produção, usar PostgreSQL
  return {
    ...baseConfig,
    type: 'postgres',
    host: configService.get('database.host'),
    port: configService.get('database.port'),
    username: configService.get('database.username'),
    password: configService.get('database.password'),
    database: configService.get('database.name'),
    synchronize: false,
    logging: false,
    ssl: { rejectUnauthorized: false },
  };
};

export const getMongoConfig = (configService: ConfigService) => ({
  uri: configService.get('mongodb.uri'),
  useNewUrlParser: true,
  useUnifiedTopology: true,
});
