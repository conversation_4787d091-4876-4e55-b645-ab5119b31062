import { Contact, Prisma } from '@prisma/client'
import prisma from '../lib/prisma'

export interface CreateContactData {
  phone: string
  name?: string
  email?: string
  avatar?: string
  tags?: string[]
  notes?: string
  customFields?: any
  companyId: string
  whatsappConnectionId: string
}

export interface UpdateContactData {
  name?: string
  email?: string
  avatar?: string
  tags?: string[]
  notes?: string
  customFields?: any
  lastMessageAt?: Date
  messageCount?: number
  isBlocked?: boolean
}

export interface ContactFilters {
  companyId?: string
  whatsappConnectionId?: string
  tags?: string[]
  isBlocked?: boolean
  hasMessages?: boolean
  search?: string
}

export class ContactRepository {
  async create(data: CreateContactData): Promise<Contact> {
    return prisma.contact.create({
      data,
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      }
    })
  }

  async findById(id: string): Promise<Contact | null> {
    return prisma.contact.findUnique({
      where: { id },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 10,
          orderBy: { timestamp: 'desc' }
        },
        conversations: true
      }
    })
  }

  async findByPhone(phone: string, whatsappConnectionId: string): Promise<Contact | null> {
    return prisma.contact.findFirst({
      where: { 
        phone,
        whatsappConnectionId
      },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      }
    })
  }

  async findOrCreate(data: CreateContactData): Promise<Contact> {
    const existing = await this.findByPhone(data.phone, data.whatsappConnectionId)
    
    if (existing) {
      return existing
    }

    return this.create(data)
  }

  async update(id: string, data: UpdateContactData): Promise<Contact> {
    return prisma.contact.update({
      where: { id },
      data,
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      }
    })
  }

  async delete(id: string): Promise<Contact> {
    return prisma.contact.delete({
      where: { id }
    })
  }

  async findMany(filters: ContactFilters = {}, page = 1, limit = 20): Promise<{
    contacts: Contact[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.ContactWhereInput = {}

    if (filters.companyId) {
      where.companyId = filters.companyId
    }

    if (filters.whatsappConnectionId) {
      where.whatsappConnectionId = filters.whatsappConnectionId
    }

    if (filters.isBlocked !== undefined) {
      where.isBlocked = filters.isBlocked
    }

    if (filters.tags && filters.tags.length > 0) {
      where.tags = {
        hasSome: filters.tags
      }
    }

    if (filters.hasMessages !== undefined) {
      if (filters.hasMessages) {
        where.messageCount = { gt: 0 }
      } else {
        where.messageCount = 0
      }
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { phone: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    const [contacts, total] = await Promise.all([
      prisma.contact.findMany({
        where,
        include: {
          company: true,
          whatsappConnection: true,
          messages: {
            take: 1,
            orderBy: { timestamp: 'desc' }
          }
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          lastMessageAt: 'desc'
        }
      }),
      prisma.contact.count({ where })
    ])

    return {
      contacts,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async addTag(id: string, tag: string): Promise<Contact> {
    const contact = await prisma.contact.findUnique({
      where: { id },
      select: { tags: true }
    })

    if (!contact) {
      throw new Error('Contact not found')
    }

    const updatedTags = [...new Set([...contact.tags, tag])]

    return prisma.contact.update({
      where: { id },
      data: { tags: updatedTags },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      }
    })
  }

  async removeTag(id: string, tag: string): Promise<Contact> {
    const contact = await prisma.contact.findUnique({
      where: { id },
      select: { tags: true }
    })

    if (!contact) {
      throw new Error('Contact not found')
    }

    const updatedTags = contact.tags.filter(t => t !== tag)

    return prisma.contact.update({
      where: { id },
      data: { tags: updatedTags },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      }
    })
  }

  async updateMessageStats(id: string, lastMessageAt: Date): Promise<Contact> {
    return prisma.contact.update({
      where: { id },
      data: {
        lastMessageAt,
        messageCount: {
          increment: 1
        }
      },
      include: {
        company: true,
        whatsappConnection: true
      }
    })
  }

  async block(id: string): Promise<Contact> {
    return prisma.contact.update({
      where: { id },
      data: { isBlocked: true },
      include: {
        company: true,
        whatsappConnection: true
      }
    })
  }

  async unblock(id: string): Promise<Contact> {
    return prisma.contact.update({
      where: { id },
      data: { isBlocked: false },
      include: {
        company: true,
        whatsappConnection: true
      }
    })
  }

  async findByCompany(companyId: string): Promise<Contact[]> {
    return prisma.contact.findMany({
      where: { companyId },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      },
      orderBy: {
        lastMessageAt: 'desc'
      }
    })
  }

  async findByConnection(whatsappConnectionId: string): Promise<Contact[]> {
    return prisma.contact.findMany({
      where: { whatsappConnectionId },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      },
      orderBy: {
        lastMessageAt: 'desc'
      }
    })
  }

  async getStats(companyId: string): Promise<{
    total: number
    withMessages: number
    blocked: number
    newToday: number
  }> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const [total, withMessages, blocked, newToday] = await Promise.all([
      prisma.contact.count({ where: { companyId } }),
      prisma.contact.count({ 
        where: { companyId, messageCount: { gt: 0 } } 
      }),
      prisma.contact.count({ 
        where: { companyId, isBlocked: true } 
      }),
      prisma.contact.count({ 
        where: { 
          companyId, 
          createdAt: { gte: today } 
        } 
      })
    ])

    return {
      total,
      withMessages,
      blocked,
      newToday
    }
  }

  async findRecentlyActive(companyId: string, days = 7): Promise<Contact[]> {
    const since = new Date()
    since.setDate(since.getDate() - days)

    return prisma.contact.findMany({
      where: {
        companyId,
        lastMessageAt: {
          gte: since
        }
      },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      },
      orderBy: {
        lastMessageAt: 'desc'
      },
      take: 50
    })
  }

  async searchByTags(tags: string[], companyId: string): Promise<Contact[]> {
    return prisma.contact.findMany({
      where: {
        companyId,
        tags: {
          hasSome: tags
        }
      },
      include: {
        company: true,
        whatsappConnection: true,
        messages: {
          take: 1,
          orderBy: { timestamp: 'desc' }
        }
      },
      orderBy: {
        lastMessageAt: 'desc'
      }
    })
  }
}
