# 🚀 WhatsApp Business Platform API

Uma plataforma completa e robusta para gestão de WhatsApp Business com recursos avançados de automação, analytics, faturamento e integrações externas.

![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)
![NestJS](https://img.shields.io/badge/NestJS-10.0+-red.svg)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)
![MongoDB](https://img.shields.io/badge/MongoDB-6.0+-green.svg)
![Redis](https://img.shields.io/badge/Redis-7.0+-red.svg)
![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

## 📋 Índice

- [Características](#-características)
- [Arquitetura](#-arquitetura)
- [Pré-requisitos](#-pré-requisitos)
- [Instalação](#-instalação)
- [Configuração](#-configuração)
- [Uso](#-uso)
- [API Documentation](#-api-documentation)
- [Testes](#-testes)
- [Deploy](#-deploy)
- [Monitoramento](#-monitoramento)
- [Contribuição](#-contribuição)
- [Licença](#-licença)

## ✨ Características

### 🔐 **Autenticação e Autorização**
- Sistema JWT com refresh tokens
- RBAC (Role-Based Access Control)
- Multi-tenant com isolamento completo
- Recuperação de senha por email
- Middleware de segurança avançado

### 📱 **Gestão de Conexões WhatsApp**
- Integração com Evolution API V2
- Suporte para API Oficial da Meta (preparado)
- Múltiplas conexões por empresa
- Monitoramento de status em tempo real
- QR Code para conexão

### 💬 **Sistema de Mensagens**
- Envio e recebimento de mensagens
- Suporte a texto, imagem, áudio, vídeo, documento
- WebSocket para tempo real
- Histórico completo de conversas
- Busca avançada

### 👥 **Gestão de Contatos**
- Cadastro automático via WhatsApp
- Tags e campos customizados
- Segmentação avançada
- Importação/exportação
- Gestão de leads

### 📊 **Analytics e Relatórios**
- Dashboard em tempo real
- Métricas de engajamento
- Relatórios customizáveis
- Tracking de eventos
- Funil de conversão

### 🤖 **Automação e Chatbot**
- Construtor visual de fluxos
- Integração com IA (OpenAI/Google)
- Respostas automáticas
- Condições e ações avançadas
- Agendamento de mensagens

### 💳 **Sistema de Faturamento**
- Múltiplos planos e ciclos
- Integração com Stripe
- Gestão de assinaturas
- Comissões para agências
- Controle de uso em tempo real

### 🔗 **Integrações Externas**
- Webhooks customizáveis
- Google Sheets, CRMs, Zapier
- APIs de terceiros
- Sistema de retry inteligente
- Mapeamento de dados

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Mobile App    │    │   Third Party   │
│   (React)       │    │   (React Native)│    │   Integrations  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      Load Balancer        │
                    │        (Nginx)            │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │      NestJS API           │
                    │   (Node.js + TypeScript)  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴─────────┐   ┌─────────┴─────────┐   ┌─────────┴─────────┐
│   PostgreSQL      │   │     MongoDB       │   │      Redis        │
│ (Structured Data) │   │  (Messages/Logs)  │   │   (Cache/Queue)   │
└───────────────────┘   └───────────────────┘   └───────────────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │    Evolution API          │
                    │   (WhatsApp Gateway)      │
                    └───────────────────────────┘
```

### 🛠️ **Stack Tecnológico**

- **Backend**: Node.js 18+, TypeScript 5.0+, NestJS 10.0+
- **Bancos de Dados**: PostgreSQL 15+, MongoDB 6.0+, Redis 7.0+
- **Autenticação**: JWT, Passport.js, bcrypt
- **Validação**: class-validator, class-transformer
- **Documentação**: Swagger/OpenAPI 3.0
- **Testes**: Jest, Supertest
- **Monitoramento**: Prometheus, Grafana
- **Deploy**: Docker, Docker Compose
- **CI/CD**: GitHub Actions
- **WhatsApp**: Evolution API V2

## 📋 Pré-requisitos

- Node.js 18+ 
- npm 9+ ou yarn 1.22+
- PostgreSQL 15+
- MongoDB 6.0+
- Redis 7.0+
- Docker & Docker Compose (opcional)

## 🚀 Instalação

### 1. Clone o repositório
```bash
git clone https://github.com/your-org/whatsapp-platform-api.git
cd whatsapp-platform-api
```

### 2. Instale as dependências
```bash
npm install
```

### 3. Configure as variáveis de ambiente
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

### 4. Execute as migrações do banco
```bash
npm run migration:run
```

### 5. Inicie a aplicação
```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod
```

## ⚙️ Configuração

### Variáveis de Ambiente

```env
# Aplicação
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Banco de Dados
DATABASE_URL=postgresql://user:password@localhost:5432/whatsapp_platform
MONGODB_URI=mongodb://localhost:27017/whatsapp_platform
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# WhatsApp
WHATSAPP_API_URL=http://localhost:8080
WHATSAPP_API_KEY=your-evolution-api-key

# Stripe
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Uploads
UPLOAD_DEST=./uploads
MAX_FILE_SIZE=10485760

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100
```

## 📖 Uso

### Iniciando com Docker

```bash
# Subir todos os serviços
docker-compose up -d

# Ver logs
docker-compose logs -f api

# Parar serviços
docker-compose down
```

### Comandos Úteis

```bash
# Desenvolvimento
npm run start:dev          # Inicia em modo desenvolvimento
npm run start:debug        # Inicia com debug
npm run start:prod         # Inicia em produção

# Banco de Dados
npm run migration:generate # Gera nova migração
npm run migration:run      # Executa migrações
npm run migration:revert   # Reverte última migração
npm run seed:run           # Executa seeds

# Testes
npm run test               # Testes unitários
npm run test:e2e           # Testes de integração
npm run test:cov           # Cobertura de testes
npm run test:watch         # Testes em modo watch

# Qualidade de Código
npm run lint               # ESLint
npm run lint:fix           # Corrige problemas do ESLint
npm run format             # Prettier
npm run build              # Build de produção
```

## 📚 API Documentation

A documentação completa da API está disponível via Swagger:

- **Desenvolvimento**: http://localhost:3000/api/docs
- **Staging**: https://api-staging.whatsappplatform.com/api/docs
- **Produção**: https://api.whatsappplatform.com/api/docs

### Principais Endpoints

```
# Autenticação
POST   /auth/register          # Registrar usuário
POST   /auth/login             # Login
POST   /auth/refresh           # Refresh token
GET    /auth/profile           # Perfil do usuário

# Mensagens
GET    /messages               # Listar mensagens
POST   /messages               # Enviar mensagem
GET    /messages/:id           # Obter mensagem
DELETE /messages/:id           # Deletar mensagem

# Contatos
GET    /contacts               # Listar contatos
POST   /contacts               # Criar contato
GET    /contacts/:id           # Obter contato
PATCH  /contacts/:id           # Atualizar contato

# Conexões WhatsApp
GET    /whatsapp/connections   # Listar conexões
POST   /whatsapp/connections   # Criar conexão
GET    /whatsapp/qr/:id        # Obter QR Code

# Analytics
GET    /analytics/dashboard    # Dashboard
GET    /analytics/reports      # Relatórios

# Automação
GET    /automation/flows       # Listar fluxos
POST   /automation/flows       # Criar fluxo
POST   /automation/execute     # Executar automação

# Faturamento
GET    /billing/plans          # Listar planos
POST   /billing/subscriptions  # Criar assinatura
GET    /billing/invoices       # Listar faturas

# Integrações
GET    /integrations           # Listar integrações
POST   /integrations           # Criar integração
POST   /integrations/:id/test  # Testar integração
```

## 🧪 Testes

### Executar Testes

```bash
# Todos os testes
npm run test:all

# Testes unitários
npm run test

# Testes E2E
npm run test:e2e

# Cobertura
npm run test:cov

# Testes específicos
npm run test -- auth.service.spec.ts
```

### Estrutura de Testes

```
test/
├── setup.ts                 # Configuração global
├── global-setup.ts          # Setup antes de todos os testes
├── global-teardown.ts       # Cleanup após todos os testes
├── auth.e2e-spec.ts         # Testes E2E de autenticação
└── ...

src/
├── modules/
│   ├── auth/
│   │   ├── auth.service.spec.ts    # Testes unitários
│   │   └── auth.controller.spec.ts
│   └── ...
```

### Cobertura de Testes

O projeto mantém uma cobertura mínima de **80%** em:
- Statements
- Branches  
- Functions
- Lines

## 🚀 Deploy

### Deploy Automatizado

```bash
# Staging
./scripts/deploy.sh staging

# Produção
./scripts/deploy.sh production v1.0.0
```

### Deploy Manual

```bash
# 1. Executar testes
./scripts/test.sh

# 2. Build
npm run build

# 3. Configurar ambiente
export NODE_ENV=production

# 4. Executar migrações
npm run migration:run

# 5. Iniciar aplicação
npm run start:prod
```

### Deploy com Docker

```bash
# Build da imagem
docker build -t whatsapp-platform-api .

# Executar container
docker run -d \
  --name whatsapp-api \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e DATABASE_URL=... \
  whatsapp-platform-api
```

## 📊 Monitoramento

### Health Check

```bash
curl http://localhost:3000/health
```

### Métricas

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin)

### Logs

```bash
# Ver logs da aplicação
docker-compose logs -f api

# Ver logs do banco
docker-compose logs -f postgres

# Ver logs do Redis
docker-compose logs -f redis
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

### Padrões de Código

- Use TypeScript
- Siga o ESLint configurado
- Escreva testes para novas funcionalidades
- Mantenha cobertura > 80%
- Use Conventional Commits

## 📄 Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

- 📧 **Email**: <EMAIL>
- 📚 **Documentação**: https://docs.whatsappplatform.com
- 💬 **Chat**: https://whatsappplatform.com/chat
- 🐛 **Issues**: https://github.com/whatsapp-platform/api/issues

---

<p align="center">
  Feito com ❤️ pela equipe WhatsApp Platform
</p>
