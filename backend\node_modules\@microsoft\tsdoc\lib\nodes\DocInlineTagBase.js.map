{"version": 3, "file": "DocInlineTagBase.js", "sourceRoot": "", "sources": ["../../src/nodes/DocInlineTagBase.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;;;;;;;;;;AAE3D,OAAO,EAA2B,OAAO,EAAiC,MAAM,WAAW,CAAC;AAC5F,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAsBvD;;GAEG;AACH;IAA+C,oCAAO;IAUpD;;;OAGG;IACH,0BAAmB,UAA2E;QAC5F,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,YAAY,CAAC,oBAAoB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAEtD,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,0BAA0B;gBACnD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;YAEH,KAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC;gBACpC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,iBAAiB;gBAC1C,OAAO,EAAE,UAAU,CAAC,cAAc;aACnC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,0BAA0B,EAAE,CAAC;gBAC1C,KAAI,CAAC,2BAA2B,GAAG,IAAI,UAAU,CAAC;oBAChD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,0BAA0B;iBAC/C,CAAC,CAAC;YACL,CAAC;YAED,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,WAAW,CAAC,0BAA0B;gBACnD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;QACL,CAAC;QAED,KAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC;QACnC,KAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;;IAChE,CAAC;IAUD,sBAAW,qCAAO;QARlB;;;;;;;WAOG;aACH;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;;;OAAA;IAMD,sBAAW,kDAAoB;QAJ/B;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC,CAAC;;;OAAA;IAED,wBAAwB;IACd,0CAAe,GAAzB;QACE;YACE,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,2BAA2B;WAC7B,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,wBAAwB;kBAC7B;IACJ,CAAC;IAQH,uBAAC;AAAD,CAAC,AAxFD,CAA+C,OAAO,GAwFrD", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { type IDocNodeParameters, DocNode, type IDocNodeParsedParameters } from './DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocInlineTagBase}.\r\n */\r\nexport interface IDocInlineTagBaseParameters extends IDocNodeParameters {\r\n  tagName: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocInlineTagBase}.\r\n */\r\nexport interface IDocInlineTagBaseParsedParameters extends IDocNodeParsedParameters {\r\n  openingDelimiterExcerpt: TokenSequence;\r\n\r\n  tagNameExcerpt: TokenSequence;\r\n  tagName: string;\r\n  spacingAfterTagNameExcerpt?: TokenSequence;\r\n\r\n  closingDelimiterExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * The abstract base class for {@link DocInlineTag}, {@link DocLinkTag}, and {@link DocInheritDocTag}.\r\n */\r\nexport abstract class DocInlineTagBase extends DocNode {\r\n  private readonly _openingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _tagName: string;\r\n  private readonly _tagNameWithUpperCase: string;\r\n  private readonly _tagNameExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterTagNameExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _closingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocInlineTagBaseParameters | IDocInlineTagBaseParsedParameters) {\r\n    super(parameters);\r\n\r\n    StringChecks.validateTSDocTagName(parameters.tagName);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.InlineTag_OpeningDelimiter,\r\n        content: parameters.openingDelimiterExcerpt\r\n      });\r\n\r\n      this._tagNameExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.InlineTag_TagName,\r\n        content: parameters.tagNameExcerpt\r\n      });\r\n\r\n      if (parameters.spacingAfterTagNameExcerpt) {\r\n        this._spacingAfterTagNameExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterTagNameExcerpt\r\n        });\r\n      }\r\n\r\n      this._closingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.InlineTag_ClosingDelimiter,\r\n        content: parameters.closingDelimiterExcerpt\r\n      });\r\n    }\r\n\r\n    this._tagName = parameters.tagName;\r\n    this._tagNameWithUpperCase = parameters.tagName.toUpperCase();\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag name.  TSDoc tag names start with an at-sign (`@`) followed\r\n   * by ASCII letters using \"camelCase\" capitalization.\r\n   *\r\n   * @remarks\r\n   * For example, if the inline tag is `{@link Guid.toString | the toString() method}`\r\n   * then the tag name would be `@link`.\r\n   */\r\n  public get tagName(): string {\r\n    return this._tagName;\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tag name in all capitals, which is used for performing\r\n   * case-insensitive comparisons or lookups.\r\n   */\r\n  public get tagNameWithUpperCase(): string {\r\n    return this._tagNameWithUpperCase;\r\n  }\r\n\r\n  /** @override @sealed */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._openingDelimiterExcerpt,\r\n      this._tagNameExcerpt,\r\n      this._spacingAfterTagNameExcerpt,\r\n      ...this.getChildNodesForContent(),\r\n      this._closingDelimiterExcerpt\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Allows child classes to replace the tagContentParticle with a more detailed\r\n   * set of nodes.\r\n   * @virtual\r\n   */\r\n  protected abstract getChildNodesForContent(): ReadonlyArray<DocNode | undefined>;\r\n}\r\n"]}