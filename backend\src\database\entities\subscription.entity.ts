import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Company } from './company.entity';
import { Plan } from './plan.entity';
import { Invoice } from './invoice.entity';
import { Payment } from './payment.entity';

export enum SubscriptionStatus {
  TRIAL = 'trial',
  ACTIVE = 'active',
  PAST_DUE = 'past_due',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

export enum SubscriptionType {
  DIRECT = 'direct',
  AGENCY = 'agency',
  RESELLER = 'reseller',
}

export interface UsageMetrics {
  connections: number;
  contacts: number;
  messages: number;
  users: number;
  automations: number;
  chatbots: number;
  storageUsedGB: number;
  apiCalls: number;
  webhookCalls: number;
  lastUpdated: Date;
}

@Entity('subscriptions')
export class Subscription {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  companyId: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column()
  planId: string;

  @ManyToOne(() => Plan)
  @JoinColumn({ name: 'planId' })
  plan: Plan;

  @Column({
    type: 'enum',
    enum: SubscriptionStatus,
    default: SubscriptionStatus.PENDING,
  })
  status: SubscriptionStatus;

  @Column({
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.DIRECT,
  })
  type: SubscriptionType;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @Column({ nullable: true })
  trialEndDate: Date;

  @Column({ nullable: true })
  cancelledAt: Date;

  @Column({ nullable: true })
  cancelReason: string | null;

  @Column({ default: false })
  cancelAtPeriodEnd: boolean;

  // Configurações de cobrança
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  setupFee: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  discountPercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount: number;

  @Column({ nullable: true })
  discountEndDate: Date;

  @Column({ nullable: true })
  couponCode: string;

  // Configurações de agência
  @Column({ nullable: true })
  agencyId: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  agencyCommissionPercentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  agencyFixedCommission: number;

  // Métricas de uso
  @Column({ type: 'json', nullable: true })
  currentUsage: UsageMetrics;

  @Column({ type: 'json', nullable: true })
  usageHistory: Record<string, UsageMetrics>; // Por mês: "2023-12" -> UsageMetrics

  // Configurações de pagamento
  @Column({ nullable: true })
  stripeSubscriptionId: string;

  @Column({ nullable: true })
  stripeCustomerId: string;

  @Column({ nullable: true })
  paymentMethodId: string;

  @Column({ default: true })
  autoRenew: boolean;

  @Column({ nullable: true })
  nextBillingDate: Date;

  @Column({ nullable: true })
  lastBillingDate: Date;

  // Configurações de upgrade/downgrade
  @Column({ nullable: true })
  pendingPlanId: string;

  @Column({ nullable: true })
  planChangeDate: Date;

  @Column({ default: false })
  isProrated: boolean;

  // Metadados
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  customFeatures: Record<string, any>;

  // Configurações de notificação
  @Column({ default: true })
  emailNotifications: boolean;

  @Column({ default: false })
  webhookNotifications: boolean;

  @Column({ nullable: true })
  webhookUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Invoice, invoice => invoice.subscription)
  invoices: Invoice[];

  @OneToMany(() => Payment, payment => payment.subscription)
  payments: Payment[];

  // Métodos auxiliares
  isActive(): boolean {
    return this.status === SubscriptionStatus.ACTIVE;
  }

  isTrial(): boolean {
    return this.status === SubscriptionStatus.TRIAL;
  }

  isExpired(): boolean {
    return this.status === SubscriptionStatus.EXPIRED || 
           (this.endDate && new Date() > this.endDate);
  }

  isCancelled(): boolean {
    return this.status === SubscriptionStatus.CANCELLED;
  }

  isPastDue(): boolean {
    return this.status === SubscriptionStatus.PAST_DUE;
  }

  daysUntilExpiry(): number {
    if (!this.endDate) return 0;
    const now = new Date();
    const diffTime = this.endDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  daysInTrial(): number {
    if (!this.trialEndDate) return 0;
    const now = new Date();
    const diffTime = this.trialEndDate.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  }

  getCurrentPrice(): number {
    let finalPrice = this.price;
    
    if (this.discountPercentage > 0) {
      finalPrice = finalPrice * (1 - this.discountPercentage / 100);
    }
    
    if (this.discountAmount > 0) {
      finalPrice = Math.max(0, finalPrice - this.discountAmount);
    }
    
    return finalPrice;
  }

  getAgencyCommission(): number {
    if (!this.agencyId) return 0;
    
    const subscriptionPrice = this.getCurrentPrice();
    const percentageCommission = (subscriptionPrice * this.agencyCommissionPercentage) / 100;
    return percentageCommission + this.agencyFixedCommission;
  }

  hasExceededLimit(feature: string, currentValue: number, planLimit: number): boolean {
    if (planLimit === -1) return false; // Unlimited
    return currentValue >= planLimit;
  }

  getUsagePercentage(feature: string, currentValue: number, planLimit: number): number {
    if (planLimit === -1) return 0; // Unlimited
    if (planLimit === 0) return 100;
    return Math.min(100, (currentValue / planLimit) * 100);
  }

  canUpgrade(): boolean {
    return this.isActive() && !this.pendingPlanId;
  }

  canDowngrade(): boolean {
    return this.isActive() && !this.pendingPlanId;
  }

  canCancel(): boolean {
    return this.isActive() || this.isTrial();
  }

  shouldRenew(): boolean {
    return this.autoRenew && this.isActive() && !this.cancelAtPeriodEnd;
  }
}
