import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { MessagesService, FindMessagesOptions } from '../services/messages.service';
import { MessageResponseDto } from '../dto/message-response.dto';
import { CurrentUser, AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { TenantGuard } from '../../../common/guards/tenant.guard';
import { RequirePermissions, PERMISSIONS } from '../../../common/decorators/permissions.decorator';
import { MessageDirection, MessageType, MessageStatus } from '../../../database/entities';

@ApiTags('Mensagens')
@Controller('messages')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class MessagesController {
  constructor(private readonly messagesService: MessagesService) {}

  @Get()
  @RequirePermissions(PERMISSIONS.MESSAGES_READ)
  @ApiOperation({ summary: 'Listar mensagens' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filtrar por empresa' })
  @ApiQuery({ name: 'whatsappConnectionId', required: false, description: 'Filtrar por conexão WhatsApp' })
  @ApiQuery({ name: 'contactPhone', required: false, description: 'Filtrar por telefone do contato' })
  @ApiQuery({ name: 'conversationId', required: false, description: 'Filtrar por conversa' })
  @ApiQuery({ name: 'direction', required: false, enum: MessageDirection, description: 'Filtrar por direção' })
  @ApiQuery({ name: 'type', required: false, enum: MessageType, description: 'Filtrar por tipo' })
  @ApiQuery({ name: 'status', required: false, enum: MessageStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'isAutomated', required: false, type: Boolean, description: 'Filtrar por automatizadas' })
  @ApiQuery({ name: 'sentBy', required: false, description: 'Filtrar por remetente' })
  @ApiQuery({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' })
  @ApiQuery({ name: 'dateTo', required: false, type: Date, description: 'Data final' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar no conteúdo' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Campo para ordenação' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' })
  @ApiResponse({
    status: 200,
    description: 'Lista de mensagens',
    type: [MessageResponseDto],
  })
  async findAll(
    @Query() query: FindMessagesOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    const result = await this.messagesService.findAll(query, currentUser);
    
    return {
      ...result,
      messages: result.messages.map(message => 
        plainToClass(MessageResponseDto, message.toObject(), { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('stats')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter estatísticas de mensagens' })
  @ApiQuery({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' })
  @ApiQuery({ name: 'dateTo', required: false, type: Date, description: 'Data final' })
  @ApiResponse({
    status: 200,
    description: 'Estatísticas de mensagens',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        sent: { type: 'number' },
        received: { type: 'number' },
        automated: { type: 'number' },
        failed: { type: 'number' },
      },
    },
  })
  async getStats(
    @Query('dateFrom') dateFrom?: Date,
    @Query('dateTo') dateTo?: Date,
    @CurrentUser() currentUser?: AuthenticatedUser,
  ) {
    return this.messagesService.getMessageStats(currentUser!, dateFrom, dateTo);
  }

  @Get('conversation/:contactPhone/:whatsappConnectionId')
  @RequirePermissions(PERMISSIONS.MESSAGES_READ)
  @ApiOperation({ summary: 'Obter conversa com contato' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Limite de mensagens' })
  @ApiResponse({
    status: 200,
    description: 'Mensagens da conversa',
    type: [MessageResponseDto],
  })
  async getConversation(
    @Param('contactPhone') contactPhone: string,
    @Param('whatsappConnectionId') whatsappConnectionId: string,
    @Query('limit') limit?: number,
    @CurrentUser() currentUser?: AuthenticatedUser,
  ) {
    const messages = await this.messagesService.getConversation(
      contactPhone,
      whatsappConnectionId,
      currentUser!,
      limit,
    );

    return messages.map(message => 
      plainToClass(MessageResponseDto, message.toObject(), { excludeExtraneousValues: true })
    );
  }

  @Get(':id')
  @RequirePermissions(PERMISSIONS.MESSAGES_READ)
  @ApiOperation({ summary: 'Obter mensagem por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados da mensagem',
    type: MessageResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Mensagem não encontrada',
  })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<MessageResponseDto> {
    const message = await this.messagesService.findOne(id, currentUser);
    return plainToClass(MessageResponseDto, message.toObject(), { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @RequirePermissions(PERMISSIONS.MESSAGES_DELETE)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deletar mensagem' })
  @ApiResponse({
    status: 204,
    description: 'Mensagem deletada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Mensagem não encontrada',
  })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.messagesService.deleteMessage(id, currentUser);
  }

  @Patch(':messageId/status')
  @RequirePermissions(PERMISSIONS.MESSAGES_SEND)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Atualizar status da mensagem' })
  @ApiResponse({
    status: 204,
    description: 'Status atualizado com sucesso',
  })
  async updateStatus(
    @Param('messageId') messageId: string,
    @Body() statusData: { status: MessageStatus; timestamp?: Date },
  ): Promise<void> {
    return this.messagesService.updateStatus(
      messageId,
      statusData.status,
      statusData.timestamp,
    );
  }
}
