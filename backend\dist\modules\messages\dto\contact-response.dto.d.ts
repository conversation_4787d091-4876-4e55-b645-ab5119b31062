import { ContactStatus, ContactTag, ContactSource } from '../../../database/entities';
export declare class ContactResponseDto {
    id: string;
    phoneNumber: string;
    name?: string;
    email?: string;
    profilePicture?: string;
    status: ContactStatus;
    tags?: ContactTag[];
    source?: ContactSource;
    customFields?: Record<string, any>;
    lastMessageAt?: Date;
    lastInteractionAt?: Date;
    messageCount: number;
    isLead: boolean;
    leadScore?: number;
    leadStage?: string;
    assignedTo?: string;
    notes?: string;
    whatsappConnectionId: string;
    createdAt: Date;
    updatedAt: Date;
}
