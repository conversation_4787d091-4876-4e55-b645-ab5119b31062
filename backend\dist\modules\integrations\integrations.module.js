"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const axios_1 = require("@nestjs/axios");
const integration_entity_1 = require("../../database/entities/integration.entity");
const webhook_log_entity_1 = require("../../database/entities/webhook-log.entity");
const integrations_service_1 = require("./services/integrations.service");
const integration_events_service_1 = require("./services/integration-events.service");
const integrations_controller_1 = require("./integrations.controller");
let IntegrationsModule = class IntegrationsModule {
};
exports.IntegrationsModule = IntegrationsModule;
exports.IntegrationsModule = IntegrationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                integration_entity_1.Integration,
                webhook_log_entity_1.WebhookLog,
            ]),
            axios_1.HttpModule.register({
                timeout: 30000,
                maxRedirects: 5,
            }),
        ],
        controllers: [integrations_controller_1.IntegrationsController],
        providers: [integrations_service_1.IntegrationsService, integration_events_service_1.IntegrationEventsService],
        exports: [integrations_service_1.IntegrationsService, integration_events_service_1.IntegrationEventsService],
    })
], IntegrationsModule);
//# sourceMappingURL=integrations.module.js.map