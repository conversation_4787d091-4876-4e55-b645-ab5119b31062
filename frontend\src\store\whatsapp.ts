import { create } from 'zustand'
import { 
  WhatsAppConnection, 
  Message, 
  Contact, 
  Conversation,
  ConnectionFilters,
  MessageFilters,
  ContactFilters,
  SendMessageForm,
  CreateConnectionForm
} from '@/types'
import { apiService } from '@/services/api'

interface WhatsAppState {
  // Connections
  connections: WhatsAppConnection[]
  selectedConnection: WhatsAppConnection | null
  connectionsLoading: boolean
  
  // Messages
  messages: Message[]
  selectedConversation: Message[]
  messagesLoading: boolean
  
  // Contacts
  contacts: Contact[]
  contactsLoading: boolean
  
  // Conversations
  conversations: Conversation[]
  selectedConversationId: string | null
  
  // Filters
  connectionFilters: ConnectionFilters
  messageFilters: MessageFilters
  contactFilters: ContactFilters
  
  // Error handling
  error: string | null
  
  // Actions - Connections
  fetchConnections: (filters?: ConnectionFilters) => Promise<void>
  createConnection: (data: CreateConnectionForm) => Promise<WhatsAppConnection>
  updateConnection: (id: string, data: Partial<WhatsAppConnection>) => Promise<void>
  deleteConnection: (id: string) => Promise<void>
  connectWhatsApp: (id: string) => Promise<{ qrCode?: string }>
  disconnectWhatsApp: (id: string) => Promise<void>
  setSelectedConnection: (connection: WhatsAppConnection | null) => void
  
  // Actions - Messages
  fetchMessages: (filters?: MessageFilters) => Promise<void>
  fetchConversation: (contactPhone: string, whatsappConnectionId: string) => Promise<void>
  sendMessage: (data: SendMessageForm) => Promise<Message>
  markMessageAsRead: (messageId: string) => Promise<void>
  addMessage: (message: Message) => void
  updateMessage: (messageId: string, updates: Partial<Message>) => void
  
  // Actions - Contacts
  fetchContacts: (filters?: ContactFilters) => Promise<void>
  createContact: (data: Partial<Contact>) => Promise<Contact>
  updateContact: (id: string, data: Partial<Contact>) => Promise<void>
  deleteContact: (id: string) => Promise<void>
  
  // Actions - Conversations
  setSelectedConversation: (conversationId: string | null) => void
  
  // Actions - Filters
  setConnectionFilters: (filters: ConnectionFilters) => void
  setMessageFilters: (filters: MessageFilters) => void
  setContactFilters: (filters: ContactFilters) => void
  
  // Actions - General
  clearError: () => void
}

export const useWhatsAppStore = create<WhatsAppState>((set, get) => ({
  // Initial state
  connections: [],
  selectedConnection: null,
  connectionsLoading: false,
  
  messages: [],
  selectedConversation: [],
  messagesLoading: false,
  
  contacts: [],
  contactsLoading: false,
  
  conversations: [],
  selectedConversationId: null,
  
  connectionFilters: {},
  messageFilters: {},
  contactFilters: {},
  
  error: null,

  // Connection actions
  fetchConnections: async (filters?: ConnectionFilters) => {
    try {
      set({ connectionsLoading: true, error: null })
      
      const response = await apiService.getConnections(filters)
      const connections = response.data.data
      
      set({ 
        connections, 
        connectionsLoading: false,
        connectionFilters: filters || {}
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao buscar conexões'
      set({ 
        error: errorMessage, 
        connectionsLoading: false 
      })
    }
  },

  createConnection: async (data: CreateConnectionForm) => {
    try {
      set({ error: null })
      
      const response = await apiService.createConnection(data)
      const newConnection = response.data.data
      
      set(state => ({ 
        connections: [...state.connections, newConnection]
      }))
      
      return newConnection
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao criar conexão'
      set({ error: errorMessage })
      throw error
    }
  },

  updateConnection: async (id: string, data: Partial<WhatsAppConnection>) => {
    try {
      set({ error: null })
      
      const response = await apiService.updateConnection(id, data)
      const updatedConnection = response.data.data
      
      set(state => ({
        connections: state.connections.map(conn => 
          conn.id === id ? updatedConnection : conn
        ),
        selectedConnection: state.selectedConnection?.id === id 
          ? updatedConnection 
          : state.selectedConnection
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao atualizar conexão'
      set({ error: errorMessage })
      throw error
    }
  },

  deleteConnection: async (id: string) => {
    try {
      set({ error: null })
      
      await apiService.deleteConnection(id)
      
      set(state => ({
        connections: state.connections.filter(conn => conn.id !== id),
        selectedConnection: state.selectedConnection?.id === id 
          ? null 
          : state.selectedConnection
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao deletar conexão'
      set({ error: errorMessage })
      throw error
    }
  },

  connectWhatsApp: async (id: string) => {
    try {
      set({ error: null })
      
      const response = await apiService.connectWhatsApp(id)
      const result = response.data.data
      
      // Atualizar status da conexão
      set(state => ({
        connections: state.connections.map(conn => 
          conn.id === id 
            ? { ...conn, status: 'connecting' as any }
            : conn
        )
      }))
      
      return result
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao conectar WhatsApp'
      set({ error: errorMessage })
      throw error
    }
  },

  disconnectWhatsApp: async (id: string) => {
    try {
      set({ error: null })
      
      await apiService.disconnectWhatsApp(id)
      
      // Atualizar status da conexão
      set(state => ({
        connections: state.connections.map(conn => 
          conn.id === id 
            ? { ...conn, status: 'disconnected' as any }
            : conn
        )
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao desconectar WhatsApp'
      set({ error: errorMessage })
      throw error
    }
  },

  setSelectedConnection: (connection: WhatsAppConnection | null) => {
    set({ selectedConnection: connection })
  },

  // Message actions
  fetchMessages: async (filters?: MessageFilters) => {
    try {
      set({ messagesLoading: true, error: null })
      
      const response = await apiService.getMessages(filters)
      const messages = response.data.data
      
      set({ 
        messages, 
        messagesLoading: false,
        messageFilters: filters || {}
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao buscar mensagens'
      set({ 
        error: errorMessage, 
        messagesLoading: false 
      })
    }
  },

  fetchConversation: async (contactPhone: string, whatsappConnectionId: string) => {
    try {
      set({ messagesLoading: true, error: null })
      
      const response = await apiService.getConversation(contactPhone, whatsappConnectionId)
      const conversation = response.data.data
      
      set({ 
        selectedConversation: conversation, 
        messagesLoading: false 
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao buscar conversa'
      set({ 
        error: errorMessage, 
        messagesLoading: false 
      })
    }
  },

  sendMessage: async (data: SendMessageForm) => {
    try {
      set({ error: null })
      
      const response = await apiService.sendMessage(data)
      const newMessage = response.data.data
      
      // Adicionar mensagem à conversa atual se for a mesma
      const state = get()
      if (state.selectedConversation.length > 0) {
        const firstMessage = state.selectedConversation[0]
        if (firstMessage.contactPhone === data.contactPhone && 
            firstMessage.whatsappConnectionId === data.whatsappConnectionId) {
          set(state => ({
            selectedConversation: [...state.selectedConversation, newMessage]
          }))
        }
      }
      
      return newMessage
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao enviar mensagem'
      set({ error: errorMessage })
      throw error
    }
  },

  markMessageAsRead: async (messageId: string) => {
    try {
      await apiService.markMessageAsRead(messageId)
      
      // Atualizar status da mensagem localmente
      set(state => ({
        selectedConversation: state.selectedConversation.map(msg =>
          msg.id === messageId 
            ? { ...msg, status: 'read' as any, readAt: new Date() }
            : msg
        )
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao marcar mensagem como lida'
      set({ error: errorMessage })
    }
  },

  addMessage: (message: Message) => {
    set(state => ({
      selectedConversation: [...state.selectedConversation, message]
    }))
  },

  updateMessage: (messageId: string, updates: Partial<Message>) => {
    set(state => ({
      selectedConversation: state.selectedConversation.map(msg =>
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    }))
  },

  // Contact actions
  fetchContacts: async (filters?: ContactFilters) => {
    try {
      set({ contactsLoading: true, error: null })
      
      const response = await apiService.getContacts(filters)
      const contacts = response.data.data
      
      set({ 
        contacts, 
        contactsLoading: false,
        contactFilters: filters || {}
      })
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao buscar contatos'
      set({ 
        error: errorMessage, 
        contactsLoading: false 
      })
    }
  },

  createContact: async (data: Partial<Contact>) => {
    try {
      set({ error: null })
      
      const response = await apiService.createContact(data)
      const newContact = response.data.data
      
      set(state => ({ 
        contacts: [...state.contacts, newContact]
      }))
      
      return newContact
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao criar contato'
      set({ error: errorMessage })
      throw error
    }
  },

  updateContact: async (id: string, data: Partial<Contact>) => {
    try {
      set({ error: null })
      
      const response = await apiService.updateContact(id, data)
      const updatedContact = response.data.data
      
      set(state => ({
        contacts: state.contacts.map(contact => 
          contact.id === id ? updatedContact : contact
        )
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao atualizar contato'
      set({ error: errorMessage })
      throw error
    }
  },

  deleteContact: async (id: string) => {
    try {
      set({ error: null })
      
      await apiService.deleteContact(id)
      
      set(state => ({
        contacts: state.contacts.filter(contact => contact.id !== id)
      }))
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Erro ao deletar contato'
      set({ error: errorMessage })
      throw error
    }
  },

  // Conversation actions
  setSelectedConversation: (conversationId: string | null) => {
    set({ selectedConversationId: conversationId })
  },

  // Filter actions
  setConnectionFilters: (filters: ConnectionFilters) => {
    set({ connectionFilters: filters })
  },

  setMessageFilters: (filters: MessageFilters) => {
    set({ messageFilters: filters })
  },

  setContactFilters: (filters: ContactFilters) => {
    set({ contactFilters: filters })
  },

  // General actions
  clearError: () => {
    set({ error: null })
  }
}))
