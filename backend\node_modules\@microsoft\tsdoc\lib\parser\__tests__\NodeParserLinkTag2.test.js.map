{"version": 3, "file": "NodeParserLinkTag2.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserLinkTag2.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,IAAI,CAAC,gDAAgD,EAAE;IACrD,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,mBAAmB;QACnB,2BAA2B;QAC3B,oCAAoC;QACpC,qCAAqC;QACrC,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,gDAAgD,EAAE;IACrD,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,4BAA4B,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACtG,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wCAAwC,EAAE;IAC7C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,2BAA2B;QAC3B,4CAA4C;QAC5C,oCAAoC;QACpC,gDAAgD;QAChD,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,wCAAwC,EAAE;IAC7C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,2BAA2B;QAC3B,8BAA8B;QAC9B,0CAA0C;QAC1C,yBAAyB;QACzB,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,4BAA4B,EAAE,uCAAuC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjG,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,2BAA2B,EAAE,0BAA0B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACnF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,6BAA6B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxG,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,uCAAuC,EAAE;IAC5C,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,2BAA2B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtG,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2CAA2C,EAAE;IAChD,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,iCAAiC,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5G,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2CAA2C,EAAE;IAChD,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/F,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE;IAC/C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,qBAAqB;QACrB,8BAA8B;QAC9B,mCAAmC;QACnC,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,0CAA0C,EAAE;IAC/C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,oBAAoB;QACpB,sBAAsB;QACtB,6BAA6B;QAC7B,qBAAqB;QACrB,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICEN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Simple member references: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link Class1}',\r\n      ' * {@link Class2.member2}',\r\n      ' * {@link namespace3 . namespace4 ',\r\n      ' *        . namespace5 | link text}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 Simple member references: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link Class1..member2}', ' * {@link .member3}', ' * {@link member4.}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 System selectors: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link (Class1:class)}',\r\n      ' * {@link (Class2:class).(member3:static)}',\r\n      ' * {@link Class4.(member5:static)}',\r\n      ' * {@link (Class6:class ) . ( member7:static)}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('03 System selectors: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link (Class1:class}',\r\n      ' * {@link (Class2:class))}',\r\n      ' * {@link (Class3::class)}',\r\n      ' * {@link (Class4 class)}',\r\n      ' * {@link (member5:badname)}',\r\n      ' * {@link (Class6:class)(member:static)}',\r\n      ' * {@link Class7:class}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 Label selectors: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link (Class1:LABEL1)}', ' * {@link ( function2 : MY_LABEL2 ) }', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('05 Label selectors: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@link (Class1:Label)}', ' * {@link (Class2:SPAß)}', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('06 Index selectors: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@link (function2 : 3 )}', ' */'].join('\\n'));\r\n});\r\n\r\ntest('07 Index selectors: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@link (function2:03)}', ' */'].join('\\n'));\r\n});\r\n\r\ntest('08 Unusual identifiers: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@link Class$_1 . $_1member}', ' */'].join('\\n'));\r\n});\r\n\r\ntest('09 Unusual identifiers: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@link Class-1}', ' */'].join('\\n'));\r\n});\r\n\r\ntest('10 Quoted identifiers: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link \"static\"}',\r\n      ' * {@link Class1 . \"member\"}',\r\n      ' * {@link Class2.\"|\" | link text}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('11 Quoted identifiers: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * {@link \"static}',\r\n      ' * {@link Class1.\"\"}',\r\n      ' * {@link Class2.interface}',\r\n      ' * {@link Class3.1}',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n"]}