"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateIntegrationDto = exports.IntegrationConfigDto = exports.DataMappingDto = exports.FiltersDto = exports.SlackConfigDto = exports.EmailConfigDto = exports.CrmConfigDto = exports.GoogleSheetsConfigDto = exports.AuthConfigDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const integration_entity_1 = require("../../../database/entities/integration.entity");
class AuthConfigDto {
    authType;
    apiKey;
    bearerToken;
    basicAuth;
    oauth2;
}
exports.AuthConfigDto = AuthConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tipo de autenticação', enum: ['none', 'bearer', 'basic', 'api_key', 'oauth2'] }),
    (0, class_validator_1.IsEnum)(['none', 'bearer', 'basic', 'api_key', 'oauth2']),
    __metadata("design:type", String)
], AuthConfigDto.prototype, "authType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Chave da API' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigDto.prototype, "apiKey", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Token Bearer' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AuthConfigDto.prototype, "bearerToken", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Autenticação básica' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AuthConfigDto.prototype, "basicAuth", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração OAuth2' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AuthConfigDto.prototype, "oauth2", void 0);
class GoogleSheetsConfigDto {
    spreadsheetId;
    sheetName;
    range;
    serviceAccountKey;
}
exports.GoogleSheetsConfigDto = GoogleSheetsConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID da planilha do Google Sheets' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GoogleSheetsConfigDto.prototype, "spreadsheetId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Nome da aba' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GoogleSheetsConfigDto.prototype, "sheetName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Intervalo de células (ex: A1:Z1000)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GoogleSheetsConfigDto.prototype, "range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Chave da conta de serviço (JSON)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GoogleSheetsConfigDto.prototype, "serviceAccountKey", void 0);
class CrmConfigDto {
    apiUrl;
    objectType;
    fieldMapping;
    customFields;
}
exports.CrmConfigDto = CrmConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL da API do CRM' }),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CrmConfigDto.prototype, "apiUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tipo de objeto (contact, lead, deal, etc.)' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CrmConfigDto.prototype, "objectType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Mapeamento de campos' }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CrmConfigDto.prototype, "fieldMapping", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Campos customizados' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CrmConfigDto.prototype, "customFields", void 0);
class EmailConfigDto {
    listId;
    templateId;
    fromEmail;
    fromName;
}
exports.EmailConfigDto = EmailConfigDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID da lista de email' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfigDto.prototype, "listId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID do template' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfigDto.prototype, "templateId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Email do remetente' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfigDto.prototype, "fromEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Nome do remetente' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], EmailConfigDto.prototype, "fromName", void 0);
class SlackConfigDto {
    channelId;
    botToken;
    messageFormat;
}
exports.SlackConfigDto = SlackConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'ID do canal do Slack' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SlackConfigDto.prototype, "channelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Token do bot' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SlackConfigDto.prototype, "botToken", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Formato da mensagem' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SlackConfigDto.prototype, "messageFormat", void 0);
class FiltersDto {
    contactTags;
    messageTypes;
    connectionIds;
    userIds;
    customConditions;
}
exports.FiltersDto = FiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tags de contato para filtrar', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FiltersDto.prototype, "contactTags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tipos de mensagem para filtrar', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FiltersDto.prototype, "messageTypes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'IDs de conexão para filtrar', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FiltersDto.prototype, "connectionIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'IDs de usuário para filtrar', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], FiltersDto.prototype, "userIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Condições customizadas' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FiltersDto.prototype, "customConditions", void 0);
class DataMappingDto {
    staticFields;
    dynamicFields;
    transformations;
}
exports.DataMappingDto = DataMappingDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Campos estáticos' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DataMappingDto.prototype, "staticFields", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mapeamento de campos dinâmicos' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], DataMappingDto.prototype, "dynamicFields", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Transformações de dados' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], DataMappingDto.prototype, "transformations", void 0);
class IntegrationConfigDto {
    url;
    method;
    headers;
    timeout;
    retryAttempts;
    retryDelay;
    authType;
    apiKey;
    bearerToken;
    googleSheets;
    crm;
    email;
    slack;
    filters;
    dataMapping;
}
exports.IntegrationConfigDto = IntegrationConfigDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL do webhook' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], IntegrationConfigDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Método HTTP', enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['GET', 'POST', 'PUT', 'PATCH', 'DELETE']),
    __metadata("design:type", String)
], IntegrationConfigDto.prototype, "method", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Headers HTTP' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], IntegrationConfigDto.prototype, "headers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timeout em milissegundos', example: 30000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    (0, class_validator_1.Max)(300000),
    __metadata("design:type", Number)
], IntegrationConfigDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Número de tentativas', example: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], IntegrationConfigDto.prototype, "retryAttempts", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Delay entre tentativas em milissegundos', example: 5000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], IntegrationConfigDto.prototype, "retryDelay", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração de autenticação', type: AuthConfigDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AuthConfigDto),
    __metadata("design:type", String)
], IntegrationConfigDto.prototype, "authType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Chave da API' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IntegrationConfigDto.prototype, "apiKey", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Token Bearer' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], IntegrationConfigDto.prototype, "bearerToken", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração do Google Sheets', type: GoogleSheetsConfigDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => GoogleSheetsConfigDto),
    __metadata("design:type", GoogleSheetsConfigDto)
], IntegrationConfigDto.prototype, "googleSheets", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração do CRM', type: CrmConfigDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CrmConfigDto),
    __metadata("design:type", CrmConfigDto)
], IntegrationConfigDto.prototype, "crm", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração de email', type: EmailConfigDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => EmailConfigDto),
    __metadata("design:type", EmailConfigDto)
], IntegrationConfigDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Configuração do Slack', type: SlackConfigDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SlackConfigDto),
    __metadata("design:type", SlackConfigDto)
], IntegrationConfigDto.prototype, "slack", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filtros de eventos', type: FiltersDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FiltersDto),
    __metadata("design:type", FiltersDto)
], IntegrationConfigDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Mapeamento de dados', type: DataMappingDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => DataMappingDto),
    __metadata("design:type", DataMappingDto)
], IntegrationConfigDto.prototype, "dataMapping", void 0);
class CreateIntegrationDto {
    name;
    description;
    type;
    triggerEvents;
    config;
    active;
    rateLimit;
    maxRetries;
    retryDelay;
    timeout;
    metadata;
    tags;
}
exports.CreateIntegrationDto = CreateIntegrationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Nome da integração', example: 'Webhook para CRM' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIntegrationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Descrição da integração', example: 'Envia novos contatos para o CRM' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateIntegrationDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tipo da integração', enum: integration_entity_1.IntegrationType }),
    (0, class_validator_1.IsEnum)(integration_entity_1.IntegrationType),
    __metadata("design:type", String)
], CreateIntegrationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Eventos que disparam a integração', type: [String], enum: integration_entity_1.TriggerEvent }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(integration_entity_1.TriggerEvent, { each: true }),
    __metadata("design:type", Array)
], CreateIntegrationDto.prototype, "triggerEvents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Configuração da integração', type: IntegrationConfigDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => IntegrationConfigDto),
    __metadata("design:type", IntegrationConfigDto)
], CreateIntegrationDto.prototype, "config", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Se a integração está ativa', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateIntegrationDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Limite de requisições por minuto', example: 60, default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateIntegrationDto.prototype, "rateLimit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Máximo de tentativas', example: 3, default: 3 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], CreateIntegrationDto.prototype, "maxRetries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Delay entre tentativas em milissegundos', example: 5000, default: 5000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    __metadata("design:type", Number)
], CreateIntegrationDto.prototype, "retryDelay", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Timeout em milissegundos', example: 30000, default: 30000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1000),
    (0, class_validator_1.Max)(300000),
    __metadata("design:type", Number)
], CreateIntegrationDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Metadados adicionais' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateIntegrationDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tags da integração', type: [String] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateIntegrationDto.prototype, "tags", void 0);
//# sourceMappingURL=create-integration.dto.js.map