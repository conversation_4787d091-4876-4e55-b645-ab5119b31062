{"version": 3, "file": "MongoBinaryDownloadUrl.js", "sourceRoot": "", "sources": ["../../src/util/MongoBinaryDownloadUrl.ts"], "names": [], "mappings": ";;;;AAAA,mCAAgD;AAChD,mDAAwE;AACxE,0DAA0B;AAC1B,uDAAiC;AACjC,mCAA4C;AAC5C,6BAA0B;AAC1B,qCAOkB;AAElB,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,gCAAgC,CAAC,CAAC;AASpD,4CAA4C;AAC/B,QAAA,mBAAmB,GAAG,EAAE,CAAC,CAAC,sDAAsD;AAE7F;;GAEG;AACH,MAAa,sBAAsB;IAMjC,YAAY,IAAgC;QAC1C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACtD,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;IACpB,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;QAEvE,IAAI,WAAW,EAAE,CAAC;YAChB,GAAG,CAAC,UAAU,WAAW,uBAAuB,CAAC,CAAC;YAElD,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,WAAW,CAAC,CAAC,CAAC,gCAAgC;YAElE,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,GAAG,CAAC,UAAU,OAAO,yBAAyB,CAAC,CAAC;QAEhD,MAAM,MAAM,GACV,IAAA,6BAAa,EAAC,sCAAsB,CAAC,eAAe,CAAC,IAAI,4BAA4B,CAAC;QACxF,GAAG,CAAC,UAAU,MAAM,iBAAiB,CAAC,CAAC;QAEvC,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,MAAM,CAAC,CAAC;QAE5B,8CAA8C;QAC9C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC;QACpC,CAAC;QAED,+HAA+H;QAC/H,GAAG,CAAC,QAAQ,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,OAAO,EAAE,CAAC;QAE5D,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,MAAM,YAAY,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;QAExE,yCAAyC;QACzC,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;YACnB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClC,KAAK,OAAO;gBACV,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpC;gBACE,MAAM,IAAI,6BAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,IAAI,IAAI,GAAG,WAAW,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACnD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,CAAC;YACvC,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC9C,IAAI,IAAI,WAAW,CAAC;YACtB,CAAC;iBAAM,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC9C,IAAI,IAAI,eAAe,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,iBAAiB;QACf,IAAI,IAAI,GAAG,aAAa,CAAC;QACzB,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;YAC9E,IAAI,IAAI,MAAM,CAAC;QACjB,CAAC;QACD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;YAC7E,IAAI,GAAG,eAAe,CAAC,CAAC,uEAAuE;QACjG,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC5B,mEAAmE;YACnE,IAAI,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC7E,GAAG,CAAC,mFAAmF,CAAC,CAAC;gBACzF,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,GAAG,CACD,iGAAiG,CAClG,CAAC;gBACF,yDAAyD;gBACzD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;YACtB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE5C,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7D,IAAI,CAAC,EAAE,GAAG,MAAM,IAAA,aAAK,GAAE,CAAC;QAC1B,CAAC;QAED,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,QAAQ,GAAW,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAa,CAAC,CAAC;QAE1E,uEAAuE;QACvE,IAAI,IAAI,GAAG,iBAAiB,IAAI,CAAC,IAAI,EAAE,CAAC;QAExC,iCAAiC;QACjC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;YACf,IAAI,IAAI,IAAI,QAAQ,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,MAAM,CAAC;QAE/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACO,eAAe;QACvB,MAAM,GAAG,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,MAAM,CAAC,CAAC;QAEzD,IAAI,IAAA,yBAAiB,EAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAE7B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEzB,IAAI,IAAA,yBAAiB,EAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,wBAAe,CAAC,6DAA6D,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,wBAAe,CACvB,iFAAiF,CAClF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,EAAE,GAAG;YACR,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,OAAO;SACN,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,uBAAuB,CAAC,EAAW;QACjC,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACrC,2HAA2H;QAC7H,CAAC;aAAM,IAAI,WAAW,CAAC,gCAAgC,EAAE,EAAE,CAAC,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;QACvC,CAAC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;YAClE,4EAA4E;QAC9E,CAAC;aAAM,IAAI,WAAW,CAAC,iCAAiC,EAAE,EAAE,CAAC,EAAE,CAAC;YAC9D,OAAO,CAAC,IAAI,CACV,wDAAwD,EAAE,CAAC,IAAI,0CAA0C,CAC1G,CAAC;YAEF,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBACjC,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,cAAc;gBACpB,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,WAAW,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,CAAC;YACtC,uIAAuI;YACvI,OAAO,CAAC,IAAI,CACV,qDAAqD,EAAE,CAAC,IAAI,4BAA4B,CACzF,CAAC;YAEF,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBACjC,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,WAAW,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC;YACvC,4EAA4E;YAC5E,OAAO,CAAC,IAAI,CACV,iHAAiH,CAClH,CAAC;QACJ,CAAC;QAED,qDAAqD;QACrD,MAAM,IAAI,2BAAkB,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,MAAM,OAAO,GAAW,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,2DAA2D;QAC3D,2FAA2F;QAC3F,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;QAEnE,IAAI,SAAS,IAAI,OAAO,IAAI,EAAE,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE,CAAC;YACzB,0DAA0D;YAC1D,8CAA8C;YAC9C,kFAAkF;YAClF,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7E,GAAG,CAAC,sEAAsE,CAAC,CAAC;gBAC5E,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE,CAAC;YACzB,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;YACxB,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;YAC1B,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC;YAC1B,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;QAED,IAAI,SAAS,IAAI,OAAO,IAAI,EAAE,EAAE,CAAC;YAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7E,MAAM,IAAI,yCAAgC,CACxC,UAAU,OAAO,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,QAAQ,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,mIAAmI,CACpI,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,EAAE,EAAE,CAAC;YACzB,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7E,MAAM,IAAI,yCAAgC,CACxC,UAAU,OAAO,IAAI,EAAE,CAAC,OAAO,IAAI,EAAE,CAAC,QAAQ,EAAE,EAChD,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,mIAAmI,CACpI,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,MAAM,SAAS,GAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAEnD,MAAM,MAAM,GAAY;YACtB,EAAE,EAAE,OAAO;YACX,IAAI,EAAE,MAAM;YACZ,kBAAkB;YAClB,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,0GAA0G;QAC1G,yGAAyG;QACzG,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;YACpB,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;YAC3B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;YAC3B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACzB,CAAC;aAAM,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;YAC1B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACzB,CAAC;QAED,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACH,oBAAoB,CAAC,EAAW;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC;QAClB,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;QACvB,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,qGAAqG;QACrJ,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,eAAe,EAAE,CAAC;YACpB,gDAAgD;YAChD,qCAAqC;YACrC,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC5B,yEAAyE;gBACzE,IAAI,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,OAAO,CAAC,EAAE,CAAC;oBACxC,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,8DAA8D,CAC/D,CAAC;gBACJ,CAAC;gBACD,yDAAyD;gBACzD,uJAAuJ;gBACvJ,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC7E,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,CACV,CAAC;gBACJ,CAAC;gBAED,6FAA6F;gBAC7F,8BAA8B;gBAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;oBACvF,MAAM,IAAI,yCAAgC,CACxC,QAAQ,OAAO,QAAQ,EACvB,IAAI,CAAC,OAAO,EACZ,SAAS,CACV,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE,CAAC;gBACjD,iDAAiD;gBACjD,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;gBAChF,yFAAyF;gBACzF,qFAAqF;gBACrF,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,yCAAyC,CAAC,EAAE,CAAC;oBAChF,IAAI,IAAI,GAAG,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,IAAI,CAAC;gBACf,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACvD,yFAAyF;gBACzF,qFAAqF;gBACrF,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,yCAAyC,CAAC,EAAE,CAAC;oBAChF,IAAI,IAAI,GAAG,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,IAAI,IAAI,IAAI,CAAC;gBACf,CAAC;YACH,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACvD,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACvD,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;iBAAM,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACvD,IAAI,IAAI,IAAI,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,4BAA4B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,gCAAgC,OAAO,GAAG,CAAC,CAAC;QAC3D,CAAC;QACD,6CAA6C;QAC7C,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YACpB,GAAG,CAAC,4CAA4C,CAAC,CAAC;YAClD,sGAAsG;YACtG,IAAI,IAAI,IAAI,CAAC;QACf,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,IAAI,GAAG,QAAQ,CAAC;QACpB,MAAM,OAAO,GAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAEjD,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;YACjB,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC7B,CAAC;QACD,mFAAmF;QAEnF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,2CAA2C;IAC3C,oBAAoB,CAAC,EAAW;QAC9B,MAAM,YAAY,GAA4B,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAEhF,OAAO,YAAY,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACtD,CAAC;IAED;;;OAGG;IACH,sBAAsB,CAAC,EAAW;QAChC,IAAI,QAAQ,GAAwB,SAAS,CAAC;QAC9C,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnD,IAAI,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI,4BAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,iFAAiF;QACjF,CAAC;YACC,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,MAAM,mBAAmB,GAA2B;oBAClD,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;oBACX,EAAE,EAAE,OAAO;iBACZ,CAAC;gBAEF,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO,EACL,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,EAAE,CAAC;iBACrF,CAAC;YACJ,CAAC;YAED,IAAI,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/C,MAAM,yBAAyB,GAA2B;oBACxD,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;oBACV,CAAC,EAAE,OAAO;iBACX,CAAC;gBAEF,oGAAoG;gBACpG,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC3F,gFAAgF;gBAChF,MAAM,SAAS,GAAG,eAAe,IAAI,eAAe,CAAC;gBAErD,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,yBAAyB,CAAC,SAAS,CAAC,IAAI,yBAAyB,CAAC,CAAC,CAAC;iBAC9E,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,IAAA,yBAAiB,EAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,uIAAuI;YACvI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,IAAI,CACV,oBAAoB,EAAE,CAAC,IAAI,kEAAkE;oBAC3F,gLAAgL,CACnL,CAAC;gBAEF,QAAQ,GAAG;oBACT,EAAE,EAAE,OAAO;oBACX,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,GAAG,2BAAmB,KAAK;iBACrC,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;QAED,IAAI,UAAU,GAAW,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEtE,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,qCAAqC,QAAQ,CAAC,OAAO,kBAAkB,CAAC,CAAC;YACtF,UAAU,GAAG,2BAAmB,CAAC;QACnC,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC5B,oHAAoH;YACpH,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;gBAEpB,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,2FAA2F;YAC3F,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,CAAC,EAAE,CAAC;gBACxD,OAAO,YAAY,CAAC;YACtB,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,kBAAkB,CAAC;QAC5B,CAAC;QAED,iEAAiE;QACjE,qDAAqD;QACrD,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,CAAC;YAClE,GAAG,CACD,0CAA0C,UAAU,4DAA4D,CACjH,CAAC;YAEF,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,oEAAoE;QACpE,qDAAqD;QACrD,IAAI,UAAU,GAAG,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,CAAC;YACnE,GAAG,CACD,0CAA0C,UAAU,gEAAgE,CACrH,CAAC;YAEF,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,iFAAiF;QACjF,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,CAAC;YACnE,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,2DAA2D;QAC3D,IAAI,UAAU,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,EAAE,CAAC;YACnE,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,8DAA8D;QAC9D,CAAC;YACC,mFAAmF;YACnF;;;eAGG;YACH,MAAM,iBAAiB,GAAG,EAAE,CAAC,CAAC,kDAAkD;YAEhF,IAAI,UAAU,GAAG,iBAAiB,EAAE,CAAC;gBACnC,GAAG,CACD,uCAAuC,UAAU,6DAA6D,iBAAiB,wBAAwB,CACxJ,CAAC;gBAEF,OAAO,YAAY,CAAC;YACtB,CAAC;QACH,CAAC;QAED,iEAAiE;QACjE,OAAO,SAAS,UAAU,IAAI,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACH,iBAAiB,CAAC,QAAgB;QAChC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAC;YACf,KAAK,OAAO;gBACV,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAE5C,IAAI,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE,CAAC;oBAC/B,OAAO,SAAS,CAAC;gBACnB,CAAC;gBAED,OAAO,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5D,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC;YACjB;gBACE,MAAM,IAAI,6BAAoB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,aAAa,CAAC,IAAY;QAC/B,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ,CAAC;YACd,KAAK,KAAK;gBACR,OAAO,QAAQ,CAAC;YAClB,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,SAAS,CAAC;YACnB;gBACE,MAAM,IAAI,iCAAwB,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;CACF;AA/nBD,wDA+nBC;AAED,kBAAe,sBAAsB,CAAC;AAEtC;;GAEG;AACH,SAAS,WAAW,CAAC,KAAa,EAAE,EAAW;IAC7C,OAAO,CACL,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QACnB,CAAC,CAAC,IAAA,yBAAiB,EAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAC/F,CAAC;AACJ,CAAC;AAED,0FAA0F;AAC1F,SAAS,mBAAmB,CAAC,OAAe;IAC1C,OAAO,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5C,CAAC"}