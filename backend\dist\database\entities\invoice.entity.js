"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Invoice = exports.InvoiceType = exports.InvoiceStatus = void 0;
const typeorm_1 = require("typeorm");
const company_entity_1 = require("./company.entity");
const subscription_entity_1 = require("./subscription.entity");
const payment_entity_1 = require("./payment.entity");
var InvoiceStatus;
(function (InvoiceStatus) {
    InvoiceStatus["DRAFT"] = "draft";
    InvoiceStatus["PENDING"] = "pending";
    InvoiceStatus["PAID"] = "paid";
    InvoiceStatus["PARTIALLY_PAID"] = "partially_paid";
    InvoiceStatus["OVERDUE"] = "overdue";
    InvoiceStatus["CANCELLED"] = "cancelled";
    InvoiceStatus["REFUNDED"] = "refunded";
})(InvoiceStatus || (exports.InvoiceStatus = InvoiceStatus = {}));
var InvoiceType;
(function (InvoiceType) {
    InvoiceType["SUBSCRIPTION"] = "subscription";
    InvoiceType["SETUP_FEE"] = "setup_fee";
    InvoiceType["USAGE_BASED"] = "usage_based";
    InvoiceType["ONE_TIME"] = "one_time";
    InvoiceType["CREDIT_NOTE"] = "credit_note";
    InvoiceType["ADJUSTMENT"] = "adjustment";
})(InvoiceType || (exports.InvoiceType = InvoiceType = {}));
let Invoice = class Invoice {
    id;
    invoiceNumber;
    companyId;
    company;
    subscriptionId;
    subscription;
    status;
    type;
    issueDate;
    dueDate;
    paidDate;
    periodStart;
    periodEnd;
    subtotal;
    taxAmount;
    discountAmount;
    total;
    amountPaid;
    amountDue;
    lineItems;
    taxDetails;
    discountDetails;
    notes;
    terms;
    footer;
    autoCharge;
    paymentMethodId;
    attemptCount;
    nextAttemptDate;
    stripeInvoiceId;
    stripePaymentIntentId;
    invoiceUrl;
    pdfUrl;
    hostedInvoiceUrl;
    emailSent;
    emailSentAt;
    remindersSent;
    lastReminderSentAt;
    metadata;
    currency;
    locale;
    createdAt;
    updatedAt;
    payments;
    isPaid() {
        return this.status === InvoiceStatus.PAID;
    }
    isOverdue() {
        return this.status === InvoiceStatus.OVERDUE ||
            (this.status === InvoiceStatus.PENDING && new Date() > this.dueDate);
    }
    isDraft() {
        return this.status === InvoiceStatus.DRAFT;
    }
    canBePaid() {
        return [InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE].includes(this.status);
    }
    canBeCancelled() {
        return [InvoiceStatus.DRAFT, InvoiceStatus.PENDING].includes(this.status);
    }
    getRemainingAmount() {
        return Math.max(0, this.total - this.amountPaid);
    }
    getPaymentProgress() {
        if (this.total === 0)
            return 100;
        return (this.amountPaid / this.total) * 100;
    }
    addLineItem(item) {
        const newItem = {
            ...item,
            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };
        this.lineItems = [...(this.lineItems || []), newItem];
        this.recalculateAmounts();
    }
    removeLineItem(itemId) {
        this.lineItems = (this.lineItems || []).filter(item => item.id !== itemId);
        this.recalculateAmounts();
    }
    updateLineItem(itemId, updates) {
        this.lineItems = (this.lineItems || []).map(item => item.id === itemId ? { ...item, ...updates } : item);
        this.recalculateAmounts();
    }
    recalculateAmounts() {
        this.subtotal = (this.lineItems || []).reduce((sum, item) => sum + item.totalPrice, 0);
        this.taxAmount = (this.taxDetails || []).reduce((sum, tax) => sum + tax.taxAmount, 0);
        this.discountAmount = (this.discountDetails || []).reduce((sum, discount) => sum + discount.discountAmount, 0);
        this.total = this.subtotal + this.taxAmount - this.discountAmount;
        this.amountDue = Math.max(0, this.total - this.amountPaid);
    }
    addPayment(amount) {
        this.amountPaid += amount;
        this.amountDue = Math.max(0, this.total - this.amountPaid);
        if (this.amountDue === 0) {
            this.status = InvoiceStatus.PAID;
            this.paidDate = new Date();
        }
        else if (this.amountPaid > 0) {
            this.status = InvoiceStatus.PARTIALLY_PAID;
        }
    }
    markAsOverdue() {
        if (this.canBePaid() && new Date() > this.dueDate) {
            this.status = InvoiceStatus.OVERDUE;
        }
    }
    cancel() {
        if (this.canBeCancelled()) {
            this.status = InvoiceStatus.CANCELLED;
        }
    }
    finalize() {
        if (this.isDraft()) {
            this.status = InvoiceStatus.PENDING;
            this.issueDate = new Date();
        }
    }
    getDaysOverdue() {
        if (!this.isOverdue())
            return 0;
        const now = new Date();
        const diffTime = now.getTime() - this.dueDate.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    generateInvoiceNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const timestamp = Date.now().toString().slice(-6);
        return `INV-${year}${month}-${timestamp}`;
    }
};
exports.Invoice = Invoice;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Invoice.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], Invoice.prototype, "invoiceNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Invoice.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], Invoice.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "subscriptionId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => subscription_entity_1.Subscription, subscription => subscription.invoices),
    (0, typeorm_1.JoinColumn)({ name: 'subscriptionId' }),
    __metadata("design:type", subscription_entity_1.Subscription)
], Invoice.prototype, "subscription", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: InvoiceStatus.DRAFT,
    }),
    __metadata("design:type", String)
], Invoice.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: InvoiceType.SUBSCRIPTION,
    }),
    __metadata("design:type", String)
], Invoice.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Invoice.prototype, "issueDate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Invoice.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Invoice.prototype, "paidDate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Invoice.prototype, "periodStart", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Invoice.prototype, "periodEnd", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Invoice.prototype, "subtotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "discountAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Invoice.prototype, "total", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "amountPaid", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "amountDue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Array)
], Invoice.prototype, "lineItems", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Invoice.prototype, "taxDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Invoice.prototype, "discountDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "terms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "footer", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Invoice.prototype, "autoCharge", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "paymentMethodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "attemptCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Invoice.prototype, "nextAttemptDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "stripeInvoiceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "stripePaymentIntentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "invoiceUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "pdfUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Invoice.prototype, "hostedInvoiceUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Invoice.prototype, "emailSent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Invoice.prototype, "emailSentAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Invoice.prototype, "remindersSent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Invoice.prototype, "lastReminderSentAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Invoice.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 3, default: 'BRL' }),
    __metadata("design:type", String)
], Invoice.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10, default: 'pt-BR' }),
    __metadata("design:type", String)
], Invoice.prototype, "locale", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Invoice.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Invoice.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => payment_entity_1.Payment, payment => payment.invoice),
    __metadata("design:type", Array)
], Invoice.prototype, "payments", void 0);
exports.Invoice = Invoice = __decorate([
    (0, typeorm_1.Entity)('invoices')
], Invoice);
//# sourceMappingURL=invoice.entity.js.map