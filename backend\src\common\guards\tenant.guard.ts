import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { UserRole } from '../../database/entities';
import { AuthenticatedUser } from '../decorators/current-user.decorator';

@Injectable()
export class TenantGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    
    if (!user) {
      throw new ForbiddenException('Usuário não autenticado');
    }

    // Super admin tem acesso a tudo
    if (user.role === UserRole.SUPER_ADMIN) {
      return true;
    }

    // Extrair companyId dos parâmetros da rota ou body
    const companyId = request.params?.companyId || request.body?.companyId;
    
    if (companyId && !user.canAccessCompany?.(companyId)) {
      throw new ForbiddenException('Acesso negado: empresa não autorizada');
    }

    // Adicionar companyId do usuário ao request para filtros automáticos
    request.tenantId = user.companyId;
    
    return true;
  }
}
