"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateContactDto = exports.ContactSourceDto = exports.ContactTagDto = exports.ContactStatus = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
var ContactStatus;
(function (ContactStatus) {
    ContactStatus["ACTIVE"] = "active";
    ContactStatus["BLOCKED"] = "blocked";
    ContactStatus["ARCHIVED"] = "archived";
})(ContactStatus || (exports.ContactStatus = ContactStatus = {}));
class ContactTagDto {
    id;
    name;
    color;
    createdAt;
    createdBy;
}
exports.ContactTagDto = ContactTagDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da etiqueta',
        example: 'tag-id',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'ID da etiqueta deve ser uma string' }),
    __metadata("design:type", String)
], ContactTagDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da etiqueta',
        example: 'Cliente VIP',
    }),
    (0, class_validator_1.IsString)({ message: 'Nome da etiqueta deve ser uma string' }),
    __metadata("design:type", String)
], ContactTagDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cor da etiqueta',
        example: '#FF5733',
    }),
    (0, class_validator_1.IsString)({ message: 'Cor da etiqueta deve ser uma string' }),
    __metadata("design:type", String)
], ContactTagDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Data de criação',
        example: '2023-12-01T10:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Date)
], ContactTagDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Criado por',
        example: 'user-id',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Criado por deve ser uma string' }),
    __metadata("design:type", String)
], ContactTagDto.prototype, "createdBy", void 0);
class ContactSourceDto {
    type;
    campaign;
    medium;
    source;
    utm_params;
    referrer;
}
exports.ContactSourceDto = ContactSourceDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da fonte',
        example: 'google_ads',
    }),
    (0, class_validator_1.IsString)({ message: 'Tipo da fonte deve ser uma string' }),
    __metadata("design:type", String)
], ContactSourceDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Campanha',
        example: 'Campanha Black Friday',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Campanha deve ser uma string' }),
    __metadata("design:type", String)
], ContactSourceDto.prototype, "campaign", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Meio',
        example: 'cpc',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Meio deve ser uma string' }),
    __metadata("design:type", String)
], ContactSourceDto.prototype, "medium", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fonte',
        example: 'google',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Fonte deve ser uma string' }),
    __metadata("design:type", String)
], ContactSourceDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Parâmetros UTM',
        example: { utm_campaign: 'black-friday', utm_medium: 'cpc' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Parâmetros UTM devem ser um objeto' }),
    __metadata("design:type", Object)
], ContactSourceDto.prototype, "utm_params", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Referrer',
        example: 'https://google.com',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Referrer deve ser uma string' }),
    __metadata("design:type", String)
], ContactSourceDto.prototype, "referrer", void 0);
class CreateContactDto {
    phoneNumber;
    name;
    email;
    profilePicture;
    status;
    tags;
    source;
    customFields;
    isLead;
    leadScore;
    leadStage;
    assignedTo;
    notes;
    whatsappConnectionId;
}
exports.CreateContactDto = CreateContactDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de telefone',
        example: '+5511999999999',
    }),
    (0, class_validator_1.IsPhoneNumber)('BR', { message: 'Número de telefone deve ser válido' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nome do contato',
        example: 'João Silva',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Email do contato',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)({}, { message: 'Email deve ter um formato válido' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL da foto de perfil',
        example: 'https://example.com/profile.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'URL da foto deve ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "profilePicture", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do contato',
        enum: ContactStatus,
        default: ContactStatus.ACTIVE,
    }),
    (0, class_validator_1.IsEnum)(ContactStatus, { message: 'Status deve ser um valor válido' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Etiquetas do contato',
        type: [ContactTagDto],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Etiquetas devem ser um array' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => ContactTagDto),
    __metadata("design:type", Array)
], CreateContactDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fonte do contato',
        type: ContactSourceDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ContactSourceDto),
    __metadata("design:type", ContactSourceDto)
], CreateContactDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Campos customizados',
        example: { empresa: 'Tech Corp', cargo: 'Desenvolvedor' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Campos customizados devem ser um objeto' }),
    __metadata("design:type", Object)
], CreateContactDto.prototype, "customFields", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Se é um lead',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'isLead deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateContactDto.prototype, "isLead", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pontuação do lead',
        example: 85,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Pontuação do lead deve ser um número' }),
    (0, class_validator_1.Min)(0, { message: 'Pontuação deve ser maior ou igual a 0' }),
    (0, class_validator_1.Max)(100, { message: 'Pontuação deve ser menor ou igual a 100' }),
    __metadata("design:type", Number)
], CreateContactDto.prototype, "leadScore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Estágio do lead',
        example: 'qualificado',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Estágio do lead deve ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "leadStage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID do usuário responsável',
        example: 'uuid-do-usuario',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'ID do usuário deve ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "assignedTo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Observações sobre o contato',
        example: 'Cliente interessado em produtos premium',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Observações devem ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da conexão WhatsApp',
        example: 'uuid-da-conexao',
    }),
    (0, class_validator_1.IsString)({ message: 'ID da conexão deve ser uma string' }),
    __metadata("design:type", String)
], CreateContactDto.prototype, "whatsappConnectionId", void 0);
//# sourceMappingURL=create-contact.dto.js.map