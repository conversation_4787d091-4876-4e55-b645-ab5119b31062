#!/bin/bash

# Script de deploy para produção
set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar argumentos
if [ $# -eq 0 ]; then
    error "Uso: $0 <ambiente> [versao]"
    echo "Ambientes disponíveis: staging, production"
    exit 1
fi

ENVIRONMENT=$1
VERSION=${2:-$(date +%Y%m%d%H%M%S)}

if [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
    error "Ambiente deve ser 'staging' ou 'production'"
    exit 1
fi

log "🚀 Iniciando deploy para $ENVIRONMENT (versão: $VERSION)..."

# Verificar se estamos na branch correta
CURRENT_BRANCH=$(git branch --show-current)
if [ "$ENVIRONMENT" = "production" ] && [ "$CURRENT_BRANCH" != "main" ]; then
    error "Deploy para produção deve ser feito a partir da branch 'main'"
    exit 1
fi

if [ "$ENVIRONMENT" = "staging" ] && [ "$CURRENT_BRANCH" != "develop" ] && [ "$CURRENT_BRANCH" != "main" ]; then
    error "Deploy para staging deve ser feito a partir da branch 'develop' ou 'main'"
    exit 1
fi

# Verificar se há mudanças não commitadas
if [ -n "$(git status --porcelain)" ]; then
    error "Há mudanças não commitadas. Faça commit antes do deploy."
    exit 1
fi

# Executar testes antes do deploy
log "Executando testes..."
./scripts/test.sh || {
    error "Testes falharam. Deploy cancelado."
    exit 1
}
success "Testes passaram"

# Fazer backup do banco de dados (apenas produção)
if [ "$ENVIRONMENT" = "production" ]; then
    log "Fazendo backup do banco de dados..."
    
    # PostgreSQL backup
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    pg_dump $DATABASE_URL > "backups/$BACKUP_FILE" || {
        error "Backup do PostgreSQL falhou"
        exit 1
    }
    
    # MongoDB backup
    MONGO_BACKUP_DIR="backups/mongo_$(date +%Y%m%d_%H%M%S)"
    mongodump --uri="$MONGODB_URI" --out="$MONGO_BACKUP_DIR" || {
        error "Backup do MongoDB falhou"
        exit 1
    }
    
    success "Backup concluído"
fi

# Build da aplicação
log "Fazendo build da aplicação..."
npm run build || {
    error "Build falhou"
    exit 1
}
success "Build concluído"

# Configurar variáveis de ambiente
log "Configurando variáveis de ambiente..."
if [ "$ENVIRONMENT" = "production" ]; then
    export NODE_ENV=production
    export DATABASE_URL=$PROD_DATABASE_URL
    export MONGODB_URI=$PROD_MONGODB_URI
    export REDIS_URL=$PROD_REDIS_URL
    export JWT_SECRET=$PROD_JWT_SECRET
else
    export NODE_ENV=staging
    export DATABASE_URL=$STAGING_DATABASE_URL
    export MONGODB_URI=$STAGING_MONGODB_URI
    export REDIS_URL=$STAGING_REDIS_URL
    export JWT_SECRET=$STAGING_JWT_SECRET
fi

# Executar migrações do banco de dados
log "Executando migrações do banco de dados..."
npm run migration:run || {
    error "Migrações falharam"
    exit 1
}
success "Migrações executadas"

# Deploy usando Docker (se disponível)
if command -v docker &> /dev/null; then
    log "Fazendo deploy com Docker..."
    
    # Build da imagem Docker
    docker build -t whatsapp-platform-api:$VERSION . || {
        error "Build da imagem Docker falhou"
        exit 1
    }
    
    # Tag da imagem
    docker tag whatsapp-platform-api:$VERSION whatsapp-platform-api:latest
    
    # Push para registry (se configurado)
    if [ -n "$DOCKER_REGISTRY" ]; then
        docker tag whatsapp-platform-api:$VERSION $DOCKER_REGISTRY/whatsapp-platform-api:$VERSION
        docker push $DOCKER_REGISTRY/whatsapp-platform-api:$VERSION || {
            error "Push para Docker registry falhou"
            exit 1
        }
        success "Imagem enviada para registry"
    fi
    
    # Deploy usando docker-compose
    if [ -f "docker-compose.$ENVIRONMENT.yml" ]; then
        docker-compose -f docker-compose.$ENVIRONMENT.yml down
        docker-compose -f docker-compose.$ENVIRONMENT.yml up -d || {
            error "Deploy com docker-compose falhou"
            exit 1
        }
        success "Deploy com Docker concluído"
    fi
else
    # Deploy tradicional com PM2
    log "Fazendo deploy com PM2..."
    
    # Instalar dependências de produção
    npm ci --only=production || {
        error "Instalação de dependências falhou"
        exit 1
    }
    
    # Restart da aplicação com PM2
    if command -v pm2 &> /dev/null; then
        pm2 restart ecosystem.config.js --env $ENVIRONMENT || {
            error "Restart com PM2 falhou"
            exit 1
        }
        success "Deploy com PM2 concluído"
    else
        warning "PM2 não encontrado. Iniciando aplicação diretamente..."
        npm run start:prod &
    fi
fi

# Verificar se a aplicação está rodando
log "Verificando se a aplicação está rodando..."
sleep 10

if [ "$ENVIRONMENT" = "production" ]; then
    HEALTH_URL="https://api.whatsappplatform.com/health"
else
    HEALTH_URL="https://api-staging.whatsappplatform.com/health"
fi

HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL || echo "000")

if [ "$HTTP_STATUS" = "200" ]; then
    success "Aplicação está rodando corretamente"
else
    error "Aplicação não está respondendo (HTTP $HTTP_STATUS)"
    
    # Rollback em caso de falha
    if [ "$ENVIRONMENT" = "production" ]; then
        warning "Iniciando rollback..."
        
        # Restaurar backup do banco
        if [ -f "backups/$BACKUP_FILE" ]; then
            psql $DATABASE_URL < "backups/$BACKUP_FILE"
        fi
        
        # Voltar versão anterior
        if command -v docker &> /dev/null; then
            docker-compose -f docker-compose.$ENVIRONMENT.yml down
            docker run -d whatsapp-platform-api:previous
        else
            pm2 restart ecosystem.config.js --env $ENVIRONMENT
        fi
        
        error "Rollback executado"
    fi
    
    exit 1
fi

# Executar testes de smoke
log "Executando testes de smoke..."
npm run test:smoke || {
    warning "Testes de smoke falharam, mas deploy foi concluído"
}

# Notificar equipe (se configurado)
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚀 Deploy para $ENVIRONMENT concluído com sucesso! Versão: $VERSION\"}" \
        $SLACK_WEBHOOK_URL
fi

# Criar tag de release
if [ "$ENVIRONMENT" = "production" ]; then
    git tag -a "v$VERSION" -m "Release version $VERSION"
    git push origin "v$VERSION"
    success "Tag de release criada: v$VERSION"
fi

success "🎉 Deploy para $ENVIRONMENT concluído com sucesso!"

echo ""
echo "📊 Resumo do Deploy:"
echo "🌍 Ambiente: $ENVIRONMENT"
echo "🏷️  Versão: $VERSION"
echo "🌿 Branch: $CURRENT_BRANCH"
echo "⏰ Horário: $(date)"
echo "🔗 URL: $HEALTH_URL"
echo ""
echo "🚀 A aplicação está rodando em $ENVIRONMENT!"
