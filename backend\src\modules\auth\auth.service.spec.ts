import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UnauthorizedException, BadRequestException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { UsersService } from '../users/users.service';
import { User } from '../../database/entities/user.entity';
import { Company } from '../../database/entities/company.entity';
import { 
  createMockRepository, 
  createMockJwtService, 
  mockUser, 
  mockCompany 
} from '../../../test/setup';
import * as bcrypt from 'bcrypt';

// Mock do bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AuthService', () => {
  let service: AuthService;
  let usersService: UsersService;
  let jwtService: JwtService;
  let userRepository: any;
  let companyRepository: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: UsersService,
          useValue: {
            findByEmail: jest.fn(),
            create: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: JwtService,
          useValue: createMockJwtService(),
        },
        {
          provide: getRepositoryToken(User),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(Company),
          useValue: createMockRepository(),
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    jwtService = module.get<JwtService>(JwtService);
    userRepository = module.get(getRepositoryToken(User));
    companyRepository = module.get(getRepositoryToken(Company));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateUser', () => {
    it('should return user data when credentials are valid', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const hashedPassword = 'hashedPassword123';

      const user = { ...mockUser, password: hashedPassword };
      
      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(user as any);
      mockedBcrypt.compare.mockResolvedValue(true as never);

      const result = await service.validateUser(email, password);

      expect(result).toEqual({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        companyId: user.companyId,
      });
      expect(usersService.findByEmail).toHaveBeenCalledWith(email);
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hashedPassword);
    });

    it('should return null when user is not found', async () => {
      const email = '<EMAIL>';
      const password = 'password123';

      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(null);

      const result = await service.validateUser(email, password);

      expect(result).toBeNull();
    });

    it('should return null when password is invalid', async () => {
      const email = '<EMAIL>';
      const password = 'wrongpassword';
      const hashedPassword = 'hashedPassword123';

      const user = { ...mockUser, password: hashedPassword };
      
      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(user as any);
      mockedBcrypt.compare.mockResolvedValue(false as never);

      const result = await service.validateUser(email, password);

      expect(result).toBeNull();
    });

    it('should return null when user is inactive', async () => {
      const email = '<EMAIL>';
      const password = 'password123';
      const hashedPassword = 'hashedPassword123';

      const user = { ...mockUser, password: hashedPassword, isActive: false };
      
      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(user as any);

      const result = await service.validateUser(email, password);

      expect(result).toBeNull();
    });
  });

  describe('login', () => {
    it('should return access token when login is successful', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const user = mockUser;
      const token = 'mock-jwt-token';

      jest.spyOn(service, 'validateUser').mockResolvedValue(user);
      jest.spyOn(jwtService, 'sign').mockReturnValue(token);

      const result = await service.login(loginDto);

      expect(result).toEqual({
        access_token: token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          companyId: user.companyId,
        },
      });
    });

    it('should throw UnauthorizedException when credentials are invalid', async () => {
      const loginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      jest.spyOn(service, 'validateUser').mockResolvedValue(null);

      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('register', () => {
    it('should create new user and company successfully', async () => {
      const registerDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        companyName: 'Test Company',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511999999999',
        companyDocument: '12345678901',
      };

      const hashedPassword = 'hashedPassword123';
      const company = { ...mockCompany, id: 'new-company-id' };
      const user = { ...mockUser, id: 'new-user-id', companyId: company.id };
      const token = 'mock-jwt-token';

      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(null);
      companyRepository.save.mockResolvedValue(company);
      userRepository.save.mockResolvedValue(user);
      jest.spyOn(jwtService, 'sign').mockReturnValue(token);

      const result = await service.register(registerDto);

      expect(result).toEqual({
        access_token: token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          companyId: user.companyId,
        },
      });
    });

    it('should throw BadRequestException when email already exists', async () => {
      const registerDto = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        companyName: 'Test Company',
        companyEmail: '<EMAIL>',
        companyPhone: '+5511999999999',
        companyDocument: '12345678901',
      };

      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(mockUser as any);

      await expect(service.register(registerDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('refreshToken', () => {
    it('should return new access token when refresh is successful', async () => {
      const refreshTokenDto = {
        refreshToken: 'valid-refresh-token',
      };

      const user = mockUser;
      const newToken = 'new-mock-jwt-token';

      jest.spyOn(jwtService, 'verify').mockReturnValue({ userId: user.id });
      jest.spyOn(usersService, 'findById').mockResolvedValue(user as any);
      jest.spyOn(jwtService, 'sign').mockReturnValue(newToken);

      const result = await service.refreshToken(refreshTokenDto);

      expect(result).toEqual({
        access_token: newToken,
      });
    });

    it('should throw UnauthorizedException when refresh token is invalid', async () => {
      const refreshTokenDto = {
        refreshToken: 'invalid-refresh-token',
      };

      jest.spyOn(jwtService, 'verify').mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(service.refreshToken(refreshTokenDto)).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('forgotPassword', () => {
    it('should send password reset email when user exists', async () => {
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      const user = mockUser;

      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(user as any);
      userRepository.save.mockResolvedValue(user);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        message: 'E-mail de recuperação enviado com sucesso',
      });
    });

    it('should return success message even when user does not exist', async () => {
      const forgotPasswordDto = {
        email: '<EMAIL>',
      };

      jest.spyOn(usersService, 'findByEmail').mockResolvedValue(null);

      const result = await service.forgotPassword(forgotPasswordDto);

      expect(result).toEqual({
        message: 'E-mail de recuperação enviado com sucesso',
      });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully with valid token', async () => {
      const resetPasswordDto = {
        token: 'valid-reset-token',
        password: 'newpassword123',
      };

      const user = {
        ...mockUser,
        resetPasswordToken: 'valid-reset-token',
        resetPasswordExpires: new Date(Date.now() + 3600000), // 1 hour from now
      };

      const hashedPassword = 'hashedNewPassword123';

      userRepository.findOne.mockResolvedValue(user);
      mockedBcrypt.hash.mockResolvedValue(hashedPassword as never);
      userRepository.save.mockResolvedValue(user);

      const result = await service.resetPassword(resetPasswordDto);

      expect(result).toEqual({
        message: 'Senha alterada com sucesso',
      });
    });

    it('should throw BadRequestException when token is invalid or expired', async () => {
      const resetPasswordDto = {
        token: 'invalid-token',
        password: 'newpassword123',
      };

      userRepository.findOne.mockResolvedValue(null);

      await expect(service.resetPassword(resetPasswordDto)).rejects.toThrow(BadRequestException);
    });
  });
});
