import { UserRole } from '../../database/entities';
export interface AuthenticatedUser {
    id: string;
    email: string;
    role: UserRole;
    companyId: string;
    permissions?: string[];
    canAccessCompany?: (companyId: string) => boolean;
}
export declare const CurrentUser: (...dataOrPipes: (keyof AuthenticatedUser | import("@nestjs/common").PipeTransform<any, any> | import("@nestjs/common").Type<import("@nestjs/common").PipeTransform<any, any>> | undefined)[]) => ParameterDecorator;
