"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkMessageDto = exports.SendMessageDto = exports.ContactDto = exports.LocationDto = exports.MediaDto = exports.MessageType = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
var MessageType;
(function (MessageType) {
    MessageType["TEXT"] = "text";
    MessageType["IMAGE"] = "image";
    MessageType["AUDIO"] = "audio";
    MessageType["VIDEO"] = "video";
    MessageType["DOCUMENT"] = "document";
    MessageType["LOCATION"] = "location";
    MessageType["CONTACT"] = "contact";
})(MessageType || (exports.MessageType = MessageType = {}));
class MediaDto {
    url;
    filename;
    caption;
}
exports.MediaDto = MediaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL do arquivo de mídia',
        example: 'https://example.com/image.jpg',
    }),
    (0, class_validator_1.IsUrl)({}, { message: 'URL deve ser válida' }),
    __metadata("design:type", String)
], MediaDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nome do arquivo',
        example: 'imagem.jpg',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nome do arquivo deve ser uma string' }),
    __metadata("design:type", String)
], MediaDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Legenda da mídia',
        example: 'Confira nossa promoção!',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Legenda deve ser uma string' }),
    __metadata("design:type", String)
], MediaDto.prototype, "caption", void 0);
class LocationDto {
    latitude;
    longitude;
    address;
    name;
}
exports.LocationDto = LocationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude',
        example: -23.5505,
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'Latitude deve ser um número' }),
    (0, class_validator_1.Min)(-90, { message: 'Latitude deve ser maior que -90' }),
    (0, class_validator_1.Max)(90, { message: 'Latitude deve ser menor que 90' }),
    __metadata("design:type", Number)
], LocationDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude',
        example: -46.6333,
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'Longitude deve ser um número' }),
    (0, class_validator_1.Min)(-180, { message: 'Longitude deve ser maior que -180' }),
    (0, class_validator_1.Max)(180, { message: 'Longitude deve ser menor que 180' }),
    __metadata("design:type", Number)
], LocationDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Endereço',
        example: 'Av. Paulista, 1000 - São Paulo, SP',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Endereço deve ser uma string' }),
    __metadata("design:type", String)
], LocationDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nome do local',
        example: 'Shopping Center',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nome do local deve ser uma string' }),
    __metadata("design:type", String)
], LocationDto.prototype, "name", void 0);
class ContactDto {
    name;
    phone;
    email;
}
exports.ContactDto = ContactDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome do contato',
        example: 'João Silva',
    }),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    __metadata("design:type", String)
], ContactDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do contato',
        example: '+5511999999999',
    }),
    (0, class_validator_1.IsPhoneNumber)('BR', { message: 'Telefone deve ser válido' }),
    __metadata("design:type", String)
], ContactDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Email do contato',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Email deve ser uma string' }),
    __metadata("design:type", String)
], ContactDto.prototype, "email", void 0);
class SendMessageDto {
    to;
    type;
    content;
    media;
    location;
    contact;
    quotedMessageId;
    metadata;
}
exports.SendMessageDto = SendMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número de telefone do destinatário',
        example: '+5511999999999',
    }),
    (0, class_validator_1.IsPhoneNumber)('BR', { message: 'Número de telefone deve ser válido' }),
    __metadata("design:type", String)
], SendMessageDto.prototype, "to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da mensagem',
        enum: MessageType,
        default: MessageType.TEXT,
    }),
    (0, class_validator_1.IsEnum)(MessageType, { message: 'Tipo de mensagem deve ser válido' }),
    __metadata("design:type", String)
], SendMessageDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Conteúdo da mensagem (para mensagens de texto)',
        example: 'Olá! Como posso ajudá-lo?',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Conteúdo deve ser uma string' }),
    __metadata("design:type", String)
], SendMessageDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de mídia',
        type: MediaDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => MediaDto),
    __metadata("design:type", MediaDto)
], SendMessageDto.prototype, "media", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de localização',
        type: LocationDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => LocationDto),
    __metadata("design:type", LocationDto)
], SendMessageDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de contato',
        type: ContactDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => ContactDto),
    __metadata("design:type", ContactDto)
], SendMessageDto.prototype, "contact", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID da mensagem sendo respondida',
        example: 'msg-123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'ID da mensagem deve ser uma string' }),
    __metadata("design:type", String)
], SendMessageDto.prototype, "quotedMessageId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados adicionais',
        example: { campaign: 'promo-natal', source: 'website' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Metadados devem ser um objeto' }),
    __metadata("design:type", Object)
], SendMessageDto.prototype, "metadata", void 0);
class BulkMessageDto {
    to;
    type;
    content;
    media;
    minInterval;
    maxInterval;
    metadata;
}
exports.BulkMessageDto = BulkMessageDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lista de números de telefone',
        example: ['+5511999999999', '+5511888888888'],
    }),
    (0, class_validator_1.IsArray)({ message: 'Números devem ser um array' }),
    (0, class_validator_1.IsPhoneNumber)('BR', { each: true, message: 'Cada número deve ser válido' }),
    __metadata("design:type", Array)
], BulkMessageDto.prototype, "to", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo da mensagem',
        enum: MessageType,
        default: MessageType.TEXT,
    }),
    (0, class_validator_1.IsEnum)(MessageType, { message: 'Tipo de mensagem deve ser válido' }),
    __metadata("design:type", String)
], BulkMessageDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Conteúdo da mensagem (para mensagens de texto)',
        example: 'Olá! Confira nossa promoção especial!',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Conteúdo deve ser uma string' }),
    __metadata("design:type", String)
], BulkMessageDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Dados de mídia',
        type: MediaDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => MediaDto),
    __metadata("design:type", MediaDto)
], BulkMessageDto.prototype, "media", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Intervalo mínimo entre envios (segundos)',
        example: 5,
        default: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Intervalo mínimo deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Intervalo mínimo deve ser pelo menos 1 segundo' }),
    __metadata("design:type", Number)
], BulkMessageDto.prototype, "minInterval", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Intervalo máximo entre envios (segundos)',
        example: 15,
        default: 5,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'Intervalo máximo deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Intervalo máximo deve ser pelo menos 1 segundo' }),
    __metadata("design:type", Number)
], BulkMessageDto.prototype, "maxInterval", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados adicionais',
        example: { campaign: 'promo-natal', source: 'website' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)({ message: 'Metadados devem ser um objeto' }),
    __metadata("design:type", Object)
], BulkMessageDto.prototype, "metadata", void 0);
//# sourceMappingURL=send-message.dto.js.map