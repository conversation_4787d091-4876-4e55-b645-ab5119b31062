import { UserRole, UserStatus } from '../../../database/entities';
export declare class UserResponseDto {
    id: string;
    firstName: string;
    lastName: string;
    fullName: string;
    email: string;
    phone?: string;
    role: UserRole;
    status: UserStatus;
    avatar?: string;
    lastLoginAt?: Date;
    emailVerifiedAt?: Date;
    companyId: string;
    permissions?: string[];
    createdAt: Date;
    updatedAt: Date;
    password: string;
    emailVerificationToken?: string;
    passwordResetToken?: string;
    passwordResetExpiresAt?: Date;
}
