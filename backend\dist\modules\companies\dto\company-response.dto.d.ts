import { CompanyStatus } from '../../../database/entities';
export declare class CompanyResponseDto {
    id: string;
    name: string;
    cnpj: string;
    email: string;
    phone: string;
    address?: string;
    website?: string;
    status: CompanyStatus;
    logo?: string;
    agencyId?: string;
    isAgency: boolean;
    activeUsersCount: number;
    settings?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
