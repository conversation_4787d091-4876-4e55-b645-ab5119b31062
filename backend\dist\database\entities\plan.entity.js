"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Plan = exports.BillingCycle = exports.PlanStatus = exports.PlanType = void 0;
const typeorm_1 = require("typeorm");
const subscription_entity_1 = require("./subscription.entity");
var PlanType;
(function (PlanType) {
    PlanType["STARTER"] = "starter";
    PlanType["PROFESSIONAL"] = "professional";
    PlanType["ENTERPRISE"] = "enterprise";
    PlanType["AGENCY"] = "agency";
    PlanType["CUSTOM"] = "custom";
})(PlanType || (exports.PlanType = PlanType = {}));
var PlanStatus;
(function (PlanStatus) {
    PlanStatus["ACTIVE"] = "active";
    PlanStatus["INACTIVE"] = "inactive";
    PlanStatus["DEPRECATED"] = "deprecated";
})(PlanStatus || (exports.PlanStatus = PlanStatus = {}));
var BillingCycle;
(function (BillingCycle) {
    BillingCycle["MONTHLY"] = "monthly";
    BillingCycle["QUARTERLY"] = "quarterly";
    BillingCycle["SEMI_ANNUAL"] = "semi_annual";
    BillingCycle["ANNUAL"] = "annual";
})(BillingCycle || (exports.BillingCycle = BillingCycle = {}));
let Plan = class Plan {
    id;
    name;
    description;
    type;
    status;
    billingCycle;
    price;
    setupFee;
    discountPercentage;
    features;
    metadata;
    hasTrialPeriod;
    trialDays;
    isAgencyPlan;
    agencyCommissionPercentage;
    agencyFixedCommission;
    isRecurring;
    stripeProductId;
    stripePriceId;
    allowUpgrade;
    allowDowngrade;
    upgradeableTo;
    downgradeableTo;
    isPublic;
    isCustom;
    targetAudience;
    sortOrder;
    createdAt;
    updatedAt;
    subscriptions;
    getMonthlyPrice() {
        switch (this.billingCycle) {
            case BillingCycle.MONTHLY:
                return this.price;
            case BillingCycle.QUARTERLY:
                return this.price / 3;
            case BillingCycle.SEMI_ANNUAL:
                return this.price / 6;
            case BillingCycle.ANNUAL:
                return this.price / 12;
            default:
                return this.price;
        }
    }
    getDiscountedPrice() {
        return this.price * (1 - this.discountPercentage / 100);
    }
    hasFeature(feature) {
        return Boolean(this.features[feature]);
    }
    getFeatureLimit(feature) {
        const value = this.features[feature];
        return typeof value === 'number' ? value : 0;
    }
    canUpgradeTo(planId) {
        return this.allowUpgrade && (this.upgradeableTo?.includes(planId) ?? false);
    }
    canDowngradeTo(planId) {
        return this.allowDowngrade && (this.downgradeableTo?.includes(planId) ?? false);
    }
    isEnterprise() {
        return this.type === PlanType.ENTERPRISE || this.type === PlanType.CUSTOM;
    }
    isAgency() {
        return this.type === PlanType.AGENCY || this.isAgencyPlan;
    }
    getCommissionAmount(subscriptionPrice) {
        if (!this.isAgency())
            return 0;
        const percentageCommission = (subscriptionPrice * this.agencyCommissionPercentage) / 100;
        return percentageCommission + this.agencyFixedCommission;
    }
};
exports.Plan = Plan;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Plan.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], Plan.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Plan.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: PlanType.STARTER,
    }),
    __metadata("design:type", String)
], Plan.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: PlanStatus.ACTIVE,
    }),
    __metadata("design:type", String)
], Plan.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: BillingCycle.MONTHLY,
    }),
    __metadata("design:type", String)
], Plan.prototype, "billingCycle", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Plan.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "setupFee", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "discountPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], Plan.prototype, "features", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Plan.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Plan.prototype, "hasTrialPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "trialDays", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Plan.prototype, "isAgencyPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "agencyCommissionPercentage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "agencyFixedCommission", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Plan.prototype, "isRecurring", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Plan.prototype, "stripeProductId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Plan.prototype, "stripePriceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Plan.prototype, "allowUpgrade", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Plan.prototype, "allowDowngrade", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], Plan.prototype, "upgradeableTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], Plan.prototype, "downgradeableTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], Plan.prototype, "isPublic", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Plan.prototype, "isCustom", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Plan.prototype, "targetAudience", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Plan.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Plan.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => subscription_entity_1.Subscription, subscription => subscription.plan),
    __metadata("design:type", Array)
], Plan.prototype, "subscriptions", void 0);
exports.Plan = Plan = __decorate([
    (0, typeorm_1.Entity)('plans')
], Plan);
//# sourceMappingURL=plan.entity.js.map