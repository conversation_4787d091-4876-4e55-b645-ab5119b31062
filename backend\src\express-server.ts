import * as express from 'express';
import * as cors from 'cors';
import webhookRoutes from './routes/webhook.routes';

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rotas de webhook (sem autenticação)
app.use('/api/v1/webhooks', webhookRoutes);

// Rotas básicas
app.get('/', (req, res) => {
  res.json({
    status: 'ok',
    message: 'WhatsApp Platform API está funcionando!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    database: 'connected',
    services: {
      auth: 'active',
      whatsapp: 'ready',
      billing: 'active'
    }
  });
});

// Rotas da API
app.get('/api/users', (req, res) => {
  res.json({
    users: [
      { id: 1, name: 'Admin', email: '<EMAIL>', role: 'admin' },
      { id: 2, name: 'User', email: '<EMAIL>', role: 'user' }
    ]
  });
});

app.get('/api/organizations', (req, res) => {
  res.json({
    organizations: [
      { id: 1, name: 'Empresa Demo', plan: 'premium', status: 'active' }
    ]
  });
});

app.get('/api/whatsapp/status', (req, res) => {
  res.json({
    status: 'connected',
    phone: '+55 11 99999-9999',
    qrCode: null,
    lastSeen: new Date().toISOString()
  });
});

app.get('/api/messages', (req, res) => {
  res.json({
    messages: [
      {
        id: 1,
        from: '+5511999999999',
        to: '+5511888888888',
        content: 'Olá! Como posso ajudar?',
        timestamp: new Date().toISOString(),
        type: 'text',
        direction: 'inbound'
      }
    ]
  });
});

app.get('/api/analytics', (req, res) => {
  res.json({
    totalMessages: 1250,
    totalContacts: 89,
    activeAutomations: 5,
    conversionRate: 23.5
  });
});

// Documentação simples
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'WhatsApp Platform API',
    version: '1.0.0',
    endpoints: {
      'GET /': 'Status da API',
      'GET /api/health': 'Health check',
      'GET /api/users': 'Lista de usuários',
      'GET /api/organizations': 'Lista de organizações',
      'GET /api/whatsapp/status': 'Status do WhatsApp',
      'GET /api/messages': 'Lista de mensagens',
      'GET /api/analytics': 'Dados de analytics'
    }
  });
});

// Middleware de erro
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Algo deu errado!' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint não encontrado' });
});

app.listen(port, () => {
  console.log(`🚀 Servidor Express rodando em: http://localhost:${port}`);
  console.log(`📚 Documentação da API: http://localhost:${port}/api/docs`);
  console.log(`💚 Health check: http://localhost:${port}/api/health`);
});
