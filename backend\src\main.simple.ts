import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppSimpleModule } from './app.simple.module';

async function bootstrap() {
  const app = await NestFactory.create(AppSimpleModule);
  const configService = app.get(ConfigService);

  // Configurar CORS
  app.enableCors({
    origin: configService.get('cors.origin'),
    credentials: true,
  });

  // Configurar validação global
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Configurar prefixo da API
  const apiPrefix = configService.get('api.prefix');
  if (apiPrefix) {
    app.setGlobalPrefix(apiPrefix);
  }

  // Configurar Swagger
  const config = new DocumentBuilder()
    .setTitle('WhatsApp Platform API')
    .setDescription('API para plataforma de WhatsApp Business')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  const port = configService.get('port') || 3000;
  await app.listen(port);
  
  console.log(`🚀 Aplicação rodando em: http://localhost:${port}`);
  console.log(`📚 Documentação da API: http://localhost:${port}/api/docs`);
}

bootstrap();
