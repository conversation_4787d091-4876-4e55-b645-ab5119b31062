﻿export default function DashboardPage() {
  const stats = [
    {
      title: '<PERSON><PERSON><PERSON>',
      value: '1,234',
      change: '+12%',
      icon: '💬',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Contatos Ativos',
      value: '856',
      change: '+5%',
      icon: '👥',
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Conexões WhatsApp',
      value: '12',
      change: '+2',
      icon: '📱',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Taxa de Resposta',
      value: '94%',
      change: '+3%',
      icon: '📊',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">Visão geral da sua plataforma WhatsApp</p>
            </div>
            <div className="flex space-x-4">
              <a href="/connections" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Nova Conexão
              </a>
              <a href="/messages" className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                Mensagens
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600">{stat.change} vs mês anterior</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <span className="text-2xl">{stat.icon}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Atividade Recente</h3>
              <p className="text-sm text-gray-600">Últimas mensagens e interações</p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Nova mensagem de João Silva</p>
                    <p className="text-xs text-gray-500">há 2 minutos</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Conexão WhatsApp conectada</p>
                    <p className="text-xs text-gray-500">há 5 minutos</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Novo contato adicionado</p>
                    <p className="text-xs text-gray-500">há 10 minutos</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Status das Conexões</h3>
              <p className="text-sm text-gray-600">Estado atual das suas conexões WhatsApp</p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Conectado
                    </span>
                    <span className="text-sm">Vendas Principal</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 99999-9999</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Conectado
                    </span>
                    <span className="text-sm">Suporte</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 88888-8888</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Desconectado
                    </span>
                    <span className="text-sm">Marketing</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 77777-7777</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  const stats = [
    {
      title: 'Mensagens Hoje',
      value: '1,234',
      change: '+12%',
      icon: MessageSquare,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Contatos Ativos',
      value: '856',
      change: '+5%',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'Conexões WhatsApp',
      value: '12',
      change: '+2',
      icon: Smartphone,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Taxa de Resposta',
      value: '94%',
      change: '+3%',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    }
  ]

  return (
    <MainLayout title="Dashboard">
      <div className="p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Visão geral da sua plataforma WhatsApp</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <Badge variant="success" className="mt-1">
                  {stat.change} vs mês anterior
                </Badge>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Atividade Recente</CardTitle>
              <CardDescription>
                Últimas mensagens e interações
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Nova mensagem de João Silva</p>
                    <p className="text-xs text-gray-500">há 2 minutos</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Conexão WhatsApp conectada</p>
                    <p className="text-xs text-gray-500">há 5 minutos</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Novo contato adicionado</p>
                    <p className="text-xs text-gray-500">há 10 minutos</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Status das Conexões</CardTitle>
              <CardDescription>
                Estado atual das suas conexões WhatsApp
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="connected">Conectado</Badge>
                    <span className="text-sm">Vendas Principal</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 99999-9999</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="connected">Conectado</Badge>
                    <span className="text-sm">Suporte</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 88888-8888</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="disconnected">Desconectado</Badge>
                    <span className="text-sm">Marketing</span>
                  </div>
                  <span className="text-xs text-gray-500">+55 11 77777-7777</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
