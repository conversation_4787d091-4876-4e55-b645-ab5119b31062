import { AutomationType, AutomationStatus, TriggerCondition, ActionStep, FlowStep } from '../../../database/entities/automation.schema';
export declare class AutomationResponseDto {
    id: string;
    name: string;
    description?: string;
    type: AutomationType;
    status: AutomationStatus;
    companyId: string;
    createdBy: string;
    updatedBy?: string;
    trigger: TriggerCondition;
    actions?: ActionStep[];
    flowSteps?: FlowStep[];
    startStepId?: string;
    settings: Record<string, any>;
    executionCount: number;
    successCount: number;
    errorCount: number;
    successRate: number;
    lastExecutedAt?: Date;
    lastModifiedAt?: Date;
    version: number;
    isTemplate: boolean;
    templateCategory?: string;
    isActive: boolean;
    stepCount: number;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
export declare class AutomationExecutionResponseDto {
    id: string;
    automationId: string;
    automationName: string;
    status: string;
    context: Record<string, any>;
    steps: Array<{
        stepId: string;
        stepName: string;
        status: string;
        startedAt?: Date;
        completedAt?: Date;
        duration?: number;
        error?: any;
    }>;
    currentStepId?: string;
    startedAt?: Date;
    completedAt?: Date;
    duration: number;
    error?: Record<string, any>;
    retryCount: number;
    triggerType: string;
    results: Record<string, any>;
    progress: number;
    totalSteps: number;
    completedSteps: number;
    failedSteps: number;
    executionId: string;
    isTest: boolean;
    createdAt: Date;
}
