import { Model } from 'mongoose';
import { AutomationDocument, AutomationType, AutomationStatus, TriggerType } from '../../../database/entities/automation.schema';
import { AutomationExecutionDocument, ExecutionStatus } from '../../../database/entities/automation-execution.schema';
import { CreateAutomationDto } from '../dto/create-automation.dto';
import { UpdateAutomationDto } from '../dto/update-automation.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
export interface FindAutomationsOptions {
    companyId?: string;
    type?: AutomationType;
    status?: AutomationStatus;
    triggerType?: TriggerType;
    isTemplate?: boolean;
    templateCategory?: string;
    createdBy?: string;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface FindExecutionsOptions {
    automationId?: string;
    companyId?: string;
    status?: ExecutionStatus;
    contactPhone?: string;
    connectionId?: string;
    triggeredBy?: string;
    dateFrom?: Date;
    dateTo?: Date;
    page?: number;
    limit?: number;
}
export declare class AutomationService {
    private automationModel;
    private executionModel;
    private readonly logger;
    constructor(automationModel: Model<AutomationDocument>, executionModel: Model<AutomationExecutionDocument>);
    create(createAutomationDto: CreateAutomationDto, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    findAll(options: FindAutomationsOptions, currentUser: AuthenticatedUser): Promise<{
        automations: AutomationDocument[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    update(id: string, updateAutomationDto: UpdateAutomationDto, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    activate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    deactivate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    duplicate(id: string, currentUser: AuthenticatedUser): Promise<AutomationDocument>;
    getExecutions(options: FindExecutionsOptions, currentUser: AuthenticatedUser): Promise<{
        executions: AutomationExecutionDocument[];
        total: number;
        page: number;
        limit: number;
    }>;
    getAutomationStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        inactive: number;
        draft: number;
        totalExecutions: number;
        successRate: number;
    }>;
    private validateTriggerConfiguration;
    private validateActionSteps;
    private validateFlowSteps;
    private validateAutomationCompleteness;
}
