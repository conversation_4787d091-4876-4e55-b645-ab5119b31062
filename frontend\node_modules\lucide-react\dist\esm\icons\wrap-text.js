/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m16 16-2 2 2 2", key: "kkc6pm" }],
  ["path", { d: "M3 12h15a3 3 0 1 1 0 6h-4", key: "1cl7v7" }],
  ["path", { d: "M3 18h7", key: "sq21v6" }],
  ["path", { d: "M3 6h18", key: "d0wm0j" }]
];
const WrapText = createLucideIcon("wrap-text", __iconNode);

export { __iconNode, WrapText as default };
//# sourceMappingURL=wrap-text.js.map
