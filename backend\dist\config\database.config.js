"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMongoConfig = exports.getDatabaseConfig = void 0;
const getDatabaseConfig = (configService) => {
    const nodeEnv = configService.get('nodeEnv');
    if (nodeEnv === 'development') {
        return {
            type: 'sqlite',
            database: ':memory:',
            entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
            synchronize: true,
            logging: true,
            dropSchema: true,
        };
    }
    return {
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.name'),
        entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
        migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
        synchronize: false,
        logging: false,
        ssl: { rejectUnauthorized: false },
    };
};
exports.getDatabaseConfig = getDatabaseConfig;
const getMongoConfig = (configService) => ({
    uri: configService.get('mongodb.uri'),
    useNewUrlParser: true,
    useUnifiedTopology: true,
});
exports.getMongoConfig = getMongoConfig;
//# sourceMappingURL=database.config.js.map