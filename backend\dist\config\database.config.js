"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMongoConfig = exports.getDatabaseConfig = void 0;
const getDatabaseConfig = (configService) => ({
    type: 'postgres',
    host: configService.get('database.host'),
    port: configService.get('database.port'),
    username: configService.get('database.username'),
    password: configService.get('database.password'),
    database: configService.get('database.name'),
    entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
    synchronize: configService.get('nodeEnv') === 'development',
    logging: configService.get('nodeEnv') === 'development',
    ssl: configService.get('nodeEnv') === 'production' ? { rejectUnauthorized: false } : false,
});
exports.getDatabaseConfig = getDatabaseConfig;
const getMongoConfig = (configService) => ({
    uri: configService.get('mongodb.uri'),
    useNewUrlParser: true,
    useUnifiedTopology: true,
});
exports.getMongoConfig = getMongoConfig;
//# sourceMappingURL=database.config.js.map