"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMongoConfig = exports.getDatabaseConfig = void 0;
const getDatabaseConfig = (configService) => {
    const nodeEnv = configService.get('nodeEnv');
    const baseConfig = {
        entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
        migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
        synchronize: nodeEnv === 'development',
        logging: nodeEnv === 'development',
    };
    if (nodeEnv === 'development') {
        const databaseUrl = configService.get('DATABASE_URL');
        if (databaseUrl && databaseUrl.includes('postgresql')) {
            return {
                ...baseConfig,
                type: 'postgres',
                host: configService.get('database.host') || 'localhost',
                port: configService.get('database.port') || 5432,
                username: configService.get('database.username') || 'postgres',
                password: configService.get('database.password') || 'password',
                database: configService.get('database.name') || 'whatsapp_platform',
            };
        }
        return {
            ...baseConfig,
            type: 'sqlite',
            database: './dev-database.sqlite',
            dropSchema: false,
        };
    }
    return {
        ...baseConfig,
        type: 'postgres',
        host: configService.get('database.host'),
        port: configService.get('database.port'),
        username: configService.get('database.username'),
        password: configService.get('database.password'),
        database: configService.get('database.name'),
        synchronize: false,
        logging: false,
        ssl: { rejectUnauthorized: false },
    };
};
exports.getDatabaseConfig = getDatabaseConfig;
const getMongoConfig = (configService) => ({
    uri: configService.get('mongodb.uri'),
    useNewUrlParser: true,
    useUnifiedTopology: true,
});
exports.getMongoConfig = getMongoConfig;
//# sourceMappingURL=database.config.js.map