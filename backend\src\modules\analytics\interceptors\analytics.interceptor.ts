import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { TrackingService } from '../services/tracking.service';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';

@Injectable()
export class AnalyticsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AnalyticsInterceptor.name);

  constructor(private trackingService: TrackingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const user: AuthenticatedUser = request.user;
    
    const startTime = Date.now();
    const method = request.method;
    const url = request.url;
    const userAgent = request.headers['user-agent'];
    const ipAddress = request.ip || request.connection.remoteAddress;

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        const statusCode = response.statusCode;

        // Rastrear requisição da API se o usuário estiver autenticado
        if (user && user.companyId) {
          this.trackingService.trackApiRequest(
            user.companyId,
            url,
            method,
            responseTime,
            statusCode,
            user.id,
          ).catch(error => {
            this.logger.error(`Failed to track API request: ${error.message}`);
          });
        }

        // Log para debug
        this.logger.debug(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
      }),
      catchError((error) => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        const statusCode = error.status || 500;

        // Rastrear erro se o usuário estiver autenticado
        if (user && user.companyId) {
          this.trackingService.trackError(
            user.companyId,
            error.code || 'UNKNOWN_ERROR',
            error.message || 'Unknown error occurred',
            `${method} ${url}`,
            user.id,
          ).catch(trackingError => {
            this.logger.error(`Failed to track error: ${trackingError.message}`);
          });

          // Também rastrear a requisição com erro
          this.trackingService.trackApiRequest(
            user.companyId,
            url,
            method,
            responseTime,
            statusCode,
            user.id,
          ).catch(trackingError => {
            this.logger.error(`Failed to track API request with error: ${trackingError.message}`);
          });
        }

        // Log do erro
        this.logger.error(`${method} ${url} - ${statusCode} - ${responseTime}ms - Error: ${error.message}`);
        
        throw error;
      }),
    );
  }
}

@Injectable()
export class AuthAnalyticsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuthAnalyticsInterceptor.name);

  constructor(private trackingService: TrackingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const url = request.url;
    const method = request.method;

    return next.handle().pipe(
      tap((result) => {
        // Rastrear eventos de autenticação
        if (url.includes('/auth/login') && method === 'POST' && result?.user) {
          const sessionId = this.generateSessionId();
          
          this.trackingService.trackUserLogin(
            result.user.id,
            result.user.companyId,
            sessionId,
            {
              userAgent: request.headers['user-agent'],
              ipAddress: request.ip || request.connection.remoteAddress,
            },
          ).catch(error => {
            this.logger.error(`Failed to track user login: ${error.message}`);
          });
        }

        if (url.includes('/auth/logout') && method === 'POST') {
          const user: AuthenticatedUser = request.user;
          if (user) {
            this.trackingService.trackUserLogout(
              user.id,
              user.companyId,
              request.sessionId || 'unknown',
            ).catch(error => {
              this.logger.error(`Failed to track user logout: ${error.message}`);
            });
          }
        }

        if (url.includes('/auth/register') && method === 'POST' && result?.user) {
          this.trackingService.trackUserCreated(
            result.user.id,
            result.user.companyId,
            result.user.id, // Self-created
            result.user.role,
          ).catch(error => {
            this.logger.error(`Failed to track user creation: ${error.message}`);
          });
        }
      }),
    );
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

@Injectable()
export class WhatsAppAnalyticsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(WhatsAppAnalyticsInterceptor.name);

  constructor(private trackingService: TrackingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    const url = request.url;
    const method = request.method;

    return next.handle().pipe(
      tap((result) => {
        if (!user) return;

        // Rastrear eventos de conexão WhatsApp
        if (url.includes('/whatsapp/connections') && method === 'POST' && result?.id) {
          this.trackingService.trackWhatsAppConnected(
            result.id,
            user.companyId,
            user.id,
            result.phoneNumber,
          ).catch(error => {
            this.logger.error(`Failed to track WhatsApp connection: ${error.message}`);
          });
        }

        if (url.includes('/connect') && method === 'POST' && result?.qrCode) {
          const connectionId = this.extractConnectionId(url);
          if (connectionId) {
            this.trackingService.trackQRCodeGenerated(
              connectionId,
              user.companyId,
              user.id,
            ).catch(error => {
              this.logger.error(`Failed to track QR code generation: ${error.message}`);
            });
          }
        }

        if (url.includes('/disconnect') && method === 'POST') {
          const connectionId = this.extractConnectionId(url);
          if (connectionId) {
            this.trackingService.trackWhatsAppDisconnected(
              connectionId,
              user.companyId,
              user.id,
              'manual_disconnect',
            ).catch(error => {
              this.logger.error(`Failed to track WhatsApp disconnection: ${error.message}`);
            });
          }
        }

        if (url.includes('/send-message') && method === 'POST') {
          const connectionId = this.extractConnectionId(url);
          const messageData = request.body;
          
          if (connectionId && messageData) {
            this.trackingService.trackMessageSent(
              `msg_${Date.now()}`, // Temporary ID
              user.companyId,
              connectionId,
              messageData.to,
              messageData.type || 'text',
              false,
              user.id,
            ).catch(error => {
              this.logger.error(`Failed to track message sent: ${error.message}`);
            });
          }
        }

        if (url.includes('/send-bulk-message') && method === 'POST') {
          const connectionId = this.extractConnectionId(url);
          const messageData = request.body;
          
          if (connectionId && messageData?.to) {
            this.trackingService.trackBulkMessageSent(
              user.companyId,
              connectionId,
              messageData.to.length,
              messageData.type || 'text',
              user.id,
            ).catch(error => {
              this.logger.error(`Failed to track bulk message sent: ${error.message}`);
            });
          }
        }
      }),
    );
  }

  private extractConnectionId(url: string): string | null {
    const matches = url.match(/\/connections\/([^\/]+)/);
    return matches ? matches[1] : null;
  }
}

@Injectable()
export class ContactAnalyticsInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ContactAnalyticsInterceptor.name);

  constructor(private trackingService: TrackingService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user: AuthenticatedUser = request.user;
    const url = request.url;
    const method = request.method;

    return next.handle().pipe(
      tap((result) => {
        if (!user) return;

        // Rastrear eventos de contatos
        if (url.includes('/contacts') && method === 'POST' && result?.id) {
          this.trackingService.trackContactCreated(
            result.id,
            user.companyId,
            result.whatsappConnectionId,
            result.phoneNumber,
            result.source?.type,
            user.id,
          ).catch(error => {
            this.logger.error(`Failed to track contact creation: ${error.message}`);
          });
        }

        if (url.includes('/contacts') && method === 'PATCH' && result?.id) {
          const updatedFields = Object.keys(request.body);
          
          this.trackingService.trackContactUpdated(
            result.id,
            user.companyId,
            result.phoneNumber,
            updatedFields,
            user.id,
          ).catch(error => {
            this.logger.error(`Failed to track contact update: ${error.message}`);
          });
        }

        if (url.includes('/tags') && method === 'POST') {
          const contactId = this.extractContactId(url);
          const tagData = request.body;
          
          if (contactId && tagData?.name) {
            this.trackingService.trackContactTagged(
              contactId,
              user.companyId,
              'unknown', // Phone number not available in this context
              tagData.name,
              user.id,
            ).catch(error => {
              this.logger.error(`Failed to track contact tagging: ${error.message}`);
            });
          }
        }
      }),
    );
  }

  private extractContactId(url: string): string | null {
    const matches = url.match(/\/contacts\/([^\/]+)/);
    return matches ? matches[1] : null;
  }
}
