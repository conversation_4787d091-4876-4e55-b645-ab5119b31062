export declare enum MessageType {
    TEXT = "text",
    IMAGE = "image",
    AUDIO = "audio",
    VIDEO = "video",
    DOCUMENT = "document",
    LOCATION = "location",
    CONTACT = "contact"
}
export declare class MediaDto {
    url: string;
    filename?: string;
    caption?: string;
}
export declare class LocationDto {
    latitude: number;
    longitude: number;
    address?: string;
    name?: string;
}
export declare class ContactDto {
    name: string;
    phone: string;
    email?: string;
}
export declare class SendMessageDto {
    to: string;
    type: MessageType;
    content?: string;
    media?: MediaDto;
    location?: LocationDto;
    contact?: ContactDto;
    quotedMessageId?: string;
    metadata?: Record<string, any>;
}
export declare class BulkMessageDto {
    to: string[];
    type: MessageType;
    content?: string;
    media?: MediaDto;
    minInterval?: number;
    maxInterval?: number;
    metadata?: Record<string, any>;
}
