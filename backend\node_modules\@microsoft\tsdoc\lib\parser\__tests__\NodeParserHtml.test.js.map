{"version": 3, "file": "NodeParserHtml.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserHtml.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,wCAAwC,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,IAAI,CAAC,sCAAsC,EAAE;IAC3C,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,WAAW;QACX,cAAc;QACd,sBAAsB;QACtB,WAAW;QACX,MAAM;QACN,WAAW;QACX,cAAc;QACd,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,sCAAsC,EAAE;IAC3C,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,uBAAuB,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/F,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+CAA+C,EAAE;IACpD,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,4BAA4B,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CACpG,IAAI,CACL,CACF,CAAC;IACF,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,uCAAuC;QACvC,WAAW;QACX,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,YAAY;QACZ,OAAO;QACP,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,wCAAwC;QACxC,WAAW;QACX,eAAe;QACf,cAAc;QACd,UAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+CAA+C,EAAE;IACpD,WAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,8BAA8B;QAC9B,8BAA8B;QAC9B,kCAAkC;QAClC,gCAAgC;QAChC,gCAAgC;QAChC,0CAA0C;QAC1C,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;IACF,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,uBAAuB,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mBAAmB,EAAE;IACxB,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,4BAA4B,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACvG,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2BAA2B,EAAE;IAChC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2BAA2B,EAAE;IAChC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACxE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iCAAiC,EAAE;IACtC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAChE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,iCAAiC,EAAE;IACtC,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5G,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,sCAAsC,EAAE;IAC3C,IAAM,MAAM,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IAC5D,MAAM,CAAC,wBAAwB,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAEjD,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACzD,MAAM,CACP,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,sCAAsC,EAAE;IAC3C,IAAM,MAAM,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IAC5D,MAAM,CAAC,wBAAwB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvC,MAAM,CAAC,UAAU,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAEvD,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACvD,MAAM,CACP,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,2CAA2C,EAAE;IAChD,IAAM,MAAM,GAAuB,IAAI,kBAAkB,EAAE,CAAC;IAC5D,MAAM,CAAC,wBAAwB,CAAC,EAAE,CAAC,CAAC;IACpC,MAAM,CAAC,UAAU,CAAC,6BAA6B,GAAG,IAAI,CAAC;IAEvD,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACvD,MAAM,CACP,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TSDocConfiguration } from '../../configuration/TSDocConfiguration';\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('01 HTML start tags: simple, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * <tag/>',\r\n      ' * <tag-a />',\r\n      ' * <tag-b ><tag-c />',\r\n      ' * <tag-d',\r\n      ' * >',\r\n      ' * <tag-e',\r\n      ' *      />  ',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 HTML start tags: simple, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * < tag/>', ' * <tag -a />', ' * <tag-b /<tag-c / >', ' * <tag-d', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('03 HTML start tags: with attributes, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <tag-a attr-one=\"one\" >', ' * <tag-b', ' *   attr-two', ' *   = \"2\"', ' * />', ' */'].join(\r\n      '\\n'\r\n    )\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * <tag-c attr-three=\"3\" four=\\'4\\'/>',\r\n      ' * <tag-d',\r\n      ' *   attr-five',\r\n      ' *   = \"5\"',\r\n      ' *   six',\r\n      \" *   = '6'\",\r\n      ' * />',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * <tag-e attr-one=\"one\" two=\\'two\\'/>',\r\n      ' * <tag-f',\r\n      ' *   attr-one',\r\n      ' *   = \"one\"',\r\n      ' *   two',\r\n      \" *   = 'two'\",\r\n      ' * />',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 HTML start tags: with attributes, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * <tag-a attr -one=\"one\" />',\r\n      ' * <tag-b attr- two=\"two\" />',\r\n      ' * <tag-c attr-three=\\'three\" />',\r\n      ' * <tag-d attr-four=@\"four\" />',\r\n      ' * <tag-e attr-five@=\"five\" />',\r\n      ' * <tag-f attr-six=\"six\"seven=\"seven\" />',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <tag-g attr=\"multi', ' * line\" />', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('05 Eclipsed TSDoc', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * <tag attr-one=\"@tag\" />', ' */'].join('\\n'));\r\n});\r\n\r\ntest('06 Closing tags, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * </tag-a>', ' * </tag-b  >', ' * </tag-c', ' *   >', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('07 Closing tags, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * </tag-a/>', ' * </ tag-b>', ' * </tag-c', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('08 Unusual HTML names, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <a1/>', ' * <a-a>', ' * <a--9->', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('09 Unusual HTML names, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * <1a/>', ' * <a.a>', ' * <_a>', ' */'].join('\\n'));\r\n});\r\n\r\ntest('10 Supported HTML elements, positive', () => {\r\n  const config: TSDocConfiguration = new TSDocConfiguration();\r\n  config.setSupportedHtmlElements(['a', 'b', 'c']);\r\n\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <a>', ' * <b/>', ' * </c>', ' */'].join('\\n'),\r\n    config\r\n  );\r\n});\r\n\r\ntest('11 Supported HTML elements, negative', () => {\r\n  const config: TSDocConfiguration = new TSDocConfiguration();\r\n  config.setSupportedHtmlElements(['d']);\r\n  config.validation.reportUnsupportedHtmlElements = true;\r\n\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <a>', ' * <b>', ' * <c>', ' */'].join('\\n'),\r\n    config\r\n  );\r\n});\r\n\r\ntest('12 Forbidding all HTML elements, negative', () => {\r\n  const config: TSDocConfiguration = new TSDocConfiguration();\r\n  config.setSupportedHtmlElements([]);\r\n  config.validation.reportUnsupportedHtmlElements = true;\r\n\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * <a>', ' * <b>', ' * <c>', ' */'].join('\\n'),\r\n    config\r\n  );\r\n});\r\n"]}