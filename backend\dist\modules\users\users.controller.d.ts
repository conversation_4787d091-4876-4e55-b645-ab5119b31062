import { UsersService, FindUsersOptions } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto, currentUser: AuthenticatedUser): Promise<UserResponseDto>;
    findAll(query: FindUsersOptions, currentUser: AuthenticatedUser): Promise<{
        users: UserResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto, currentUser: AuthenticatedUser): Promise<UserResponseDto>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    changePassword(id: string, newPassword: string, currentUser: AuthenticatedUser): Promise<void>;
}
