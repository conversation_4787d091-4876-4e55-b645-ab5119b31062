declare const _default: () => {
    port: number;
    nodeEnv: string;
    apiPrefix: string;
    database: {
        host: string;
        port: number;
        username: string;
        password: string;
        name: string;
    };
    mongodb: {
        uri: string;
    };
    redis: {
        host: string;
        port: number;
        password: string;
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshSecret: string;
        refreshExpiresIn: string;
    };
    whatsapp: {
        evolution: {
            apiUrl: string;
            apiKey: string;
        };
        meta: {
            token: string;
            phoneNumberId: string;
            webhookVerifyToken: string;
        };
    };
    ai: {
        openai: {
            apiKey: string;
        };
        google: {
            apiKey: string;
        };
    };
    stripe: {
        secretKey: string;
        webhookSecret: string;
    };
    email: {
        host: string;
        port: number;
        user: string;
        pass: string;
    };
    upload: {
        maxFileSize: number;
        destination: string;
    };
    rateLimit: {
        ttl: number;
        limit: number;
    };
    cors: {
        origin: string;
    };
    logging: {
        level: string;
    };
};
export default _default;
