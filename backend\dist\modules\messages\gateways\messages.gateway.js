"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessagesGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const messages_service_1 = require("../services/messages.service");
const contacts_service_1 = require("../services/contacts.service");
let MessagesGateway = MessagesGateway_1 = class MessagesGateway {
    jwtService;
    configService;
    messagesService;
    contactsService;
    server;
    logger = new common_1.Logger(MessagesGateway_1.name);
    connectedUsers = new Map();
    constructor(jwtService, configService, messagesService, contactsService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.messagesService = messagesService;
        this.contactsService = contactsService;
    }
    async handleConnection(client) {
        try {
            const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
            if (!token) {
                this.logger.warn(`Connection rejected: No token provided`);
                client.disconnect();
                return;
            }
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('jwt.secret'),
            });
            const user = {
                id: payload.sub,
                email: payload.email,
                role: payload.role,
                companyId: payload.companyId,
            };
            client.user = user;
            if (!this.connectedUsers.has(user.id)) {
                this.connectedUsers.set(user.id, new Set());
            }
            this.connectedUsers.get(user.id).add(client.id);
            await client.join(`company:${user.companyId}`);
            this.logger.log(`User connected: ${user.email} (${client.id})`);
            client.emit('connected', {
                message: 'Conectado ao sistema de mensagens',
                userId: user.id,
                companyId: user.companyId,
            });
        }
        catch (error) {
            this.logger.error(`Connection error: ${error.message}`);
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        if (client.user) {
            const userSockets = this.connectedUsers.get(client.user.id);
            if (userSockets) {
                userSockets.delete(client.id);
                if (userSockets.size === 0) {
                    this.connectedUsers.delete(client.user.id);
                }
            }
            this.logger.log(`User disconnected: ${client.user.email} (${client.id})`);
        }
    }
    async handleJoinConversation(client, data) {
        if (!client.user)
            return;
        const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
        await client.join(conversationId);
        this.logger.log(`User ${client.user.email} joined conversation: ${conversationId}`);
        try {
            const messages = await this.messagesService.getConversation(data.contactPhone, data.whatsappConnectionId, client.user, 50);
            client.emit('conversation_history', {
                conversationId,
                messages: messages.reverse(),
            });
        }
        catch (error) {
            this.logger.error(`Error loading conversation history: ${error.message}`);
            client.emit('error', { message: 'Erro ao carregar histórico da conversa' });
        }
    }
    async handleLeaveConversation(client, data) {
        if (!client.user)
            return;
        const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
        await client.leave(conversationId);
        this.logger.log(`User ${client.user.email} left conversation: ${conversationId}`);
    }
    async handleTypingStart(client, data) {
        if (!client.user)
            return;
        const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
        client.to(conversationId).emit('user_typing', {
            userId: client.user.id,
            userName: client.user.email,
            conversationId,
        });
    }
    async handleTypingStop(client, data) {
        if (!client.user)
            return;
        const conversationId = `${data.whatsappConnectionId}:${data.contactPhone}`;
        client.to(conversationId).emit('user_stopped_typing', {
            userId: client.user.id,
            userName: client.user.email,
            conversationId,
        });
    }
    async handleMarkAsRead(client, data) {
        if (!client.user)
            return;
        try {
            this.logger.log(`Message marked as read: ${data.messageId} by ${client.user.email}`);
            client.emit('message_read_confirmed', { messageId: data.messageId });
        }
        catch (error) {
            this.logger.error(`Error marking message as read: ${error.message}`);
            client.emit('error', { message: 'Erro ao marcar mensagem como lida' });
        }
    }
    async emitNewMessage(message, companyId) {
        const conversationId = `${message.whatsappConnectionId}:${message.contactPhone}`;
        this.server.to(conversationId).emit('new_message', message);
        this.server.to(`company:${companyId}`).emit('message_notification', {
            conversationId,
            message,
            type: 'new_message',
        });
        this.logger.log(`New message emitted to conversation: ${conversationId}`);
    }
    async emitMessageStatusUpdate(messageId, status, companyId) {
        this.server.to(`company:${companyId}`).emit('message_status_update', {
            messageId,
            status,
            timestamp: new Date(),
        });
        this.logger.log(`Message status update emitted: ${messageId} -> ${status}`);
    }
    async emitContactUpdate(contact, companyId) {
        this.server.to(`company:${companyId}`).emit('contact_update', contact);
        this.logger.log(`Contact update emitted for company: ${companyId}`);
    }
    async emitConnectionStatusUpdate(connectionId, status, companyId) {
        this.server.to(`company:${companyId}`).emit('connection_status_update', {
            connectionId,
            status,
            timestamp: new Date(),
        });
        this.logger.log(`Connection status update emitted: ${connectionId} -> ${status}`);
    }
    getConnectedUsers() {
        return this.connectedUsers;
    }
    isUserOnline(userId) {
        return this.connectedUsers.has(userId) && this.connectedUsers.get(userId).size > 0;
    }
    async sendNotificationToUser(userId, notification) {
        const userSockets = this.connectedUsers.get(userId);
        if (userSockets) {
            userSockets.forEach(socketId => {
                this.server.to(socketId).emit('notification', notification);
            });
        }
    }
};
exports.MessagesGateway = MessagesGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], MessagesGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('join_conversation'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesGateway.prototype, "handleJoinConversation", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave_conversation'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesGateway.prototype, "handleLeaveConversation", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('typing_start'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesGateway.prototype, "handleTypingStart", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('typing_stop'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesGateway.prototype, "handleTypingStop", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('mark_as_read'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesGateway.prototype, "handleMarkAsRead", null);
exports.MessagesGateway = MessagesGateway = MessagesGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
            credentials: true,
        },
        namespace: '/messages',
    }),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService,
        messages_service_1.MessagesService,
        contacts_service_1.ContactsService])
], MessagesGateway);
//# sourceMappingURL=messages.gateway.js.map