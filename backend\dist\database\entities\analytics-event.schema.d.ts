import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type AnalyticsEventDocument = AnalyticsEvent & Document;
export declare enum EventType {
    USER_LOGIN = "user_login",
    USER_LOGOUT = "user_logout",
    USER_CREATED = "user_created",
    WHATSAPP_CONNECTED = "whatsapp_connected",
    WHATSAPP_DISCONNECTED = "whatsapp_disconnected",
    WHATSAPP_QR_GENERATED = "whatsapp_qr_generated",
    MESSAGE_SENT = "message_sent",
    MESSAGE_RECEIVED = "message_received",
    MESSAGE_DELIVERED = "message_delivered",
    MESSAGE_READ = "message_read",
    MESSAGE_FAILED = "message_failed",
    BULK_MESSAGE_SENT = "bulk_message_sent",
    CONTACT_CREATED = "contact_created",
    CONTACT_UPDATED = "contact_updated",
    CONTACT_TAGGED = "contact_tagged",
    LEAD_CONVERTED = "lead_converted",
    LEAD_SCORED = "lead_scored",
    AUTOMATION_TRIGGERED = "automation_triggered",
    AUTOMATION_COMPLETED = "automation_completed",
    CHATBOT_INTERACTION = "chatbot_interaction",
    CONVERSATION_STARTED = "conversation_started",
    CONVERSATION_ENDED = "conversation_ended",
    FIRST_RESPONSE = "first_response",
    RESPONSE_TIME_MEASURED = "response_time_measured",
    SUBSCRIPTION_CREATED = "subscription_created",
    SUBSCRIPTION_UPDATED = "subscription_updated",
    PAYMENT_PROCESSED = "payment_processed",
    PAYMENT_FAILED = "payment_failed",
    API_REQUEST = "api_request",
    ERROR_OCCURRED = "error_occurred",
    PERFORMANCE_METRIC = "performance_metric"
}
export declare enum EventCategory {
    USER = "user",
    WHATSAPP = "whatsapp",
    MESSAGE = "message",
    CONTACT = "contact",
    AUTOMATION = "automation",
    CONVERSION = "conversion",
    BILLING = "billing",
    SYSTEM = "system"
}
export interface EventProperties {
    userId?: string;
    connectionId?: string;
    contactPhone?: string;
    messageId?: string;
    messageType?: string;
    messageDirection?: string;
    responseTime?: number;
    isAutomated?: boolean;
    contactSource?: string;
    leadScore?: number;
    leadStage?: string;
    tags?: string[];
    automationId?: string;
    flowStep?: string;
    aiProvider?: string;
    conversionType?: string;
    conversionValue?: number;
    funnelStage?: string;
    duration?: number;
    errorCode?: string;
    errorMessage?: string;
    [key: string]: any;
}
export declare class AnalyticsEvent {
    type: EventType;
    category: EventCategory;
    timestamp: Date;
    companyId: Types.ObjectId;
    userId?: string;
    sessionId?: string;
    properties: EventProperties;
    metadata: Record<string, any>;
    date: string;
    hour: string;
    month: string;
    year: string;
    dayOfWeek: number;
    hourOfDay: number;
    value?: number;
    duration?: number;
    country?: string;
    region?: string;
    city?: string;
    timezone?: string;
    userAgent?: string;
    ipAddress?: string;
}
export declare const AnalyticsEventSchema: mongoose.Schema<AnalyticsEvent, mongoose.Model<AnalyticsEvent, any, any, any, Document<unknown, any, AnalyticsEvent, any> & AnalyticsEvent & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, AnalyticsEvent, Document<unknown, {}, mongoose.FlatRecord<AnalyticsEvent>, {}> & mongoose.FlatRecord<AnalyticsEvent> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
