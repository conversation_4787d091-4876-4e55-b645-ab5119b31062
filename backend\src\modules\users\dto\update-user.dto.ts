import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import { UserStatus } from '../../../database/entities';

export class UpdateUserDto extends PartialType(
  OmitType(CreateUserDto, ['password', 'companyId'] as const)
) {
  @ApiPropertyOptional({
    description: 'Status do usuário',
    enum: UserStatus,
  })
  @IsOptional()
  @IsEnum(UserStatus, { message: 'Status deve ser um valor válido' })
  status?: UserStatus;
}
