import { PartialType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateIntegrationDto } from './create-integration.dto';
import { IntegrationStatus } from '../../../database/entities/integration.entity';

export class UpdateIntegrationDto extends PartialType(CreateIntegrationDto) {
  @ApiPropertyOptional({
    description: 'Status da integração',
    enum: IntegrationStatus,
  })
  @IsOptional()
  @IsEnum(IntegrationStatus)
  status?: IntegrationStatus;
}
