import {
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Message,
  MessageDocument,
  MessageType,
  MessageDirection,
  MessageStatus,
  UserRole
} from '../../../database/entities';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { ContactsService } from './contacts.service';

export interface FindMessagesOptions {
  companyId?: string;
  whatsappConnectionId?: string;
  contactPhone?: string;
  conversationId?: string;
  direction?: MessageDirection;
  type?: MessageType;
  status?: MessageStatus;
  isAutomated?: boolean;
  sentBy?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateMessageData {
  messageId: string;
  type: MessageType;
  direction: MessageDirection;
  content?: string;
  media?: any;
  location?: any;
  contact?: any;
  quotedMessageId?: string;
  metadata?: Record<string, any>;
  timestamp: Date;
  whatsappConnectionId: string;
  contactPhone: string;
  sentBy?: string;
  isAutomated?: boolean;
  automationId?: string;
  isTemplate?: boolean;
  templateName?: string;
  conversationId?: string;
  isFirstMessage?: boolean;
  tags?: string[];
}

@Injectable()
export class MessagesService {
  private readonly logger = new Logger(MessagesService.name);

  constructor(
    @InjectModel(Message.name)
    private messageModel: Model<MessageDocument>,
    private contactsService: ContactsService,
  ) {}

  async create(
    messageData: CreateMessageData,
    companyId: string,
  ): Promise<MessageDocument> {
    // Verificar se a mensagem já existe
    const existingMessage = await this.messageModel.findOne({
      messageId: messageData.messageId,
    });

    if (existingMessage) {
      this.logger.warn(`Message already exists: ${messageData.messageId}`);
      return existingMessage;
    }

    // Criar ou atualizar contato
    await this.contactsService.getOrCreateContact(
      messageData.contactPhone,
      messageData.whatsappConnectionId,
      companyId,
    );

    // Atualizar estatísticas do contato
    await this.contactsService.updateLastMessage(
      messageData.contactPhone,
      messageData.whatsappConnectionId,
      companyId,
    );

    // Calcular tempo de resposta se for uma resposta
    let responseTime: number | undefined;
    if (messageData.direction === MessageDirection.OUTBOUND && !messageData.isAutomated) {
      const lastInboundMessage = await this.messageModel
        .findOne({
          contactPhone: messageData.contactPhone,
          whatsappConnectionId: messageData.whatsappConnectionId,
          direction: MessageDirection.INBOUND,
          companyId: new Types.ObjectId(companyId),
        })
        .sort({ timestamp: -1 });

      if (lastInboundMessage) {
        responseTime = Math.floor(
          (messageData.timestamp.getTime() - lastInboundMessage.timestamp.getTime()) / 1000
        );
      }
    }

    // Verificar se é a primeira mensagem do contato
    const isFirstMessage = await this.isFirstMessageFromContact(
      messageData.contactPhone,
      messageData.whatsappConnectionId,
      companyId,
    );

    const message = new this.messageModel({
      ...messageData,
      companyId: new Types.ObjectId(companyId),
      status: MessageStatus.PENDING,
      responseTime,
      isFirstMessage,
    });

    const savedMessage = await message.save();
    this.logger.log(`Message created: ${savedMessage._id} - ${messageData.messageId}`);

    return savedMessage;
  }

  async findAll(
    options: FindMessagesOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    messages: MessageDocument[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      companyId,
      whatsappConnectionId,
      contactPhone,
      conversationId,
      direction,
      type,
      status,
      isAutomated,
      sentBy,
      dateFrom,
      dateTo,
      search,
      page = 1,
      limit = 50,
      sortBy = 'timestamp',
      sortOrder = 'desc',
    } = options;

    // Filtro base por empresa
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
    };

    // Filtros adicionais
    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      filter.companyId = new Types.ObjectId(companyId);
    }

    if (whatsappConnectionId) {
      filter.whatsappConnectionId = whatsappConnectionId;
    }

    if (contactPhone) {
      filter.contactPhone = contactPhone;
    }

    if (conversationId) {
      filter.conversationId = conversationId;
    }

    if (direction) {
      filter.direction = direction;
    }

    if (type) {
      filter.type = type;
    }

    if (status) {
      filter.status = status;
    }

    if (typeof isAutomated === 'boolean') {
      filter.isAutomated = isAutomated;
    }

    if (sentBy) {
      filter.sentBy = sentBy;
    }

    if (dateFrom || dateTo) {
      filter.timestamp = {};
      if (dateFrom) filter.timestamp.$gte = dateFrom;
      if (dateTo) filter.timestamp.$lte = dateTo;
    }

    if (search) {
      filter.$or = [
        { content: { $regex: search, $options: 'i' } },
        { contactPhone: { $regex: search, $options: 'i' } },
        { 'media.caption': { $regex: search, $options: 'i' } },
      ];
    }

    // Paginação
    const skip = (page - 1) * limit;

    // Ordenação
    const sort: any = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const [messages, total] = await Promise.all([
      this.messageModel
        .find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.messageModel.countDocuments(filter),
    ]);

    return {
      messages,
      total,
      page,
      limit,
    };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<MessageDocument> {
    const message = await this.messageModel.findOne({
      _id: new Types.ObjectId(id),
      companyId: new Types.ObjectId(currentUser.companyId),
    });

    if (!message) {
      throw new NotFoundException('Mensagem não encontrada');
    }

    return message;
  }

  async findByMessageId(messageId: string): Promise<MessageDocument | null> {
    return this.messageModel.findOne({ messageId });
  }

  async updateStatus(
    messageId: string,
    status: MessageStatus,
    timestamp?: Date,
  ): Promise<void> {
    const updateData: any = { status };

    switch (status) {
      case MessageStatus.SENT:
        updateData.sentAt = timestamp || new Date();
        break;
      case MessageStatus.DELIVERED:
        updateData.deliveredAt = timestamp || new Date();
        break;
      case MessageStatus.READ:
        updateData.readAt = timestamp || new Date();
        break;
      case MessageStatus.FAILED:
        updateData.errorMessage = 'Falha na entrega';
        break;
    }

    await this.messageModel.updateOne({ messageId }, updateData);
    this.logger.log(`Message status updated: ${messageId} -> ${status}`);
  }

  async getConversation(
    contactPhone: string,
    whatsappConnectionId: string,
    currentUser: AuthenticatedUser,
    limit: number = 50,
  ): Promise<MessageDocument[]> {
    return this.messageModel
      .find({
        contactPhone,
        whatsappConnectionId,
        companyId: new Types.ObjectId(currentUser.companyId),
      })
      .sort({ timestamp: -1 })
      .limit(limit)
      .exec();
  }

  async getMessageStats(
    currentUser: AuthenticatedUser,
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<{
    total: number;
    sent: number;
    received: number;
    automated: number;
    failed: number;
  }> {
    const filter: any = {
      companyId: new Types.ObjectId(currentUser.companyId),
    };

    if (dateFrom || dateTo) {
      filter.timestamp = {};
      if (dateFrom) filter.timestamp.$gte = dateFrom;
      if (dateTo) filter.timestamp.$lte = dateTo;
    }

    const [total, sent, received, automated, failed] = await Promise.all([
      this.messageModel.countDocuments(filter),
      this.messageModel.countDocuments({ ...filter, direction: MessageDirection.OUTBOUND }),
      this.messageModel.countDocuments({ ...filter, direction: MessageDirection.INBOUND }),
      this.messageModel.countDocuments({ ...filter, isAutomated: true }),
      this.messageModel.countDocuments({ ...filter, status: MessageStatus.FAILED }),
    ]);

    return { total, sent, received, automated, failed };
  }

  async deleteMessage(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const message = await this.findOne(id, currentUser);
    await this.messageModel.findByIdAndDelete(message._id);
    this.logger.log(`Message deleted: ${id}`);
  }

  private async isFirstMessageFromContact(
    contactPhone: string,
    whatsappConnectionId: string,
    companyId: string,
  ): Promise<boolean> {
    const messageCount = await this.messageModel.countDocuments({
      contactPhone,
      whatsappConnectionId,
      companyId: new Types.ObjectId(companyId),
    });

    return messageCount === 0;
  }
}
