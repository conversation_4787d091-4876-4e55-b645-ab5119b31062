import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { Integration } from './integration.entity';
import { Company } from './company.entity';

export enum WebhookStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  RETRYING = 'retrying',
  CANCELLED = 'cancelled',
}

export interface WebhookRequest {
  url: string;
  method: string;
  headers: Record<string, string>;
  body: any;
  timeout: number;
}

export interface WebhookResponse {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  body: any;
  duration: number;
}

@Entity('webhook_logs')
@Index(['integrationId', 'status'])
@Index(['companyId', 'createdAt'])
@Index(['eventType', 'createdAt'])
export class WebhookLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  integrationId: string;

  @ManyToOne(() => Integration, integration => integration.logs)
  @JoinColumn({ name: 'integrationId' })
  integration: Integration;

  @Column()
  companyId: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({
    type: 'enum',
    enum: WebhookStatus,
    default: WebhookStatus.PENDING,
  })
  status: WebhookStatus;

  @Column()
  eventType: string;

  @Column({ type: 'json' })
  eventData: any;

  @Column({ type: 'json' })
  request: WebhookRequest;

  @Column({ type: 'json', nullable: true })
  response: WebhookResponse | null;

  @Column({ nullable: true })
  errorMessage: string | null;

  @Column({ nullable: true })
  errorCode: string | null;

  @Column({ type: 'text', nullable: true })
  errorStack: string | null;

  @Column({ default: 0 })
  attemptCount: number;

  @Column({ default: 3 })
  maxAttempts: number;

  @Column({ nullable: true })
  nextRetryAt: Date | null;

  @Column({ nullable: true })
  completedAt: Date;

  @Column({ default: 0 })
  duration: number; // milliseconds

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @CreateDateColumn()
  createdAt: Date;

  // Métodos auxiliares
  isSuccess(): boolean {
    return this.status === WebhookStatus.SUCCESS;
  }

  isFailed(): boolean {
    return this.status === WebhookStatus.FAILED;
  }

  isPending(): boolean {
    return this.status === WebhookStatus.PENDING;
  }

  isRetrying(): boolean {
    return this.status === WebhookStatus.RETRYING;
  }

  canRetry(): boolean {
    return this.isFailed() && 
           this.attemptCount < this.maxAttempts &&
           (!this.nextRetryAt || new Date() >= this.nextRetryAt);
  }

  markAsSuccess(response: WebhookResponse): void {
    this.status = WebhookStatus.SUCCESS;
    this.response = response;
    this.completedAt = new Date();
    this.duration = response.duration;
    this.errorMessage = null;
    this.errorCode = null;
    this.errorStack = null;
  }

  markAsFailed(error: any, response?: WebhookResponse): void {
    this.status = WebhookStatus.FAILED;
    this.response = response || null;
    this.completedAt = new Date();
    this.errorMessage = error.message || 'Unknown error';
    this.errorCode = error.code || 'UNKNOWN_ERROR';
    this.errorStack = error.stack || null;
    
    if (response) {
      this.duration = response.duration;
    }

    this.attemptCount += 1;

    // Agendar próxima tentativa se possível
    if (this.canRetry()) {
      const delayMinutes = Math.pow(2, this.attemptCount) * 5; // Backoff exponencial
      this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
      this.status = WebhookStatus.RETRYING;
    }
  }

  retry(): void {
    if (this.canRetry()) {
      this.status = WebhookStatus.PENDING;
      this.nextRetryAt = null;
      this.errorMessage = null;
      this.errorCode = null;
      this.errorStack = null;
    }
  }

  cancel(): void {
    this.status = WebhookStatus.CANCELLED;
    this.completedAt = new Date();
  }

  getRetryDelay(): number {
    return Math.pow(2, this.attemptCount) * 5 * 60 * 1000; // Exponential backoff in milliseconds
  }

  isHttpSuccess(): boolean {
    return Boolean(this.response && this.response.status >= 200 && this.response.status < 300);
  }

  getStatusDescription(): string {
    switch (this.status) {
      case WebhookStatus.PENDING:
        return 'Aguardando execução';
      case WebhookStatus.SUCCESS:
        return 'Executado com sucesso';
      case WebhookStatus.FAILED:
        return `Falhou após ${this.attemptCount} tentativa(s)`;
      case WebhookStatus.RETRYING:
        return `Tentativa ${this.attemptCount}/${this.maxAttempts} - Próxima em ${this.getNextRetryDescription()}`;
      case WebhookStatus.CANCELLED:
        return 'Cancelado';
      default:
        return 'Status desconhecido';
    }
  }

  private getNextRetryDescription(): string {
    if (!this.nextRetryAt) return 'breve';
    
    const now = new Date();
    const diff = this.nextRetryAt.getTime() - now.getTime();
    
    if (diff <= 0) return 'agora';
    
    const minutes = Math.ceil(diff / (1000 * 60));
    if (minutes < 60) return `${minutes} minuto(s)`;
    
    const hours = Math.ceil(minutes / 60);
    return `${hours} hora(s)`;
  }
}
