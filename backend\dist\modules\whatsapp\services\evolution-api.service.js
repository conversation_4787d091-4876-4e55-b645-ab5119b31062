"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EvolutionApiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EvolutionApiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const entities_1 = require("../../../database/entities");
let EvolutionApiService = EvolutionApiService_1 = class EvolutionApiService {
    configService;
    httpService;
    logger = new common_1.Logger(EvolutionApiService_1.name);
    baseUrl;
    apiKey;
    constructor(configService, httpService) {
        this.configService = configService;
        this.httpService = httpService;
        this.baseUrl = this.configService.get('whatsapp.evolution.apiUrl') || 'http://localhost:8080';
        this.apiKey = this.configService.get('whatsapp.evolution.apiKey') || '';
    }
    getHeaders() {
        return {
            'Content-Type': 'application/json',
            'apikey': this.apiKey,
        };
    }
    async createInstance(instanceName, webhookUrl) {
        try {
            const payload = {
                instanceName,
                qrcode: true,
                integration: 'WHATSAPP-BAILEYS',
            };
            if (webhookUrl) {
                payload.webhook = {
                    url: webhookUrl,
                    events: [
                        'APPLICATION_STARTUP',
                        'QRCODE_UPDATED',
                        'CONNECTION_UPDATE',
                        'MESSAGES_UPSERT',
                        'MESSAGES_UPDATE',
                        'SEND_MESSAGE',
                    ],
                };
            }
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.baseUrl}/instance/create`, payload, { headers: this.getHeaders() }));
            this.logger.log(`Instance created: ${instanceName}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Failed to create instance ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao criar instância do WhatsApp');
        }
    }
    async deleteInstance(instanceName) {
        try {
            await (0, rxjs_1.firstValueFrom)(this.httpService.delete(`${this.baseUrl}/instance/delete/${instanceName}`, { headers: this.getHeaders() }));
            this.logger.log(`Instance deleted: ${instanceName}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete instance ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao deletar instância do WhatsApp');
        }
    }
    async getInstanceStatus(instanceName) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.baseUrl}/instance/connectionState/${instanceName}`, { headers: this.getHeaders() }));
            return response.data;
        }
        catch (error) {
            this.logger.error(`Failed to get instance status ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao obter status da instância');
        }
    }
    async getQRCode(instanceName) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(`${this.baseUrl}/instance/connect/${instanceName}`, { headers: this.getHeaders() }));
            return response.data.qrcode || response.data.base64;
        }
        catch (error) {
            this.logger.error(`Failed to get QR code ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao obter QR Code');
        }
    }
    async sendMessage(instanceName, message) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.baseUrl}/message/sendText/${instanceName}`, message, { headers: this.getHeaders() }));
            this.logger.log(`Message sent from ${instanceName} to ${message.number}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Failed to send message from ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao enviar mensagem');
        }
    }
    async sendMediaMessage(instanceName, message) {
        try {
            const endpoint = this.getMediaEndpoint(message.media?.mediatype);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.baseUrl}/message/${endpoint}/${instanceName}`, message, { headers: this.getHeaders() }));
            this.logger.log(`Media message sent from ${instanceName} to ${message.number}`);
            return response.data;
        }
        catch (error) {
            this.logger.error(`Failed to send media message from ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao enviar mensagem de mídia');
        }
    }
    async logout(instanceName) {
        try {
            await (0, rxjs_1.firstValueFrom)(this.httpService.delete(`${this.baseUrl}/instance/logout/${instanceName}`, { headers: this.getHeaders() }));
            this.logger.log(`Instance logged out: ${instanceName}`);
        }
        catch (error) {
            this.logger.error(`Failed to logout instance ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao desconectar instância');
        }
    }
    async restart(instanceName) {
        try {
            await (0, rxjs_1.firstValueFrom)(this.httpService.put(`${this.baseUrl}/instance/restart/${instanceName}`, {}, { headers: this.getHeaders() }));
            this.logger.log(`Instance restarted: ${instanceName}`);
        }
        catch (error) {
            this.logger.error(`Failed to restart instance ${instanceName}:`, error.response?.data || error.message);
            throw new common_1.BadRequestException('Falha ao reiniciar instância');
        }
    }
    getMediaEndpoint(mediaType) {
        switch (mediaType) {
            case 'image':
                return 'sendMedia';
            case 'audio':
                return 'sendWhatsAppAudio';
            case 'video':
                return 'sendMedia';
            case 'document':
                return 'sendMedia';
            default:
                return 'sendMedia';
        }
    }
    mapEvolutionStatusToConnectionStatus(evolutionStatus) {
        switch (evolutionStatus?.toLowerCase()) {
            case 'open':
            case 'connected':
                return entities_1.ConnectionStatus.CONNECTED;
            case 'connecting':
            case 'pairing':
                return entities_1.ConnectionStatus.CONNECTING;
            case 'close':
            case 'closed':
                return entities_1.ConnectionStatus.DISCONNECTED;
            default:
                return entities_1.ConnectionStatus.ERROR;
        }
    }
    generateInstanceName(companyId, phoneNumber) {
        const cleanNumber = phoneNumber.replace(/\D/g, '');
        const shortCompanyId = companyId.substring(0, 8);
        return `${shortCompanyId}_${cleanNumber}`;
    }
};
exports.EvolutionApiService = EvolutionApiService;
exports.EvolutionApiService = EvolutionApiService = EvolutionApiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService])
], EvolutionApiService);
//# sourceMappingURL=evolution-api.service.js.map