"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AutomationExecutorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationExecutorService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const automation_schema_1 = require("../../../database/entities/automation.schema");
const automation_execution_schema_1 = require("../../../database/entities/automation-execution.schema");
const tracking_service_1 = require("../../analytics/services/tracking.service");
const whatsapp_service_1 = require("../../whatsapp/whatsapp.service");
const contacts_service_1 = require("../../messages/services/contacts.service");
const messages_service_1 = require("../../messages/services/messages.service");
let AutomationExecutorService = AutomationExecutorService_1 = class AutomationExecutorService {
    automationModel;
    executionModel;
    trackingService;
    whatsappService;
    contactsService;
    messagesService;
    logger = new common_1.Logger(AutomationExecutorService_1.name);
    constructor(automationModel, executionModel, trackingService, whatsappService, contactsService, messagesService) {
        this.automationModel = automationModel;
        this.executionModel = executionModel;
        this.trackingService = trackingService;
        this.whatsappService = whatsappService;
        this.contactsService = contactsService;
        this.messagesService = messagesService;
    }
    async triggerAutomations(context) {
        try {
            const automations = await this.findTriggeredAutomations(context);
            this.logger.log(`Found ${automations.length} automations to trigger for ${context.contactPhone}`);
            for (const automation of automations) {
                try {
                    await this.executeAutomation(automation, context);
                }
                catch (error) {
                    this.logger.error(`Failed to execute automation ${automation._id}: ${error.message}`);
                }
            }
        }
        catch (error) {
            this.logger.error(`Failed to trigger automations: ${error.message}`);
        }
    }
    async executeAutomation(automation, triggerContext) {
        if (automation.status !== automation_schema_1.AutomationStatus.ACTIVE) {
            throw new Error('Automation is not active');
        }
        const contact = await this.contactsService.findByPhone(triggerContext.contactPhone, triggerContext.connectionId, { companyId: triggerContext.companyId });
        if (!contact) {
            throw new Error('Contact not found');
        }
        const executionContext = {
            contact: {
                id: contact._id.toString(),
                phoneNumber: contact.phoneNumber,
                name: contact.name,
                tags: contact.tags?.map(tag => tag.name) || [],
                customFields: contact.customFields || {},
            },
            connection: {
                id: triggerContext.connectionId,
                phoneNumber: '',
                name: '',
            },
            variables: {},
            session: {},
        };
        if (triggerContext.messageId) {
            const message = await this.messagesService.findByMessageId(triggerContext.messageId);
            if (message) {
                executionContext.message = {
                    id: message._id.toString(),
                    content: message.content,
                    type: message.type,
                    timestamp: message.timestamp,
                };
            }
        }
        const execution = new this.executionModel({
            automationId: automation._id,
            automationName: automation.name,
            automationVersion: automation.version,
            companyId: new mongoose_2.Types.ObjectId(triggerContext.companyId),
            status: automation_execution_schema_1.ExecutionStatus.PENDING,
            context: executionContext,
            triggerType: triggerContext.type,
            triggerData: triggerContext.metadata || {},
            executionId: this.generateExecutionId(),
            startedAt: new Date(),
        });
        await execution.save();
        this.logger.log(`Tracking automation triggered: ${automation._id}`);
        try {
            await this.executeSteps(automation, execution);
            execution.status = automation_execution_schema_1.ExecutionStatus.COMPLETED;
            execution.completedAt = new Date();
            execution.duration = execution.completedAt.getTime() - (execution.startedAt?.getTime() || 0);
            automation.executionCount += 1;
            automation.successCount += 1;
            automation.lastExecutedAt = new Date();
            await Promise.all([execution.save(), automation.save()]);
            this.logger.log(`Tracking automation completed: ${automation._id}`);
            this.logger.log(`Automation executed successfully: ${automation._id}`);
        }
        catch (error) {
            execution.status = automation_execution_schema_1.ExecutionStatus.FAILED;
            execution.completedAt = new Date();
            execution.duration = execution.completedAt.getTime() - (execution.startedAt?.getTime() || 0);
            execution.error = {
                code: error.code || 'EXECUTION_FAILED',
                message: error.message || 'Execution failed',
                stack: error.stack,
                stepId: execution.currentStepId,
            };
            automation.executionCount += 1;
            automation.errorCount += 1;
            automation.lastExecutedAt = new Date();
            await Promise.all([execution.save(), automation.save()]);
            this.logger.error(`Automation execution failed: ${automation._id} - ${error.message}`);
            throw error;
        }
        return execution;
    }
    async retryExecution(executionId) {
        const execution = await this.executionModel.findOne({ executionId });
        if (!execution) {
            throw new Error('Execution not found');
        }
        if (execution.status !== automation_execution_schema_1.ExecutionStatus.FAILED ||
            execution.retryCount >= execution.maxRetries ||
            (execution.nextRetryAt && new Date() < execution.nextRetryAt)) {
            throw new Error('Execution cannot be retried');
        }
        const automation = await this.automationModel.findById(execution.automationId);
        if (!automation) {
            throw new Error('Automation not found');
        }
        execution.retryCount += 1;
        execution.nextRetryAt = new Date(Date.now() + 5 * 60 * 1000);
        execution.status = automation_execution_schema_1.ExecutionStatus.PENDING;
        await execution.save();
        try {
            await this.executeSteps(automation, execution);
            execution.status = automation_execution_schema_1.ExecutionStatus.COMPLETED;
            execution.completedAt = new Date();
        }
        catch (error) {
            execution.status = automation_execution_schema_1.ExecutionStatus.FAILED;
            execution.completedAt = new Date();
            execution.error = {
                code: error.code || 'RETRY_FAILED',
                message: error.message,
                stack: error.stack,
            };
        }
        await execution.save();
    }
    async findTriggeredAutomations(context) {
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(context.companyId),
            status: automation_schema_1.AutomationStatus.ACTIVE,
            'trigger.type': context.type,
        };
        switch (context.type) {
            case automation_schema_1.TriggerType.KEYWORD_MATCH:
                if (context.messageContent) {
                    const keywords = await this.extractKeywords(context.messageContent);
                    filter['trigger.keywords'] = { $in: keywords };
                }
                break;
            case automation_schema_1.TriggerType.FIRST_MESSAGE:
                const messageCount = await this.messagesService.findAll({ contactPhone: context.contactPhone, whatsappConnectionId: context.connectionId }, { companyId: context.companyId });
                if (messageCount.total > 1) {
                    return [];
                }
                break;
        }
        const automations = await this.automationModel.find(filter);
        return automations.filter(automation => {
            if (automation.settings?.allowedConnections && automation.settings.allowedConnections.length > 0) {
                return automation.settings.allowedConnections.includes(context.connectionId);
            }
            return true;
        });
    }
    async executeSteps(automation, execution) {
        execution.status = automation_execution_schema_1.ExecutionStatus.RUNNING;
        await execution.save();
        if (automation.actions && automation.actions.length > 0) {
            await this.executeActionSteps(automation.actions, execution);
        }
        else if (automation.flowSteps && automation.flowSteps.length > 0) {
            await this.executeFlowSteps(automation.flowSteps, automation.startStepId, execution);
        }
    }
    async executeActionSteps(actions, execution) {
        for (const action of actions) {
            const step = {
                stepId: action.id,
                stepName: action.name,
                status: automation_execution_schema_1.StepStatus.PENDING,
                startedAt: new Date(),
            };
            execution.steps.push(step);
            execution.totalSteps = execution.steps.length;
            execution.currentStepId = action.id;
            await execution.save();
            try {
                const result = await this.executeAction(action, execution);
                step.status = automation_execution_schema_1.StepStatus.COMPLETED;
                step.completedAt = new Date();
                step.duration = step.completedAt.getTime() - step.startedAt.getTime();
                step.output = result;
                const stepIndex = execution.steps.findIndex(s => s.stepId === action.id);
                if (stepIndex !== -1) {
                    Object.assign(execution.steps[stepIndex], step);
                    execution.completedSteps = execution.steps.filter(s => s.status === automation_execution_schema_1.StepStatus.COMPLETED).length;
                }
                await execution.save();
                if (action.delay && action.delay > 0) {
                    await this.delay(action.delay * 1000);
                }
            }
            catch (error) {
                step.status = automation_execution_schema_1.StepStatus.FAILED;
                step.completedAt = new Date();
                step.error = {
                    code: error.code || 'ACTION_FAILED',
                    message: error.message,
                    stack: error.stack,
                };
                const stepIndex = execution.steps.findIndex(s => s.stepId === action.id);
                if (stepIndex !== -1) {
                    Object.assign(execution.steps[stepIndex], step);
                    execution.failedSteps = execution.steps.filter(s => s.status === automation_execution_schema_1.StepStatus.FAILED).length;
                }
                await execution.save();
                throw error;
            }
        }
    }
    async executeFlowSteps(flowSteps, startStepId, execution) {
        let currentStepId = startStepId;
        const stepMap = new Map(flowSteps.map(step => [step.id, step]));
        while (currentStepId) {
            const flowStep = stepMap.get(currentStepId);
            if (!flowStep)
                break;
            const step = {
                stepId: flowStep.id,
                stepName: flowStep.name,
                status: automation_execution_schema_1.StepStatus.PENDING,
                startedAt: new Date(),
            };
            execution.steps.push(step);
            execution.totalSteps = execution.steps.length;
            execution.currentStepId = flowStep.id;
            await execution.save();
            try {
                const result = await this.executeFlowStep(flowStep, execution);
                step.status = automation_execution_schema_1.StepStatus.COMPLETED;
                step.completedAt = new Date();
                step.duration = step.completedAt.getTime() - step.startedAt.getTime();
                step.output = result;
                step.nextStepId = result.nextStepId;
                const stepIndex = execution.steps.findIndex(s => s.stepId === flowStep.id);
                if (stepIndex !== -1) {
                    Object.assign(execution.steps[stepIndex], step);
                    execution.completedSteps = execution.steps.filter(s => s.status === automation_execution_schema_1.StepStatus.COMPLETED).length;
                }
                await execution.save();
                currentStepId = result.nextStepId;
            }
            catch (error) {
                step.status = automation_execution_schema_1.StepStatus.FAILED;
                step.completedAt = new Date();
                step.error = {
                    code: error.code || 'FLOW_STEP_FAILED',
                    message: error.message,
                    stack: error.stack,
                };
                const stepIndex = execution.steps.findIndex(s => s.stepId === flowStep.id);
                if (stepIndex !== -1) {
                    Object.assign(execution.steps[stepIndex], step);
                    execution.failedSteps = execution.steps.filter(s => s.status === automation_execution_schema_1.StepStatus.FAILED).length;
                }
                await execution.save();
                throw error;
            }
        }
    }
    async executeAction(action, execution) {
        switch (action.type) {
            case automation_schema_1.ActionType.SEND_MESSAGE:
                return this.executeSendMessage(action.config, execution);
            case automation_schema_1.ActionType.ADD_TAG:
                return this.executeAddTag(action.config, execution);
            case automation_schema_1.ActionType.REMOVE_TAG:
                return this.executeRemoveTag(action.config, execution);
            case automation_schema_1.ActionType.UPDATE_CONTACT:
                return this.executeUpdateContact(action.config, execution);
            case automation_schema_1.ActionType.WAIT:
                return this.executeWait(action.config, execution);
            default:
                throw new Error(`Unsupported action type: ${action.type}`);
        }
    }
    async executeFlowStep(flowStep, execution) {
        switch (flowStep.type) {
            case 'message':
                await this.executeSendMessage(flowStep.config.message, execution);
                return { nextStepId: flowStep.nextSteps?.[0] };
            case 'condition':
                const conditionResult = await this.evaluateCondition(flowStep.config.condition, execution);
                return {
                    nextStepId: conditionResult ?
                        flowStep.config.condition.trueStepId :
                        flowStep.config.condition.falseStepId
                };
            case 'action':
                await this.executeAction(flowStep.config.action, execution);
                return { nextStepId: flowStep.nextSteps?.[0] };
            case 'ai':
                const aiResponse = await this.executeAIInteraction(flowStep.config.ai, execution);
                return { nextStepId: flowStep.nextSteps?.[0], aiResponse };
            case 'human_handoff':
                await this.executeHumanHandoff(execution);
                return { nextStepId: null };
            default:
                throw new Error(`Unsupported flow step type: ${flowStep.type}`);
        }
    }
    async executeSendMessage(config, execution) {
        this.logger.log(`Sending message: ${config.content} to ${execution.context.contact.phoneNumber}`);
        await this.delay(1000);
        return { messageId: `msg_${Date.now()}`, sent: true };
    }
    async executeAddTag(config, execution) {
        this.logger.log(`Adding tag: ${config.tagName} to ${execution.context.contact.phoneNumber}`);
        return { tagAdded: config.tagName };
    }
    async executeRemoveTag(config, execution) {
        this.logger.log(`Removing tag: ${config.tagName} from ${execution.context.contact.phoneNumber}`);
        return { tagRemoved: config.tagName };
    }
    async executeUpdateContact(config, execution) {
        this.logger.log(`Updating contact: ${execution.context.contact.phoneNumber}`);
        return { contactUpdated: true, fields: config.fields };
    }
    async executeWait(config, execution) {
        const waitTime = config.seconds * 1000;
        await this.delay(waitTime);
        return { waited: config.seconds };
    }
    async evaluateCondition(condition, execution) {
        const fieldValue = this.getFieldValue(condition.field, execution);
        switch (condition.operator) {
            case 'equals':
                return fieldValue === condition.value;
            case 'contains':
                return String(fieldValue).includes(condition.value);
            case 'greater_than':
                return Number(fieldValue) > Number(condition.value);
            case 'less_than':
                return Number(fieldValue) < Number(condition.value);
            default:
                return false;
        }
    }
    async executeAIInteraction(config, execution) {
        this.logger.log(`AI interaction with ${config.provider} model ${config.model}`);
        await this.delay(2000);
        return `AI response from ${config.provider}: This is a simulated response to the prompt.`;
    }
    async executeHumanHandoff(execution) {
        this.logger.log(`Human handoff for ${execution.context.contact.phoneNumber}`);
        execution.results.humanHandoff = true;
    }
    generateExecutionId() {
        return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async extractKeywords(message) {
        return message.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    }
    getFieldValue(field, execution) {
        const parts = field.split('.');
        let value = execution.context;
        for (const part of parts) {
            value = value?.[part];
        }
        return value;
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
};
exports.AutomationExecutorService = AutomationExecutorService;
exports.AutomationExecutorService = AutomationExecutorService = AutomationExecutorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(automation_schema_1.Automation.name)),
    __param(1, (0, mongoose_1.InjectModel)(automation_execution_schema_1.AutomationExecution.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        tracking_service_1.TrackingService,
        whatsapp_service_1.WhatsAppService,
        contacts_service_1.ContactsService,
        messages_service_1.MessagesService])
], AutomationExecutorService);
//# sourceMappingURL=automation-executor.service.js.map