{"version": 3, "file": "analytics-query.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/analytics/dto/analytics-query.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAUyB;AACzB,yDAAoD;AACpD,6CAAsD;AACtD,8FAA6F;AAE7F,IAAY,SAOX;AAPD,WAAY,SAAS;IACnB,4CAA+B,CAAA;IAC/B,wCAA2B,CAAA;IAC3B,0CAA6B,CAAA;IAC7B,0CAA6B,CAAA;IAC7B,oCAAuB,CAAA;IACvB,8BAAiB,CAAA;AACnB,CAAC,EAPW,SAAS,yBAAT,SAAS,QAOpB;AAED,IAAY,OAMX;AAND,WAAY,OAAO;IACjB,wBAAa,CAAA;IACb,sBAAW,CAAA;IACX,wBAAa,CAAA;IACb,0BAAe,CAAA;IACf,wBAAa,CAAA;AACf,CAAC,EANW,OAAO,uBAAP,OAAO,QAMlB;AAED,IAAY,UAOX;AAPD,WAAY,UAAU;IACpB,6BAAe,CAAA;IACf,yBAAW,CAAA;IACX,iCAAmB,CAAA;IACnB,yBAAW,CAAA;IACX,yBAAW,CAAA;IACX,+BAAiB,CAAA;AACnB,CAAC,EAPW,UAAU,0BAAV,UAAU,QAOrB;AAED,MAAa,iBAAiB;IAQ5B,SAAS,GAAe,SAAS,CAAC,WAAW,CAAC;IAQ9C,SAAS,CAAU;IAQnB,OAAO,CAAU;IAUjB,UAAU,CAAe;IAUzB,UAAU,CAAmB;IAS7B,OAAO,CAAY;IASnB,aAAa,CAAY;IASzB,aAAa,CAAY;IASzB,OAAO,GAAa,OAAO,CAAC,GAAG,CAAC;IAShC,UAAU,GAAgB,UAAU,CAAC,KAAK,CAAC;IAQ3C,WAAW,CAAU;IAcrB,OAAO,CAAuB;IAS9B,mBAAmB,GAAa,KAAK,CAAC;IAatC,KAAK,GAAY,GAAG,CAAC;IAWrB,IAAI,GAAY,CAAC,CAAC;CACnB;AAjJD,8CAiJC;AAzIC;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS,CAAC,WAAW;KAC/B,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,SAAS,CAAC;;oDAC4B;AAQ9C;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,2CAA2C;QACxD,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;oDACI;AAQnB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,wCAAwC;QACrD,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;kDACE;AAUjB;IARC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,kCAAS;QACf,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,wBAAM,EAAC,kCAAS,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qDACT;AAUzB;IARC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,sCAAa;QACnB,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,wBAAM,EAAC,sCAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;qDACT;AAS7B;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,iBAAiB;QAC9B,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;kDACN;AASnB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;wDACA;AASzB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;wDACA;AASzB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,aAAa;QAC1B,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,OAAO,CAAC,GAAG;KACrB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,OAAO,CAAC;;kDACgB;AAShC;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,iBAAiB;QAC9B,IAAI,EAAE,UAAU;QAChB,OAAO,EAAE,UAAU,CAAC,KAAK;KAC1B,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,UAAU,CAAC;;qDACwB;AAQ3C;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,uDAAuD;QACpE,OAAO,EAAE,OAAO;KACjB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACU;AAcrB;IAZC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE,oCAAoC;KAC9C,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;QACvB,IAAI,CAAC;YACH,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/D,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC;;kDAC4B;AAS9B;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,yCAAyC;QACtD,OAAO,EAAE,KAAK;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,CAAC;;8DACvB;AAatC;IAXC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,GAAG;QACZ,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,IAAI,CAAC;;gDACW;AAWrB;IATC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,qBAAG,EAAC,CAAC,CAAC;;+CACW;AAGpB,MAAa,iBAAiB;IAQ5B,SAAS,GAAe,SAAS,CAAC,WAAW,CAAC;IAQ9C,SAAS,CAAU;IAQnB,OAAO,CAAU;IASjB,aAAa,CAAY;IASzB,mBAAmB,GAAa,IAAI,CAAC;CACtC;AA3CD,8CA2CC;AAnCC;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS,CAAC,WAAW;KAC/B,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,SAAS,CAAC;;oDAC4B;AAQ9C;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,2CAA2C;QACxD,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;oDACI;AAQnB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,wCAAwC;QACrD,OAAO,EAAE,sBAAsB;KAChC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;kDACE;AASjB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;wDACA;AASzB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,yCAAyC;QACtD,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,CAAC;;8DACxB;AAGvC,MAAa,cAAe,SAAQ,iBAAiB;IAQnD,MAAM,GAAY,MAAM,CAAC;IASzB,cAAc,GAAa,KAAK,CAAC;IASjC,MAAM,CAAY;CACnB;AA3BD,wCA2BC;AAnBC;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,sBAAsB;QACnC,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC;QAC9B,OAAO,EAAE,MAAM;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,EAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;;8CACR;AASzB;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,KAAK;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;IACX,IAAA,6BAAS,EAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,CAAC;;sDAC5B;AASjC;IAPC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kCAAkC;QAC/C,IAAI,EAAE,CAAC,MAAM,CAAC;KACf,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;8CACP"}