{"version": 3, "file": "StringBuilder.js", "sourceRoot": "", "sources": ["../../src/emitters/StringBuilder.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AA0B3D;;;;;;;;;;GAUG;AACH;IAGE;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAED,0CAA0C;IACnC,8BAAM,GAAb,UAAc,IAAY;QACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED,4CAA4C;IACrC,gCAAQ,GAAf;QACE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,IAAM,MAAM,GAAW,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAC3B,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;IACH,oBAAC;AAAD,CAAC,AA1BD,IA0BC;AA1BY,sCAAa", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\r\n\r\n/**\r\n * An interface for a builder object that allows a large text string to be constructed incrementally by appending\r\n * small chunks.\r\n *\r\n * @remarks\r\n *\r\n * {@link StringBuilder} is the default implementation of this contract.\r\n */\r\nexport interface IStringBuilder {\r\n  /**\r\n   * Append the specified text to the buffer.\r\n   */\r\n  append(text: string): void;\r\n\r\n  /**\r\n   * Returns a single string containing all the text that was appended to the buffer so far.\r\n   *\r\n   * @remarks\r\n   *\r\n   * This is a potentially expensive operation.\r\n   */\r\n  toString(): string;\r\n}\r\n\r\n/**\r\n * This class allows a large text string to be constructed incrementally by appending small chunks.  The final\r\n * string can be obtained by calling StringBuilder.toString().\r\n *\r\n * @remarks\r\n * A naive approach might use the `+=` operator to append strings:  This would have the downside of copying\r\n * the entire string each time a chunk is appended, resulting in `O(n^2)` bytes of memory being allocated\r\n * (and later freed by the garbage  collector), and many of the allocations could be very large objects.\r\n * StringBuilder avoids this overhead by accumulating the chunks in an array, and efficiently joining them\r\n * when `getText()` is finally called.\r\n */\r\nexport class StringBuilder implements IStringBuilder {\r\n  private _chunks: string[];\r\n\r\n  public constructor() {\r\n    this._chunks = [];\r\n  }\r\n\r\n  /** {@inheritdoc IStringBuilder.append} */\r\n  public append(text: string): void {\r\n    this._chunks.push(text);\r\n  }\r\n\r\n  /** {@inheritdoc IStringBuilder.toString} */\r\n  public toString(): string {\r\n    if (this._chunks.length === 0) {\r\n      return '';\r\n    }\r\n\r\n    if (this._chunks.length > 1) {\r\n      const joined: string = this._chunks.join('');\r\n      this._chunks.length = 1;\r\n      this._chunks[0] = joined;\r\n    }\r\n\r\n    return this._chunks[0];\r\n  }\r\n}\r\n"]}