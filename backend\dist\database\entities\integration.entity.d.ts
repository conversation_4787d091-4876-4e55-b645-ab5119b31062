import { Company } from './company.entity';
import { User } from './user.entity';
import { WebhookLog } from './webhook-log.entity';
export declare enum IntegrationType {
    WEBHOOK = "webhook",
    GOOGLE_SHEETS = "google_sheets",
    ZAPIER = "zapier",
    CRM_HUBSPOT = "crm_hubspot",
    CRM_SALESFORCE = "crm_salesforce",
    CRM_PIPEDRIVE = "crm_pipedrive",
    EMAIL_MAILCHIMP = "email_mailchimp",
    EMAIL_SENDGRID = "email_sendgrid",
    SMS_TWILIO = "sms_twilio",
    SLACK = "slack",
    DISCORD = "discord",
    TELEGRAM = "telegram",
    CUSTOM_API = "custom_api"
}
export declare enum IntegrationStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    ERROR = "error",
    PENDING = "pending",
    EXPIRED = "expired"
}
export declare enum TriggerEvent {
    MESSAGE_RECEIVED = "message_received",
    MESSAGE_SENT = "message_sent",
    CONTACT_CREATED = "contact_created",
    CONTACT_UPDATED = "contact_updated",
    LEAD_CONVERTED = "lead_converted",
    AUTOMATION_TRIGGERED = "automation_triggered",
    AUTOMATION_COMPLETED = "automation_completed",
    CONNECTION_STATUS_CHANGED = "connection_status_changed",
    USER_CREATED = "user_created",
    SUBSCRIPTION_CHANGED = "subscription_changed",
    PAYMENT_RECEIVED = "payment_received",
    CUSTOM_EVENT = "custom_event"
}
export interface IntegrationConfig {
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    headers?: Record<string, string>;
    timeout?: number;
    retryAttempts?: number;
    retryDelay?: number;
    authType?: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';
    apiKey?: string;
    bearerToken?: string;
    basicAuth?: {
        username: string;
        password: string;
    };
    oauth2?: {
        clientId: string;
        clientSecret: string;
        accessToken: string;
        refreshToken: string;
        tokenExpiry?: Date;
        scope?: string[];
    };
    googleSheets?: {
        spreadsheetId: string;
        sheetName: string;
        range?: string;
        serviceAccountKey?: string;
    };
    crm?: {
        apiUrl: string;
        objectType: string;
        fieldMapping: Record<string, string>;
        customFields?: Record<string, any>;
    };
    email?: {
        listId?: string;
        templateId?: string;
        fromEmail?: string;
        fromName?: string;
    };
    sms?: {
        fromNumber?: string;
        messagingServiceSid?: string;
    };
    slack?: {
        channelId: string;
        botToken: string;
        messageFormat?: string;
    };
    filters?: {
        contactTags?: string[];
        messageTypes?: string[];
        connectionIds?: string[];
        userIds?: string[];
        customConditions?: Record<string, any>;
    };
    dataMapping?: {
        staticFields?: Record<string, any>;
        dynamicFields?: Record<string, string>;
        transformations?: Array<{
            field: string;
            type: 'uppercase' | 'lowercase' | 'date_format' | 'number_format' | 'custom';
            config?: any;
        }>;
    };
}
export declare class Integration {
    id: string;
    name: string;
    description: string;
    type: IntegrationType;
    status: IntegrationStatus;
    companyId: string;
    company: Company;
    createdBy: string;
    creator: User;
    triggerEvents: TriggerEvent[];
    config: IntegrationConfig;
    active: boolean;
    executionCount: number;
    successCount: number;
    errorCount: number;
    lastExecutedAt: Date;
    lastSuccessAt: Date;
    lastErrorAt: Date;
    lastError: string | null;
    rateLimit: number;
    currentRateCount: number;
    rateLimitResetAt: Date;
    maxRetries: number;
    retryDelay: number;
    timeout: number;
    metadata: Record<string, any>;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
    logs: WebhookLog[];
    isActive(): boolean;
    canExecute(): boolean;
    incrementExecution(success: boolean): void;
    setError(error: string): void;
    getSuccessRate(): number;
    hasEvent(event: TriggerEvent): boolean;
    shouldTrigger(event: TriggerEvent, data: any): boolean;
    transformData(data: any): any;
    private getNestedValue;
    private setNestedValue;
    private applyTransformation;
}
