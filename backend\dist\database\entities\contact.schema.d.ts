import { Document, Types } from 'mongoose';
export type ContactDocument = Contact & Document;
export declare enum ContactStatus {
    ACTIVE = "active",
    BLOCKED = "blocked",
    ARCHIVED = "archived"
}
export interface ContactTag {
    id: string;
    name: string;
    color: string;
    createdAt: Date;
    createdBy: string;
}
export interface ContactSource {
    type: string;
    campaign?: string;
    medium?: string;
    source?: string;
    utm_params?: Record<string, string>;
    referrer?: string;
    capturedAt: Date;
}
export declare class Contact {
    phoneNumber: string;
    name?: string;
    email?: string;
    profilePicture?: string;
    status: ContactStatus;
    tags: ContactTag[];
    source?: ContactSource;
    customFields: Record<string, any>;
    metadata: Record<string, any>;
    lastMessageAt?: Date;
    lastInteractionAt?: Date;
    messageCount: number;
    isLead: boolean;
    leadScore?: number;
    leadStage?: string;
    assignedTo?: string;
    notes?: string;
    companyId: Types.ObjectId;
    whatsappConnectionId: string;
    createdBy?: string;
    updatedBy?: string;
}
export declare const ContactSchema: import("mongoose").Schema<Contact, import("mongoose").Model<Contact, any, any, any, Document<unknown, any, Contact, any> & Contact & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Contact, Document<unknown, {}, import("mongoose").FlatRecord<Contact>, {}> & import("mongoose").FlatRecord<Contact> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
