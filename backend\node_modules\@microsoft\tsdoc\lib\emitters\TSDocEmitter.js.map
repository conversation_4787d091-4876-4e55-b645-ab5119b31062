{"version": 3, "file": "TSDocEmitter.js", "sourceRoot": "", "sources": ["../../src/emitters/TSDocEmitter.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;AA4B3D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAC;AACpE,OAAO,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAC;AAGvD,IAAK,SAIJ;AAJD,WAAK,SAAS;IACZ,6CAAM,CAAA;IACN,uDAAW,CAAA;IACX,yDAAY,CAAA;AACd,CAAC,EAJI,SAAS,KAAT,SAAS,QAIb;AAED;;GAEG;AACH;IAAA;QACkB,QAAG,GAAW,IAAI,CAAC;QAEnC,qCAAqC;QAC7B,wBAAmB,GAAY,IAAI,CAAC;QAI5C,6GAA6G;QACrG,eAAU,GAAc,SAAS,CAAC,MAAM,CAAC;QAEjD,iCAAiC;QACzB,4BAAuB,GAAY,KAAK,CAAC;QAEjD,yFAAyF;QACzF,qGAAqG;QACrG,wEAAwE;QAChE,sBAAiB,GAAY,KAAK,CAAC;IA2Y7C,CAAC;IAzYQ,oCAAa,GAApB,UAAqB,MAAsB,EAAE,UAAsB;QACjE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;IACjD,CAAC;IAEM,oCAAa,GAApB,UAAqB,MAAsB,EAAE,OAAwC;QACnF,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAEM,iDAA0B,GAAjC,UACE,MAAsB,EACtB,oBAA6C;QAE7C,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;IAC3D,CAAC;IAEO,4CAAqB,GAA7B,UAA8B,MAAsB,EAAE,OAAgB;QACpE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;QACnC,IAAI,CAAC,uBAAuB,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAE/B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,kCAAW,GAAnB,UAAoB,OAA4B;QAAhD,iBAkQC;QAjQC,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO;QACT,CAAC;QAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,WAAW,CAAC,KAAK;gBACpB,IAAM,QAAQ,GAAa,OAAmB,CAAC;gBAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,oBAAoB,KAAK,YAAY,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBACzF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAChC,CAAC;gBAED,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACnC,MAAM;YAER,KAAK,WAAW,CAAC,QAAQ;gBACvB,IAAM,WAAW,GAAgB,OAAsB,CAAC;gBACxD,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC;oBAC/C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACxC,MAAM;YAER,KAAK,WAAW,CAAC,QAAQ;gBACvB,IAAM,WAAW,GAAgB,OAAsB,CAAC;gBACxD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,MAAM;YAER,KAAK,WAAW,CAAC,OAAO;gBACtB,IAAM,UAAU,GAAe,OAAqB,CAAC;gBACrD,IAAI,CAAC,YAAY;oBACf,UAAU,CAAC,cAAc;oBACzB,UAAU,CAAC,YAAY;oBACvB,UAAU,CAAC,cAAc;oBACzB,UAAU,CAAC,eAAe;oBAC1B,UAAU,CAAC,MAAM;oBACjB,UAAU,CAAC,UAAU;oBACrB,UAAU,CAAC,YAAY;mBACpB,UAAU,CAAC,YAAY,SACvB,UAAU,CAAC,SAAS;oBACvB,UAAU,CAAC,aAAa;0BACxB,CAAC;gBACH,IAAI,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/C,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC1B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM;YAER,KAAK,WAAW,CAAC,oBAAoB;gBACnC,IAAM,uBAAuB,GAA4B,OAAkC,CAAC;gBAC5F,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;gBACxD,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;gBACvD,IACE,uBAAuB,CAAC,WAAW,KAAK,SAAS;oBACjD,uBAAuB,CAAC,UAAU,KAAK,SAAS,EAChD,CAAC;oBACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC;gBAC5D,MAAM;YAER,KAAK,WAAW,CAAC,SAAS;gBACxB,IAAM,YAAY,GAAiB,OAAuB,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;YAER,KAAK,WAAW,CAAC,WAAW;gBAC1B,IAAM,cAAc,GAAmB,OAAyB,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBAC/C,MAAM;YAER,KAAK,WAAW,CAAC,UAAU;gBACzB,IAAM,aAAa,GAAkB,OAAwB,CAAC;gBAE9D,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAE5B,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBAC3C,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,IAAI,CAAC,aAAa,EAAE,CAAC;gBACrB,MAAM;YAER,KAAK,WAAW,CAAC,aAAa;gBAC5B,IAAM,gBAAgB,GAAqB,OAA2B,CAAC;gBACvE,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;gBACtD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;gBACxD,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;gBACvD,MAAM;YAER,KAAK,WAAW,CAAC,UAAU;gBACzB,IAAM,aAAa,GAAkB,OAAwB,CAAC;gBAC9D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,MAAM;YAER,KAAK,WAAW,CAAC,YAAY;gBAC3B,IAAM,eAAe,GAAoB,OAA0B,CAAC;gBACpE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBAErD,IAAI,UAAU,GACZ,eAAe,CAAC,gBAAgB,KAAK,SAAS,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,CAAC;gBAElG,KAAwB,UAA8B,EAA9B,KAAA,eAAe,CAAC,cAAc,EAA9B,cAA8B,EAA9B,IAA8B,EAAE,CAAC;oBAApD,IAAM,SAAS,SAAA;oBAClB,IAAI,UAAU,EAAE,CAAC;wBACf,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBAC1B,CAAC;oBACD,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;oBAC5B,UAAU,GAAG,SAAS,CAAC,iBAAiB,KAAK,SAAS,IAAI,SAAS,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,CAAC;gBACrG,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAChE,MAAM;YAER,KAAK,WAAW,CAAC,aAAa;gBAC5B,IAAM,kBAAgB,GAAqB,OAA2B,CAAC;gBACvE,IAAI,CAAC,gBAAgB,CAAC,kBAAgB,EAAE;oBACtC,IAAI,kBAAgB,CAAC,oBAAoB,EAAE,CAAC;wBAC1C,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBACxB,KAAI,CAAC,WAAW,CAAC,kBAAgB,CAAC,oBAAoB,CAAC,CAAC;oBAC1D,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,WAAW,CAAC,SAAS;gBACxB,IAAM,cAAY,GAAiB,OAAuB,CAAC;gBAC3D,IAAI,CAAC,gBAAgB,CAAC,cAAY,EAAE;oBAClC,IAAI,cAAY,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACvC,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBACxB,KAAI,CAAC,aAAa,CAAC,cAAY,CAAC,UAAU,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,WAAW,CAAC,OAAO;gBACtB,IAAM,YAAU,GAAe,OAAqB,CAAC;gBACrD,IAAI,CAAC,gBAAgB,CAAC,YAAU,EAAE;oBAChC,IAAI,YAAU,CAAC,cAAc,KAAK,SAAS,IAAI,YAAU,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;wBACxF,IAAI,YAAU,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;4BAC5C,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;4BACxB,KAAI,CAAC,aAAa,CAAC,YAAU,CAAC,cAAc,CAAC,CAAC;wBAChD,CAAC;6BAAM,IAAI,YAAU,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;4BACpD,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;4BACxB,KAAI,CAAC,WAAW,CAAC,YAAU,CAAC,eAAe,CAAC,CAAC;wBAC/C,CAAC;oBACH,CAAC;oBACD,IAAI,YAAU,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;wBACtC,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBACxB,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBACxB,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;wBACxB,KAAI,CAAC,aAAa,CAAC,YAAU,CAAC,QAAQ,CAAC,CAAC;oBAC1C,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,WAAW,CAAC,gBAAgB;gBAC/B,IAAM,mBAAmB,GAAwB,OAA8B,CAAC;gBAChF,IAAI,mBAAmB,CAAC,SAAS,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB;oBACrE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;gBACrD,CAAC;gBACD,MAAM;YAER,KAAK,WAAW,CAAC,eAAe;gBAC9B,IAAM,kBAAkB,GAAuB,OAA6B,CAAC;gBAC7E,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;oBAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBAED,IAAI,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oBAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBAED,IAAI,kBAAkB,CAAC,YAAY,EAAE,CAAC;oBACpC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;gBACxD,CAAC;gBAED,IAAI,kBAAkB,CAAC,QAAQ,EAAE,CAAC;oBAChC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;oBACxB,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;oBAC9C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBACD,MAAM;YAER,KAAK,WAAW,CAAC,cAAc;gBAC7B,IAAM,iBAAiB,GAAsB,OAA4B,CAAC;gBAC1E,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBAC/C,MAAM;YAER,KAAK,WAAW,CAAC,YAAY;gBAC3B,IAAM,eAAe,GAAoB,OAA0B,CAAC;gBACpE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;gBAClD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,MAAM;YAER,KAAK,WAAW,CAAC,OAAO;gBACtB,IAAM,UAAU,GAAe,OAAqB,CAAC;gBACrD,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;gBACpC,MAAM;YAER,KAAK,WAAW,CAAC,SAAS;gBACxB,IAAM,gBAAgB,GAAiB,iBAAiB,CAAC,qBAAqB,CAC5E,OAAuB,CACxB,CAAC;gBACF,IAAI,gBAAgB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAC3B,sDAAsD;wBACtD,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBACjC,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAC5B,CAAC;oBAED,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAC1C,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC;gBACD,MAAM;YAER,KAAK,WAAW,CAAC,UAAU;gBACzB,IAAM,aAAa,GAAkB,OAAwB,CAAC;gBAC9D,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxB,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAChD,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;gBAC9B,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;gBAC/B,MAAM;YAER,KAAK,WAAW,CAAC,eAAe;gBAC9B,IAAM,kBAAkB,GAAuB,OAA6B,CAAC;gBAC7E,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;YAER,KAAK,WAAW,CAAC,SAAS;gBACxB,IAAM,YAAY,GAAiB,OAAuB,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;QACV,CAAC;IACH,CAAC;IAEO,uCAAgB,GAAxB,UAAyB,gBAAkC,EAAE,qBAAiC;QAC5F,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC7C,qBAAqB,EAAE,CAAC;QACxB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAEO,mCAAY,GAApB,UAAqB,QAA4C;QAC/D,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;YAA5B,IAAM,OAAO,iBAAA;YAChB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,6EAA6E;IACrE,2CAAoB,GAA5B;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,mFAAmF;IAC3E,yCAAkB,GAA1B;QACE,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED,iGAAiG;IACjG,kFAAkF;IAC1E,oCAAa,GAArB,UAAsB,OAA2B;QAC/C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClD,OAAO;QACT,CAAC;QAED,IAAM,UAAU,GAAa,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,SAAS,GAAY,IAAI,CAAC;YAC9B,KAAmB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE,CAAC;gBAA3B,IAAM,IAAI,mBAAA;gBACb,IAAI,SAAS,EAAE,CAAC;oBACd,SAAS,GAAG,KAAK,CAAC;gBACpB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC;gBACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;YAChD,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;IACtC,CAAC;IAED,8DAA8D;IACtD,oCAAa,GAArB;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;YAChD,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,CAAC;QAE1E,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACjC,CAAC;IAED,sDAAsD;IAC9C,gCAAS,GAAjB;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC;YAC/C,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;QACrC,CAAC;IACH,CAAC;IACH,mBAAC;AAAD,CAAC,AA5ZD,IA4ZC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport type {\r\n  <PERSON><PERSON><PERSON>,\r\n  DocComment,\r\n  DocPlainText,\r\n  DocSection,\r\n  DocBlock,\r\n  DocParagraph,\r\n  DocBlockTag,\r\n  DocCodeSpan,\r\n  DocFencedCode,\r\n  DocDeclarationReference,\r\n  DocErrorText,\r\n  DocEscapedText,\r\n  DocHtmlEndTag,\r\n  DocHtmlStartTag,\r\n  DocHtmlAttribute,\r\n  DocInheritDocTag,\r\n  DocInlineTagBase,\r\n  DocInlineTag,\r\n  DocLinkTag,\r\n  DocMemberIdentifier,\r\n  DocMemberReference,\r\n  DocMemberSymbol,\r\n  DocMemberSelector,\r\n  DocParamBlock\r\n} from '../nodes';\r\nimport { DocNodeKind } from '../nodes/DocNode';\r\nimport type { IStringBuilder } from './StringBuilder';\r\nimport { DocNodeTransforms } from '../transforms/DocNodeTransforms';\r\nimport { StandardTags } from '../details/StandardTags';\r\nimport type { DocParamCollection } from '../nodes/DocParamCollection';\r\n\r\nenum LineState {\r\n  Closed,\r\n  StartOfLine,\r\n  MiddleOfLine\r\n}\r\n\r\n/**\r\n * Renders a DocNode tree as a code comment.\r\n */\r\nexport class TSDocEmitter {\r\n  public readonly eol: string = '\\n';\r\n\r\n  // Whether to emit the /** */ framing\r\n  private _emitCommentFraming: boolean = true;\r\n\r\n  private _output: IStringBuilder | undefined;\r\n\r\n  // This state machine is used by the writer functions to generate the /** */ framing around the emitted lines\r\n  private _lineState: LineState = LineState.Closed;\r\n\r\n  // State for _ensureLineSkipped()\r\n  private _previousLineHadContent: boolean = false;\r\n\r\n  // Normally a paragraph is precede by a blank line (unless it's the first thing written).\r\n  // But sometimes we want the paragraph to be attached to the previous element, e.g. when it's part of\r\n  // an \"@param\" block.  Setting _hangingParagraph=true accomplishes that.\r\n  private _hangingParagraph: boolean = false;\r\n\r\n  public renderComment(output: IStringBuilder, docComment: DocComment): void {\r\n    this._emitCommentFraming = true;\r\n    this._renderCompleteObject(output, docComment);\r\n  }\r\n\r\n  public renderHtmlTag(output: IStringBuilder, htmlTag: DocHtmlStartTag | DocHtmlEndTag): void {\r\n    this._emitCommentFraming = false;\r\n    this._renderCompleteObject(output, htmlTag);\r\n  }\r\n\r\n  public renderDeclarationReference(\r\n    output: IStringBuilder,\r\n    declarationReference: DocDeclarationReference\r\n  ): void {\r\n    this._emitCommentFraming = false;\r\n    this._renderCompleteObject(output, declarationReference);\r\n  }\r\n\r\n  private _renderCompleteObject(output: IStringBuilder, docNode: DocNode): void {\r\n    this._output = output;\r\n\r\n    this._lineState = LineState.Closed;\r\n    this._previousLineHadContent = false;\r\n    this._hangingParagraph = false;\r\n\r\n    this._renderNode(docNode);\r\n\r\n    this._writeEnd();\r\n  }\r\n\r\n  private _renderNode(docNode: DocNode | undefined): void {\r\n    if (docNode === undefined) {\r\n      return;\r\n    }\r\n\r\n    switch (docNode.kind) {\r\n      case DocNodeKind.Block:\r\n        const docBlock: DocBlock = docNode as DocBlock;\r\n        this._ensureLineSkipped();\r\n        this._renderNode(docBlock.blockTag);\r\n\r\n        if (docBlock.blockTag.tagNameWithUpperCase === StandardTags.returns.tagNameWithUpperCase) {\r\n          this._writeContent(' ');\r\n          this._hangingParagraph = true;\r\n        }\r\n\r\n        this._renderNode(docBlock.content);\r\n        break;\r\n\r\n      case DocNodeKind.BlockTag:\r\n        const docBlockTag: DocBlockTag = docNode as DocBlockTag;\r\n        if (this._lineState === LineState.MiddleOfLine) {\r\n          this._writeContent(' ');\r\n        }\r\n        this._writeContent(docBlockTag.tagName);\r\n        break;\r\n\r\n      case DocNodeKind.CodeSpan:\r\n        const docCodeSpan: DocCodeSpan = docNode as DocCodeSpan;\r\n        this._writeContent('`');\r\n        this._writeContent(docCodeSpan.code);\r\n        this._writeContent('`');\r\n        break;\r\n\r\n      case DocNodeKind.Comment:\r\n        const docComment: DocComment = docNode as DocComment;\r\n        this._renderNodes([\r\n          docComment.summarySection,\r\n          docComment.remarksBlock,\r\n          docComment.privateRemarks,\r\n          docComment.deprecatedBlock,\r\n          docComment.params,\r\n          docComment.typeParams,\r\n          docComment.returnsBlock,\r\n          ...docComment.customBlocks,\r\n          ...docComment.seeBlocks,\r\n          docComment.inheritDocTag\r\n        ]);\r\n        if (docComment.modifierTagSet.nodes.length > 0) {\r\n          this._ensureLineSkipped();\r\n          this._renderNodes(docComment.modifierTagSet.nodes);\r\n        }\r\n        break;\r\n\r\n      case DocNodeKind.DeclarationReference:\r\n        const docDeclarationReference: DocDeclarationReference = docNode as DocDeclarationReference;\r\n        this._writeContent(docDeclarationReference.packageName);\r\n        this._writeContent(docDeclarationReference.importPath);\r\n        if (\r\n          docDeclarationReference.packageName !== undefined ||\r\n          docDeclarationReference.importPath !== undefined\r\n        ) {\r\n          this._writeContent('#');\r\n        }\r\n        this._renderNodes(docDeclarationReference.memberReferences);\r\n        break;\r\n\r\n      case DocNodeKind.ErrorText:\r\n        const docErrorText: DocErrorText = docNode as DocErrorText;\r\n        this._writeContent(docErrorText.text);\r\n        break;\r\n\r\n      case DocNodeKind.EscapedText:\r\n        const docEscapedText: DocEscapedText = docNode as DocEscapedText;\r\n        this._writeContent(docEscapedText.encodedText);\r\n        break;\r\n\r\n      case DocNodeKind.FencedCode:\r\n        const docFencedCode: DocFencedCode = docNode as DocFencedCode;\r\n\r\n        this._ensureAtStartOfLine();\r\n\r\n        this._writeContent('```');\r\n        this._writeContent(docFencedCode.language);\r\n        this._writeNewline();\r\n        this._writeContent(docFencedCode.code);\r\n        this._writeContent('```');\r\n        this._writeNewline();\r\n        this._writeNewline();\r\n        break;\r\n\r\n      case DocNodeKind.HtmlAttribute:\r\n        const docHtmlAttribute: DocHtmlAttribute = docNode as DocHtmlAttribute;\r\n        this._writeContent(docHtmlAttribute.name);\r\n        this._writeContent(docHtmlAttribute.spacingAfterName);\r\n        this._writeContent('=');\r\n        this._writeContent(docHtmlAttribute.spacingAfterEquals);\r\n        this._writeContent(docHtmlAttribute.value);\r\n        this._writeContent(docHtmlAttribute.spacingAfterValue);\r\n        break;\r\n\r\n      case DocNodeKind.HtmlEndTag:\r\n        const docHtmlEndTag: DocHtmlEndTag = docNode as DocHtmlEndTag;\r\n        this._writeContent('</');\r\n        this._writeContent(docHtmlEndTag.name);\r\n        this._writeContent('>');\r\n        break;\r\n\r\n      case DocNodeKind.HtmlStartTag:\r\n        const docHtmlStartTag: DocHtmlStartTag = docNode as DocHtmlStartTag;\r\n        this._writeContent('<');\r\n        this._writeContent(docHtmlStartTag.name);\r\n        this._writeContent(docHtmlStartTag.spacingAfterName);\r\n\r\n        let needsSpace: boolean =\r\n          docHtmlStartTag.spacingAfterName === undefined || docHtmlStartTag.spacingAfterName.length === 0;\r\n\r\n        for (const attribute of docHtmlStartTag.htmlAttributes) {\r\n          if (needsSpace) {\r\n            this._writeContent(' ');\r\n          }\r\n          this._renderNode(attribute);\r\n          needsSpace = attribute.spacingAfterValue === undefined || attribute.spacingAfterValue.length === 0;\r\n        }\r\n        this._writeContent(docHtmlStartTag.selfClosingTag ? '/>' : '>');\r\n        break;\r\n\r\n      case DocNodeKind.InheritDocTag:\r\n        const docInheritDocTag: DocInheritDocTag = docNode as DocInheritDocTag;\r\n        this._renderInlineTag(docInheritDocTag, () => {\r\n          if (docInheritDocTag.declarationReference) {\r\n            this._writeContent(' ');\r\n            this._renderNode(docInheritDocTag.declarationReference);\r\n          }\r\n        });\r\n        break;\r\n\r\n      case DocNodeKind.InlineTag:\r\n        const docInlineTag: DocInlineTag = docNode as DocInlineTag;\r\n        this._renderInlineTag(docInlineTag, () => {\r\n          if (docInlineTag.tagContent.length > 0) {\r\n            this._writeContent(' ');\r\n            this._writeContent(docInlineTag.tagContent);\r\n          }\r\n        });\r\n        break;\r\n\r\n      case DocNodeKind.LinkTag:\r\n        const docLinkTag: DocLinkTag = docNode as DocLinkTag;\r\n        this._renderInlineTag(docLinkTag, () => {\r\n          if (docLinkTag.urlDestination !== undefined || docLinkTag.codeDestination !== undefined) {\r\n            if (docLinkTag.urlDestination !== undefined) {\r\n              this._writeContent(' ');\r\n              this._writeContent(docLinkTag.urlDestination);\r\n            } else if (docLinkTag.codeDestination !== undefined) {\r\n              this._writeContent(' ');\r\n              this._renderNode(docLinkTag.codeDestination);\r\n            }\r\n          }\r\n          if (docLinkTag.linkText !== undefined) {\r\n            this._writeContent(' ');\r\n            this._writeContent('|');\r\n            this._writeContent(' ');\r\n            this._writeContent(docLinkTag.linkText);\r\n          }\r\n        });\r\n        break;\r\n\r\n      case DocNodeKind.MemberIdentifier:\r\n        const docMemberIdentifier: DocMemberIdentifier = docNode as DocMemberIdentifier;\r\n        if (docMemberIdentifier.hasQuotes) {\r\n          this._writeContent('\"');\r\n          this._writeContent(docMemberIdentifier.identifier); // todo: encoding\r\n          this._writeContent('\"');\r\n        } else {\r\n          this._writeContent(docMemberIdentifier.identifier);\r\n        }\r\n        break;\r\n\r\n      case DocNodeKind.MemberReference:\r\n        const docMemberReference: DocMemberReference = docNode as DocMemberReference;\r\n        if (docMemberReference.hasDot) {\r\n          this._writeContent('.');\r\n        }\r\n\r\n        if (docMemberReference.selector) {\r\n          this._writeContent('(');\r\n        }\r\n\r\n        if (docMemberReference.memberSymbol) {\r\n          this._renderNode(docMemberReference.memberSymbol);\r\n        } else {\r\n          this._renderNode(docMemberReference.memberIdentifier);\r\n        }\r\n\r\n        if (docMemberReference.selector) {\r\n          this._writeContent(':');\r\n          this._renderNode(docMemberReference.selector);\r\n          this._writeContent(')');\r\n        }\r\n        break;\r\n\r\n      case DocNodeKind.MemberSelector:\r\n        const docMemberSelector: DocMemberSelector = docNode as DocMemberSelector;\r\n        this._writeContent(docMemberSelector.selector);\r\n        break;\r\n\r\n      case DocNodeKind.MemberSymbol:\r\n        const docMemberSymbol: DocMemberSymbol = docNode as DocMemberSymbol;\r\n        this._writeContent('[');\r\n        this._renderNode(docMemberSymbol.symbolReference);\r\n        this._writeContent(']');\r\n        break;\r\n\r\n      case DocNodeKind.Section:\r\n        const docSection: DocSection = docNode as DocSection;\r\n        this._renderNodes(docSection.nodes);\r\n        break;\r\n\r\n      case DocNodeKind.Paragraph:\r\n        const trimmedParagraph: DocParagraph = DocNodeTransforms.trimSpacesInParagraph(\r\n          docNode as DocParagraph\r\n        );\r\n        if (trimmedParagraph.nodes.length > 0) {\r\n          if (this._hangingParagraph) {\r\n            // If it's a hanging paragraph, then don't skip a line\r\n            this._hangingParagraph = false;\r\n          } else {\r\n            this._ensureLineSkipped();\r\n          }\r\n\r\n          this._renderNodes(trimmedParagraph.nodes);\r\n          this._writeNewline();\r\n        }\r\n        break;\r\n\r\n      case DocNodeKind.ParamBlock:\r\n        const docParamBlock: DocParamBlock = docNode as DocParamBlock;\r\n        this._ensureLineSkipped();\r\n        this._renderNode(docParamBlock.blockTag);\r\n        this._writeContent(' ');\r\n        this._writeContent(docParamBlock.parameterName);\r\n        this._writeContent(' - ');\r\n        this._hangingParagraph = true;\r\n        this._renderNode(docParamBlock.content);\r\n        this._hangingParagraph = false;\r\n        break;\r\n\r\n      case DocNodeKind.ParamCollection:\r\n        const docParamCollection: DocParamCollection = docNode as DocParamCollection;\r\n        this._renderNodes(docParamCollection.blocks);\r\n        break;\r\n\r\n      case DocNodeKind.PlainText:\r\n        const docPlainText: DocPlainText = docNode as DocPlainText;\r\n        this._writeContent(docPlainText.text);\r\n        break;\r\n    }\r\n  }\r\n\r\n  private _renderInlineTag(docInlineTagBase: DocInlineTagBase, writeInlineTagContent: () => void): void {\r\n    this._writeContent('{');\r\n    this._writeContent(docInlineTagBase.tagName);\r\n    writeInlineTagContent();\r\n    this._writeContent('}');\r\n  }\r\n\r\n  private _renderNodes(docNodes: ReadonlyArray<DocNode | undefined>): void {\r\n    for (const docNode of docNodes) {\r\n      this._renderNode(docNode);\r\n    }\r\n  }\r\n\r\n  // Calls _writeNewline() only if we're not already at the start of a new line\r\n  private _ensureAtStartOfLine(): void {\r\n    if (this._lineState === LineState.MiddleOfLine) {\r\n      this._writeNewline();\r\n    }\r\n  }\r\n\r\n  // Calls _writeNewline() if needed to ensure that we have skipped at least one line\r\n  private _ensureLineSkipped(): void {\r\n    this._ensureAtStartOfLine();\r\n    if (this._previousLineHadContent) {\r\n      this._writeNewline();\r\n    }\r\n  }\r\n\r\n  // Writes literal text content.  If it contains newlines, they will automatically be converted to\r\n  // _writeNewline() calls, to ensure that \"*\" is written at the start of each line.\r\n  private _writeContent(content: string | undefined): void {\r\n    if (content === undefined || content.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const splitLines: string[] = content.split(/\\r?\\n/g);\r\n    if (splitLines.length > 1) {\r\n      let firstLine: boolean = true;\r\n      for (const line of splitLines) {\r\n        if (firstLine) {\r\n          firstLine = false;\r\n        } else {\r\n          this._writeNewline();\r\n        }\r\n        this._writeContent(line);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (this._lineState === LineState.Closed) {\r\n      if (this._emitCommentFraming) {\r\n        this._output!.append('/**' + this.eol + ' *');\r\n      }\r\n      this._lineState = LineState.StartOfLine;\r\n    }\r\n\r\n    if (this._lineState === LineState.StartOfLine) {\r\n      if (this._emitCommentFraming) {\r\n        this._output!.append(' ');\r\n      }\r\n    }\r\n\r\n    this._output!.append(content);\r\n    this._lineState = LineState.MiddleOfLine;\r\n    this._previousLineHadContent = true;\r\n  }\r\n\r\n  // Starts a new line, and inserts \"/**\" or \"*\" as appropriate.\r\n  private _writeNewline(): void {\r\n    if (this._lineState === LineState.Closed) {\r\n      if (this._emitCommentFraming) {\r\n        this._output!.append('/**' + this.eol + ' *');\r\n      }\r\n      this._lineState = LineState.StartOfLine;\r\n    }\r\n\r\n    this._previousLineHadContent = this._lineState === LineState.MiddleOfLine;\r\n\r\n    if (this._emitCommentFraming) {\r\n      this._output!.append(this.eol + ' *');\r\n    } else {\r\n      this._output!.append(this.eol);\r\n    }\r\n\r\n    this._lineState = LineState.StartOfLine;\r\n    this._hangingParagraph = false;\r\n  }\r\n\r\n  // Closes the comment, adding the final \"*/\" delimiter\r\n  private _writeEnd(): void {\r\n    if (this._lineState === LineState.MiddleOfLine) {\r\n      if (this._emitCommentFraming) {\r\n        this._writeNewline();\r\n      }\r\n    }\r\n\r\n    if (this._lineState !== LineState.Closed) {\r\n      if (this._emitCommentFraming) {\r\n        this._output!.append('/' + this.eol);\r\n      }\r\n      this._lineState = LineState.Closed;\r\n    }\r\n  }\r\n}\r\n"]}