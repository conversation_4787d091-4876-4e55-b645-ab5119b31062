{"version": 3, "file": "NodeParserBasics.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserBasics.test.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAE3D,6CAA4C;AAE5C,IAAI,CAAC,0BAA0B,EAAE;IAC/B,yBAAW,CAAC,+BAA+B,CACzC;QACE,KAAK;QACL,YAAY,EAAE,6BAA6B;QAC3C,WAAW;QACX,KAAK;KACN,CAAC,IAAI,CAAC,IAAI,CAAC,CACb,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,+BAA+B,EAAE;IACpC,yBAAW,CAAC,+BAA+B,CAAC,OAAO,CAAC,CAAC;IAErD,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE7E,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yCAAyC,EAAE;IAC9C,yBAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3F,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,yCAAyC,EAAE;IAC9C,yBAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,2CAA2C,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACvE,CAAC;AACJ,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Tokenizer simple case', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    [\r\n      '/**',\r\n      ' * line 1 ', // extra space at end of line\r\n      ' * line 2',\r\n      ' */'\r\n    ].join('\\n')\r\n  );\r\n});\r\n\r\ntest('01 Tokenizer degenerate cases', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot('/***/');\r\n\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' *', ' */'].join('\\n'));\r\n\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' ', ' ', ' */'].join('\\n'));\r\n});\r\n\r\ntest('02 Backslash escapes: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * \\\\$\\\\@param', ' */'].join('\\n'));\r\n});\r\n\r\ntest('03 Backslash escapes: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * letter: \\\\A space: \\\\  end of line: \\\\', ' */'].join('\\n')\r\n  );\r\n});\r\n"]}