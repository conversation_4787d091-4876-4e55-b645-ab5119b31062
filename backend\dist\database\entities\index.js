"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeOrmEntities = exports.WebhookStatus = exports.WebhookLog = exports.TriggerEvent = exports.IntegrationStatus = exports.IntegrationType = exports.Integration = exports.PaymentGateway = exports.PaymentMethod = exports.PaymentStatus = exports.Payment = exports.InvoiceType = exports.InvoiceStatus = exports.Invoice = exports.BillingCycle = exports.PlanStatus = exports.PlanType = exports.Plan = exports.SubscriptionType = exports.SubscriptionStatus = exports.Subscription = exports.ConnectionStatus = exports.ConnectionType = exports.WhatsAppConnection = exports.UserStatus = exports.UserRole = exports.User = exports.CompanyStatus = exports.Company = exports.BaseEntity = void 0;
var base_entity_1 = require("./base.entity");
Object.defineProperty(exports, "BaseEntity", { enumerable: true, get: function () { return base_entity_1.BaseEntity; } });
var company_entity_1 = require("./company.entity");
Object.defineProperty(exports, "Company", { enumerable: true, get: function () { return company_entity_1.Company; } });
Object.defineProperty(exports, "CompanyStatus", { enumerable: true, get: function () { return company_entity_1.CompanyStatus; } });
var user_entity_1 = require("./user.entity");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return user_entity_1.User; } });
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return user_entity_1.UserRole; } });
Object.defineProperty(exports, "UserStatus", { enumerable: true, get: function () { return user_entity_1.UserStatus; } });
var whatsapp_connection_entity_1 = require("./whatsapp-connection.entity");
Object.defineProperty(exports, "WhatsAppConnection", { enumerable: true, get: function () { return whatsapp_connection_entity_1.WhatsAppConnection; } });
Object.defineProperty(exports, "ConnectionType", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionType; } });
Object.defineProperty(exports, "ConnectionStatus", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionStatus; } });
var subscription_entity_1 = require("./subscription.entity");
Object.defineProperty(exports, "Subscription", { enumerable: true, get: function () { return subscription_entity_1.Subscription; } });
Object.defineProperty(exports, "SubscriptionStatus", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionStatus; } });
Object.defineProperty(exports, "SubscriptionType", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionType; } });
var plan_entity_1 = require("./plan.entity");
Object.defineProperty(exports, "Plan", { enumerable: true, get: function () { return plan_entity_1.Plan; } });
Object.defineProperty(exports, "PlanType", { enumerable: true, get: function () { return plan_entity_1.PlanType; } });
Object.defineProperty(exports, "PlanStatus", { enumerable: true, get: function () { return plan_entity_1.PlanStatus; } });
Object.defineProperty(exports, "BillingCycle", { enumerable: true, get: function () { return plan_entity_1.BillingCycle; } });
var invoice_entity_1 = require("./invoice.entity");
Object.defineProperty(exports, "Invoice", { enumerable: true, get: function () { return invoice_entity_1.Invoice; } });
Object.defineProperty(exports, "InvoiceStatus", { enumerable: true, get: function () { return invoice_entity_1.InvoiceStatus; } });
Object.defineProperty(exports, "InvoiceType", { enumerable: true, get: function () { return invoice_entity_1.InvoiceType; } });
var payment_entity_1 = require("./payment.entity");
Object.defineProperty(exports, "Payment", { enumerable: true, get: function () { return payment_entity_1.Payment; } });
Object.defineProperty(exports, "PaymentStatus", { enumerable: true, get: function () { return payment_entity_1.PaymentStatus; } });
Object.defineProperty(exports, "PaymentMethod", { enumerable: true, get: function () { return payment_entity_1.PaymentMethod; } });
Object.defineProperty(exports, "PaymentGateway", { enumerable: true, get: function () { return payment_entity_1.PaymentGateway; } });
var integration_entity_1 = require("./integration.entity");
Object.defineProperty(exports, "Integration", { enumerable: true, get: function () { return integration_entity_1.Integration; } });
Object.defineProperty(exports, "IntegrationType", { enumerable: true, get: function () { return integration_entity_1.IntegrationType; } });
Object.defineProperty(exports, "IntegrationStatus", { enumerable: true, get: function () { return integration_entity_1.IntegrationStatus; } });
Object.defineProperty(exports, "TriggerEvent", { enumerable: true, get: function () { return integration_entity_1.TriggerEvent; } });
var webhook_log_entity_1 = require("./webhook-log.entity");
Object.defineProperty(exports, "WebhookLog", { enumerable: true, get: function () { return webhook_log_entity_1.WebhookLog; } });
Object.defineProperty(exports, "WebhookStatus", { enumerable: true, get: function () { return webhook_log_entity_1.WebhookStatus; } });
const company_entity_2 = require("./company.entity");
const user_entity_2 = require("./user.entity");
const whatsapp_connection_entity_2 = require("./whatsapp-connection.entity");
const subscription_entity_2 = require("./subscription.entity");
const plan_entity_2 = require("./plan.entity");
const invoice_entity_2 = require("./invoice.entity");
const payment_entity_2 = require("./payment.entity");
const integration_entity_2 = require("./integration.entity");
const webhook_log_entity_2 = require("./webhook-log.entity");
exports.TypeOrmEntities = [
    company_entity_2.Company,
    user_entity_2.User,
    whatsapp_connection_entity_2.WhatsAppConnection,
    subscription_entity_2.Subscription,
    plan_entity_2.Plan,
    invoice_entity_2.Invoice,
    payment_entity_2.Payment,
    integration_entity_2.Integration,
    webhook_log_entity_2.WebhookLog,
];
//# sourceMappingURL=index.js.map