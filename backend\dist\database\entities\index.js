"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationExecutionSchema = exports.AutomationSchema = exports.AnalyticsEventSchema = exports.MessageSchema = exports.ContactSchema = exports.MongooseSchemas = exports.TypeOrmEntities = exports.StepStatus = exports.ExecutionStatus = exports.AutomationExecution = exports.ActionType = exports.TriggerType = exports.AutomationStatus = exports.AutomationType = exports.Automation = exports.EventCategory = exports.EventType = exports.AnalyticsEvent = exports.MessageStatus = exports.MessageDirection = exports.MessageType = exports.Message = exports.ContactStatus = exports.Contact = exports.PaymentGateway = exports.PaymentMethod = exports.PaymentStatus = exports.Payment = exports.InvoiceType = exports.InvoiceStatus = exports.Invoice = exports.BillingCycle = exports.PlanStatus = exports.PlanType = exports.Plan = exports.SubscriptionType = exports.SubscriptionStatus = exports.Subscription = exports.ConnectionStatus = exports.ConnectionType = exports.WhatsAppConnection = exports.UserStatus = exports.UserRole = exports.User = exports.CompanyStatus = exports.Company = exports.BaseEntity = void 0;
var base_entity_1 = require("./base.entity");
Object.defineProperty(exports, "BaseEntity", { enumerable: true, get: function () { return base_entity_1.BaseEntity; } });
var company_entity_1 = require("./company.entity");
Object.defineProperty(exports, "Company", { enumerable: true, get: function () { return company_entity_1.Company; } });
Object.defineProperty(exports, "CompanyStatus", { enumerable: true, get: function () { return company_entity_1.CompanyStatus; } });
var user_entity_1 = require("./user.entity");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return user_entity_1.User; } });
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return user_entity_1.UserRole; } });
Object.defineProperty(exports, "UserStatus", { enumerable: true, get: function () { return user_entity_1.UserStatus; } });
var whatsapp_connection_entity_1 = require("./whatsapp-connection.entity");
Object.defineProperty(exports, "WhatsAppConnection", { enumerable: true, get: function () { return whatsapp_connection_entity_1.WhatsAppConnection; } });
Object.defineProperty(exports, "ConnectionType", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionType; } });
Object.defineProperty(exports, "ConnectionStatus", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionStatus; } });
var subscription_entity_1 = require("./subscription.entity");
Object.defineProperty(exports, "Subscription", { enumerable: true, get: function () { return subscription_entity_1.Subscription; } });
Object.defineProperty(exports, "SubscriptionStatus", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionStatus; } });
Object.defineProperty(exports, "SubscriptionType", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionType; } });
var plan_entity_1 = require("./plan.entity");
Object.defineProperty(exports, "Plan", { enumerable: true, get: function () { return plan_entity_1.Plan; } });
Object.defineProperty(exports, "PlanType", { enumerable: true, get: function () { return plan_entity_1.PlanType; } });
Object.defineProperty(exports, "PlanStatus", { enumerable: true, get: function () { return plan_entity_1.PlanStatus; } });
Object.defineProperty(exports, "BillingCycle", { enumerable: true, get: function () { return plan_entity_1.BillingCycle; } });
var invoice_entity_1 = require("./invoice.entity");
Object.defineProperty(exports, "Invoice", { enumerable: true, get: function () { return invoice_entity_1.Invoice; } });
Object.defineProperty(exports, "InvoiceStatus", { enumerable: true, get: function () { return invoice_entity_1.InvoiceStatus; } });
Object.defineProperty(exports, "InvoiceType", { enumerable: true, get: function () { return invoice_entity_1.InvoiceType; } });
var payment_entity_1 = require("./payment.entity");
Object.defineProperty(exports, "Payment", { enumerable: true, get: function () { return payment_entity_1.Payment; } });
Object.defineProperty(exports, "PaymentStatus", { enumerable: true, get: function () { return payment_entity_1.PaymentStatus; } });
Object.defineProperty(exports, "PaymentMethod", { enumerable: true, get: function () { return payment_entity_1.PaymentMethod; } });
Object.defineProperty(exports, "PaymentGateway", { enumerable: true, get: function () { return payment_entity_1.PaymentGateway; } });
var contact_schema_1 = require("./contact.schema");
Object.defineProperty(exports, "Contact", { enumerable: true, get: function () { return contact_schema_1.Contact; } });
Object.defineProperty(exports, "ContactStatus", { enumerable: true, get: function () { return contact_schema_1.ContactStatus; } });
var message_schema_1 = require("./message.schema");
Object.defineProperty(exports, "Message", { enumerable: true, get: function () { return message_schema_1.Message; } });
Object.defineProperty(exports, "MessageType", { enumerable: true, get: function () { return message_schema_1.MessageType; } });
Object.defineProperty(exports, "MessageDirection", { enumerable: true, get: function () { return message_schema_1.MessageDirection; } });
Object.defineProperty(exports, "MessageStatus", { enumerable: true, get: function () { return message_schema_1.MessageStatus; } });
var analytics_event_schema_1 = require("./analytics-event.schema");
Object.defineProperty(exports, "AnalyticsEvent", { enumerable: true, get: function () { return analytics_event_schema_1.AnalyticsEvent; } });
Object.defineProperty(exports, "EventType", { enumerable: true, get: function () { return analytics_event_schema_1.EventType; } });
Object.defineProperty(exports, "EventCategory", { enumerable: true, get: function () { return analytics_event_schema_1.EventCategory; } });
var automation_schema_1 = require("./automation.schema");
Object.defineProperty(exports, "Automation", { enumerable: true, get: function () { return automation_schema_1.Automation; } });
Object.defineProperty(exports, "AutomationType", { enumerable: true, get: function () { return automation_schema_1.AutomationType; } });
Object.defineProperty(exports, "AutomationStatus", { enumerable: true, get: function () { return automation_schema_1.AutomationStatus; } });
Object.defineProperty(exports, "TriggerType", { enumerable: true, get: function () { return automation_schema_1.TriggerType; } });
Object.defineProperty(exports, "ActionType", { enumerable: true, get: function () { return automation_schema_1.ActionType; } });
var automation_execution_schema_1 = require("./automation-execution.schema");
Object.defineProperty(exports, "AutomationExecution", { enumerable: true, get: function () { return automation_execution_schema_1.AutomationExecution; } });
Object.defineProperty(exports, "ExecutionStatus", { enumerable: true, get: function () { return automation_execution_schema_1.ExecutionStatus; } });
Object.defineProperty(exports, "StepStatus", { enumerable: true, get: function () { return automation_execution_schema_1.StepStatus; } });
const contact_schema_2 = require("./contact.schema");
const message_schema_2 = require("./message.schema");
const analytics_event_schema_2 = require("./analytics-event.schema");
const automation_schema_2 = require("./automation.schema");
const automation_execution_schema_2 = require("./automation-execution.schema");
const company_entity_2 = require("./company.entity");
const user_entity_2 = require("./user.entity");
const whatsapp_connection_entity_2 = require("./whatsapp-connection.entity");
const subscription_entity_2 = require("./subscription.entity");
const plan_entity_2 = require("./plan.entity");
const invoice_entity_2 = require("./invoice.entity");
const payment_entity_2 = require("./payment.entity");
const contact_schema_3 = require("./contact.schema");
const message_schema_3 = require("./message.schema");
const analytics_event_schema_3 = require("./analytics-event.schema");
const automation_schema_3 = require("./automation.schema");
const automation_execution_schema_3 = require("./automation-execution.schema");
exports.TypeOrmEntities = [
    company_entity_2.Company,
    user_entity_2.User,
    whatsapp_connection_entity_2.WhatsAppConnection,
    subscription_entity_2.Subscription,
    plan_entity_2.Plan,
    invoice_entity_2.Invoice,
    payment_entity_2.Payment,
];
exports.MongooseSchemas = [
    { name: contact_schema_3.Contact.name, schema: contact_schema_2.ContactSchema },
    { name: message_schema_3.Message.name, schema: message_schema_2.MessageSchema },
    { name: analytics_event_schema_3.AnalyticsEvent.name, schema: analytics_event_schema_2.AnalyticsEventSchema },
    { name: automation_schema_3.Automation.name, schema: automation_schema_2.AutomationSchema },
    { name: automation_execution_schema_3.AutomationExecution.name, schema: automation_execution_schema_2.AutomationExecutionSchema },
];
var contact_schema_4 = require("./contact.schema");
Object.defineProperty(exports, "ContactSchema", { enumerable: true, get: function () { return contact_schema_4.ContactSchema; } });
var message_schema_4 = require("./message.schema");
Object.defineProperty(exports, "MessageSchema", { enumerable: true, get: function () { return message_schema_4.MessageSchema; } });
var analytics_event_schema_4 = require("./analytics-event.schema");
Object.defineProperty(exports, "AnalyticsEventSchema", { enumerable: true, get: function () { return analytics_event_schema_4.AnalyticsEventSchema; } });
var automation_schema_4 = require("./automation.schema");
Object.defineProperty(exports, "AutomationSchema", { enumerable: true, get: function () { return automation_schema_4.AutomationSchema; } });
var automation_execution_schema_4 = require("./automation-execution.schema");
Object.defineProperty(exports, "AutomationExecutionSchema", { enumerable: true, get: function () { return automation_execution_schema_4.AutomationExecutionSchema; } });
//# sourceMappingURL=index.js.map