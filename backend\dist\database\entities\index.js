"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageSchema = exports.ContactSchema = exports.MongooseSchemas = exports.TypeOrmEntities = exports.MessageStatus = exports.MessageDirection = exports.MessageType = exports.Message = exports.ContactStatus = exports.Contact = exports.SubscriptionPlan = exports.SubscriptionStatus = exports.Subscription = exports.ConnectionStatus = exports.ConnectionType = exports.WhatsAppConnection = exports.UserStatus = exports.UserRole = exports.User = exports.CompanyStatus = exports.Company = exports.BaseEntity = void 0;
var base_entity_1 = require("./base.entity");
Object.defineProperty(exports, "BaseEntity", { enumerable: true, get: function () { return base_entity_1.BaseEntity; } });
var company_entity_1 = require("./company.entity");
Object.defineProperty(exports, "Company", { enumerable: true, get: function () { return company_entity_1.Company; } });
Object.defineProperty(exports, "CompanyStatus", { enumerable: true, get: function () { return company_entity_1.CompanyStatus; } });
var user_entity_1 = require("./user.entity");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return user_entity_1.User; } });
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return user_entity_1.UserRole; } });
Object.defineProperty(exports, "UserStatus", { enumerable: true, get: function () { return user_entity_1.UserStatus; } });
var whatsapp_connection_entity_1 = require("./whatsapp-connection.entity");
Object.defineProperty(exports, "WhatsAppConnection", { enumerable: true, get: function () { return whatsapp_connection_entity_1.WhatsAppConnection; } });
Object.defineProperty(exports, "ConnectionType", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionType; } });
Object.defineProperty(exports, "ConnectionStatus", { enumerable: true, get: function () { return whatsapp_connection_entity_1.ConnectionStatus; } });
var subscription_entity_1 = require("./subscription.entity");
Object.defineProperty(exports, "Subscription", { enumerable: true, get: function () { return subscription_entity_1.Subscription; } });
Object.defineProperty(exports, "SubscriptionStatus", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionStatus; } });
Object.defineProperty(exports, "SubscriptionPlan", { enumerable: true, get: function () { return subscription_entity_1.SubscriptionPlan; } });
var contact_schema_1 = require("./contact.schema");
Object.defineProperty(exports, "Contact", { enumerable: true, get: function () { return contact_schema_1.Contact; } });
Object.defineProperty(exports, "ContactStatus", { enumerable: true, get: function () { return contact_schema_1.ContactStatus; } });
var message_schema_1 = require("./message.schema");
Object.defineProperty(exports, "Message", { enumerable: true, get: function () { return message_schema_1.Message; } });
Object.defineProperty(exports, "MessageType", { enumerable: true, get: function () { return message_schema_1.MessageType; } });
Object.defineProperty(exports, "MessageDirection", { enumerable: true, get: function () { return message_schema_1.MessageDirection; } });
Object.defineProperty(exports, "MessageStatus", { enumerable: true, get: function () { return message_schema_1.MessageStatus; } });
const contact_schema_2 = require("./contact.schema");
const message_schema_2 = require("./message.schema");
const company_entity_2 = require("./company.entity");
const user_entity_2 = require("./user.entity");
const whatsapp_connection_entity_2 = require("./whatsapp-connection.entity");
const subscription_entity_2 = require("./subscription.entity");
const contact_schema_3 = require("./contact.schema");
const message_schema_3 = require("./message.schema");
exports.TypeOrmEntities = [
    company_entity_2.Company,
    user_entity_2.User,
    whatsapp_connection_entity_2.WhatsAppConnection,
    subscription_entity_2.Subscription,
];
exports.MongooseSchemas = [
    { name: contact_schema_3.Contact.name, schema: contact_schema_2.ContactSchema },
    { name: message_schema_3.Message.name, schema: message_schema_2.MessageSchema },
];
var contact_schema_4 = require("./contact.schema");
Object.defineProperty(exports, "ContactSchema", { enumerable: true, get: function () { return contact_schema_4.ContactSchema; } });
var message_schema_4 = require("./message.schema");
Object.defineProperty(exports, "MessageSchema", { enumerable: true, get: function () { return message_schema_4.MessageSchema; } });
//# sourceMappingURL=index.js.map