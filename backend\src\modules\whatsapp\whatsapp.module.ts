import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { WhatsAppConnection } from '../../database/entities';
import { WhatsAppService } from './whatsapp.service';
import { WhatsAppController } from './whatsapp.controller';
import { EvolutionApiService } from './services/evolution-api.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([WhatsAppConnection]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
  ],
  controllers: [WhatsAppController],
  providers: [WhatsAppService, EvolutionApiService],
  exports: [WhatsAppService, EvolutionApiService],
})
export class WhatsAppModule {}
