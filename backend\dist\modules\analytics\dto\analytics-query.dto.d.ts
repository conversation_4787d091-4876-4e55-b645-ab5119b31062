import { EventType, EventCategory } from '../../../database/entities/analytics-event.schema';
export declare enum TimeRange {
    LAST_24_HOURS = "last_24_hours",
    LAST_7_DAYS = "last_7_days",
    LAST_30_DAYS = "last_30_days",
    LAST_90_DAYS = "last_90_days",
    LAST_YEAR = "last_year",
    CUSTOM = "custom"
}
export declare enum GroupBy {
    HOUR = "hour",
    DAY = "day",
    WEEK = "week",
    MONTH = "month",
    YEAR = "year"
}
export declare enum MetricType {
    COUNT = "count",
    SUM = "sum",
    AVERAGE = "average",
    MIN = "min",
    MAX = "max",
    UNIQUE = "unique"
}
export declare class AnalyticsQueryDto {
    timeRange?: TimeRange;
    startDate?: string;
    endDate?: string;
    eventTypes?: EventType[];
    categories?: EventCategory[];
    userIds?: string[];
    connectionIds?: string[];
    contactPhones?: string[];
    groupBy?: GroupBy;
    metricType?: MetricType;
    metricField?: string;
    filters?: Record<string, any>;
    compareWithPrevious?: boolean;
    limit?: number;
    page?: number;
}
export declare class DashboardQueryDto {
    timeRange?: TimeRange;
    startDate?: string;
    endDate?: string;
    connectionIds?: string[];
    compareWithPrevious?: boolean;
}
export declare class ReportQueryDto extends AnalyticsQueryDto {
    format?: string;
    includeDetails?: boolean;
    fields?: string[];
}
