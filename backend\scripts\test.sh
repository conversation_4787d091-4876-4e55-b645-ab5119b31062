#!/bin/bash

# Script para executar testes da aplicação
set -e

echo "🧪 Iniciando testes da WhatsApp Platform API..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se o Node.js está instalado
if ! command -v node &> /dev/null; then
    error "Node.js não está instalado"
    exit 1
fi

# Verificar se o npm está instalado
if ! command -v npm &> /dev/null; then
    error "npm não está instalado"
    exit 1
fi

# Verificar se as dependências estão instaladas
if [ ! -d "node_modules" ]; then
    warning "Dependências não encontradas. Instalando..."
    npm install
fi

# Configurar variáveis de ambiente para teste
export NODE_ENV=test
export JWT_SECRET=test-secret-key-for-testing
export DATABASE_URL=sqlite::memory:
export MONGODB_URI=mongodb://localhost:27017/test
export REDIS_URL=redis://localhost:6379/1

log "Executando linting..."
npm run lint || {
    error "Linting falhou"
    exit 1
}
success "Linting passou"

log "Executando testes unitários..."
npm run test || {
    error "Testes unitários falharam"
    exit 1
}
success "Testes unitários passaram"

log "Executando testes de integração (E2E)..."
npm run test:e2e || {
    error "Testes E2E falharam"
    exit 1
}
success "Testes E2E passaram"

log "Gerando relatório de cobertura..."
npm run test:cov || {
    error "Geração de cobertura falhou"
    exit 1
}
success "Relatório de cobertura gerado"

log "Verificando cobertura mínima..."
COVERAGE=$(npm run test:cov --silent | grep -o 'All files.*[0-9]\+\.[0-9]\+' | grep -o '[0-9]\+\.[0-9]\+' | head -1)
MINIMUM_COVERAGE=80

if (( $(echo "$COVERAGE < $MINIMUM_COVERAGE" | bc -l) )); then
    error "Cobertura de testes ($COVERAGE%) está abaixo do mínimo ($MINIMUM_COVERAGE%)"
    exit 1
fi
success "Cobertura de testes ($COVERAGE%) está acima do mínimo"

log "Executando build de produção..."
npm run build || {
    error "Build de produção falhou"
    exit 1
}
success "Build de produção concluído"

log "Verificando vulnerabilidades de segurança..."
npm audit --audit-level=high || {
    warning "Vulnerabilidades de segurança encontradas"
    npm audit fix || {
        error "Não foi possível corrigir automaticamente as vulnerabilidades"
        exit 1
    }
    success "Vulnerabilidades corrigidas"
}
success "Verificação de segurança passou"

log "Executando análise de qualidade de código..."
if command -v sonar-scanner &> /dev/null; then
    sonar-scanner || {
        warning "Análise SonarQube falhou"
    }
else
    warning "SonarQube Scanner não encontrado, pulando análise"
fi

success "🎉 Todos os testes passaram com sucesso!"

echo ""
echo "📊 Resumo dos Testes:"
echo "✅ Linting: Passou"
echo "✅ Testes Unitários: Passou"
echo "✅ Testes E2E: Passou"
echo "✅ Cobertura: $COVERAGE% (mínimo: $MINIMUM_COVERAGE%)"
echo "✅ Build: Passou"
echo "✅ Segurança: Passou"
echo ""
echo "🚀 A aplicação está pronta para deploy!"
