{"version": 3, "file": "TokenSequence.js", "sourceRoot": "", "sources": ["../../src/parser/TokenSequence.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D,yCAAwC;AAWxC;;;GAGG;AACH;IASE,uBAAmB,UAAoC;QACrD,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACrC,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACW,yBAAW,GAAzB,UAA0B,aAA4B;QACpD,OAAO,IAAI,aAAa,CAAC,EAAE,aAAa,eAAA,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1E,CAAC;IAKD,sBAAW,qCAAU;QAHrB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAKD,sBAAW,mCAAQ;QAHnB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED,sBAAW,iCAAM;aAAjB;YACE,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3E,CAAC;;;OAAA;IAED;;;OAGG;IACI,sCAAc,GAArB,UAAsB,UAAkB,EAAE,QAAgB;QACxD,OAAO,IAAI,aAAa,CAAC;YACvB,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,UAAU,EAAE,UAAU;YACtB,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,8CAAsB,GAA7B;QACE,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;YACnB,OAAO,qBAAS,CAAC,KAAK,CAAC;QACzB,CAAC;QAED,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAC/C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,GAAG,EACrD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CACxD,CAAC;IACJ,CAAC;IAEM,+BAAO,GAAd;QACE,OAAO,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,gCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,EAAE,EAAZ,CAAY,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAEO,uCAAe,GAAvB;QACE,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,wEAAwE,CAAC,CAAC;QAC5F,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;QACvF,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrD,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAhGD,IAgGC;AAhGY,sCAAa", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport type { ParserContext } from './ParserContext';\r\nimport type { Token } from './Token';\r\nimport { TextRange } from './TextRange';\r\n\r\n/**\r\n * Constructor parameters for {@link TokenSequence}\r\n */\r\nexport interface ITokenSequenceParameters {\r\n  parserContext: ParserContext;\r\n  startIndex: number;\r\n  endIndex: number;\r\n}\r\n\r\n/**\r\n * Represents a sequence of tokens extracted from `ParserContext.tokens`.\r\n * This sequence is defined by a starting index and ending index into that array.\r\n */\r\nexport class TokenSequence {\r\n  /**\r\n   * The associated parser context that the tokens come from.\r\n   */\r\n  public readonly parserContext: ParserContext;\r\n\r\n  private _startIndex: number;\r\n  private _endIndex: number;\r\n\r\n  public constructor(parameters: ITokenSequenceParameters) {\r\n    this.parserContext = parameters.parserContext;\r\n    this._startIndex = parameters.startIndex;\r\n    this._endIndex = parameters.endIndex;\r\n    this._validateBounds();\r\n  }\r\n\r\n  /**\r\n   * Constructs a TokenSequence object with no tokens.\r\n   */\r\n  public static createEmpty(parserContext: ParserContext): TokenSequence {\r\n    return new TokenSequence({ parserContext, startIndex: 0, endIndex: 0 });\r\n  }\r\n\r\n  /**\r\n   * The starting index into the associated `ParserContext.tokens` list.\r\n   */\r\n  public get startIndex(): number {\r\n    return this._startIndex;\r\n  }\r\n\r\n  /**\r\n   * The (non-inclusive) ending index into the associated `ParserContext.tokens` list.\r\n   */\r\n  public get endIndex(): number {\r\n    return this._endIndex;\r\n  }\r\n\r\n  public get tokens(): ReadonlyArray<Token> {\r\n    return this.parserContext.tokens.slice(this._startIndex, this._endIndex);\r\n  }\r\n\r\n  /**\r\n   * Constructs a TokenSequence that corresponds to a different range of tokens,\r\n   * e.g. a subrange.\r\n   */\r\n  public getNewSequence(startIndex: number, endIndex: number): TokenSequence {\r\n    return new TokenSequence({\r\n      parserContext: this.parserContext,\r\n      startIndex: startIndex,\r\n      endIndex: endIndex\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Returns a TextRange that includes all tokens in the sequence (including any additional\r\n   * characters between doc comment lines).\r\n   */\r\n  public getContainingTextRange(): TextRange {\r\n    if (this.isEmpty()) {\r\n      return TextRange.empty;\r\n    }\r\n\r\n    return this.parserContext.sourceRange.getNewRange(\r\n      this.parserContext.tokens[this._startIndex].range.pos,\r\n      this.parserContext.tokens[this._endIndex - 1].range.end\r\n    );\r\n  }\r\n\r\n  public isEmpty(): boolean {\r\n    return this._startIndex === this._endIndex;\r\n  }\r\n\r\n  /**\r\n   * Returns the concatenated text of all the tokens.\r\n   */\r\n  public toString(): string {\r\n    return this.tokens.map((x) => x.toString()).join('');\r\n  }\r\n\r\n  private _validateBounds(): void {\r\n    if (this.startIndex < 0) {\r\n      throw new Error('TokenSequence.startIndex cannot be negative');\r\n    }\r\n    if (this.endIndex < 0) {\r\n      throw new Error('TokenSequence.endIndex cannot be negative');\r\n    }\r\n    if (this.endIndex < this.startIndex) {\r\n      throw new Error('TokenSequence.endIndex cannot be smaller than TokenSequence.startIndex');\r\n    }\r\n    if (this.startIndex > this.parserContext.tokens.length) {\r\n      throw new Error('TokenSequence.startIndex cannot exceed the associated token array');\r\n    }\r\n    if (this.endIndex > this.parserContext.tokens.length) {\r\n      throw new Error('TokenSequence.endIndex cannot exceed the associated token array');\r\n    }\r\n  }\r\n}\r\n"]}