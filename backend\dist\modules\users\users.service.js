"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcryptjs");
const entities_1 = require("../../database/entities");
let UsersService = class UsersService {
    userRepository;
    constructor(userRepository) {
        this.userRepository = userRepository;
    }
    async create(createUserDto, currentUser) {
        if (currentUser.canAccessCompany && !currentUser.canAccessCompany(createUserDto.companyId)) {
            throw new common_1.ForbiddenException('Não é possível criar usuário nesta empresa');
        }
        const existingUser = await this.userRepository.findOne({
            where: { email: createUserDto.email },
        });
        if (existingUser) {
            throw new common_1.ConflictException('Email já está em uso');
        }
        this.validateRoleHierarchy(currentUser.role, createUserDto.role);
        const hashedPassword = await bcrypt.hash(createUserDto.password, 12);
        const user = this.userRepository.create({
            ...createUserDto,
            password: hashedPassword,
            status: entities_1.UserStatus.ACTIVE,
            createdBy: currentUser.id,
        });
        return this.userRepository.save(user);
    }
    async findAll(options, currentUser) {
        const { companyId, role, status, search, page = 1, limit = 10 } = options;
        const queryBuilder = this.userRepository.createQueryBuilder('user')
            .leftJoinAndSelect('user.company', 'company');
        if (currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            if (companyId) {
                queryBuilder.andWhere('user.companyId = :companyId', { companyId });
            }
        }
        else if (currentUser.role === entities_1.UserRole.AGENCY_ADMIN) {
            queryBuilder.andWhere('(user.companyId = :currentCompanyId OR user.companyId IN (SELECT c.id FROM companies c WHERE c.agencyId = :currentCompanyId))', { currentCompanyId: currentUser.companyId });
        }
        else {
            queryBuilder.andWhere('user.companyId = :companyId', { companyId: currentUser.companyId });
        }
        if (role) {
            queryBuilder.andWhere('user.role = :role', { role });
        }
        if (status) {
            queryBuilder.andWhere('user.status = :status', { status });
        }
        if (search) {
            queryBuilder.andWhere('(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)', { search: `%${search}%` });
        }
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        queryBuilder.orderBy('user.createdAt', 'DESC');
        const [users, total] = await queryBuilder.getManyAndCount();
        return {
            users,
            total,
            page,
            limit,
        };
    }
    async findOne(id, currentUser) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['company'],
        });
        if (!user) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (currentUser.canAccessCompany && !currentUser.canAccessCompany(user.companyId)) {
            throw new common_1.ForbiddenException('Acesso negado a este usuário');
        }
        return user;
    }
    async update(id, updateUserDto, currentUser) {
        const user = await this.findOne(id, currentUser);
        if (updateUserDto.role) {
            this.validateRoleHierarchy(currentUser.role, updateUserDto.role);
        }
        if (updateUserDto.email && updateUserDto.email !== user.email) {
            const existingUser = await this.userRepository.findOne({
                where: { email: updateUserDto.email },
            });
            if (existingUser) {
                throw new common_1.ConflictException('Email já está em uso');
            }
        }
        Object.assign(user, updateUserDto);
        user.updatedBy = currentUser.id;
        return this.userRepository.save(user);
    }
    async remove(id, currentUser) {
        const user = await this.findOne(id, currentUser);
        if (user.id === currentUser.id) {
            throw new common_1.ForbiddenException('Não é possível deletar sua própria conta');
        }
        this.validateRoleHierarchy(currentUser.role, user.role);
        await this.userRepository.softDelete(id);
    }
    async changePassword(id, newPassword, currentUser) {
        const user = await this.findOne(id, currentUser);
        const hashedPassword = await bcrypt.hash(newPassword, 12);
        await this.userRepository.update(id, {
            password: hashedPassword,
            updatedBy: currentUser.id,
        });
    }
    validateRoleHierarchy(currentUserRole, targetRole) {
        const roleHierarchy = {
            [entities_1.UserRole.SUPER_ADMIN]: 4,
            [entities_1.UserRole.AGENCY_ADMIN]: 3,
            [entities_1.UserRole.COMPANY_ADMIN]: 2,
            [entities_1.UserRole.SELLER]: 1,
        };
        const currentLevel = roleHierarchy[currentUserRole];
        const targetLevel = roleHierarchy[targetRole];
        if (currentLevel <= targetLevel) {
            throw new common_1.ForbiddenException('Não é possível gerenciar usuários com papel igual ou superior');
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UsersService);
//# sourceMappingURL=users.service.js.map