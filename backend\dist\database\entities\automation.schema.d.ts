import { Document, Types } from 'mongoose';
import * as mongoose from 'mongoose';
export type AutomationDocument = Automation & Document;
export declare enum AutomationType {
    WELCOME_MESSAGE = "welcome_message",
    KEYWORD_RESPONSE = "keyword_response",
    CHATBOT_FLOW = "chatbot_flow",
    SCHEDULED_MESSAGE = "scheduled_message",
    FOLLOW_UP = "follow_up",
    LEAD_QUALIFICATION = "lead_qualification",
    AWAY_MESSAGE = "away_message",
    BUSINESS_HOURS = "business_hours"
}
export declare enum AutomationStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    DRAFT = "draft",
    PAUSED = "paused"
}
export declare enum TriggerType {
    MESSAGE_RECEIVED = "message_received",
    KEYWORD_MATCH = "keyword_match",
    FIRST_MESSAGE = "first_message",
    SCHEDULE = "schedule",
    WEBHOOK = "webhook",
    MANUAL = "manual",
    CONTACT_CREATED = "contact_created",
    TAG_ADDED = "tag_added",
    INACTIVITY = "inactivity"
}
export declare enum ActionType {
    SEND_MESSAGE = "send_message",
    ADD_TAG = "add_tag",
    REMOVE_TAG = "remove_tag",
    ASSIGN_USER = "assign_user",
    CREATE_LEAD = "create_lead",
    UPDATE_CONTACT = "update_contact",
    WEBHOOK_CALL = "webhook_call",
    WAIT = "wait",
    CONDITION = "condition",
    AI_RESPONSE = "ai_response",
    TRANSFER_TO_HUMAN = "transfer_to_human"
}
export interface TriggerCondition {
    type: TriggerType;
    keywords?: string[];
    schedule?: {
        time: string;
        days: number[];
        timezone: string;
    };
    inactivityMinutes?: number;
    webhookUrl?: string;
    customConditions?: Record<string, any>;
}
export interface ActionStep {
    id: string;
    type: ActionType;
    name: string;
    description?: string;
    config: Record<string, any>;
    nextStepId?: string;
    conditions?: {
        field: string;
        operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
        value: any;
        nextStepId: string;
    }[];
    delay?: number;
}
export interface FlowStep {
    id: string;
    name: string;
    type: 'message' | 'condition' | 'action' | 'ai' | 'human_handoff';
    position: {
        x: number;
        y: number;
    };
    config: {
        message?: {
            type: 'text' | 'image' | 'audio' | 'video' | 'document';
            content?: string;
            mediaUrl?: string;
            buttons?: Array<{
                id: string;
                text: string;
                nextStepId?: string;
            }>;
        };
        condition?: {
            field: string;
            operator: string;
            value: any;
            trueStepId?: string;
            falseStepId?: string;
        };
        action?: {
            type: ActionType;
            config: Record<string, any>;
        };
        ai?: {
            provider: 'openai' | 'google' | 'anthropic';
            model: string;
            prompt: string;
            maxTokens?: number;
            temperature?: number;
        };
    };
    nextSteps?: string[];
}
export declare class Automation {
    name: string;
    description?: string;
    type: AutomationType;
    status: AutomationStatus;
    companyId: Types.ObjectId;
    createdBy: string;
    updatedBy?: string;
    trigger: TriggerCondition;
    actions: ActionStep[];
    flowSteps: FlowStep[];
    startStepId?: string;
    settings: {
        isActive?: boolean;
        priority?: number;
        maxExecutionsPerContact?: number;
        cooldownMinutes?: number;
        businessHoursOnly?: boolean;
        businessHours?: {
            start: string;
            end: string;
            timezone: string;
            days: number[];
        };
        allowedConnections?: string[];
        excludedContacts?: string[];
        tags?: string[];
    };
    executionCount: number;
    successCount: number;
    errorCount: number;
    lastExecutedAt?: Date;
    lastModifiedAt?: Date;
    version: number;
    isTemplate: boolean;
    templateCategory?: string;
    metadata: Record<string, any>;
}
export declare const AutomationSchema: mongoose.Schema<Automation, mongoose.Model<Automation, any, any, any, Document<unknown, any, Automation, any> & Automation & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Automation, Document<unknown, {}, mongoose.FlatRecord<Automation>, {}> & mongoose.FlatRecord<Automation> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
