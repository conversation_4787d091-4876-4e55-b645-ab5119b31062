import { Test } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmEntities, MongooseSchemas } from '../src/database/entities';

// Mock do Redis para testes
jest.mock('ioredis', () => {
  const mockRedis = {
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ttl: jest.fn(),
    keys: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    on: jest.fn(),
    connect: jest.fn(),
    disconnect: jest.fn(),
  };
  return jest.fn(() => mockRedis);
});

// Mock do HttpService para testes
jest.mock('@nestjs/axios', () => ({
  HttpService: jest.fn().mockImplementation(() => ({
    request: jest.fn(),
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  })),
  HttpModule: {
    register: jest.fn(() => ({
      module: class MockHttpModule {},
      providers: [],
      exports: [],
    })),
  },
}));

// Configuração global para testes
export const createTestingModule = async (providers: any[] = [], imports: any[] = []) => {
  const moduleBuilder = Test.createTestingModule({
    imports: [
      ConfigModule.forRoot({
        isGlobal: true,
        envFilePath: '.env.test',
      }),
      TypeOrmModule.forRootAsync({
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          type: 'sqlite',
          database: ':memory:',
          entities: TypeOrmEntities,
          synchronize: true,
          logging: false,
        }),
        inject: [ConfigService],
      }),
      MongooseModule.forRootAsync({
        imports: [ConfigModule],
        useFactory: () => ({
          uri: 'mongodb://localhost:27017/test',
          useNewUrlParser: true,
          useUnifiedTopology: true,
        }),
      }),
      JwtModule.registerAsync({
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          secret: 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
        inject: [ConfigService],
      }),
      ...imports,
    ],
    providers: [
      ...providers,
    ],
  });

  return moduleBuilder.compile();
};

// Mocks globais
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Configuração de timeout para testes
jest.setTimeout(30000);

// Limpar mocks após cada teste
afterEach(() => {
  jest.clearAllMocks();
});

// Dados de teste comuns
export const mockUser = {
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'ADMIN',
  companyId: 'company-123',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockCompany = {
  id: 'company-123',
  name: 'Test Company',
  email: '<EMAIL>',
  phone: '+5511999999999',
  document: '12345678901',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockConnection = {
  id: 'connection-123',
  name: 'Test Connection',
  phoneNumber: '+5511999999999',
  status: 'CONNECTED',
  companyId: 'company-123',
  apiUrl: 'http://localhost:8080',
  apiKey: 'test-key',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockContact = {
  id: 'contact-123',
  phoneNumber: '+5511888888888',
  name: 'Test Contact',
  companyId: 'company-123',
  whatsappConnectionId: 'connection-123',
  status: 'ACTIVE',
  source: 'WHATSAPP',
  tags: [],
  customFields: {},
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const mockMessage = {
  id: 'message-123',
  messageId: 'msg-123',
  contactId: 'contact-123',
  whatsappConnectionId: 'connection-123',
  companyId: 'company-123',
  direction: 'INBOUND',
  type: 'TEXT',
  content: 'Test message',
  status: 'DELIVERED',
  timestamp: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Helpers para testes
export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  remove: jest.fn(),
  count: jest.fn(),
  createQueryBuilder: jest.fn(() => ({
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    orWhere: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    leftJoinAndSelect: jest.fn().mockReturnThis(),
    innerJoinAndSelect: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    addSelect: jest.fn().mockReturnThis(),
    groupBy: jest.fn().mockReturnThis(),
    having: jest.fn().mockReturnThis(),
    getOne: jest.fn(),
    getMany: jest.fn(),
    getManyAndCount: jest.fn(),
    getRawOne: jest.fn(),
    getRawMany: jest.fn(),
    clone: jest.fn().mockReturnThis(),
  })),
});

export const createMockModel = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findById: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  updateOne: jest.fn(),
  deleteOne: jest.fn(),
  countDocuments: jest.fn(),
  aggregate: jest.fn(),
  populate: jest.fn(),
  exec: jest.fn(),
});

export const createMockJwtService = () => ({
  sign: jest.fn(() => 'mock-token'),
  verify: jest.fn(() => mockUser),
  decode: jest.fn(() => mockUser),
});

export const createMockConfigService = () => ({
  get: jest.fn((key: string) => {
    const config = {
      'JWT_SECRET': 'test-secret',
      'JWT_EXPIRES_IN': '1h',
      'DATABASE_URL': 'postgresql://test:test@localhost:5432/test',
      'MONGODB_URI': 'mongodb://localhost:27017/test',
      'REDIS_URL': 'redis://localhost:6379',
      'PORT': 3000,
    };
    return config[key];
  }),
});

export const createMockLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
});

// Utilitários para testes de API
export const createMockRequest = (overrides = {}) => ({
  user: mockUser,
  headers: {
    authorization: 'Bearer mock-token',
  },
  body: {},
  query: {},
  params: {},
  ...overrides,
});

export const createMockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  res.cookie = jest.fn().mockReturnValue(res);
  res.clearCookie = jest.fn().mockReturnValue(res);
  res.redirect = jest.fn().mockReturnValue(res);
  return res;
};
