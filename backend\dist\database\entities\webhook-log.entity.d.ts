import { Integration } from './integration.entity';
import { Company } from './company.entity';
export declare enum WebhookStatus {
    PENDING = "pending",
    SUCCESS = "success",
    FAILED = "failed",
    RETRYING = "retrying",
    CANCELLED = "cancelled"
}
export interface WebhookRequest {
    url: string;
    method: string;
    headers: Record<string, string>;
    body: any;
    timeout: number;
}
export interface WebhookResponse {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    body: any;
    duration: number;
}
export declare class WebhookLog {
    id: string;
    integrationId: string;
    integration: Integration;
    companyId: string;
    company: Company;
    status: WebhookStatus;
    eventType: string;
    eventData: any;
    request: WebhookRequest;
    response: WebhookResponse | null;
    errorMessage: string | null;
    errorCode: string | null;
    errorStack: string | null;
    attemptCount: number;
    maxAttempts: number;
    nextRetryAt: Date | null;
    completedAt: Date;
    duration: number;
    metadata: Record<string, any>;
    createdAt: Date;
    isSuccess(): boolean;
    isFailed(): boolean;
    isPending(): boolean;
    isRetrying(): boolean;
    canRetry(): boolean;
    markAsSuccess(response: WebhookResponse): void;
    markAsFailed(error: any, response?: WebhookResponse): void;
    retry(): void;
    cancel(): void;
    getRetryDelay(): number;
    isHttpSuccess(): boolean;
    getStatusDescription(): string;
    private getNextRetryDescription;
}
