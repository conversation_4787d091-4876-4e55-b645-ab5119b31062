{"version": 3, "file": "TokenCoverageChecker.d.ts", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/TokenCoverageChecker.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,KAAK,OAAO,EAAc,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAQtD;;;;;;;;;;;GAWG;AACH,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAgB;IAC/C,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAoC;gBAEpD,aAAa,EAAE,aAAa;IAMxC,OAAO,CAAC,QAAQ,EAAE,OAAO,GAAG,aAAa,EAAE;IAK3C,UAAU,CAAC,QAAQ,EAAE,OAAO,GAAG,IAAI;IAK1C,OAAO,CAAC,YAAY;IAUpB,OAAO,CAAC,YAAY;IAiBpB,OAAO,CAAC,aAAa;IAyDrB,OAAO,CAAC,UAAU;IAmBlB,OAAO,CAAC,uBAAuB;CAGhC"}