export declare function ApiProperty(): () => void;
export declare function ApiPropertyOptional(): () => void;
export declare function ApiResponseProperty(): () => void;
export declare function ApiBasicAuth(): () => void;
export declare function ApiBearerAuth(): () => void;
export declare function ApiBody(): () => void;
export declare function ApiConsumes(): () => void;
export declare function ApiCookieAuth(): () => void;
export declare function ApiExcludeEndpoint(): () => void;
export declare function ApiExcludeController(): () => void;
export declare function ApiExtraModels(): () => void;
export declare function ApiHeader(): () => void;
export declare function ApiHeaders(): () => void;
export declare function ApiHideProperty(): () => void;
export declare function ApiOAuth2(): () => void;
export declare function ApiOperation(): () => void;
export declare function ApiParam(): () => void;
export declare function ApiProduces(): () => void;
export declare function ApiQuery(): () => void;
export declare function ApiResponse(): () => void;
export declare function ApiContinueResponse(): () => void;
export declare function ApiSwitchingProtocolsResponse(): () => void;
export declare function ApiProcessingResponse(): () => void;
export declare function ApiEarlyhintsResponse(): () => void;
export declare function ApiOkResponse(): () => void;
export declare function ApiCreatedResponse(): () => void;
export declare function ApiAcceptedResponse(): () => void;
export declare function ApiNonAuthoritativeInformationResponse(): () => void;
export declare function ApiNoContentResponse(): () => void;
export declare function ApiResetContentResponse(): () => void;
export declare function ApiPartialContentResponse(): () => void;
export declare function ApiAmbiguousResponse(): () => void;
export declare function ApiMovedPermanentlyResponse(): () => void;
export declare function ApiFoundResponse(): () => void;
export declare function ApiSeeOtherResponse(): () => void;
export declare function ApiNotModifiedResponse(): () => void;
export declare function ApiTemporaryRedirectResponse(): () => void;
export declare function ApiPermanentRedirectResponse(): () => void;
export declare function ApiBadRequestResponse(): () => void;
export declare function ApiUnauthorizedResponse(): () => void;
export declare function ApiPaymentRequiredResponse(): () => void;
export declare function ApiForbiddenResponse(): () => void;
export declare function ApiNotFoundResponse(): () => void;
export declare function ApiMethodNotAllowedResponse(): () => void;
export declare function ApiNotAcceptableResponse(): () => void;
export declare function ApiProxyAuthenticationRequiredResponse(): () => void;
export declare function ApiRequestTimeoutResponse(): () => void;
export declare function ApiConflictResponse(): () => void;
export declare function ApiGoneResponse(): () => void;
export declare function ApiLengthRequiredResponse(): () => void;
export declare function ApiPreconditionFailedResponse(): () => void;
export declare function ApiPayloadTooLargeResponse(): () => void;
export declare function ApiUriTooLongResponse(): () => void;
export declare function ApiUnsupportedMediaTypeResponse(): () => void;
export declare function ApiRequestedRangeNotSatisfiableResponse(): () => void;
export declare function ApiExpectationFailedResponse(): () => void;
export declare function ApiIAmATeapotResponse(): () => void;
export declare function ApiMisdirectedResponse(): () => void;
export declare function ApiUnprocessableEntityResponse(): () => void;
export declare function ApiFailedDependencyResponse(): () => void;
export declare function ApiPreconditionRequiredResponse(): () => void;
export declare function ApiTooManyRequestsResponse(): () => void;
export declare function ApiInternalServerErrorResponse(): () => void;
export declare function ApiNotImplementedResponse(): () => void;
export declare function ApiBadGatewayResponse(): () => void;
export declare function ApiServiceUnavailableResponse(): () => void;
export declare function ApiGatewayTimeoutResponse(): () => void;
export declare function ApiHttpVersionNotSupportedResponse(): () => void;
export declare function ApiDefaultResponse(): () => void;
export declare function ApiSchema(): () => void;
export declare function ApiSecurity(): () => void;
export declare function ApiTags(): () => void;
export declare function ApiCallbacks(): () => void;
export declare function ApiLink(): () => void;
export declare function ApiDefaultGetter(): () => void;
export declare function ApiExtension(): () => void;
export declare function DocumentBuilder(): () => void;
export declare function SwaggerModule(): () => void;
export declare function IntersectionType(): {
    new (): {};
};
export declare function OmitType(): {
    new (): {};
};
export declare function PartialType(): {
    new (): {};
};
export declare function PickType(): {
    new (): {};
};
export declare function getSchemaPath(): () => string;
export declare function refs(): any[];
export declare function before(): () => string;
export declare function ReadonlyVisitor(): {
    new (): {};
};
