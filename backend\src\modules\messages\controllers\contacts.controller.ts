import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { ContactsService, FindContactsOptions } from '../services/contacts.service';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { ContactResponseDto } from '../dto/contact-response.dto';
import { CurrentUser, AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../../common/guards/roles.guard';
import { TenantGuard } from '../../../common/guards/tenant.guard';
import { RequirePermissions, PERMISSIONS } from '../../../common/decorators/permissions.decorator';
import { ContactStatus } from '../../../database/entities';

@ApiTags('Contatos')
@Controller('contacts')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Post()
  @RequirePermissions(PERMISSIONS.MESSAGES_SEND)
  @ApiOperation({ summary: 'Criar novo contato' })
  @ApiResponse({
    status: 201,
    description: 'Contato criado com sucesso',
    type: ContactResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Contato já existe para este número',
  })
  async create(
    @Body() createContactDto: CreateContactDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ContactResponseDto> {
    const contact = await this.contactsService.create(createContactDto, currentUser);
    return plainToClass(ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
  }

  @Get()
  @RequirePermissions(PERMISSIONS.MESSAGES_READ)
  @ApiOperation({ summary: 'Listar contatos' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filtrar por empresa' })
  @ApiQuery({ name: 'whatsappConnectionId', required: false, description: 'Filtrar por conexão WhatsApp' })
  @ApiQuery({ name: 'status', required: false, enum: ContactStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'isLead', required: false, type: Boolean, description: 'Filtrar por leads' })
  @ApiQuery({ name: 'assignedTo', required: false, description: 'Filtrar por responsável' })
  @ApiQuery({ name: 'tags', required: false, type: [String], description: 'Filtrar por etiquetas' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nome, telefone ou email' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Campo para ordenação' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' })
  @ApiResponse({
    status: 200,
    description: 'Lista de contatos',
    type: [ContactResponseDto],
  })
  async findAll(
    @Query() query: FindContactsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    const result = await this.contactsService.findAll(query, currentUser);
    
    return {
      ...result,
      contacts: result.contacts.map(contact => 
        plainToClass(ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('stats')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter estatísticas de contatos' })
  @ApiResponse({
    status: 200,
    description: 'Estatísticas de contatos',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        leads: { type: 'number' },
        blocked: { type: 'number' },
        archived: { type: 'number' },
      },
    },
  })
  async getStats(@CurrentUser() currentUser: AuthenticatedUser) {
    return this.contactsService.getContactStats(currentUser);
  }

  @Get(':id')
  @RequirePermissions(PERMISSIONS.MESSAGES_READ)
  @ApiOperation({ summary: 'Obter contato por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados do contato',
    type: ContactResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contato não encontrado',
  })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ContactResponseDto> {
    const contact = await this.contactsService.findOne(id, currentUser);
    return plainToClass(ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @RequirePermissions(PERMISSIONS.MESSAGES_SEND)
  @ApiOperation({ summary: 'Atualizar contato' })
  @ApiResponse({
    status: 200,
    description: 'Contato atualizado com sucesso',
    type: ContactResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Contato não encontrado',
  })
  async update(
    @Param('id') id: string,
    @Body() updateContactDto: UpdateContactDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ContactResponseDto> {
    const contact = await this.contactsService.update(id, updateContactDto, currentUser);
    return plainToClass(ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @RequirePermissions(PERMISSIONS.MESSAGES_DELETE)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deletar contato' })
  @ApiResponse({
    status: 204,
    description: 'Contato deletado com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Contato não encontrado',
  })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.contactsService.remove(id, currentUser);
  }

  @Post(':id/tags')
  @RequirePermissions(PERMISSIONS.MESSAGES_SEND)
  @ApiOperation({ summary: 'Adicionar etiqueta ao contato' })
  @ApiResponse({
    status: 200,
    description: 'Etiqueta adicionada com sucesso',
    type: ContactResponseDto,
  })
  async addTag(
    @Param('id') id: string,
    @Body() tagData: { name: string; color: string },
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<ContactResponseDto> {
    const contact = await this.contactsService.addTag(id, tagData.name, tagData.color, currentUser);
    return plainToClass(ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
  }

  @Delete(':id/tags/:tagId')
  @RequirePermissions(PERMISSIONS.MESSAGES_SEND)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover etiqueta do contato' })
  @ApiResponse({
    status: 204,
    description: 'Etiqueta removida com sucesso',
  })
  async removeTag(
    @Param('id') id: string,
    @Param('tagId') tagId: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    await this.contactsService.removeTag(id, tagId, currentUser);
  }
}
