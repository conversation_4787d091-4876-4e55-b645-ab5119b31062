import { ConnectionType, ConnectionStatus } from '../../../database/entities';
export declare class ConnectionResponseDto {
    id: string;
    name: string;
    phoneNumber: string;
    formattedPhoneNumber: string;
    type: ConnectionType;
    status: ConnectionStatus;
    isConnected: boolean;
    qrCode?: string;
    instanceId?: string;
    lastConnectedAt?: Date;
    lastDisconnectedAt?: Date;
    errorMessage?: string;
    isActive: boolean;
    companyId: string;
    assignedUserId?: string;
    settings?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
