# WhatsApp Management Platform - Backend

Uma plataforma SaaS robusta, escalável e segura para gestão centralizada e automação de contas de WhatsApp, desenvolvida com NestJS, TypeScript e arquitetura multi-tenant.

## 🚀 Características Principais

- **Arquitetura Multi-Tenant**: Isolamento completo de dados por empresa
- **Sistema Hierárquico**: 4 níveis de acesso (Super Admin, Agência, Empresa, Vendedor)
- **Autenticação JWT**: Sistema seguro com refresh tokens
- **RBAC**: Controle de acesso baseado em papéis e permissões
- **Integração WhatsApp**: Suporte para Evolution API V2 e Meta API Oficial
- **Real-time**: WebSocket para comunicação instantânea
- **Analytics**: Dashboard completo com métricas e relatórios
- **Automação**: Chatbot com IA e fluxos personalizáveis
- **Faturamento**: Sistema completo de assinaturas e comissões

## 🏗️ Arquitetura

### Tecnologias
- **Backend**: Node.js + TypeScript + NestJS
- **Banco de Dados**: PostgreSQL (dados estruturados) + MongoDB (mensagens/logs)
- **Cache**: Redis
- **Autenticação**: JWT + Passport
- **Documentação**: Swagger/OpenAPI
- **Validação**: Class Validator + Class Transformer

### Estrutura de Módulos
```
src/
├── modules/
│   ├── auth/           # Autenticação e autorização
│   ├── users/          # Gestão de usuários
│   ├── companies/      # Gestão de empresas
│   ├── whatsapp/       # Conexões WhatsApp
│   ├── messages/       # Sistema de mensagens
│   ├── analytics/      # Métricas e relatórios
│   ├── automation/     # Chatbot e automações
│   ├── billing/        # Faturamento e assinaturas
│   └── integrations/   # Integrações externas
├── common/
│   ├── guards/         # Guards de segurança
│   ├── decorators/     # Decorators customizados
│   ├── filters/        # Filtros de exceção
│   └── interceptors/   # Interceptors
├── database/
│   ├── entities/       # Entidades TypeORM e Mongoose
│   └── migrations/     # Migrações do banco
└── config/             # Configurações da aplicação
```

## 🛠️ Configuração do Ambiente

### Pré-requisitos
- Node.js 18+
- Docker e Docker Compose
- PostgreSQL 15+
- MongoDB 7+
- Redis 7+

### Instalação

1. **Clone o repositório**
```bash
git clone <repository-url>
cd wpp-app/backend
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure as variáveis de ambiente**
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

4. **Inicie os bancos de dados**
```bash
# Windows PowerShell
powershell -ExecutionPolicy Bypass -File start-databases.ps1

# Ou manualmente com Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

5. **Execute as migrações**
```bash
npm run migration:run
```

6. **Inicie a aplicação**
```bash
# Desenvolvimento
npm run start:dev

# Produção
npm run build
npm run start:prod
```

## 📚 Documentação da API

Após iniciar a aplicação, acesse:
- **Swagger UI**: http://localhost:3000/api/v1/docs
- **API Base**: http://localhost:3000/api/v1

## 🔐 Sistema de Autenticação

### Níveis de Acesso

1. **Super Admin (Desenvolvedor)**
   - Controle total da plataforma
   - Gestão de todas as contas e configurações globais
   - Acesso a métricas e logs do sistema

2. **Agência de Tráfego**
   - Gestão de clientes (empresas)
   - Dashboard de comissionamento (25%)
   - Criação de links de revenda

3. **Empresa (Administrador)**
   - Gestão de usuários e vendedores
   - Configuração de conexões WhatsApp
   - Relatórios e analytics da empresa

4. **Vendedor (Usuário)**
   - Interface de chat unificada
   - Gestão de contatos e etiquetas
   - Envio de mensagens em massa

### Endpoints de Autenticação

```bash
POST /api/v1/auth/login          # Login
POST /api/v1/auth/register       # Registro
POST /api/v1/auth/refresh        # Renovar token
POST /api/v1/auth/logout         # Logout
GET  /api/v1/auth/me            # Perfil do usuário
```

## 🗄️ Banco de Dados

### PostgreSQL (Dados Estruturados)
- **users**: Usuários do sistema
- **companies**: Empresas/tenants
- **whatsapp_connections**: Conexões WhatsApp
- **subscriptions**: Assinaturas e faturamento

### MongoDB (Dados Flexíveis)
- **contacts**: Contatos e leads
- **messages**: Mensagens e conversas
- **analytics_events**: Eventos para analytics

## 🔧 Scripts Disponíveis

```bash
# Desenvolvimento
npm run start:dev          # Inicia em modo desenvolvimento
npm run build             # Compila o projeto
npm run start:prod        # Inicia em modo produção

# Testes
npm run test              # Testes unitários
npm run test:e2e          # Testes end-to-end
npm run test:cov          # Cobertura de testes

# Banco de Dados
npm run migration:generate # Gera nova migração
npm run migration:run     # Executa migrações
npm run migration:revert  # Reverte última migração

# Linting e Formatação
npm run lint              # Executa ESLint
npm run format            # Formata código com Prettier
```

## 🐳 Docker

### Desenvolvimento
```bash
# Iniciar apenas os bancos de dados
docker-compose -f docker-compose.dev.yml up -d

# Parar os bancos
docker-compose -f docker-compose.dev.yml down
```

### Produção
```bash
# Build e deploy completo
docker-compose up -d
```

## 🧪 Testes

```bash
# Testes unitários
npm run test

# Testes específicos
npm run test -- --testNamePattern="AuthService"

# Testes com watch
npm run test:watch

# Cobertura
npm run test:cov
```

## 📊 Monitoramento

### Logs
- Logs estruturados com Winston
- Níveis: error, warn, info, debug
- Rotação automática de arquivos

### Métricas
- Health checks em `/health`
- Métricas de performance
- Monitoramento de conexões

## 🔒 Segurança

### Implementações
- **HTTPS/TLS**: Todas as comunicações criptografadas
- **JWT**: Tokens seguros com refresh
- **RBAC**: Controle granular de permissões
- **Rate Limiting**: Proteção contra ataques
- **CORS**: Configuração adequada
- **Helmet**: Headers de segurança
- **Validation**: Validação rigorosa de inputs

### LGPD
- Criptografia de dados sensíveis
- Logs de auditoria
- Controle de retenção de dados
- APIs para exercício de direitos

## 🚀 Deploy

### Variáveis de Ambiente (Produção)
```bash
NODE_ENV=production
DATABASE_URL=postgresql://...
MONGODB_URI=mongodb://...
REDIS_URL=redis://...
JWT_SECRET=<strong-secret>
```

### CI/CD
- GitHub Actions configurado
- Deploy automático
- Testes obrigatórios
- Build otimizado

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 📞 Suporte

Para suporte e dúvidas:
- 📧 Email: <EMAIL>
- 💬 Discord: [Link do servidor]
- 📖 Documentação: [Link da documentação]

---

Desenvolvido com ❤️ usando NestJS
