import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateCompanyDto } from './create-company.dto';
import { CompanyStatus } from '../../../database/entities';

export class UpdateCompanyDto extends PartialType(
  OmitType(CreateCompanyDto, ['cnpj'] as const)
) {
  @ApiPropertyOptional({
    description: 'Status da empresa',
    enum: CompanyStatus,
  })
  @IsOptional()
  @IsEnum(CompanyStatus, { message: 'Status deve ser um valor válido' })
  status?: CompanyStatus;
}
