import {
  IsString,
  IsEnum,
  IsNumber,
  IsBoolean,
  IsOptional,
  IsObject,
  IsArray,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PlanType, PlanStatus, BillingCycle, PlanFeatures } from '../../../database/entities/plan.entity';

export class PlanFeaturesDto implements PlanFeatures {
  @ApiProperty({ description: 'Máximo de conexões WhatsApp', example: 5 })
  @IsNumber()
  @Min(1)
  maxConnections: number;

  @ApiProperty({ description: 'Máximo de contatos', example: 10000 })
  @IsNumber()
  @Min(1)
  maxContacts: number;

  @ApiProperty({ description: 'Máximo de mensagens por mês', example: 50000 })
  @IsNumber()
  @Min(1)
  maxMessages: number;

  @ApiProperty({ description: 'Máximo de usuários', example: 10 })
  @IsNumber()
  @Min(1)
  maxUsers: number;

  @ApiProperty({ description: 'Máximo de automações', example: 20 })
  @IsNumber()
  @Min(1)
  maxAutomations: number;

  @ApiProperty({ description: 'Máximo de chatbots', example: 5 })
  @IsNumber()
  @Min(1)
  maxChatbots: number;

  @ApiProperty({ description: 'Tem analytics', example: true })
  @IsBoolean()
  hasAnalytics: boolean;

  @ApiProperty({ description: 'Tem relatórios avançados', example: true })
  @IsBoolean()
  hasAdvancedReports: boolean;

  @ApiProperty({ description: 'Tem acesso à API', example: true })
  @IsBoolean()
  hasAPI: boolean;

  @ApiProperty({ description: 'Tem webhooks', example: true })
  @IsBoolean()
  hasWebhooks: boolean;

  @ApiProperty({ description: 'Tem integrações', example: true })
  @IsBoolean()
  hasIntegrations: boolean;

  @ApiProperty({ description: 'Tem white label', example: false })
  @IsBoolean()
  hasWhiteLabel: boolean;

  @ApiProperty({ description: 'Tem domínio customizado', example: false })
  @IsBoolean()
  hasCustomDomain: boolean;

  @ApiProperty({ description: 'Tem suporte prioritário', example: true })
  @IsBoolean()
  hasPrioritySupport: boolean;

  @ApiProperty({ description: 'Tem campos customizados', example: true })
  @IsBoolean()
  hasCustomFields: boolean;

  @ApiProperty({ description: 'Tem mensagens em massa', example: true })
  @IsBoolean()
  hasBulkMessages: boolean;

  @ApiProperty({ description: 'Tem mensagens agendadas', example: true })
  @IsBoolean()
  hasScheduledMessages: boolean;

  @ApiProperty({ description: 'Tem templates de mensagem', example: true })
  @IsBoolean()
  hasMessageTemplates: boolean;

  @ApiProperty({ description: 'Tem segmentação de contatos', example: true })
  @IsBoolean()
  hasContactSegmentation: boolean;

  @ApiProperty({ description: 'Tem pontuação de leads', example: true })
  @IsBoolean()
  hasLeadScoring: boolean;

  @ApiProperty({ description: 'Tem roteamento de conversas', example: true })
  @IsBoolean()
  hasConversationRouting: boolean;

  @ApiProperty({ description: 'Tem múltiplos agentes', example: true })
  @IsBoolean()
  hasMultiAgent: boolean;

  @ApiProperty({ description: 'Tem compartilhamento de arquivos', example: true })
  @IsBoolean()
  hasFileSharing: boolean;

  @ApiProperty({ description: 'Tem chamada de vídeo', example: false })
  @IsBoolean()
  hasVideoCall: boolean;

  @ApiProperty({ description: 'Tem mensagem de voz', example: true })
  @IsBoolean()
  hasVoiceMessage: boolean;

  @ApiProperty({ description: 'Tem Google Sheets', example: true })
  @IsBoolean()
  hasGoogleSheets: boolean;

  @ApiProperty({ description: 'Tem integração CRM', example: true })
  @IsBoolean()
  hasCRMIntegration: boolean;

  @ApiProperty({ description: 'Tem integração Zapier', example: true })
  @IsBoolean()
  hasZapierIntegration: boolean;

  @ApiProperty({ description: 'Tem email marketing', example: false })
  @IsBoolean()
  hasEmailMarketing: boolean;

  @ApiProperty({ description: 'Tem integração SMS', example: false })
  @IsBoolean()
  hasSMSIntegration: boolean;

  @ApiProperty({ description: 'Tem IA', example: true })
  @IsBoolean()
  hasAI: boolean;

  @ApiProperty({ description: 'Tem IA customizada', example: false })
  @IsBoolean()
  hasCustomAI: boolean;

  @ApiProperty({ description: 'Tem automação avançada', example: true })
  @IsBoolean()
  hasAdvancedAutomation: boolean;

  @ApiProperty({ description: 'Tem construtor de workflow', example: true })
  @IsBoolean()
  hasWorkflowBuilder: boolean;

  @ApiProperty({ description: 'Tem relatórios customizados', example: true })
  @IsBoolean()
  hasCustomReports: boolean;

  @ApiProperty({ description: 'Tem exportação de dados', example: true })
  @IsBoolean()
  hasDataExport: boolean;

  @ApiProperty({ description: 'Tem backup e restore', example: true })
  @IsBoolean()
  hasBackupRestore: boolean;

  @ApiProperty({ description: 'Tem SSO', example: false })
  @IsBoolean()
  hasSSO: boolean;

  @ApiProperty({ description: 'Tem log de auditoria', example: true })
  @IsBoolean()
  hasAuditLog: boolean;

  @ApiProperty({ description: 'Tem gestão de papéis', example: true })
  @IsBoolean()
  hasRoleManagement: boolean;

  @ApiProperty({ description: 'Armazenamento em GB', example: 100 })
  @IsNumber()
  @Min(1)
  storageGB: number;

  @ApiProperty({ description: 'Retenção de mensagens em dias', example: 365 })
  @IsNumber()
  @Min(1)
  messageRetentionDays: number;

  @ApiProperty({ description: 'Nível de suporte', enum: ['basic', 'standard', 'premium', 'enterprise'] })
  @IsEnum(['basic', 'standard', 'premium', 'enterprise'])
  supportLevel: 'basic' | 'standard' | 'premium' | 'enterprise';

  @ApiProperty({ description: 'Canais de suporte', type: [String], example: ['email', 'chat'] })
  @IsArray()
  @IsString({ each: true })
  supportChannels: string[];

  @ApiProperty({ description: 'SLA de resposta em horas', example: 24 })
  @IsNumber()
  @Min(1)
  responseTimeSLA: number;
}

export class CreatePlanDto {
  @ApiProperty({ description: 'Nome do plano', example: 'Plano Professional' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Descrição do plano', example: 'Plano ideal para empresas médias' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Tipo do plano', enum: PlanType })
  @IsEnum(PlanType)
  type: PlanType;

  @ApiPropertyOptional({ description: 'Status do plano', enum: PlanStatus, default: PlanStatus.ACTIVE })
  @IsOptional()
  @IsEnum(PlanStatus)
  status?: PlanStatus;

  @ApiProperty({ description: 'Ciclo de cobrança', enum: BillingCycle })
  @IsEnum(BillingCycle)
  billingCycle: BillingCycle;

  @ApiProperty({ description: 'Preço do plano', example: 299.99 })
  @IsNumber()
  @Min(0)
  price: number;

  @ApiPropertyOptional({ description: 'Taxa de setup', example: 0, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  setupFee?: number;

  @ApiPropertyOptional({ description: 'Percentual de desconto', example: 10, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  discountPercentage?: number;

  @ApiProperty({ description: 'Funcionalidades do plano', type: PlanFeaturesDto })
  @ValidateNested()
  @Type(() => PlanFeaturesDto)
  features: PlanFeaturesDto;

  @ApiPropertyOptional({ description: 'Metadados adicionais' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Tem período de trial', default: false })
  @IsOptional()
  @IsBoolean()
  hasTrialPeriod?: boolean;

  @ApiPropertyOptional({ description: 'Dias de trial', example: 14, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trialDays?: number;

  @ApiPropertyOptional({ description: 'É plano de agência', default: false })
  @IsOptional()
  @IsBoolean()
  isAgencyPlan?: boolean;

  @ApiPropertyOptional({ description: 'Percentual de comissão da agência', example: 20, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  agencyCommissionPercentage?: number;

  @ApiPropertyOptional({ description: 'Comissão fixa da agência', example: 50, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  agencyFixedCommission?: number;

  @ApiPropertyOptional({ description: 'É recorrente', default: true })
  @IsOptional()
  @IsBoolean()
  isRecurring?: boolean;

  @ApiPropertyOptional({ description: 'ID do produto no Stripe' })
  @IsOptional()
  @IsString()
  stripeProductId?: string;

  @ApiPropertyOptional({ description: 'ID do preço no Stripe' })
  @IsOptional()
  @IsString()
  stripePriceId?: string;

  @ApiPropertyOptional({ description: 'Permite upgrade', default: true })
  @IsOptional()
  @IsBoolean()
  allowUpgrade?: boolean;

  @ApiPropertyOptional({ description: 'Permite downgrade', default: true })
  @IsOptional()
  @IsBoolean()
  allowDowngrade?: boolean;

  @ApiPropertyOptional({ description: 'IDs dos planos para upgrade', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  upgradeableTo?: string[];

  @ApiPropertyOptional({ description: 'IDs dos planos para downgrade', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  downgradeableTo?: string[];

  @ApiPropertyOptional({ description: 'É público', default: true })
  @IsOptional()
  @IsBoolean()
  isPublic?: boolean;

  @ApiPropertyOptional({ description: 'É customizado', default: false })
  @IsOptional()
  @IsBoolean()
  isCustom?: boolean;

  @ApiPropertyOptional({ description: 'Público-alvo', example: 'small_business' })
  @IsOptional()
  @IsString()
  targetAudience?: string;

  @ApiPropertyOptional({ description: 'Ordem de exibição', example: 1, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  sortOrder?: number;
}
