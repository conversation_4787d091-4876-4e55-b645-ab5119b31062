import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { plainToClass } from 'class-transformer';
import { AutomationService, FindAutomationsOptions, FindExecutionsOptions } from './services/automation.service';
import { AutomationExecutorService } from './services/automation-executor.service';
import { CreateAutomationDto } from './dto/create-automation.dto';
import { UpdateAutomationDto } from './dto/update-automation.dto';
import { AutomationResponseDto, AutomationExecutionResponseDto } from './dto/automation-response.dto';
import { CurrentUser, AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { RequirePermissions, PERMISSIONS } from '../../common/decorators/permissions.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserRole, AutomationType, AutomationStatus, TriggerType } from '../../database/entities';
import { ExecutionStatus } from '../../database/entities/automation-execution.schema';

@ApiTags('Automação')
@Controller('automation')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class AutomationController {
  constructor(
    private readonly automationService: AutomationService,
    private readonly executorService: AutomationExecutorService,
  ) {}

  @Post()
  @RequirePermissions(PERMISSIONS.AUTOMATION_CREATE)
  @ApiOperation({ summary: 'Criar nova automação' })
  @ApiResponse({
    status: 201,
    description: 'Automação criada com sucesso',
    type: AutomationResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe uma automação com este nome',
  })
  async create(
    @Body() createAutomationDto: CreateAutomationDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.create(createAutomationDto, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Get()
  @RequirePermissions(PERMISSIONS.AUTOMATION_READ)
  @ApiOperation({ summary: 'Listar automações' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filtrar por empresa' })
  @ApiQuery({ name: 'type', required: false, enum: AutomationType, description: 'Filtrar por tipo' })
  @ApiQuery({ name: 'status', required: false, enum: AutomationStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'triggerType', required: false, enum: TriggerType, description: 'Filtrar por tipo de gatilho' })
  @ApiQuery({ name: 'isTemplate', required: false, type: Boolean, description: 'Filtrar por templates' })
  @ApiQuery({ name: 'templateCategory', required: false, description: 'Filtrar por categoria de template' })
  @ApiQuery({ name: 'createdBy', required: false, description: 'Filtrar por criador' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nome ou descrição' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Campo para ordenação' })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' })
  @ApiResponse({
    status: 200,
    description: 'Lista de automações',
    type: [AutomationResponseDto],
  })
  async findAll(
    @Query() query: FindAutomationsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    const result = await this.automationService.findAll(query, currentUser);
    
    return {
      ...result,
      automations: result.automations.map(automation => 
        plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('stats')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter estatísticas de automações' })
  @ApiResponse({
    status: 200,
    description: 'Estatísticas de automações',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        inactive: { type: 'number' },
        draft: { type: 'number' },
        totalExecutions: { type: 'number' },
        successRate: { type: 'number' },
      },
    },
  })
  async getStats(@CurrentUser() currentUser: AuthenticatedUser) {
    return this.automationService.getAutomationStats(currentUser);
  }

  @Get('templates')
  @RequirePermissions(PERMISSIONS.AUTOMATION_READ)
  @ApiOperation({ summary: 'Listar templates de automação' })
  @ApiQuery({ name: 'category', required: false, description: 'Filtrar por categoria' })
  @ApiResponse({
    status: 200,
    description: 'Lista de templates',
    type: [AutomationResponseDto],
  })
  async getTemplates(
    @Query('category') category?: string,
    @CurrentUser() currentUser?: AuthenticatedUser,
  ) {
    const query: FindAutomationsOptions = {
      isTemplate: true,
      templateCategory: category,
    };
    
    const result = await this.automationService.findAll(query, currentUser!);
    
    return result.automations.map(automation => 
      plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true })
    );
  }

  @Get(':id')
  @RequirePermissions(PERMISSIONS.AUTOMATION_READ)
  @ApiOperation({ summary: 'Obter automação por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados da automação',
    type: AutomationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Automação não encontrada',
  })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.findOne(id, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Patch(':id')
  @RequirePermissions(PERMISSIONS.AUTOMATION_UPDATE)
  @ApiOperation({ summary: 'Atualizar automação' })
  @ApiResponse({
    status: 200,
    description: 'Automação atualizada com sucesso',
    type: AutomationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Automação não encontrada',
  })
  async update(
    @Param('id') id: string,
    @Body() updateAutomationDto: UpdateAutomationDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.update(id, updateAutomationDto, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Delete(':id')
  @RequirePermissions(PERMISSIONS.AUTOMATION_DELETE)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deletar automação' })
  @ApiResponse({
    status: 204,
    description: 'Automação deletada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Automação não encontrada',
  })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.automationService.remove(id, currentUser);
  }

  @Post(':id/activate')
  @RequirePermissions(PERMISSIONS.AUTOMATION_UPDATE)
  @ApiOperation({ summary: 'Ativar automação' })
  @ApiResponse({
    status: 200,
    description: 'Automação ativada com sucesso',
    type: AutomationResponseDto,
  })
  async activate(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.activate(id, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Post(':id/deactivate')
  @RequirePermissions(PERMISSIONS.AUTOMATION_UPDATE)
  @ApiOperation({ summary: 'Desativar automação' })
  @ApiResponse({
    status: 200,
    description: 'Automação desativada com sucesso',
    type: AutomationResponseDto,
  })
  async deactivate(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.deactivate(id, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Post(':id/duplicate')
  @RequirePermissions(PERMISSIONS.AUTOMATION_CREATE)
  @ApiOperation({ summary: 'Duplicar automação' })
  @ApiResponse({
    status: 201,
    description: 'Automação duplicada com sucesso',
    type: AutomationResponseDto,
  })
  async duplicate(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationResponseDto> {
    const automation = await this.automationService.duplicate(id, currentUser);
    return plainToClass(AutomationResponseDto, automation.toObject(), { excludeExtraneousValues: true });
  }

  @Post(':id/test')
  @RequirePermissions(PERMISSIONS.AUTOMATION_UPDATE)
  @ApiOperation({ summary: 'Testar automação' })
  @ApiResponse({
    status: 200,
    description: 'Teste da automação iniciado',
    type: AutomationExecutionResponseDto,
  })
  async test(
    @Param('id') id: string,
    @Body() testData: { contactPhone: string; connectionId: string },
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationExecutionResponseDto> {
    const automation = await this.automationService.findOne(id, currentUser);
    
    const execution = await this.executorService.executeAutomation(automation, {
      type: TriggerType.MANUAL,
      contactPhone: testData.contactPhone,
      connectionId: testData.connectionId,
      companyId: currentUser.companyId,
      userId: currentUser.id,
      metadata: { isTest: true },
    });

    return plainToClass(AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true });
  }

  @Get(':id/executions')
  @RequirePermissions(PERMISSIONS.AUTOMATION_READ)
  @ApiOperation({ summary: 'Listar execuções da automação' })
  @ApiQuery({ name: 'status', required: false, enum: ExecutionStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'contactPhone', required: false, description: 'Filtrar por telefone do contato' })
  @ApiQuery({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' })
  @ApiQuery({ name: 'dateTo', required: false, type: Date, description: 'Data final' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiResponse({
    status: 200,
    description: 'Lista de execuções',
    type: [AutomationExecutionResponseDto],
  })
  async getExecutions(
    @Param('id') id: string,
    @Query() query: FindExecutionsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    query.automationId = id;
    const result = await this.automationService.getExecutions(query, currentUser);
    
    return {
      ...result,
      executions: result.executions.map(execution => 
        plainToClass(AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true })
      ),
    };
  }

  @Get('executions/:executionId')
  @RequirePermissions(PERMISSIONS.AUTOMATION_READ)
  @ApiOperation({ summary: 'Obter execução por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados da execução',
    type: AutomationExecutionResponseDto,
  })
  async getExecution(
    @Param('executionId') executionId: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<AutomationExecutionResponseDto> {
    const result = await this.automationService.getExecutions(
      { page: 1, limit: 1 },
      currentUser,
    );
    
    const execution = result.executions.find(exec => exec.executionId === executionId);
    
    if (!execution) {
      throw new Error('Execution not found');
    }

    return plainToClass(AutomationExecutionResponseDto, execution.toObject(), { excludeExtraneousValues: true });
  }

  @Post('executions/:executionId/retry')
  @RequirePermissions(PERMISSIONS.AUTOMATION_UPDATE)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Tentar novamente execução falhada' })
  @ApiResponse({
    status: 204,
    description: 'Execução reagendada com sucesso',
  })
  async retryExecution(
    @Param('executionId') executionId: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ): Promise<void> {
    return this.executorService.retryExecution(executionId);
  }
}
