import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { WebhookResponse } from '../../../database/entities/webhook-log.entity';
import { 
  Integration, 
  IntegrationStatus, 
  TriggerEvent 
} from '../../../database/entities/integration.entity';
import { 
  WebhookLog, 
  WebhookStatus 
} from '../../../database/entities/webhook-log.entity';
import { CreateIntegrationDto } from '../dto/create-integration.dto';
import { UpdateIntegrationDto } from '../dto/update-integration.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { UserRole } from '../../../database/entities';

export interface FindIntegrationsOptions {
  companyId?: string;
  type?: string;
  status?: string;
  active?: boolean;
  triggerEvent?: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface FindWebhookLogsOptions {
  integrationId?: string;
  companyId?: string;
  status?: string;
  eventType?: string;
  dateFrom?: Date;
  dateTo?: Date;
  page?: number;
  limit?: number;
}

@Injectable()
export class IntegrationsService {
  private readonly logger = new Logger(IntegrationsService.name);

  constructor(
    @InjectRepository(Integration)
    private integrationRepository: Repository<Integration>,
    @InjectRepository(WebhookLog)
    private webhookLogRepository: Repository<WebhookLog>,
    private httpService: HttpService,
  ) {}

  async create(
    createIntegrationDto: CreateIntegrationDto,
    currentUser: AuthenticatedUser,
  ): Promise<Integration> {
    // Verificar se já existe integração com o mesmo nome
    const existingIntegration = await this.integrationRepository.findOne({
      where: { 
        name: createIntegrationDto.name,
        companyId: currentUser.companyId,
      },
    });

    if (existingIntegration) {
      throw new ConflictException('Já existe uma integração com este nome');
    }

    // Validar configuração específica do tipo
    this.validateIntegrationConfig(createIntegrationDto.type, createIntegrationDto.config);

    const integration = this.integrationRepository.create({
      ...createIntegrationDto,
      companyId: currentUser.companyId,
      createdBy: currentUser.id,
      status: IntegrationStatus.PENDING,
    });

    const savedIntegration = await this.integrationRepository.save(integration);

    // Testar a integração se for webhook
    if (createIntegrationDto.type === 'webhook' && createIntegrationDto.config.url) {
      await this.testIntegration(savedIntegration.id, currentUser);
    }

    this.logger.log(`Integration created: ${savedIntegration.id} by ${currentUser.email}`);
    return savedIntegration;
  }

  async findAll(
    options: FindIntegrationsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    integrations: Integration[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      companyId,
      type,
      status,
      active,
      triggerEvent,
      search,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = options;

    const queryBuilder = this.integrationRepository.createQueryBuilder('integration')
      .leftJoinAndSelect('integration.creator', 'creator');

    // Filtro por empresa
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('integration.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('integration.companyId = :companyId', { companyId });
    }

    // Filtros adicionais
    if (type) {
      queryBuilder.andWhere('integration.type = :type', { type });
    }

    if (status) {
      queryBuilder.andWhere('integration.status = :status', { status });
    }

    if (typeof active === 'boolean') {
      queryBuilder.andWhere('integration.active = :active', { active });
    }

    if (triggerEvent) {
      queryBuilder.andWhere(':triggerEvent = ANY(integration.triggerEvents)', { triggerEvent });
    }

    if (search) {
      queryBuilder.andWhere(
        '(integration.name ILIKE :search OR integration.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Paginação
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Ordenação
    queryBuilder.orderBy(`integration.${sortBy}`, sortOrder);

    const [integrations, total] = await queryBuilder.getManyAndCount();

    return { integrations, total, page, limit };
  }

  async findOne(id: string, currentUser: AuthenticatedUser): Promise<Integration> {
    const queryBuilder = this.integrationRepository.createQueryBuilder('integration')
      .leftJoinAndSelect('integration.creator', 'creator')
      .where('integration.id = :id', { id });

    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('integration.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    const integration = await queryBuilder.getOne();
    
    if (!integration) {
      throw new NotFoundException('Integração não encontrada');
    }

    return integration;
  }

  async update(
    id: string,
    updateIntegrationDto: UpdateIntegrationDto,
    currentUser: AuthenticatedUser,
  ): Promise<Integration> {
    const integration = await this.findOne(id, currentUser);

    // Verificar se nome já existe (se estiver sendo alterado)
    if (updateIntegrationDto.name && updateIntegrationDto.name !== integration.name) {
      const existingIntegration = await this.integrationRepository.findOne({
        where: { 
          name: updateIntegrationDto.name,
          companyId: currentUser.companyId,
        },
      });

      if (existingIntegration && existingIntegration.id !== id) {
        throw new ConflictException('Já existe uma integração com este nome');
      }
    }

    // Validar configuração se fornecida
    if (updateIntegrationDto.config && updateIntegrationDto.type) {
      this.validateIntegrationConfig(updateIntegrationDto.type, updateIntegrationDto.config);
    }

    Object.assign(integration, updateIntegrationDto);
    const updatedIntegration = await this.integrationRepository.save(integration);

    this.logger.log(`Integration updated: ${id} by ${currentUser.email}`);
    return updatedIntegration;
  }

  async remove(id: string, currentUser: AuthenticatedUser): Promise<void> {
    const integration = await this.findOne(id, currentUser);

    // Verificar se há logs pendentes
    const pendingLogs = await this.webhookLogRepository.count({
      where: { 
        integrationId: id,
        status: WebhookStatus.PENDING,
      },
    });

    if (pendingLogs > 0) {
      throw new ConflictException('Não é possível deletar integração com webhooks pendentes');
    }

    await this.integrationRepository.remove(integration);
    this.logger.log(`Integration deleted: ${id} by ${currentUser.email}`);
  }

  async testIntegration(id: string, currentUser: AuthenticatedUser): Promise<any> {
    const integration = await this.findOne(id, currentUser);

    const testData = {
      test: true,
      timestamp: new Date().toISOString(),
      integration: {
        id: integration.id,
        name: integration.name,
        type: integration.type,
      },
      company: {
        id: currentUser.companyId,
        name: 'Company Name', // Placeholder
      },
      user: {
        id: currentUser.id,
        name: currentUser.email, // Using email as name placeholder
        email: currentUser.email,
      },
    };

    return this.executeIntegration(integration, 'test_event', testData);
  }

  async triggerIntegration(
    event: TriggerEvent,
    data: any,
    companyId: string,
  ): Promise<void> {
    // Buscar integrações ativas para este evento
    const integrations = await this.integrationRepository.find({
      where: {
        companyId,
        status: IntegrationStatus.ACTIVE,
        active: true,
      },
    });

    const triggeredIntegrations = integrations.filter(integration => 
      integration.shouldTrigger(event, data)
    );

    this.logger.log(`Triggering ${triggeredIntegrations.length} integrations for event ${event}`);

    // Executar integrações em paralelo
    const promises = triggeredIntegrations.map(integration => 
      this.executeIntegration(integration, event, data)
    );

    await Promise.allSettled(promises);
  }

  async executeIntegration(
    integration: Integration,
    event: string,
    data: any,
  ): Promise<any> {
    if (!integration.canExecute()) {
      this.logger.warn(`Integration ${integration.id} cannot execute (rate limited or inactive)`);
      return;
    }

    // Transformar dados
    const transformedData = integration.transformData(data);

    // Criar log do webhook
    const webhookLog = this.webhookLogRepository.create({
      integrationId: integration.id,
      companyId: integration.companyId,
      eventType: event,
      eventData: transformedData,
      request: {
        url: integration.config.url || '',
        method: integration.config.method || 'POST',
        headers: integration.config.headers || {},
        body: transformedData,
        timeout: integration.timeout,
      },
    });

    await this.webhookLogRepository.save(webhookLog);

    try {
      let result;

      switch (integration.type) {
        case 'webhook':
          result = await this.executeWebhook(integration, transformedData, webhookLog);
          break;
        case 'google_sheets':
          result = await this.executeGoogleSheets(integration, transformedData, webhookLog);
          break;
        case 'slack':
          result = await this.executeSlack(integration, transformedData, webhookLog);
          break;
        default:
          throw new Error(`Integration type ${integration.type} not implemented`);
      }

      integration.incrementExecution(true);
      await this.integrationRepository.save(integration);

      return result;
    } catch (error) {
      integration.incrementExecution(false);
      integration.setError(error.message);
      await this.integrationRepository.save(integration);

      webhookLog.markAsFailed(error);
      await this.webhookLogRepository.save(webhookLog);

      this.logger.error(`Integration execution failed: ${integration.id} - ${error.message}`);
      throw error;
    }
  }

  private async executeWebhook(
    integration: Integration,
    data: any,
    webhookLog: WebhookLog,
  ): Promise<any> {
    const config = integration.config;
    const startTime = Date.now();

    try {
      const headers = { ...config.headers };

      // Adicionar autenticação
      if (config.authType === 'bearer' && config.bearerToken) {
        headers['Authorization'] = `Bearer ${config.bearerToken}`;
      } else if (config.authType === 'api_key' && config.apiKey) {
        headers['X-API-Key'] = config.apiKey;
      }

      const response = await firstValueFrom(
        this.httpService.request({
          method: config.method || 'POST',
          url: config.url!,
          data: data,
          headers,
          timeout: config.timeout || integration.timeout,
        })
      );

      const duration = Date.now() - startTime;
      const webhookResponse: WebhookResponse = {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>,
        body: response.data,
        duration,
      };

      webhookLog.markAsSuccess(webhookResponse);
      await this.webhookLogRepository.save(webhookLog);

      return response.data;
    } catch (error) {
      const duration = Date.now() - startTime;
      const webhookResponse: WebhookResponse | undefined = error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: error.response.headers as Record<string, string>,
        body: error.response.data,
        duration,
      } : undefined;

      webhookLog.markAsFailed(error, webhookResponse);
      await this.webhookLogRepository.save(webhookLog);

      throw error;
    }
  }

  private async executeGoogleSheets(
    integration: Integration,
    data: any,
    webhookLog: WebhookLog,
  ): Promise<any> {
    // Implementação simplificada - em produção, usar Google Sheets API
    this.logger.log(`Google Sheets integration: ${integration.id}`);
    
    const result = { success: true, message: 'Data sent to Google Sheets' };
    
    webhookLog.markAsSuccess({
      status: 200,
      statusText: 'OK',
      headers: {},
      body: result,
      duration: 1000,
    });
    await this.webhookLogRepository.save(webhookLog);

    return result;
  }

  private async executeSlack(
    integration: Integration,
    data: any,
    webhookLog: WebhookLog,
  ): Promise<any> {
    // Implementação simplificada - em produção, usar Slack API
    this.logger.log(`Slack integration: ${integration.id}`);
    
    const result = { success: true, message: 'Message sent to Slack' };
    
    webhookLog.markAsSuccess({
      status: 200,
      statusText: 'OK',
      headers: {},
      body: result,
      duration: 1500,
    });
    await this.webhookLogRepository.save(webhookLog);

    return result;
  }

  async getWebhookLogs(
    options: FindWebhookLogsOptions,
    currentUser: AuthenticatedUser,
  ): Promise<{
    logs: WebhookLog[];
    total: number;
    page: number;
    limit: number;
  }> {
    const {
      integrationId,
      companyId,
      status,
      eventType,
      dateFrom,
      dateTo,
      page = 1,
      limit = 10,
    } = options;

    const queryBuilder = this.webhookLogRepository.createQueryBuilder('log')
      .leftJoinAndSelect('log.integration', 'integration');

    // Filtro por empresa
    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('log.companyId = :userCompanyId', { 
        userCompanyId: currentUser.companyId 
      });
    }

    if (companyId && currentUser.role === UserRole.SUPER_ADMIN) {
      queryBuilder.andWhere('log.companyId = :companyId', { companyId });
    }

    // Filtros adicionais
    if (integrationId) {
      queryBuilder.andWhere('log.integrationId = :integrationId', { integrationId });
    }

    if (status) {
      queryBuilder.andWhere('log.status = :status', { status });
    }

    if (eventType) {
      queryBuilder.andWhere('log.eventType = :eventType', { eventType });
    }

    if (dateFrom || dateTo) {
      if (dateFrom) {
        queryBuilder.andWhere('log.createdAt >= :dateFrom', { dateFrom });
      }
      if (dateTo) {
        queryBuilder.andWhere('log.createdAt <= :dateTo', { dateTo });
      }
    }

    // Paginação
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Ordenação
    queryBuilder.orderBy('log.createdAt', 'DESC');

    const [logs, total] = await queryBuilder.getManyAndCount();

    return { logs, total, page, limit };
  }

  async retryWebhook(logId: string, currentUser: AuthenticatedUser): Promise<WebhookLog> {
    const log = await this.webhookLogRepository.findOne({
      where: { id: logId },
      relations: ['integration'],
    });

    if (!log) {
      throw new NotFoundException('Log de webhook não encontrado');
    }

    if (currentUser.role !== UserRole.SUPER_ADMIN && log.companyId !== currentUser.companyId) {
      throw new NotFoundException('Log de webhook não encontrado');
    }

    if (!log.canRetry()) {
      throw new BadRequestException('Este webhook não pode ser executado novamente');
    }

    log.retry();
    await this.webhookLogRepository.save(log);

    // Re-executar a integração
    try {
      await this.executeIntegration(log.integration, log.eventType, log.eventData);
    } catch (error) {
      this.logger.error(`Webhook retry failed: ${logId} - ${error.message}`);
    }

    return log;
  }

  private validateIntegrationConfig(type: string, config: any): void {
    switch (type) {
      case 'webhook':
        if (!config.url) {
          throw new BadRequestException('URL é obrigatória para webhooks');
        }
        break;
      case 'google_sheets':
        if (!config.googleSheets?.spreadsheetId || !config.googleSheets?.sheetName) {
          throw new BadRequestException('ID da planilha e nome da aba são obrigatórios para Google Sheets');
        }
        break;
      case 'slack':
        if (!config.slack?.channelId || !config.slack?.botToken) {
          throw new BadRequestException('ID do canal e token do bot são obrigatórios para Slack');
        }
        break;
    }
  }

  async getIntegrationStats(currentUser: AuthenticatedUser): Promise<{
    total: number;
    active: number;
    inactive: number;
    error: number;
    totalExecutions: number;
    successRate: number;
  }> {
    const queryBuilder = this.integrationRepository.createQueryBuilder('integration');

    if (currentUser.role !== UserRole.SUPER_ADMIN) {
      queryBuilder.where('integration.companyId = :userCompanyId', {
        userCompanyId: currentUser.companyId
      });
    }

    const [total, active, inactive, error] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('integration.status = :status', { status: IntegrationStatus.ACTIVE }).getCount(),
      queryBuilder.clone().andWhere('integration.status = :status', { status: IntegrationStatus.INACTIVE }).getCount(),
      queryBuilder.clone().andWhere('integration.status = :status', { status: IntegrationStatus.ERROR }).getCount(),
    ]);

    // Calcular estatísticas de execução
    const executionStats = await queryBuilder.clone()
      .select('SUM(integration.executionCount)', 'totalExecutions')
      .addSelect('SUM(integration.successCount)', 'totalSuccess')
      .getRawOne();

    const totalExecutions = parseInt(executionStats.totalExecutions) || 0;
    const totalSuccess = parseInt(executionStats.totalSuccess) || 0;
    const successRate = totalExecutions > 0 ? (totalSuccess / totalExecutions) * 100 : 0;

    return {
      total,
      active,
      inactive,
      error,
      totalExecutions,
      successRate: Math.round(successRate * 100) / 100,
    };
  }
}
