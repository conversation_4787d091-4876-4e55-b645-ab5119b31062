{"version": 3, "file": "TSDocValidationConfiguration.js", "sourceRoot": "", "sources": ["../../src/configuration/TSDocValidationConfiguration.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;GAEG;AACH;IAAA;QACE;;;;;;;;;;WAUG;QACI,wBAAmB,GAAY,KAAK,CAAC;QAE5C;;;;;;;;;;;;;;;;WAgBG;QACI,0BAAqB,GAAY,KAAK,CAAC;QAE9C;;;;;WAKG;QACI,kCAA6B,GAAY,KAAK,CAAC;IACxD,CAAC;IAAD,mCAAC;AAAD,CAAC,AAxCD,IAwCC;AAxCY,oEAA4B", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\n/**\r\n * Part of the {@link TSDocConfiguration} object.\r\n */\r\nexport class TSDocValidationConfiguration {\r\n  /**\r\n   * Set `ignoreUndefinedTags` to true to silently ignore unrecognized tags,\r\n   * instead of reporting a warning.\r\n   *\r\n   * @remarks\r\n   * Normally the parser will issue errors when it encounters tag names that do not\r\n   * have a corresponding definition in {@link TSDocConfiguration.tagDefinitions}.\r\n   * This helps to catch common mistakes such as a misspelled tag.\r\n   *\r\n   * @defaultValue `false`\r\n   */\r\n  public ignoreUndefinedTags: boolean = false;\r\n\r\n  /**\r\n   * Set `reportUnsupportedTags` to true to issue a warning for tags that are not\r\n   * supported by your tool.\r\n   *\r\n   * @remarks\r\n   * The TSDoc standard defines may tags.  By default it assumes that if your tool does\r\n   * not implement one of these tags, then it will simply ignore it.  But sometimes this\r\n   * may be misleading for developers. (For example, they might write an `@example` block\r\n   * and then be surprised if it doesn't appear in the documentation output.).\r\n   *\r\n   * For a better experience, you can tell the parser which tags you support, and then it\r\n   * will issue warnings wherever unsupported tags are used.  This is done using\r\n   * {@link TSDocConfiguration.setSupportForTag}.  Note that calling that function\r\n   * automatically sets `reportUnsupportedTags` to true.\r\n   *\r\n   * @defaultValue `false`\r\n   */\r\n  public reportUnsupportedTags: boolean = false;\r\n\r\n  /**\r\n   * Set `reportUnsupportedHtmlElements` to true to issue a warning for HTML elements which\r\n   * are not defined in your TSDoc configuration's `supportedHtmlElements` field.\r\n   *\r\n   * @defaultValue `false`\r\n   */\r\n  public reportUnsupportedHtmlElements: boolean = false;\r\n}\r\n"]}