import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Subscription } from './subscription.entity';
import { Payment } from './payment.entity';

export enum InvoiceStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PAID = 'paid',
  PARTIALLY_PAID = 'partially_paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
}

export enum InvoiceType {
  SUBSCRIPTION = 'subscription',
  SETUP_FEE = 'setup_fee',
  USAGE_BASED = 'usage_based',
  ONE_TIME = 'one_time',
  CREDIT_NOTE = 'credit_note',
  ADJUSTMENT = 'adjustment',
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  type: 'subscription' | 'usage' | 'setup' | 'discount' | 'tax' | 'adjustment';
  metadata?: Record<string, any>;
}

export interface TaxDetails {
  taxName: string;
  taxRate: number;
  taxAmount: number;
  taxType: 'percentage' | 'fixed';
}

export interface DiscountDetails {
  discountName: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  discountAmount: number;
  couponCode?: string;
}

@Entity('invoices')
export class Invoice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  invoiceNumber: string;

  @Column()
  companyId: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ nullable: true })
  subscriptionId: string;

  @ManyToOne(() => Subscription, subscription => subscription.invoices)
  @JoinColumn({ name: 'subscriptionId' })
  subscription: Subscription;

  @Column({
    type: 'enum',
    enum: InvoiceStatus,
    default: InvoiceStatus.DRAFT,
  })
  status: InvoiceStatus;

  @Column({
    type: 'enum',
    enum: InvoiceType,
    default: InvoiceType.SUBSCRIPTION,
  })
  type: InvoiceType;

  @Column()
  issueDate: Date;

  @Column()
  dueDate: Date;

  @Column({ nullable: true })
  paidDate: Date;

  @Column()
  periodStart: Date;

  @Column()
  periodEnd: Date;

  // Valores monetários
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  subtotal: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  total: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  amountPaid: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  amountDue: number;

  // Detalhes dos itens
  @Column({ type: 'json' })
  lineItems: InvoiceLineItem[];

  @Column({ type: 'json', nullable: true })
  taxDetails: TaxDetails[];

  @Column({ type: 'json', nullable: true })
  discountDetails: DiscountDetails[];

  // Informações de cobrança
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  terms: string;

  @Column({ type: 'text', nullable: true })
  footer: string;

  // Configurações de pagamento
  @Column({ default: false })
  autoCharge: boolean;

  @Column({ nullable: true })
  paymentMethodId: string;

  @Column({ default: 0 })
  attemptCount: number;

  @Column({ nullable: true })
  nextAttemptDate: Date;

  // Integração com gateway
  @Column({ nullable: true })
  stripeInvoiceId: string;

  @Column({ nullable: true })
  stripePaymentIntentId: string;

  // URLs e documentos
  @Column({ nullable: true })
  invoiceUrl: string;

  @Column({ nullable: true })
  pdfUrl: string;

  @Column({ nullable: true })
  hostedInvoiceUrl: string;

  // Configurações de notificação
  @Column({ default: false })
  emailSent: boolean;

  @Column({ nullable: true })
  emailSentAt: Date;

  @Column({ default: 0 })
  remindersSent: number;

  @Column({ nullable: true })
  lastReminderSentAt: Date;

  // Metadados
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  @Column({ type: 'varchar', length: 10, default: 'pt-BR' })
  locale: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Payment, payment => payment.invoice)
  payments: Payment[];

  // Métodos auxiliares
  isPaid(): boolean {
    return this.status === InvoiceStatus.PAID;
  }

  isOverdue(): boolean {
    return this.status === InvoiceStatus.OVERDUE || 
           (this.status === InvoiceStatus.PENDING && new Date() > this.dueDate);
  }

  isDraft(): boolean {
    return this.status === InvoiceStatus.DRAFT;
  }

  canBePaid(): boolean {
    return [InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.OVERDUE].includes(this.status);
  }

  canBeCancelled(): boolean {
    return [InvoiceStatus.DRAFT, InvoiceStatus.PENDING].includes(this.status);
  }

  getRemainingAmount(): number {
    return Math.max(0, this.total - this.amountPaid);
  }

  getPaymentProgress(): number {
    if (this.total === 0) return 100;
    return (this.amountPaid / this.total) * 100;
  }

  addLineItem(item: Omit<InvoiceLineItem, 'id'>): void {
    const newItem: InvoiceLineItem = {
      ...item,
      id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
    
    this.lineItems = [...(this.lineItems || []), newItem];
    this.recalculateAmounts();
  }

  removeLineItem(itemId: string): void {
    this.lineItems = (this.lineItems || []).filter(item => item.id !== itemId);
    this.recalculateAmounts();
  }

  updateLineItem(itemId: string, updates: Partial<InvoiceLineItem>): void {
    this.lineItems = (this.lineItems || []).map(item => 
      item.id === itemId ? { ...item, ...updates } : item
    );
    this.recalculateAmounts();
  }

  recalculateAmounts(): void {
    this.subtotal = (this.lineItems || []).reduce((sum, item) => sum + item.totalPrice, 0);
    
    // Calcular impostos
    this.taxAmount = (this.taxDetails || []).reduce((sum, tax) => sum + tax.taxAmount, 0);
    
    // Calcular descontos
    this.discountAmount = (this.discountDetails || []).reduce((sum, discount) => sum + discount.discountAmount, 0);
    
    // Total final
    this.total = this.subtotal + this.taxAmount - this.discountAmount;
    this.amountDue = Math.max(0, this.total - this.amountPaid);
  }

  addPayment(amount: number): void {
    this.amountPaid += amount;
    this.amountDue = Math.max(0, this.total - this.amountPaid);
    
    if (this.amountDue === 0) {
      this.status = InvoiceStatus.PAID;
      this.paidDate = new Date();
    } else if (this.amountPaid > 0) {
      this.status = InvoiceStatus.PARTIALLY_PAID;
    }
  }

  markAsOverdue(): void {
    if (this.canBePaid() && new Date() > this.dueDate) {
      this.status = InvoiceStatus.OVERDUE;
    }
  }

  cancel(): void {
    if (this.canBeCancelled()) {
      this.status = InvoiceStatus.CANCELLED;
    }
  }

  finalize(): void {
    if (this.isDraft()) {
      this.status = InvoiceStatus.PENDING;
      this.issueDate = new Date();
    }
  }

  getDaysOverdue(): number {
    if (!this.isOverdue()) return 0;
    const now = new Date();
    const diffTime = now.getTime() - this.dueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  generateInvoiceNumber(): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    return `INV-${year}${month}-${timestamp}`;
  }
}
