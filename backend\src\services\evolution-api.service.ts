import axios, { AxiosInstance } from 'axios'

export interface EvolutionInstance {
  instanceName: string
  status: string
  qrcode?: {
    code: string
    base64: string
  }
  profilePictureUrl?: string
  profileName?: string
  profileStatus?: string
  owner: string
  serverUrl: string
}

export interface CreateInstanceRequest {
  instanceName: string
  token?: string
  qrcode?: boolean
  number?: string
  webhook?: string
  webhookByEvents?: boolean
  webhookBase64?: boolean
  events?: string[]
}

export interface SendMessageRequest {
  number: string
  text?: string
  media?: {
    mediatype: 'image' | 'video' | 'audio' | 'document'
    media: string // base64 or url
    fileName?: string
    caption?: string
  }
  options?: {
    delay?: number
    presence?: 'unavailable' | 'available' | 'composing' | 'recording' | 'paused'
  }
}

export interface WebhookData {
  event: string
  instance: string
  data: any
  destination?: string
  date_time: string
  sender: string
  server_url: string
}

export class EvolutionApiService {
  private client: AxiosInstance
  private baseUrl: string
  private apiKey: string

  constructor() {
    this.baseUrl = process.env.EVOLUTION_API_URL || 'http://localhost:8080'
    this.apiKey = process.env.EVOLUTION_API_KEY || 'development-evolution-api-key'
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.apiKey
      },
      timeout: 30000
    })

    // Interceptor para logs
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[Evolution API] ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('[Evolution API] Request error:', error)
        return Promise.reject(error)
      }
    )

    this.client.interceptors.response.use(
      (response) => {
        console.log(`[Evolution API] Response ${response.status} from ${response.config.url}`)
        return response
      },
      (error) => {
        console.error('[Evolution API] Response error:', error.response?.data || error.message)
        return Promise.reject(error)
      }
    )
  }

  // Instance Management
  async createInstance(data: CreateInstanceRequest): Promise<EvolutionInstance> {
    try {
      const response = await this.client.post('/instance/create', {
        instanceName: data.instanceName,
        token: data.token,
        qrcode: data.qrcode !== false,
        number: data.number,
        webhook: data.webhook,
        webhook_by_events: data.webhookByEvents,
        webhook_base64: data.webhookBase64,
        events: data.events || [
          'APPLICATION_STARTUP',
          'QRCODE_UPDATED',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'MESSAGES_DELETE',
          'SEND_MESSAGE',
          'CONTACTS_SET',
          'CONTACTS_UPSERT',
          'CONTACTS_UPDATE',
          'PRESENCE_UPDATE',
          'CHATS_SET',
          'CHATS_UPSERT',
          'CHATS_UPDATE',
          'CHATS_DELETE',
          'GROUPS_UPSERT',
          'GROUP_UPDATE',
          'GROUP_PARTICIPANTS_UPDATE',
          'CONNECTION_UPDATE',
          'CALL',
          'NEW_JWT_TOKEN'
        ]
      })

      return response.data
    } catch (error) {
      console.error('Erro ao criar instância:', error)
      throw new Error(`Falha ao criar instância: ${error.response?.data?.message || error.message}`)
    }
  }

  async getInstance(instanceName: string): Promise<EvolutionInstance> {
    try {
      const response = await this.client.get(`/instance/fetchInstances?instanceName=${instanceName}`)
      return response.data[0] || null
    } catch (error) {
      console.error('Erro ao buscar instância:', error)
      throw new Error(`Falha ao buscar instância: ${error.response?.data?.message || error.message}`)
    }
  }

  async getAllInstances(): Promise<EvolutionInstance[]> {
    try {
      const response = await this.client.get('/instance/fetchInstances')
      return response.data || []
    } catch (error) {
      console.error('Erro ao buscar instâncias:', error)
      throw new Error(`Falha ao buscar instâncias: ${error.response?.data?.message || error.message}`)
    }
  }

  async deleteInstance(instanceName: string): Promise<void> {
    try {
      await this.client.delete(`/instance/delete/${instanceName}`)
    } catch (error) {
      console.error('Erro ao deletar instância:', error)
      throw new Error(`Falha ao deletar instância: ${error.response?.data?.message || error.message}`)
    }
  }

  async restartInstance(instanceName: string): Promise<void> {
    try {
      await this.client.put(`/instance/restart/${instanceName}`)
    } catch (error) {
      console.error('Erro ao reiniciar instância:', error)
      throw new Error(`Falha ao reiniciar instância: ${error.response?.data?.message || error.message}`)
    }
  }

  async logoutInstance(instanceName: string): Promise<void> {
    try {
      await this.client.delete(`/instance/logout/${instanceName}`)
    } catch (error) {
      console.error('Erro ao fazer logout da instância:', error)
      throw new Error(`Falha ao fazer logout: ${error.response?.data?.message || error.message}`)
    }
  }

  // Connection Management
  async getConnectionState(instanceName: string): Promise<string> {
    try {
      const response = await this.client.get(`/instance/connectionState/${instanceName}`)
      return response.data.state || 'close'
    } catch (error) {
      console.error('Erro ao buscar estado da conexão:', error)
      return 'close'
    }
  }

  async getQrCode(instanceName: string): Promise<{ code: string; base64: string } | null> {
    try {
      const response = await this.client.get(`/instance/connect/${instanceName}`)
      return response.data.qrcode || null
    } catch (error) {
      console.error('Erro ao buscar QR Code:', error)
      return null
    }
  }

  // Message Management
  async sendTextMessage(instanceName: string, data: SendMessageRequest): Promise<any> {
    try {
      const response = await this.client.post(`/message/sendText/${instanceName}`, {
        number: data.number,
        text: data.text,
        options: data.options
      })
      return response.data
    } catch (error) {
      console.error('Erro ao enviar mensagem de texto:', error)
      throw new Error(`Falha ao enviar mensagem: ${error.response?.data?.message || error.message}`)
    }
  }

  async sendMediaMessage(instanceName: string, data: SendMessageRequest): Promise<any> {
    try {
      const response = await this.client.post(`/message/sendMedia/${instanceName}`, {
        number: data.number,
        mediatype: data.media?.mediatype,
        media: data.media?.media,
        fileName: data.media?.fileName,
        caption: data.media?.caption,
        options: data.options
      })
      return response.data
    } catch (error) {
      console.error('Erro ao enviar mensagem de mídia:', error)
      throw new Error(`Falha ao enviar mídia: ${error.response?.data?.message || error.message}`)
    }
  }

  async getMessages(instanceName: string, number?: string): Promise<any[]> {
    try {
      const url = number 
        ? `/chat/findMessages/${instanceName}?number=${number}`
        : `/chat/findMessages/${instanceName}`
      
      const response = await this.client.get(url)
      return response.data || []
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error)
      return []
    }
  }

  // Contact Management
  async getContacts(instanceName: string): Promise<any[]> {
    try {
      const response = await this.client.get(`/chat/findContacts/${instanceName}`)
      return response.data || []
    } catch (error) {
      console.error('Erro ao buscar contatos:', error)
      return []
    }
  }

  async getProfilePicture(instanceName: string, number: string): Promise<string | null> {
    try {
      const response = await this.client.get(`/chat/whatsappNumbers/${instanceName}?numbers=${number}`)
      return response.data[0]?.profilePictureUrl || null
    } catch (error) {
      console.error('Erro ao buscar foto do perfil:', error)
      return null
    }
  }

  // Webhook Management
  async setWebhook(instanceName: string, webhook: string, events?: string[]): Promise<void> {
    try {
      await this.client.post(`/webhook/set/${instanceName}`, {
        webhook,
        webhook_by_events: true,
        webhook_base64: false,
        events: events || [
          'QRCODE_UPDATED',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'CONNECTION_UPDATE'
        ]
      })
    } catch (error) {
      console.error('Erro ao configurar webhook:', error)
      throw new Error(`Falha ao configurar webhook: ${error.response?.data?.message || error.message}`)
    }
  }

  // Health Check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/instance/fetchInstances')
      return response.status === 200
    } catch (error) {
      console.error('Evolution API health check failed:', error)
      return false
    }
  }

  // Utility Methods
  formatPhoneNumber(phone: string): string {
    // Remove todos os caracteres não numéricos
    const cleaned = phone.replace(/\D/g, '')
    
    // Se não tem código do país, adiciona 55 (Brasil)
    if (cleaned.length === 11 && cleaned.startsWith('11')) {
      return `55${cleaned}@s.whatsapp.net`
    } else if (cleaned.length === 10) {
      return `5511${cleaned}@s.whatsapp.net`
    } else if (cleaned.length === 13 && cleaned.startsWith('55')) {
      return `${cleaned}@s.whatsapp.net`
    }
    
    return `${cleaned}@s.whatsapp.net`
  }

  extractPhoneFromJid(jid: string): string {
    return jid.replace('@s.whatsapp.net', '').replace('@c.us', '')
  }
}
