# Installation
> `npm install --save @types/jsonwebtoken`

# Summary
This package contains type definitions for jsonwebtoken (https://github.com/auth0/node-jsonwebtoken).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsonwebtoken.

### Additional Details
 * Last updated: Mon, 16 Sep 2024 18:40:03 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [<PERSON>e LUCE](https://github.com/SomaticIT), [<PERSON>](https://github.com/danie<PERSON><PERSON>), [<PERSON><PERSON> BERNARD](https://github.com/brikou), [<PERSON><PERSON>-<PERSON><PERSON><PERSON>ilä](https://github.com/vpk), [<PERSON>](https://github.com/GeneralistDev), [<PERSON><PERSON><PERSON>](https://github.com/kettil), [<PERSON>](https://github.com/RunAge), [<PERSON>](https://github.com/nflaig), [<PERSON><PERSON>](https://github.com/LinusU), [<PERSON>](https://github.com/ivansieder), [Piotr Błażejewicz](https://github.com/peterblazeje<PERSON>), and [Nandor Kraszlan](https://github.com/nandi95).
