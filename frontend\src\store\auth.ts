import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { apiService } from '@/services/api'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (data: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  refreshUser: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (data: LoginRequest) => {
        try {
          set({ isLoading: true, error: null })
          
          const response = await apiService.login(data)
          const { user, accessToken, refreshToken } = response.data
          
          // Tokens são salvos automaticamente pelo apiService
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          })
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Erro ao fazer login'
          set({ 
            error: errorMessage, 
            isLoading: false,
            isAuthenticated: false,
            user: null 
          })
          throw error
        }
      },

      register: async (data: RegisterRequest) => {
        try {
          set({ isLoading: true, error: null })
          
          const response = await apiService.register(data)
          const { user, accessToken, refreshToken } = response.data
          
          // Tokens são salvos automaticamente pelo apiService
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          })
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Erro ao criar conta'
          set({ 
            error: errorMessage, 
            isLoading: false,
            isAuthenticated: false,
            user: null 
          })
          throw error
        }
      },

      logout: async () => {
        try {
          await apiService.logout()
        } catch (error) {
          // Mesmo se der erro na API, vamos limpar o estado local
          console.error('Erro ao fazer logout:', error)
        } finally {
          set({ 
            user: null, 
            isAuthenticated: false, 
            error: null,
            isLoading: false 
          })
        }
      },

      refreshUser: async () => {
        try {
          set({ isLoading: true })
          
          const response = await apiService.getMe()
          const user = response.data.data
          
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          })
        } catch (error: any) {
          // Se não conseguir buscar o usuário, provavelmente o token expirou
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null 
          })
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
