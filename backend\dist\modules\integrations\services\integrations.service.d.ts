import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { Integration, TriggerEvent } from '../../../database/entities/integration.entity';
import { WebhookLog } from '../../../database/entities/webhook-log.entity';
import { CreateIntegrationDto } from '../dto/create-integration.dto';
import { UpdateIntegrationDto } from '../dto/update-integration.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
export interface FindIntegrationsOptions {
    companyId?: string;
    type?: string;
    status?: string;
    active?: boolean;
    triggerEvent?: string;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}
export interface FindWebhookLogsOptions {
    integrationId?: string;
    companyId?: string;
    status?: string;
    eventType?: string;
    dateFrom?: Date;
    dateTo?: Date;
    page?: number;
    limit?: number;
}
export declare class IntegrationsService {
    private integrationRepository;
    private webhookLogRepository;
    private httpService;
    private readonly logger;
    constructor(integrationRepository: Repository<Integration>, webhookLogRepository: Repository<WebhookLog>, httpService: HttpService);
    create(createIntegrationDto: CreateIntegrationDto, currentUser: AuthenticatedUser): Promise<Integration>;
    findAll(options: FindIntegrationsOptions, currentUser: AuthenticatedUser): Promise<{
        integrations: Integration[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<Integration>;
    update(id: string, updateIntegrationDto: UpdateIntegrationDto, currentUser: AuthenticatedUser): Promise<Integration>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    testIntegration(id: string, currentUser: AuthenticatedUser): Promise<any>;
    triggerIntegration(event: TriggerEvent, data: any, companyId: string): Promise<void>;
    executeIntegration(integration: Integration, event: string, data: any): Promise<any>;
    private executeWebhook;
    private executeGoogleSheets;
    private executeSlack;
    getWebhookLogs(options: FindWebhookLogsOptions, currentUser: AuthenticatedUser): Promise<{
        logs: WebhookLog[];
        total: number;
        page: number;
        limit: number;
    }>;
    retryWebhook(logId: string, currentUser: AuthenticatedUser): Promise<WebhookLog>;
    private validateIntegrationConfig;
    getIntegrationStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        inactive: number;
        error: number;
        totalExecutions: number;
        successRate: number;
    }>;
}
