"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const contacts_service_1 = require("../services/contacts.service");
const create_contact_dto_1 = require("../dto/create-contact.dto");
const update_contact_dto_1 = require("../dto/update-contact.dto");
const contact_response_dto_1 = require("../dto/contact-response.dto");
const current_user_decorator_1 = require("../../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../common/guards/roles.guard");
const tenant_guard_1 = require("../../../common/guards/tenant.guard");
const permissions_decorator_1 = require("../../../common/decorators/permissions.decorator");
const entities_1 = require("../../../database/entities");
let ContactsController = class ContactsController {
    contactsService;
    constructor(contactsService) {
        this.contactsService = contactsService;
    }
    async create(createContactDto, currentUser) {
        const contact = await this.contactsService.create(createContactDto, currentUser);
        return (0, class_transformer_1.plainToClass)(contact_response_dto_1.ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
    }
    async findAll(query, currentUser) {
        const result = await this.contactsService.findAll(query, currentUser);
        return {
            ...result,
            contacts: result.contacts.map(contact => (0, class_transformer_1.plainToClass)(contact_response_dto_1.ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true })),
        };
    }
    async getStats(currentUser) {
        return this.contactsService.getContactStats(currentUser);
    }
    async findOne(id, currentUser) {
        const contact = await this.contactsService.findOne(id, currentUser);
        return (0, class_transformer_1.plainToClass)(contact_response_dto_1.ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
    }
    async update(id, updateContactDto, currentUser) {
        const contact = await this.contactsService.update(id, updateContactDto, currentUser);
        return (0, class_transformer_1.plainToClass)(contact_response_dto_1.ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
    }
    async remove(id, currentUser) {
        return this.contactsService.remove(id, currentUser);
    }
    async addTag(id, tagData, currentUser) {
        const contact = await this.contactsService.addTag(id, tagData.name, tagData.color, currentUser);
        return (0, class_transformer_1.plainToClass)(contact_response_dto_1.ContactResponseDto, contact.toObject(), { excludeExtraneousValues: true });
    }
    async removeTag(id, tagId, currentUser) {
        await this.contactsService.removeTag(id, tagId, currentUser);
    }
};
exports.ContactsController = ContactsController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_SEND),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo contato' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Contato criado com sucesso',
        type: contact_response_dto_1.ContactResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Contato já existe para este número',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_contact_dto_1.CreateContactDto, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar contatos' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'whatsappConnectionId', required: false, description: 'Filtrar por conexão WhatsApp' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: entities_1.ContactStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'isLead', required: false, type: Boolean, description: 'Filtrar por leads' }),
    (0, swagger_1.ApiQuery)({ name: 'assignedTo', required: false, description: 'Filtrar por responsável' }),
    (0, swagger_1.ApiQuery)({ name: 'tags', required: false, type: [String], description: 'Filtrar por etiquetas' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar por nome, telefone ou email' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Campo para ordenação' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de contatos',
        type: [contact_response_dto_1.ContactResponseDto],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter estatísticas de contatos' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Estatísticas de contatos',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                leads: { type: 'number' },
                blocked: { type: 'number' },
                archived: { type: 'number' },
            },
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter contato por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do contato',
        type: contact_response_dto_1.ContactResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Contato não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_SEND),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar contato' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contato atualizado com sucesso',
        type: contact_response_dto_1.ContactResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Contato não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_contact_dto_1.UpdateContactDto, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_DELETE),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar contato' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Contato deletado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Contato não encontrado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/tags'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_SEND),
    (0, swagger_1.ApiOperation)({ summary: 'Adicionar etiqueta ao contato' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Etiqueta adicionada com sucesso',
        type: contact_response_dto_1.ContactResponseDto,
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "addTag", null);
__decorate([
    (0, common_1.Delete)(':id/tags/:tagId'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_SEND),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Remover etiqueta do contato' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Etiqueta removida com sucesso',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('tagId')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ContactsController.prototype, "removeTag", null);
exports.ContactsController = ContactsController = __decorate([
    (0, swagger_1.ApiTags)('Contatos'),
    (0, common_1.Controller)('contacts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [contacts_service_1.ContactsService])
], ContactsController);
//# sourceMappingURL=contacts.controller.js.map