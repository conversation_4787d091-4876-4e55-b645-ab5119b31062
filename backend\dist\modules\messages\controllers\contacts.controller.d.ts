import { ContactsService, FindContactsOptions } from '../services/contacts.service';
import { CreateContactDto } from '../dto/create-contact.dto';
import { UpdateContactDto } from '../dto/update-contact.dto';
import { ContactResponseDto } from '../dto/contact-response.dto';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
export declare class ContactsController {
    private readonly contactsService;
    constructor(contactsService: ContactsService);
    create(createContactDto: CreateContactDto, currentUser: AuthenticatedUser): Promise<ContactResponseDto>;
    findAll(query: FindContactsOptions, currentUser: AuthenticatedUser): Promise<{
        contacts: ContactResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    getStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        leads: number;
        blocked: number;
        archived: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<ContactResponseDto>;
    update(id: string, updateContactDto: UpdateContactDto, currentUser: AuthenticatedUser): Promise<ContactResponseDto>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    addTag(id: string, tagData: {
        name: string;
        color: string;
    }, currentUser: AuthenticatedUser): Promise<ContactResponseDto>;
    removeTag(id: string, tagId: string, currentUser: AuthenticatedUser): Promise<void>;
}
