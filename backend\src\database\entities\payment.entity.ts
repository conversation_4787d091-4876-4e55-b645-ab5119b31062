import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.entity';
import { Subscription } from './subscription.entity';
import { Invoice } from './invoice.entity';

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  PIX = 'pix',
  BOLETO = 'boleto',
  PAYPAL = 'paypal',
  WALLET = 'wallet',
  CRYPTO = 'crypto',
}

export enum PaymentGateway {
  STRIPE = 'stripe',
  MERCADO_PAGO = 'mercado_pago',
  PAGSEGURO = 'pagseguro',
  PAYPAL = 'paypal',
  ASAAS = 'asaas',
  GERENCIANET = 'gerencianet',
  MANUAL = 'manual',
}

export interface PaymentMethodDetails {
  type: PaymentMethod;
  brand?: string; // Visa, Mastercard, etc.
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName?: string;
  bankName?: string;
  accountType?: string;
  pixKey?: string;
  walletProvider?: string;
}

export interface RefundDetails {
  refundId: string;
  amount: number;
  reason: string;
  refundedAt: Date;
  gatewayRefundId?: string;
}

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  paymentNumber: string;

  @Column()
  companyId: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column({ nullable: true })
  subscriptionId: string;

  @ManyToOne(() => Subscription, subscription => subscription.payments)
  @JoinColumn({ name: 'subscriptionId' })
  subscription: Subscription;

  @Column({ nullable: true })
  invoiceId: string;

  @ManyToOne(() => Invoice, invoice => invoice.payments)
  @JoinColumn({ name: 'invoiceId' })
  invoice: Invoice;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentGateway,
  })
  gateway: PaymentGateway;

  // Valores monetários
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  feeAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  netAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  refundedAmount: number;

  @Column({ type: 'varchar', length: 3, default: 'BRL' })
  currency: string;

  // Detalhes do método de pagamento
  @Column({ type: 'json', nullable: true })
  paymentMethodDetails: PaymentMethodDetails;

  // Datas importantes
  @Column({ nullable: true })
  processedAt: Date;

  @Column({ nullable: true })
  failedAt: Date;

  @Column({ nullable: true })
  refundedAt: Date;

  @Column({ nullable: true })
  dueDate: Date;

  // Informações do gateway
  @Column({ nullable: true })
  gatewayTransactionId: string;

  @Column({ nullable: true })
  gatewayPaymentId: string;

  @Column({ nullable: true })
  gatewayCustomerId: string;

  @Column({ nullable: true })
  gatewayPaymentMethodId: string;

  // Detalhes de falha
  @Column({ nullable: true })
  failureCode: string | null;

  @Column({ nullable: true })
  failureMessage: string | null;

  @Column({ default: 0 })
  attemptCount: number;

  @Column({ nullable: true })
  nextAttemptDate: Date | null;

  // Detalhes de reembolso
  @Column({ type: 'json', nullable: true })
  refunds: RefundDetails[];

  // Informações adicionais
  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  // Configurações de notificação
  @Column({ default: false })
  emailSent: boolean;

  @Column({ nullable: true })
  emailSentAt: Date;

  // Informações de reconciliação
  @Column({ default: false })
  isReconciled: boolean;

  @Column({ nullable: true })
  reconciledAt: Date;

  @Column({ nullable: true })
  reconciledBy: string;

  // Informações fiscais
  @Column({ nullable: true })
  taxDocumentNumber: string;

  @Column({ nullable: true })
  taxDocumentUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Métodos auxiliares
  isSucceeded(): boolean {
    return this.status === PaymentStatus.SUCCEEDED;
  }

  isFailed(): boolean {
    return this.status === PaymentStatus.FAILED;
  }

  isPending(): boolean {
    return this.status === PaymentStatus.PENDING;
  }

  isProcessing(): boolean {
    return this.status === PaymentStatus.PROCESSING;
  }

  isRefunded(): boolean {
    return this.status === PaymentStatus.REFUNDED || this.status === PaymentStatus.PARTIALLY_REFUNDED;
  }

  canBeRefunded(): boolean {
    return this.isSucceeded() && this.refundedAmount < this.amount;
  }

  canBeRetried(): boolean {
    return this.isFailed() && this.attemptCount < 3;
  }

  getRemainingRefundableAmount(): number {
    return Math.max(0, this.amount - this.refundedAmount);
  }

  getRefundProgress(): number {
    if (this.amount === 0) return 0;
    return (this.refundedAmount / this.amount) * 100;
  }

  markAsSucceeded(gatewayTransactionId?: string): void {
    this.status = PaymentStatus.SUCCEEDED;
    this.processedAt = new Date();
    this.netAmount = this.amount - this.feeAmount;
    
    if (gatewayTransactionId) {
      this.gatewayTransactionId = gatewayTransactionId;
    }
  }

  markAsFailed(failureCode?: string, failureMessage?: string): void {
    this.status = PaymentStatus.FAILED;
    this.failedAt = new Date();
    this.failureCode = failureCode || null;
    this.failureMessage = failureMessage || null;
    this.attemptCount += 1;
    
    // Agendar próxima tentativa se possível
    if (this.canBeRetried()) {
      const delayHours = Math.pow(2, this.attemptCount) * 24; // Backoff exponencial
      this.nextAttemptDate = new Date(Date.now() + delayHours * 60 * 60 * 1000);
    }
  }

  addRefund(amount: number, reason: string, gatewayRefundId?: string): void {
    const refund: RefundDetails = {
      refundId: `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      amount,
      reason,
      refundedAt: new Date(),
      gatewayRefundId,
    };

    this.refunds = [...(this.refunds || []), refund];
    this.refundedAmount += amount;
    this.refundedAt = new Date();

    if (this.refundedAmount >= this.amount) {
      this.status = PaymentStatus.REFUNDED;
    } else {
      this.status = PaymentStatus.PARTIALLY_REFUNDED;
    }
  }

  retry(): void {
    if (this.canBeRetried()) {
      this.status = PaymentStatus.PENDING;
      this.failureCode = null;
      this.failureMessage = null;
      this.nextAttemptDate = null;
    }
  }

  cancel(): void {
    if (this.isPending() || this.isProcessing()) {
      this.status = PaymentStatus.CANCELLED;
    }
  }

  reconcile(reconciledBy: string): void {
    this.isReconciled = true;
    this.reconciledAt = new Date();
    this.reconciledBy = reconciledBy;
  }

  generatePaymentNumber(): string {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    return `PAY-${year}${month}-${timestamp}`;
  }

  getPaymentMethodDisplay(): string {
    const details = this.paymentMethodDetails;
    if (!details) return this.paymentMethod;

    switch (details.type) {
      case PaymentMethod.CREDIT_CARD:
      case PaymentMethod.DEBIT_CARD:
        return `${details.brand} **** ${details.last4}`;
      case PaymentMethod.PIX:
        return `PIX ${details.pixKey ? `(${details.pixKey})` : ''}`;
      case PaymentMethod.BANK_TRANSFER:
        return `Transferência ${details.bankName || ''}`;
      case PaymentMethod.BOLETO:
        return 'Boleto Bancário';
      default:
        return this.paymentMethod;
    }
  }

  getDaysOverdue(): number {
    if (!this.dueDate || this.isSucceeded()) return 0;
    const now = new Date();
    if (now <= this.dueDate) return 0;
    const diffTime = now.getTime() - this.dueDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
