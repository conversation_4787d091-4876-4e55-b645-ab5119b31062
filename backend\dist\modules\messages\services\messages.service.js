"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MessagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const entities_1 = require("../../../database/entities");
const contacts_service_1 = require("./contacts.service");
let MessagesService = MessagesService_1 = class MessagesService {
    messageModel;
    contactsService;
    logger = new common_1.Logger(MessagesService_1.name);
    constructor(messageModel, contactsService) {
        this.messageModel = messageModel;
        this.contactsService = contactsService;
    }
    async create(messageData, companyId) {
        const existingMessage = await this.messageModel.findOne({
            messageId: messageData.messageId,
        });
        if (existingMessage) {
            this.logger.warn(`Message already exists: ${messageData.messageId}`);
            return existingMessage;
        }
        await this.contactsService.getOrCreateContact(messageData.contactPhone, messageData.whatsappConnectionId, companyId);
        await this.contactsService.updateLastMessage(messageData.contactPhone, messageData.whatsappConnectionId, companyId);
        let responseTime;
        if (messageData.direction === entities_1.MessageDirection.OUTBOUND && !messageData.isAutomated) {
            const lastInboundMessage = await this.messageModel
                .findOne({
                contactPhone: messageData.contactPhone,
                whatsappConnectionId: messageData.whatsappConnectionId,
                direction: entities_1.MessageDirection.INBOUND,
                companyId: new mongoose_2.Types.ObjectId(companyId),
            })
                .sort({ timestamp: -1 });
            if (lastInboundMessage) {
                responseTime = Math.floor((messageData.timestamp.getTime() - lastInboundMessage.timestamp.getTime()) / 1000);
            }
        }
        const isFirstMessage = await this.isFirstMessageFromContact(messageData.contactPhone, messageData.whatsappConnectionId, companyId);
        const message = new this.messageModel({
            ...messageData,
            companyId: new mongoose_2.Types.ObjectId(companyId),
            status: entities_1.MessageStatus.PENDING,
            responseTime,
            isFirstMessage,
        });
        const savedMessage = await message.save();
        this.logger.log(`Message created: ${savedMessage._id} - ${messageData.messageId}`);
        return savedMessage;
    }
    async findAll(options, currentUser) {
        const { companyId, whatsappConnectionId, contactPhone, conversationId, direction, type, status, isAutomated, sentBy, dateFrom, dateTo, search, page = 1, limit = 50, sortBy = 'timestamp', sortOrder = 'desc', } = options;
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        };
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            filter.companyId = new mongoose_2.Types.ObjectId(companyId);
        }
        if (whatsappConnectionId) {
            filter.whatsappConnectionId = whatsappConnectionId;
        }
        if (contactPhone) {
            filter.contactPhone = contactPhone;
        }
        if (conversationId) {
            filter.conversationId = conversationId;
        }
        if (direction) {
            filter.direction = direction;
        }
        if (type) {
            filter.type = type;
        }
        if (status) {
            filter.status = status;
        }
        if (typeof isAutomated === 'boolean') {
            filter.isAutomated = isAutomated;
        }
        if (sentBy) {
            filter.sentBy = sentBy;
        }
        if (dateFrom || dateTo) {
            filter.timestamp = {};
            if (dateFrom)
                filter.timestamp.$gte = dateFrom;
            if (dateTo)
                filter.timestamp.$lte = dateTo;
        }
        if (search) {
            filter.$or = [
                { content: { $regex: search, $options: 'i' } },
                { contactPhone: { $regex: search, $options: 'i' } },
                { 'media.caption': { $regex: search, $options: 'i' } },
            ];
        }
        const skip = (page - 1) * limit;
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const [messages, total] = await Promise.all([
            this.messageModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.messageModel.countDocuments(filter),
        ]);
        return {
            messages,
            total,
            page,
            limit,
        };
    }
    async findOne(id, currentUser) {
        const message = await this.messageModel.findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
        if (!message) {
            throw new common_1.NotFoundException('Mensagem não encontrada');
        }
        return message;
    }
    async findByMessageId(messageId) {
        return this.messageModel.findOne({ messageId });
    }
    async updateStatus(messageId, status, timestamp) {
        const updateData = { status };
        switch (status) {
            case entities_1.MessageStatus.SENT:
                updateData.sentAt = timestamp || new Date();
                break;
            case entities_1.MessageStatus.DELIVERED:
                updateData.deliveredAt = timestamp || new Date();
                break;
            case entities_1.MessageStatus.READ:
                updateData.readAt = timestamp || new Date();
                break;
            case entities_1.MessageStatus.FAILED:
                updateData.errorMessage = 'Falha na entrega';
                break;
        }
        await this.messageModel.updateOne({ messageId }, updateData);
        this.logger.log(`Message status updated: ${messageId} -> ${status}`);
    }
    async getConversation(contactPhone, whatsappConnectionId, currentUser, limit = 50) {
        return this.messageModel
            .find({
            contactPhone,
            whatsappConnectionId,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        })
            .sort({ timestamp: -1 })
            .limit(limit)
            .exec();
    }
    async getMessageStats(currentUser, dateFrom, dateTo) {
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        };
        if (dateFrom || dateTo) {
            filter.timestamp = {};
            if (dateFrom)
                filter.timestamp.$gte = dateFrom;
            if (dateTo)
                filter.timestamp.$lte = dateTo;
        }
        const [total, sent, received, automated, failed] = await Promise.all([
            this.messageModel.countDocuments(filter),
            this.messageModel.countDocuments({ ...filter, direction: entities_1.MessageDirection.OUTBOUND }),
            this.messageModel.countDocuments({ ...filter, direction: entities_1.MessageDirection.INBOUND }),
            this.messageModel.countDocuments({ ...filter, isAutomated: true }),
            this.messageModel.countDocuments({ ...filter, status: entities_1.MessageStatus.FAILED }),
        ]);
        return { total, sent, received, automated, failed };
    }
    async deleteMessage(id, currentUser) {
        const message = await this.findOne(id, currentUser);
        await this.messageModel.findByIdAndDelete(message._id);
        this.logger.log(`Message deleted: ${id}`);
    }
    async isFirstMessageFromContact(contactPhone, whatsappConnectionId, companyId) {
        const messageCount = await this.messageModel.countDocuments({
            contactPhone,
            whatsappConnectionId,
            companyId: new mongoose_2.Types.ObjectId(companyId),
        });
        return messageCount === 0;
    }
};
exports.MessagesService = MessagesService;
exports.MessagesService = MessagesService = MessagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(entities_1.Message.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        contacts_service_1.ContactsService])
], MessagesService);
//# sourceMappingURL=messages.service.js.map