"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const analytics_service_1 = require("./services/analytics.service");
const tracking_service_1 = require("./services/tracking.service");
const analytics_query_dto_1 = require("./dto/analytics-query.dto");
const analytics_response_dto_1 = require("./dto/analytics-response.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const tenant_guard_1 = require("../../common/guards/tenant.guard");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const entities_1 = require("../../database/entities");
const analytics_event_schema_1 = require("../../database/entities/analytics-event.schema");
let AnalyticsController = class AnalyticsController {
    analyticsService;
    trackingService;
    constructor(analyticsService, trackingService) {
        this.analyticsService = analyticsService;
        this.trackingService = trackingService;
    }
    async getDashboard(query, currentUser) {
        const metrics = await this.analyticsService.getDashboardMetrics(query, currentUser);
        return (0, class_transformer_1.plainToClass)(analytics_response_dto_1.DashboardMetrics, metrics, { excludeExtraneousValues: true });
    }
    async getConversationAnalytics(query, currentUser) {
        const analytics = await this.analyticsService.getConversationAnalytics(query, currentUser);
        return (0, class_transformer_1.plainToClass)(analytics_response_dto_1.ConversationAnalytics, analytics, { excludeExtraneousValues: true });
    }
    async getUserActivityAnalytics(query, currentUser) {
        const analytics = await this.analyticsService.getUserActivityAnalytics(query, currentUser);
        return (0, class_transformer_1.plainToClass)(analytics_response_dto_1.UserActivityAnalytics, analytics, { excludeExtraneousValues: true });
    }
    async getPerformanceAnalytics(query, currentUser) {
        const analytics = await this.analyticsService.getPerformanceAnalytics(query, currentUser);
        return (0, class_transformer_1.plainToClass)(analytics_response_dto_1.PerformanceAnalytics, analytics, { excludeExtraneousValues: true });
    }
    async getCustomAnalytics(query, currentUser) {
        const metrics = await this.analyticsService.getCustomAnalytics(query, currentUser);
        return metrics.map(metric => (0, class_transformer_1.plainToClass)(analytics_response_dto_1.AnalyticsMetric, metric, { excludeExtraneousValues: true }));
    }
    async getFullReport(query, currentUser) {
        const [dashboard, conversations, userActivity, performance] = await Promise.all([
            this.analyticsService.getDashboardMetrics(query, currentUser),
            this.analyticsService.getConversationAnalytics(query, currentUser),
            this.analyticsService.getUserActivityAnalytics(query, currentUser),
            this.analyticsService.getPerformanceAnalytics(query, currentUser),
        ]);
        const report = {
            dashboard,
            conversations,
            userActivity,
            performance,
            period: {
                start: query.startDate || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
                end: query.endDate || new Date().toISOString(),
            },
            generatedAt: new Date().toISOString(),
        };
        return (0, class_transformer_1.plainToClass)(analytics_response_dto_1.AnalyticsReportResponse, report, { excludeExtraneousValues: true });
    }
    async exportReport(query, currentUser, res) {
        const format = query.format || 'json';
        const report = await this.getFullReport(query, currentUser);
        switch (format) {
            case 'csv':
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.csv');
                res.send(this.convertToCSV(report));
                break;
            case 'excel':
                res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.xlsx');
                res.send(this.convertToExcel(report));
                break;
            default:
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Content-Disposition', 'attachment; filename=analytics-report.json');
                res.json(report);
        }
    }
    async trackCustomEvent(eventData, currentUser) {
        await this.analyticsService.trackEvent(eventData.type, eventData.category, currentUser.companyId, eventData.properties || {}, currentUser.id);
    }
    async getAvailableEvents() {
        return {
            eventTypes: Object.values(analytics_event_schema_1.EventType),
            categories: Object.values(analytics_event_schema_1.EventCategory),
        };
    }
    convertToCSV(data) {
        const headers = ['Metric', 'Value', 'Previous Value', 'Change %'];
        const rows = [
            ['Messages Sent', data.dashboard.messagesSent.value, data.dashboard.messagesSent.previousValue || 0, data.dashboard.messagesSent.changePercent || 0],
            ['Messages Received', data.dashboard.messagesReceived.value, data.dashboard.messagesReceived.previousValue || 0, data.dashboard.messagesReceived.changePercent || 0],
        ];
        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
        return csvContent;
    }
    convertToExcel(data) {
        return Buffer.from(JSON.stringify(data, null, 2));
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('dashboard'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter métricas do dashboard' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, description: 'Período de tempo' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Data de início' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Data de fim' }),
    (0, swagger_1.ApiQuery)({ name: 'connectionIds', required: false, type: [String], description: 'IDs das conexões' }),
    (0, swagger_1.ApiQuery)({ name: 'compareWithPrevious', required: false, type: Boolean, description: 'Comparar com período anterior' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Métricas do dashboard',
        type: analytics_response_dto_1.DashboardMetrics,
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.DashboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('conversations'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter analytics de conversas' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, description: 'Período de tempo' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Data de início' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Data de fim' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics de conversas',
        type: analytics_response_dto_1.ConversationAnalytics,
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.DashboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getConversationAnalytics", null);
__decorate([
    (0, common_1.Get)('user-activity'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter analytics de atividade de usuários' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, description: 'Período de tempo' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Data de início' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Data de fim' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics de atividade de usuários',
        type: analytics_response_dto_1.UserActivityAnalytics,
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.DashboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getUserActivityAnalytics", null);
__decorate([
    (0, common_1.Get)('performance'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter analytics de performance' }),
    (0, swagger_1.ApiQuery)({ name: 'timeRange', required: false, description: 'Período de tempo' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Data de início' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Data de fim' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics de performance',
        type: analytics_response_dto_1.PerformanceAnalytics,
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.DashboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getPerformanceAnalytics", null);
__decorate([
    (0, common_1.Get)('custom'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter analytics customizados' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Métricas customizadas',
        type: [analytics_response_dto_1.AnalyticsMetric],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.AnalyticsQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getCustomAnalytics", null);
__decorate([
    (0, common_1.Get)('report'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_EXPORT),
    (0, swagger_1.ApiOperation)({ summary: 'Gerar relatório completo' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Relatório completo de analytics',
        type: analytics_response_dto_1.AnalyticsReportResponse,
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.DashboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getFullReport", null);
__decorate([
    (0, common_1.Get)('export'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_EXPORT),
    (0, swagger_1.ApiOperation)({ summary: 'Exportar relatório em diferentes formatos' }),
    (0, swagger_1.ApiQuery)({ name: 'format', required: false, enum: ['json', 'csv', 'excel'], description: 'Formato do export' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Arquivo de relatório',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_query_dto_1.ReportQueryDto, Object, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "exportReport", null);
__decorate([
    (0, common_1.Post)('track'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Rastrear evento customizado' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Evento rastreado com sucesso',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "trackCustomEvent", null);
__decorate([
    (0, common_1.Get)('events'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ADMIN_FULL_ACCESS),
    (0, swagger_1.ApiOperation)({ summary: 'Listar eventos disponíveis (Admin)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de tipos de eventos e categorias',
        schema: {
            type: 'object',
            properties: {
                eventTypes: { type: 'array', items: { type: 'string' } },
                categories: { type: 'array', items: { type: 'string' } },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAvailableEvents", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService,
        tracking_service_1.TrackingService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map