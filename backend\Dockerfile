# Multi-stage build para otimizar o tamanho da imagem

# Stage 1: Build
FROM node:18-alpine AS builder

# Instalar dependências do sistema
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# Definir diretório de trabalho
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./
COPY tsconfig*.json ./

# Instalar dependências
RUN npm ci --only=production && npm cache clean --force

# Copiar código fonte
COPY src/ ./src/

# Build da aplicação
RUN npm run build

# Stage 2: Production
FROM node:18-alpine AS production

# Instalar dependências do sistema para produção
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata

# Criar usuário não-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Definir diretório de trabalho
WORKDIR /app

# Copiar dependências do stage de build
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules

# Copiar aplicação buildada
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist

# Copiar arquivos necessários
COPY --chown=nestjs:nodejs package*.json ./

# Definir timezone
ENV TZ=America/Sao_Paulo

# Definir variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=3000

# Expor porta
EXPOSE 3000

# Mudar para usuário não-root
USER nestjs

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Comando para iniciar a aplicação
CMD ["dumb-init", "node", "dist/main"]
