"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const app_simple_module_1 = require("./app.simple.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_simple_module_1.AppSimpleModule);
    const configService = app.get(config_1.ConfigService);
    app.enableCors({
        origin: configService.get('cors.origin'),
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    const apiPrefix = configService.get('api.prefix');
    if (apiPrefix) {
        app.setGlobalPrefix(apiPrefix);
    }
    const config = new swagger_1.DocumentBuilder()
        .setTitle('WhatsApp Platform API')
        .setDescription('API para plataforma de WhatsApp Business')
        .setVersion('1.0')
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document);
    const port = configService.get('port') || 3000;
    await app.listen(port);
    console.log(`🚀 Aplicação rodando em: http://localhost:${port}`);
    console.log(`📚 Documentação da API: http://localhost:${port}/api/docs`);
}
bootstrap();
//# sourceMappingURL=main.simple.js.map