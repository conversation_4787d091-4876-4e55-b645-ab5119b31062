"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const users_service_1 = require("./users.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const user_response_dto_1 = require("./dto/user-response.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const tenant_guard_1 = require("../../common/guards/tenant.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const entities_1 = require("../../database/entities");
let UsersController = class UsersController {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    async create(createUserDto, currentUser) {
        const user = await this.usersService.create(createUserDto, currentUser);
        return (0, class_transformer_1.plainToClass)(user_response_dto_1.UserResponseDto, user, { excludeExtraneousValues: true });
    }
    async findAll(query, currentUser) {
        const result = await this.usersService.findAll(query, currentUser);
        return {
            ...result,
            users: result.users.map(user => (0, class_transformer_1.plainToClass)(user_response_dto_1.UserResponseDto, user, { excludeExtraneousValues: true })),
        };
    }
    async findOne(id, currentUser) {
        const user = await this.usersService.findOne(id, currentUser);
        return (0, class_transformer_1.plainToClass)(user_response_dto_1.UserResponseDto, user, { excludeExtraneousValues: true });
    }
    async update(id, updateUserDto, currentUser) {
        const user = await this.usersService.update(id, updateUserDto, currentUser);
        return (0, class_transformer_1.plainToClass)(user_response_dto_1.UserResponseDto, user, { excludeExtraneousValues: true });
    }
    async remove(id, currentUser) {
        return this.usersService.remove(id, currentUser);
    }
    async changePassword(id, newPassword, currentUser) {
        return this.usersService.changePassword(id, newPassword, currentUser);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Usuário criado com sucesso',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Email já está em uso',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar usuários' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'role', required: false, enum: entities_1.UserRole, description: 'Filtrar por papel' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: entities_1.UserStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar por nome ou email' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de usuários',
        type: [user_response_dto_1.UserResponseDto],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Obter usuário por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do usuário',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Usuário não encontrado',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário atualizado com sucesso',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Usuário não encontrado',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Usuário deletado com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Usuário não encontrado',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/change-password'),
    (0, roles_decorator_1.Roles)(entities_1.UserRole.SUPER_ADMIN, entities_1.UserRole.AGENCY_ADMIN, entities_1.UserRole.COMPANY_ADMIN),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Alterar senha do usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Senha alterada com sucesso',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)('newPassword')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "changePassword", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Usuários'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map