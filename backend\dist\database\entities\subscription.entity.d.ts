import { Company } from './company.entity';
import { Plan } from './plan.entity';
import { Invoice } from './invoice.entity';
import { Payment } from './payment.entity';
export declare enum SubscriptionStatus {
    TRIAL = "trial",
    ACTIVE = "active",
    PAST_DUE = "past_due",
    CANCELLED = "cancelled",
    EXPIRED = "expired",
    SUSPENDED = "suspended",
    PENDING = "pending"
}
export declare enum SubscriptionType {
    DIRECT = "direct",
    AGENCY = "agency",
    RESELLER = "reseller"
}
export interface UsageMetrics {
    connections: number;
    contacts: number;
    messages: number;
    users: number;
    automations: number;
    chatbots: number;
    storageUsedGB: number;
    apiCalls: number;
    webhookCalls: number;
    lastUpdated: Date;
}
export declare class Subscription {
    id: string;
    companyId: string;
    company: Company;
    planId: string;
    plan: Plan;
    status: SubscriptionStatus;
    type: SubscriptionType;
    startDate: Date;
    endDate: Date;
    trialEndDate: Date;
    cancelledAt: Date;
    cancelReason: string | null;
    cancelAtPeriodEnd: boolean;
    price: number;
    setupFee: number;
    discountPercentage: number;
    discountAmount: number;
    discountEndDate: Date;
    couponCode: string;
    agencyId: string;
    agencyCommissionPercentage: number;
    agencyFixedCommission: number;
    currentUsage: UsageMetrics;
    usageHistory: Record<string, UsageMetrics>;
    stripeSubscriptionId: string;
    stripeCustomerId: string;
    paymentMethodId: string;
    autoRenew: boolean;
    nextBillingDate: Date;
    lastBillingDate: Date;
    pendingPlanId: string;
    planChangeDate: Date;
    isProrated: boolean;
    metadata: Record<string, any>;
    customFeatures: Record<string, any>;
    emailNotifications: boolean;
    webhookNotifications: boolean;
    webhookUrl: string;
    createdAt: Date;
    updatedAt: Date;
    invoices: Invoice[];
    payments: Payment[];
    isActive(): boolean;
    isTrial(): boolean;
    isExpired(): boolean;
    isCancelled(): boolean;
    isPastDue(): boolean;
    daysUntilExpiry(): number;
    daysInTrial(): number;
    getCurrentPrice(): number;
    getAgencyCommission(): number;
    hasExceededLimit(feature: string, currentValue: number, planLimit: number): boolean;
    getUsagePercentage(feature: string, currentValue: number, planLimit: number): number;
    canUpgrade(): boolean;
    canDowngrade(): boolean;
    canCancel(): boolean;
    shouldRenew(): boolean;
}
