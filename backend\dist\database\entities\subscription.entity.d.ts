import { BaseEntity } from './base.entity';
import { Company } from './company.entity';
export declare enum SubscriptionStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    PAST_DUE = "past_due",
    CANCELED = "canceled",
    TRIALING = "trialing"
}
export declare enum SubscriptionPlan {
    BASIC = "basic",
    PROFESSIONAL = "professional",
    ENTERPRISE = "enterprise"
}
export declare class Subscription extends BaseEntity {
    plan: SubscriptionPlan;
    status: SubscriptionStatus;
    basePrice: number;
    pricePerUser: number;
    includedUsers: number;
    additionalUsers: number;
    totalAmount: number;
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    trialStart: Date;
    trialEnd: Date;
    canceledAt: Date;
    cancelAtPeriodEnd: boolean;
    stripeSubscriptionId: string;
    stripeCustomerId: string;
    metadata: Record<string, any>;
    companyId: string;
    company: Company;
    get isActive(): boolean;
    get isTrialing(): boolean;
    get isPastDue(): boolean;
    get isCanceled(): boolean;
    get daysUntilExpiry(): number;
    get isExpired(): boolean;
    calculateTotalAmount(activeUsers: number): number;
    updatePricing(activeUsers: number): void;
    startTrial(days?: number): void;
    activate(): void;
    cancel(immediately?: boolean): void;
}
