"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PERMISSIONS = exports.RequirePermissions = exports.PERMISSIONS_KEY = void 0;
const common_1 = require("@nestjs/common");
exports.PERMISSIONS_KEY = 'permissions';
const RequirePermissions = (...permissions) => (0, common_1.SetMetadata)(exports.PERMISSIONS_KEY, permissions);
exports.RequirePermissions = RequirePermissions;
exports.PERMISSIONS = {
    USERS_CREATE: 'users:create',
    USERS_READ: 'users:read',
    USERS_UPDATE: 'users:update',
    USERS_DELETE: 'users:delete',
    COMPANIES_CREATE: 'companies:create',
    COMPANIES_READ: 'companies:read',
    COMPANIES_UPDATE: 'companies:update',
    COMPANIES_DELETE: 'companies:delete',
    WHATSAPP_CONNECT: 'whatsapp:connect',
    WHATSAPP_DISCONNECT: 'whatsapp:disconnect',
    WHATSAPP_SEND_MESSAGE: 'whatsapp:send_message',
    WHATSAPP_READ_MESSAGES: 'whatsapp:read_messages',
    MESSAGES_READ: 'messages:read',
    MESSAGES_SEND: 'messages:send',
    MESSAGES_DELETE: 'messages:delete',
    ANALYTICS_READ: 'analytics:read',
    ANALYTICS_EXPORT: 'analytics:export',
    AUTOMATION_CREATE: 'automation:create',
    AUTOMATION_READ: 'automation:read',
    AUTOMATION_UPDATE: 'automation:update',
    AUTOMATION_DELETE: 'automation:delete',
    BILLING_READ: 'billing:read',
    BILLING_MANAGE: 'billing:manage',
    INTEGRATIONS_CREATE: 'integrations:create',
    INTEGRATIONS_READ: 'integrations:read',
    INTEGRATIONS_UPDATE: 'integrations:update',
    INTEGRATIONS_DELETE: 'integrations:delete',
    ADMIN_FULL_ACCESS: 'admin:full_access',
};
//# sourceMappingURL=permissions.decorator.js.map