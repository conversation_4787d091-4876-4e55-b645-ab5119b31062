# Script para iniciar os bancos de dados de desenvolvimento

Write-Host "🚀 Iniciando bancos de dados de desenvolvimento..." -ForegroundColor Green

# Verificar se Docker está rodando
try {
    docker version | Out-Null
    Write-Host "✅ Docker está rodando" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker não está rodando. Por favor, inicie o Docker Desktop." -ForegroundColor Red
    exit 1
}

# Iniciar os containers
Write-Host "📦 Iniciando containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d

# Aguardar os containers ficarem saudáveis
Write-Host "⏳ Aguardando containers ficarem prontos..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Verificar status dos containers
Write-Host "🔍 Verificando status dos containers..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml ps

Write-Host "✅ Bancos de dados iniciados com sucesso!" -ForegroundColor Green
Write-Host "📊 PostgreSQL: localhost:5432" -ForegroundColor Cyan
Write-Host "🍃 MongoDB: localhost:27017" -ForegroundColor Cyan
Write-Host "🔴 Redis: localhost:6379" -ForegroundColor Cyan
Write-Host ""
Write-Host "Para parar os bancos, execute: docker-compose -f docker-compose.dev.yml down" -ForegroundColor Yellow
