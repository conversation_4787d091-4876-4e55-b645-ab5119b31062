"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessagesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const messages_service_1 = require("../services/messages.service");
const message_response_dto_1 = require("../dto/message-response.dto");
const current_user_decorator_1 = require("../../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../../common/guards/roles.guard");
const tenant_guard_1 = require("../../../common/guards/tenant.guard");
const permissions_decorator_1 = require("../../../common/decorators/permissions.decorator");
const entities_1 = require("../../../database/entities");
let MessagesController = class MessagesController {
    messagesService;
    constructor(messagesService) {
        this.messagesService = messagesService;
    }
    async findAll(query, currentUser) {
        const result = await this.messagesService.findAll(query, currentUser);
        return {
            ...result,
            messages: result.messages.map(message => (0, class_transformer_1.plainToClass)(message_response_dto_1.MessageResponseDto, message.toObject(), { excludeExtraneousValues: true })),
        };
    }
    async getStats(dateFrom, dateTo, currentUser) {
        return this.messagesService.getMessageStats(currentUser, dateFrom, dateTo);
    }
    async getConversation(contactPhone, whatsappConnectionId, limit, currentUser) {
        const messages = await this.messagesService.getConversation(contactPhone, whatsappConnectionId, currentUser, limit);
        return messages.map(message => (0, class_transformer_1.plainToClass)(message_response_dto_1.MessageResponseDto, message.toObject(), { excludeExtraneousValues: true }));
    }
    async findOne(id, currentUser) {
        const message = await this.messagesService.findOne(id, currentUser);
        return (0, class_transformer_1.plainToClass)(message_response_dto_1.MessageResponseDto, message.toObject(), { excludeExtraneousValues: true });
    }
    async remove(id, currentUser) {
        return this.messagesService.deleteMessage(id, currentUser);
    }
    async updateStatus(messageId, statusData) {
        return this.messagesService.updateStatus(messageId, statusData.status, statusData.timestamp);
    }
};
exports.MessagesController = MessagesController;
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar mensagens' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'whatsappConnectionId', required: false, description: 'Filtrar por conexão WhatsApp' }),
    (0, swagger_1.ApiQuery)({ name: 'contactPhone', required: false, description: 'Filtrar por telefone do contato' }),
    (0, swagger_1.ApiQuery)({ name: 'conversationId', required: false, description: 'Filtrar por conversa' }),
    (0, swagger_1.ApiQuery)({ name: 'direction', required: false, enum: entities_1.MessageDirection, description: 'Filtrar por direção' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, enum: entities_1.MessageType, description: 'Filtrar por tipo' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: entities_1.MessageStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'isAutomated', required: false, type: Boolean, description: 'Filtrar por automatizadas' }),
    (0, swagger_1.ApiQuery)({ name: 'sentBy', required: false, description: 'Filtrar por remetente' }),
    (0, swagger_1.ApiQuery)({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' }),
    (0, swagger_1.ApiQuery)({ name: 'dateTo', required: false, type: Date, description: 'Data final' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar no conteúdo' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, description: 'Campo para ordenação' }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: 'Ordem da ordenação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de mensagens',
        type: [message_response_dto_1.MessageResponseDto],
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter estatísticas de mensagens' }),
    (0, swagger_1.ApiQuery)({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' }),
    (0, swagger_1.ApiQuery)({ name: 'dateTo', required: false, type: Date, description: 'Data final' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Estatísticas de mensagens',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                sent: { type: 'number' },
                received: { type: 'number' },
                automated: { type: 'number' },
                failed: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Query)('dateFrom')),
    __param(1, (0, common_1.Query)('dateTo')),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date,
        Date, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('conversation/:contactPhone/:whatsappConnectionId'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter conversa com contato' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Limite de mensagens' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Mensagens da conversa',
        type: [message_response_dto_1.MessageResponseDto],
    }),
    __param(0, (0, common_1.Param)('contactPhone')),
    __param(1, (0, common_1.Param)('whatsappConnectionId')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "getConversation", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter mensagem por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da mensagem',
        type: message_response_dto_1.MessageResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Mensagem não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_DELETE),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar mensagem' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Mensagem deletada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Mensagem não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':messageId/status'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.MESSAGES_SEND),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar status da mensagem' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Status atualizado com sucesso',
    }),
    __param(0, (0, common_1.Param)('messageId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MessagesController.prototype, "updateStatus", null);
exports.MessagesController = MessagesController = __decorate([
    (0, swagger_1.ApiTags)('Mensagens'),
    (0, common_1.Controller)('messages'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [messages_service_1.MessagesService])
], MessagesController);
//# sourceMappingURL=messages.controller.js.map