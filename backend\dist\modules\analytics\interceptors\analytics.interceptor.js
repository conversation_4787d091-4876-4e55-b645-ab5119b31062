"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AnalyticsInterceptor_1, AuthAnalyticsInterceptor_1, WhatsAppAnalyticsInterceptor_1, ContactAnalyticsInterceptor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactAnalyticsInterceptor = exports.WhatsAppAnalyticsInterceptor = exports.AuthAnalyticsInterceptor = exports.AnalyticsInterceptor = void 0;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const tracking_service_1 = require("../services/tracking.service");
let AnalyticsInterceptor = AnalyticsInterceptor_1 = class AnalyticsInterceptor {
    trackingService;
    logger = new common_1.Logger(AnalyticsInterceptor_1.name);
    constructor(trackingService) {
        this.trackingService = trackingService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const response = context.switchToHttp().getResponse();
        const user = request.user;
        const startTime = Date.now();
        const method = request.method;
        const url = request.url;
        const userAgent = request.headers['user-agent'];
        const ipAddress = request.ip || request.connection.remoteAddress;
        return next.handle().pipe((0, operators_1.tap)(() => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            const statusCode = response.statusCode;
            if (user && user.companyId) {
                this.trackingService.trackApiRequest(user.companyId, url, method, responseTime, statusCode, user.id).catch(error => {
                    this.logger.error(`Failed to track API request: ${error.message}`);
                });
            }
            this.logger.debug(`${method} ${url} - ${statusCode} - ${responseTime}ms`);
        }), (0, operators_1.catchError)((error) => {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            const statusCode = error.status || 500;
            if (user && user.companyId) {
                this.trackingService.trackError(user.companyId, error.code || 'UNKNOWN_ERROR', error.message || 'Unknown error occurred', `${method} ${url}`, user.id).catch(trackingError => {
                    this.logger.error(`Failed to track error: ${trackingError.message}`);
                });
                this.trackingService.trackApiRequest(user.companyId, url, method, responseTime, statusCode, user.id).catch(trackingError => {
                    this.logger.error(`Failed to track API request with error: ${trackingError.message}`);
                });
            }
            this.logger.error(`${method} ${url} - ${statusCode} - ${responseTime}ms - Error: ${error.message}`);
            throw error;
        }));
    }
};
exports.AnalyticsInterceptor = AnalyticsInterceptor;
exports.AnalyticsInterceptor = AnalyticsInterceptor = AnalyticsInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tracking_service_1.TrackingService])
], AnalyticsInterceptor);
let AuthAnalyticsInterceptor = AuthAnalyticsInterceptor_1 = class AuthAnalyticsInterceptor {
    trackingService;
    logger = new common_1.Logger(AuthAnalyticsInterceptor_1.name);
    constructor(trackingService) {
        this.trackingService = trackingService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const url = request.url;
        const method = request.method;
        return next.handle().pipe((0, operators_1.tap)((result) => {
            if (url.includes('/auth/login') && method === 'POST' && result?.user) {
                const sessionId = this.generateSessionId();
                this.trackingService.trackUserLogin(result.user.id, result.user.companyId, sessionId, {
                    userAgent: request.headers['user-agent'],
                    ipAddress: request.ip || request.connection.remoteAddress,
                }).catch(error => {
                    this.logger.error(`Failed to track user login: ${error.message}`);
                });
            }
            if (url.includes('/auth/logout') && method === 'POST') {
                const user = request.user;
                if (user) {
                    this.trackingService.trackUserLogout(user.id, user.companyId, request.sessionId || 'unknown').catch(error => {
                        this.logger.error(`Failed to track user logout: ${error.message}`);
                    });
                }
            }
            if (url.includes('/auth/register') && method === 'POST' && result?.user) {
                this.trackingService.trackUserCreated(result.user.id, result.user.companyId, result.user.id, result.user.role).catch(error => {
                    this.logger.error(`Failed to track user creation: ${error.message}`);
                });
            }
        }));
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.AuthAnalyticsInterceptor = AuthAnalyticsInterceptor;
exports.AuthAnalyticsInterceptor = AuthAnalyticsInterceptor = AuthAnalyticsInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tracking_service_1.TrackingService])
], AuthAnalyticsInterceptor);
let WhatsAppAnalyticsInterceptor = WhatsAppAnalyticsInterceptor_1 = class WhatsAppAnalyticsInterceptor {
    trackingService;
    logger = new common_1.Logger(WhatsAppAnalyticsInterceptor_1.name);
    constructor(trackingService) {
        this.trackingService = trackingService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const url = request.url;
        const method = request.method;
        return next.handle().pipe((0, operators_1.tap)((result) => {
            if (!user)
                return;
            if (url.includes('/whatsapp/connections') && method === 'POST' && result?.id) {
                this.trackingService.trackWhatsAppConnected(result.id, user.companyId, user.id, result.phoneNumber).catch(error => {
                    this.logger.error(`Failed to track WhatsApp connection: ${error.message}`);
                });
            }
            if (url.includes('/connect') && method === 'POST' && result?.qrCode) {
                const connectionId = this.extractConnectionId(url);
                if (connectionId) {
                    this.trackingService.trackQRCodeGenerated(connectionId, user.companyId, user.id).catch(error => {
                        this.logger.error(`Failed to track QR code generation: ${error.message}`);
                    });
                }
            }
            if (url.includes('/disconnect') && method === 'POST') {
                const connectionId = this.extractConnectionId(url);
                if (connectionId) {
                    this.trackingService.trackWhatsAppDisconnected(connectionId, user.companyId, user.id, 'manual_disconnect').catch(error => {
                        this.logger.error(`Failed to track WhatsApp disconnection: ${error.message}`);
                    });
                }
            }
            if (url.includes('/send-message') && method === 'POST') {
                const connectionId = this.extractConnectionId(url);
                const messageData = request.body;
                if (connectionId && messageData) {
                    this.trackingService.trackMessageSent(`msg_${Date.now()}`, user.companyId, connectionId, messageData.to, messageData.type || 'text', false, user.id).catch(error => {
                        this.logger.error(`Failed to track message sent: ${error.message}`);
                    });
                }
            }
            if (url.includes('/send-bulk-message') && method === 'POST') {
                const connectionId = this.extractConnectionId(url);
                const messageData = request.body;
                if (connectionId && messageData?.to) {
                    this.trackingService.trackBulkMessageSent(user.companyId, connectionId, messageData.to.length, messageData.type || 'text', user.id).catch(error => {
                        this.logger.error(`Failed to track bulk message sent: ${error.message}`);
                    });
                }
            }
        }));
    }
    extractConnectionId(url) {
        const matches = url.match(/\/connections\/([^\/]+)/);
        return matches ? matches[1] : null;
    }
};
exports.WhatsAppAnalyticsInterceptor = WhatsAppAnalyticsInterceptor;
exports.WhatsAppAnalyticsInterceptor = WhatsAppAnalyticsInterceptor = WhatsAppAnalyticsInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tracking_service_1.TrackingService])
], WhatsAppAnalyticsInterceptor);
let ContactAnalyticsInterceptor = ContactAnalyticsInterceptor_1 = class ContactAnalyticsInterceptor {
    trackingService;
    logger = new common_1.Logger(ContactAnalyticsInterceptor_1.name);
    constructor(trackingService) {
        this.trackingService = trackingService;
    }
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        const url = request.url;
        const method = request.method;
        return next.handle().pipe((0, operators_1.tap)((result) => {
            if (!user)
                return;
            if (url.includes('/contacts') && method === 'POST' && result?.id) {
                this.trackingService.trackContactCreated(result.id, user.companyId, result.whatsappConnectionId, result.phoneNumber, result.source?.type, user.id).catch(error => {
                    this.logger.error(`Failed to track contact creation: ${error.message}`);
                });
            }
            if (url.includes('/contacts') && method === 'PATCH' && result?.id) {
                const updatedFields = Object.keys(request.body);
                this.trackingService.trackContactUpdated(result.id, user.companyId, result.phoneNumber, updatedFields, user.id).catch(error => {
                    this.logger.error(`Failed to track contact update: ${error.message}`);
                });
            }
            if (url.includes('/tags') && method === 'POST') {
                const contactId = this.extractContactId(url);
                const tagData = request.body;
                if (contactId && tagData?.name) {
                    this.trackingService.trackContactTagged(contactId, user.companyId, 'unknown', tagData.name, user.id).catch(error => {
                        this.logger.error(`Failed to track contact tagging: ${error.message}`);
                    });
                }
            }
        }));
    }
    extractContactId(url) {
        const matches = url.match(/\/contacts\/([^\/]+)/);
        return matches ? matches[1] : null;
    }
};
exports.ContactAnalyticsInterceptor = ContactAnalyticsInterceptor;
exports.ContactAnalyticsInterceptor = ContactAnalyticsInterceptor = ContactAnalyticsInterceptor_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tracking_service_1.TrackingService])
], ContactAnalyticsInterceptor);
//# sourceMappingURL=analytics.interceptor.js.map