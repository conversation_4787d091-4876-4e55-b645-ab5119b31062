{"version": 3, "file": "automation.schema.js", "sourceRoot": "", "sources": ["../../../src/database/entities/automation.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAI3C,IAAY,cASX;AATD,WAAY,cAAc;IACxB,qDAAmC,CAAA;IACnC,uDAAqC,CAAA;IACrC,+CAA6B,CAAA;IAC7B,yDAAuC,CAAA;IACvC,yCAAuB,CAAA;IACvB,2DAAyC,CAAA;IACzC,+CAA6B,CAAA;IAC7B,mDAAiC,CAAA;AACnC,CAAC,EATW,cAAc,8BAAd,cAAc,QASzB;AAED,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,qCAAiB,CAAA;IACjB,yCAAqB,CAAA;IACrB,mCAAe,CAAA;IACf,qCAAiB,CAAA;AACnB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAED,IAAY,WAUX;AAVD,WAAY,WAAW;IACrB,oDAAqC,CAAA;IACrC,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kDAAmC,CAAA;IACnC,sCAAuB,CAAA;IACvB,wCAAyB,CAAA;AAC3B,CAAC,EAVW,WAAW,2BAAX,WAAW,QAUtB;AAED,IAAY,UAYX;AAZD,WAAY,UAAU;IACpB,2CAA6B,CAAA;IAC7B,iCAAmB,CAAA;IACnB,uCAAyB,CAAA;IACzB,yCAA2B,CAAA;IAC3B,yCAA2B,CAAA;IAC3B,+CAAiC,CAAA;IACjC,2CAA6B,CAAA;IAC7B,2BAAa,CAAA;IACb,qCAAuB,CAAA;IACvB,yCAA2B,CAAA;IAC3B,qDAAuC,CAAA;AACzC,CAAC,EAZW,UAAU,0BAAV,UAAU,QAYrB;AAsEM,IAAM,UAAU,GAAhB,MAAM,UAAU;IAErB,IAAI,CAAS;IAGb,WAAW,CAAU;IAGrB,IAAI,CAAiB;IAGrB,MAAM,CAAmB;IAGzB,SAAS,CAAiB;IAG1B,SAAS,CAAS;IAGlB,SAAS,CAAU;IAGnB,OAAO,CAAmB;IAG1B,OAAO,CAAe;IAGtB,SAAS,CAAa;IAGtB,WAAW,CAAU;IAIrB,QAAQ,CAeN;IAIF,cAAc,CAAS;IAGvB,YAAY,CAAS;IAGrB,UAAU,CAAS;IAGnB,cAAc,CAAQ;IAGtB,cAAc,CAAQ;IAItB,OAAO,CAAS;IAGhB,UAAU,CAAU;IAGpB,gBAAgB,CAAU;IAI1B,QAAQ,CAAsB;CAC/B,CAAA;AAlFY,gCAAU;AAErB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;wCACZ;AAGb;IADC,IAAA,eAAI,GAAE;;+CACc;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;;wCACxC;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,OAAO,EAAE,gBAAgB,CAAC,KAAK,EAAE,CAAC;;0CACvE;AAGzB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;8BACpD,gBAAK,CAAC,QAAQ;6CAAC;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACL;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;6CACJ;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;2CACb;AAG1B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;2CAChB;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;6CAChB;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;+CACF;AAIrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;4CAgBlC;AAIF;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;kDACZ;AAGvB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;gDACd;AAGrB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;8CAChB;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACJ,IAAI;kDAAC;AAGtB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;8BACJ,IAAI;kDAAC;AAItB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2CACnB;AAGhB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;8CACpB;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;;oDACG;AAI1B;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;4CACN;qBAjFnB,UAAU;IADtB,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;GAChB,UAAU,CAkFtB;AAEY,QAAA,gBAAgB,GAAG,wBAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAGzE,wBAAgB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACpD,wBAAgB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD,wBAAgB,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;AACzD,wBAAgB,CAAC,KAAK,CAAC,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC;AAClD,wBAAgB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AACzC,wBAAgB,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/C,wBAAgB,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAG/C,wBAAgB,CAAC,OAAO,CAAC,UAAU,GAAG,UAAS,OAAY,EAAE,OAAa;IACxE,IAAI,IAAI,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC5C,OAAO,KAAK,CAAC;IACf,CAAC;IAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC;QACpC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QAClD,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9D,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxC,WAAW,GAAG,SAAS;gBACvB,WAAW,IAAI,OAAO,EAAE,CAAC;gBAC3B,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;QACzD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7D,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,UAAU,EAAE,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAGD,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;QAClE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,wBAAgB,CAAC,OAAO,CAAC,kBAAkB,GAAG,UAAS,UAAmB,IAAI;IAC5E,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;IACzB,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;AACnC,CAAC,CAAC;AAEF,wBAAgB,CAAC,OAAO,CAAC,cAAc,GAAG;IACxC,IAAI,IAAI,CAAC,cAAc,KAAK,CAAC;QAAE,OAAO,CAAC,CAAC;IACxC,OAAO,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;AACzD,CAAC,CAAC"}