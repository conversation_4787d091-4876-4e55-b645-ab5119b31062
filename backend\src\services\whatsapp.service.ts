import { WhatsAppConnectionRepository } from '../repositories/whatsapp-connection.repository'
import { MessageRepository } from '../repositories/message.repository'
import { ContactRepository } from '../repositories/contact.repository'
import { EvolutionApiService } from './evolution-api.service'
import { ConnectionType, ConnectionStatus, MessageDirection, MessageStatus, MessageType } from '@prisma/client'

export interface CreateConnectionRequest {
  name: string
  phoneNumber: string
  type?: ConnectionType
  companyId: string
  assignedUserId?: string
}

export interface SendMessageRequest {
  connectionId: string
  contactPhone: string
  content: string
  type?: MessageType
  userId?: string
}

export class WhatsAppService {
  private connectionRepository: WhatsAppConnectionRepository
  private messageRepository: MessageRepository
  private contactRepository: ContactRepository
  private evolutionApi: EvolutionApiService

  constructor() {
    this.connectionRepository = new WhatsAppConnectionRepository()
    this.messageRepository = new MessageRepository()
    this.contactRepository = new ContactRepository()
    this.evolutionApi = new EvolutionApiService()
  }

  async createConnection(data: CreateConnectionRequest) {
    // Verificar se já existe uma conexão com esse número na empresa
    const existingConnection = await this.connectionRepository.findByPhoneNumber(
      data.phoneNumber, 
      data.companyId
    )

    if (existingConnection) {
      throw new Error('Já existe uma conexão com este número de telefone')
    }

    // Criar conexão
    const connection = await this.connectionRepository.create({
      name: data.name,
      phoneNumber: data.phoneNumber,
      type: data.type || ConnectionType.EVOLUTION_API,
      status: ConnectionStatus.DISCONNECTED,
      companyId: data.companyId,
      assignedUserId: data.assignedUserId
    })

    return connection
  }

  async getConnections(companyId: string, filters?: any) {
    return this.connectionRepository.findMany(
      { companyId, ...filters },
      filters?.page || 1,
      filters?.limit || 10
    )
  }

  async getConnection(id: string) {
    const connection = await this.connectionRepository.findById(id)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }
    return connection
  }

  async updateConnection(id: string, data: any) {
    const connection = await this.connectionRepository.findById(id)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }

    return this.connectionRepository.update(id, data)
  }

  async deleteConnection(id: string) {
    const connection = await this.connectionRepository.findById(id)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }

    return this.connectionRepository.delete(id)
  }

  async connectWhatsApp(id: string) {
    const connection = await this.connectionRepository.findById(id)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }

    if (connection.status === ConnectionStatus.CONNECTED) {
      throw new Error('Conexão já está ativa')
    }

    // Atualizar status para connecting
    await this.connectionRepository.updateStatus(id, ConnectionStatus.CONNECTING)

    try {
      const instanceName = `instance_${connection.id}`
      const webhookUrl = `${process.env.APP_URL || 'http://localhost:3000'}/api/v1/webhooks/evolution`

      // Criar instância na Evolution API
      const instance = await this.evolutionApi.createInstance({
        instanceName,
        qrcode: true,
        webhook: webhookUrl,
        webhookByEvents: true,
        events: [
          'QRCODE_UPDATED',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'CONNECTION_UPDATE'
        ]
      })

      // Salvar instanceId na conexão
      await this.connectionRepository.update(id, {
        instanceId: instanceName,
        connectionData: instance
      })

      // Buscar QR Code
      const qrData = await this.evolutionApi.getQrCode(instanceName)
      if (qrData) {
        await this.connectionRepository.updateQrCode(id, qrData.base64)
        return { qrCode: qrData.base64 }
      }

      return { message: 'Instância criada, aguardando QR Code...' }
    } catch (error) {
      await this.connectionRepository.updateStatus(id, ConnectionStatus.ERROR, error.message)
      throw error
    }
  }

  async disconnectWhatsApp(id: string) {
    const connection = await this.connectionRepository.findById(id)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }

    if (connection.status === ConnectionStatus.DISCONNECTED) {
      throw new Error('Conexão já está desconectada')
    }

    try {
      if (connection.instanceId) {
        // Fazer logout da instância na Evolution API
        await this.evolutionApi.logoutInstance(connection.instanceId)
      }

      await this.connectionRepository.updateStatus(id, ConnectionStatus.DISCONNECTED)
      await this.connectionRepository.clearQrCode(id)
    } catch (error) {
      await this.connectionRepository.updateStatus(id, ConnectionStatus.ERROR, error.message)
      throw error
    }
  }

  async sendMessage(data: SendMessageRequest) {
    const connection = await this.connectionRepository.findById(data.connectionId)
    if (!connection) {
      throw new Error('Conexão não encontrada')
    }

    if (connection.status !== ConnectionStatus.CONNECTED) {
      throw new Error('Conexão não está ativa')
    }

    // Buscar ou criar contato
    const contact = await this.contactRepository.findOrCreate({
      phone: data.contactPhone,
      whatsappConnectionId: data.connectionId,
      companyId: connection.companyId
    })

    // Criar mensagem
    const message = await this.messageRepository.create({
      messageId: this.generateMessageId(),
      whatsappConnectionId: data.connectionId,
      contactPhone: data.contactPhone,
      contactId: contact.id,
      content: { text: data.content },
      type: data.type || MessageType.TEXT,
      direction: MessageDirection.OUTBOUND,
      status: MessageStatus.PENDING,
      timestamp: new Date(),
      companyId: connection.companyId,
      userId: data.userId
    })

    // Atualizar estatísticas do contato
    await this.contactRepository.updateMessageStats(contact.id, new Date())

    try {
      if (!connection.instanceId) {
        throw new Error('Instância não configurada')
      }

      // Formatar número de telefone
      const formattedPhone = this.evolutionApi.formatPhoneNumber(data.contactPhone)

      // Enviar mensagem via Evolution API
      const result = await this.evolutionApi.sendTextMessage(connection.instanceId, {
        number: formattedPhone,
        text: data.content
      })

      // Atualizar mensagem com ID retornado pela API
      if (result.key?.id) {
        await this.messageRepository.update(message.id, {
          metadata: { evolutionMessageId: result.key.id, ...result }
        })
      }

      // Atualizar status para enviado
      await this.messageRepository.updateStatus(message.id, MessageStatus.SENT)

      return message
    } catch (error) {
      await this.messageRepository.updateStatus(message.id, MessageStatus.FAILED)
      throw error
    }
  }

  async getMessages(filters: any) {
    return this.messageRepository.findMany(filters, filters?.page || 1, filters?.limit || 50)
  }

  async getConversation(contactPhone: string, whatsappConnectionId: string) {
    return this.messageRepository.findConversation(contactPhone, whatsappConnectionId)
  }

  async markMessageAsRead(messageId: string) {
    const message = await this.messageRepository.findById(messageId)
    if (!message) {
      throw new Error('Mensagem não encontrada')
    }

    return this.messageRepository.updateStatus(messageId, MessageStatus.READ)
  }

  async getConnectionStats(companyId: string) {
    return this.connectionRepository.countByStatus(companyId)
  }

  async getMessageStats(companyId: string, startDate?: Date, endDate?: Date) {
    return this.messageRepository.getStats(companyId, startDate, endDate)
  }

  // Webhook handlers
  async handleIncomingMessage(data: any) {
    try {
      // Buscar conexão pelo instanceId ou phoneNumber
      const connection = await this.connectionRepository.findByInstanceId(data.instanceId)
      if (!connection) {
        console.error('Conexão não encontrada para instanceId:', data.instanceId)
        return
      }

      // Buscar ou criar contato
      const contact = await this.contactRepository.findOrCreate({
        phone: data.from,
        name: data.pushName,
        whatsappConnectionId: connection.id,
        companyId: connection.companyId
      })

      // Criar mensagem
      const message = await this.messageRepository.create({
        messageId: data.messageId,
        whatsappConnectionId: connection.id,
        contactPhone: data.from,
        contactId: contact.id,
        content: data.content,
        type: this.mapMessageType(data.type),
        direction: MessageDirection.INBOUND,
        status: MessageStatus.DELIVERED,
        timestamp: new Date(data.timestamp),
        companyId: connection.companyId,
        metadata: data.metadata
      })

      // Atualizar estatísticas do contato
      await this.contactRepository.updateMessageStats(contact.id, new Date())

      return message
    } catch (error) {
      console.error('Erro ao processar mensagem recebida:', error)
      throw error
    }
  }

  async handleConnectionStatusChange(data: any) {
    try {
      const connection = await this.connectionRepository.findByInstanceId(data.instanceId)
      if (!connection) {
        console.error('Conexão não encontrada para instanceId:', data.instanceId)
        return
      }

      const status = this.mapConnectionStatus(data.status)
      await this.connectionRepository.updateStatus(connection.id, status)

      if (status === ConnectionStatus.CONNECTED) {
        await this.connectionRepository.clearQrCode(connection.id)
      }
    } catch (error) {
      console.error('Erro ao atualizar status da conexão:', error)
      throw error
    }
  }

  // Helper methods
  private generateMockQrCode(): string {
    return `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private mapMessageType(type: string): MessageType {
    const typeMap: Record<string, MessageType> = {
      'text': MessageType.TEXT,
      'image': MessageType.IMAGE,
      'video': MessageType.VIDEO,
      'audio': MessageType.AUDIO,
      'document': MessageType.DOCUMENT,
      'location': MessageType.LOCATION,
      'contact': MessageType.CONTACT,
      'sticker': MessageType.STICKER
    }
    return typeMap[type] || MessageType.TEXT
  }

  private mapConnectionStatus(status: string): ConnectionStatus {
    const statusMap: Record<string, ConnectionStatus> = {
      'open': ConnectionStatus.CONNECTED,
      'close': ConnectionStatus.DISCONNECTED,
      'connecting': ConnectionStatus.CONNECTING,
      'qr': ConnectionStatus.PENDING
    }
    return statusMap[status] || ConnectionStatus.DISCONNECTED
  }
}
