"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var IntegrationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const integration_entity_1 = require("../../../database/entities/integration.entity");
const webhook_log_entity_1 = require("../../../database/entities/webhook-log.entity");
const entities_1 = require("../../../database/entities");
let IntegrationsService = IntegrationsService_1 = class IntegrationsService {
    integrationRepository;
    webhookLogRepository;
    httpService;
    logger = new common_1.Logger(IntegrationsService_1.name);
    constructor(integrationRepository, webhookLogRepository, httpService) {
        this.integrationRepository = integrationRepository;
        this.webhookLogRepository = webhookLogRepository;
        this.httpService = httpService;
    }
    async create(createIntegrationDto, currentUser) {
        const existingIntegration = await this.integrationRepository.findOne({
            where: {
                name: createIntegrationDto.name,
                companyId: currentUser.companyId,
            },
        });
        if (existingIntegration) {
            throw new common_1.ConflictException('Já existe uma integração com este nome');
        }
        this.validateIntegrationConfig(createIntegrationDto.type, createIntegrationDto.config);
        const integration = this.integrationRepository.create({
            ...createIntegrationDto,
            companyId: currentUser.companyId,
            createdBy: currentUser.id,
            status: integration_entity_1.IntegrationStatus.PENDING,
        });
        const savedIntegration = await this.integrationRepository.save(integration);
        if (createIntegrationDto.type === 'webhook' && createIntegrationDto.config.url) {
            await this.testIntegration(savedIntegration.id, currentUser);
        }
        this.logger.log(`Integration created: ${savedIntegration.id} by ${currentUser.email}`);
        return savedIntegration;
    }
    async findAll(options, currentUser) {
        const { companyId, type, status, active, triggerEvent, search, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
        const queryBuilder = this.integrationRepository.createQueryBuilder('integration')
            .leftJoinAndSelect('integration.creator', 'creator');
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('integration.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('integration.companyId = :companyId', { companyId });
        }
        if (type) {
            queryBuilder.andWhere('integration.type = :type', { type });
        }
        if (status) {
            queryBuilder.andWhere('integration.status = :status', { status });
        }
        if (typeof active === 'boolean') {
            queryBuilder.andWhere('integration.active = :active', { active });
        }
        if (triggerEvent) {
            queryBuilder.andWhere(':triggerEvent = ANY(integration.triggerEvents)', { triggerEvent });
        }
        if (search) {
            queryBuilder.andWhere('(integration.name ILIKE :search OR integration.description ILIKE :search)', { search: `%${search}%` });
        }
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        queryBuilder.orderBy(`integration.${sortBy}`, sortOrder);
        const [integrations, total] = await queryBuilder.getManyAndCount();
        return { integrations, total, page, limit };
    }
    async findOne(id, currentUser) {
        const queryBuilder = this.integrationRepository.createQueryBuilder('integration')
            .leftJoinAndSelect('integration.creator', 'creator')
            .where('integration.id = :id', { id });
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('integration.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        const integration = await queryBuilder.getOne();
        if (!integration) {
            throw new common_1.NotFoundException('Integração não encontrada');
        }
        return integration;
    }
    async update(id, updateIntegrationDto, currentUser) {
        const integration = await this.findOne(id, currentUser);
        if (updateIntegrationDto.name && updateIntegrationDto.name !== integration.name) {
            const existingIntegration = await this.integrationRepository.findOne({
                where: {
                    name: updateIntegrationDto.name,
                    companyId: currentUser.companyId,
                },
            });
            if (existingIntegration && existingIntegration.id !== id) {
                throw new common_1.ConflictException('Já existe uma integração com este nome');
            }
        }
        if (updateIntegrationDto.config && updateIntegrationDto.type) {
            this.validateIntegrationConfig(updateIntegrationDto.type, updateIntegrationDto.config);
        }
        Object.assign(integration, updateIntegrationDto);
        const updatedIntegration = await this.integrationRepository.save(integration);
        this.logger.log(`Integration updated: ${id} by ${currentUser.email}`);
        return updatedIntegration;
    }
    async remove(id, currentUser) {
        const integration = await this.findOne(id, currentUser);
        const pendingLogs = await this.webhookLogRepository.count({
            where: {
                integrationId: id,
                status: webhook_log_entity_1.WebhookStatus.PENDING,
            },
        });
        if (pendingLogs > 0) {
            throw new common_1.ConflictException('Não é possível deletar integração com webhooks pendentes');
        }
        await this.integrationRepository.remove(integration);
        this.logger.log(`Integration deleted: ${id} by ${currentUser.email}`);
    }
    async testIntegration(id, currentUser) {
        const integration = await this.findOne(id, currentUser);
        const testData = {
            test: true,
            timestamp: new Date().toISOString(),
            integration: {
                id: integration.id,
                name: integration.name,
                type: integration.type,
            },
            company: {
                id: currentUser.companyId,
                name: 'Company Name',
            },
            user: {
                id: currentUser.id,
                name: currentUser.email,
                email: currentUser.email,
            },
        };
        return this.executeIntegration(integration, 'test_event', testData);
    }
    async triggerIntegration(event, data, companyId) {
        const integrations = await this.integrationRepository.find({
            where: {
                companyId,
                status: integration_entity_1.IntegrationStatus.ACTIVE,
                active: true,
            },
        });
        const triggeredIntegrations = integrations.filter(integration => integration.shouldTrigger(event, data));
        this.logger.log(`Triggering ${triggeredIntegrations.length} integrations for event ${event}`);
        const promises = triggeredIntegrations.map(integration => this.executeIntegration(integration, event, data));
        await Promise.allSettled(promises);
    }
    async executeIntegration(integration, event, data) {
        if (!integration.canExecute()) {
            this.logger.warn(`Integration ${integration.id} cannot execute (rate limited or inactive)`);
            return;
        }
        const transformedData = integration.transformData(data);
        const webhookLog = this.webhookLogRepository.create({
            integrationId: integration.id,
            companyId: integration.companyId,
            eventType: event,
            eventData: transformedData,
            request: {
                url: integration.config.url || '',
                method: integration.config.method || 'POST',
                headers: integration.config.headers || {},
                body: transformedData,
                timeout: integration.timeout,
            },
        });
        await this.webhookLogRepository.save(webhookLog);
        try {
            let result;
            switch (integration.type) {
                case 'webhook':
                    result = await this.executeWebhook(integration, transformedData, webhookLog);
                    break;
                case 'google_sheets':
                    result = await this.executeGoogleSheets(integration, transformedData, webhookLog);
                    break;
                case 'slack':
                    result = await this.executeSlack(integration, transformedData, webhookLog);
                    break;
                default:
                    throw new Error(`Integration type ${integration.type} not implemented`);
            }
            integration.incrementExecution(true);
            await this.integrationRepository.save(integration);
            return result;
        }
        catch (error) {
            integration.incrementExecution(false);
            integration.setError(error.message);
            await this.integrationRepository.save(integration);
            webhookLog.markAsFailed(error);
            await this.webhookLogRepository.save(webhookLog);
            this.logger.error(`Integration execution failed: ${integration.id} - ${error.message}`);
            throw error;
        }
    }
    async executeWebhook(integration, data, webhookLog) {
        const config = integration.config;
        const startTime = Date.now();
        try {
            const headers = { ...config.headers };
            if (config.authType === 'bearer' && config.bearerToken) {
                headers['Authorization'] = `Bearer ${config.bearerToken}`;
            }
            else if (config.authType === 'api_key' && config.apiKey) {
                headers['X-API-Key'] = config.apiKey;
            }
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.request({
                method: config.method || 'POST',
                url: config.url,
                data: data,
                headers,
                timeout: config.timeout || integration.timeout,
            }));
            const duration = Date.now() - startTime;
            const webhookResponse = {
                status: response.status,
                statusText: response.statusText,
                headers: response.headers,
                body: response.data,
                duration,
            };
            webhookLog.markAsSuccess(webhookResponse);
            await this.webhookLogRepository.save(webhookLog);
            return response.data;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            const webhookResponse = error.response ? {
                status: error.response.status,
                statusText: error.response.statusText,
                headers: error.response.headers,
                body: error.response.data,
                duration,
            } : undefined;
            webhookLog.markAsFailed(error, webhookResponse);
            await this.webhookLogRepository.save(webhookLog);
            throw error;
        }
    }
    async executeGoogleSheets(integration, data, webhookLog) {
        this.logger.log(`Google Sheets integration: ${integration.id}`);
        const result = { success: true, message: 'Data sent to Google Sheets' };
        webhookLog.markAsSuccess({
            status: 200,
            statusText: 'OK',
            headers: {},
            body: result,
            duration: 1000,
        });
        await this.webhookLogRepository.save(webhookLog);
        return result;
    }
    async executeSlack(integration, data, webhookLog) {
        this.logger.log(`Slack integration: ${integration.id}`);
        const result = { success: true, message: 'Message sent to Slack' };
        webhookLog.markAsSuccess({
            status: 200,
            statusText: 'OK',
            headers: {},
            body: result,
            duration: 1500,
        });
        await this.webhookLogRepository.save(webhookLog);
        return result;
    }
    async getWebhookLogs(options, currentUser) {
        const { integrationId, companyId, status, eventType, dateFrom, dateTo, page = 1, limit = 10, } = options;
        const queryBuilder = this.webhookLogRepository.createQueryBuilder('log')
            .leftJoinAndSelect('log.integration', 'integration');
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('log.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('log.companyId = :companyId', { companyId });
        }
        if (integrationId) {
            queryBuilder.andWhere('log.integrationId = :integrationId', { integrationId });
        }
        if (status) {
            queryBuilder.andWhere('log.status = :status', { status });
        }
        if (eventType) {
            queryBuilder.andWhere('log.eventType = :eventType', { eventType });
        }
        if (dateFrom || dateTo) {
            if (dateFrom) {
                queryBuilder.andWhere('log.createdAt >= :dateFrom', { dateFrom });
            }
            if (dateTo) {
                queryBuilder.andWhere('log.createdAt <= :dateTo', { dateTo });
            }
        }
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        queryBuilder.orderBy('log.createdAt', 'DESC');
        const [logs, total] = await queryBuilder.getManyAndCount();
        return { logs, total, page, limit };
    }
    async retryWebhook(logId, currentUser) {
        const log = await this.webhookLogRepository.findOne({
            where: { id: logId },
            relations: ['integration'],
        });
        if (!log) {
            throw new common_1.NotFoundException('Log de webhook não encontrado');
        }
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN && log.companyId !== currentUser.companyId) {
            throw new common_1.NotFoundException('Log de webhook não encontrado');
        }
        if (!log.canRetry()) {
            throw new common_1.BadRequestException('Este webhook não pode ser executado novamente');
        }
        log.retry();
        await this.webhookLogRepository.save(log);
        try {
            await this.executeIntegration(log.integration, log.eventType, log.eventData);
        }
        catch (error) {
            this.logger.error(`Webhook retry failed: ${logId} - ${error.message}`);
        }
        return log;
    }
    validateIntegrationConfig(type, config) {
        switch (type) {
            case 'webhook':
                if (!config.url) {
                    throw new common_1.BadRequestException('URL é obrigatória para webhooks');
                }
                break;
            case 'google_sheets':
                if (!config.googleSheets?.spreadsheetId || !config.googleSheets?.sheetName) {
                    throw new common_1.BadRequestException('ID da planilha e nome da aba são obrigatórios para Google Sheets');
                }
                break;
            case 'slack':
                if (!config.slack?.channelId || !config.slack?.botToken) {
                    throw new common_1.BadRequestException('ID do canal e token do bot são obrigatórios para Slack');
                }
                break;
        }
    }
    async getIntegrationStats(currentUser) {
        const queryBuilder = this.integrationRepository.createQueryBuilder('integration');
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.where('integration.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        const [total, active, inactive, error] = await Promise.all([
            queryBuilder.getCount(),
            queryBuilder.clone().andWhere('integration.status = :status', { status: integration_entity_1.IntegrationStatus.ACTIVE }).getCount(),
            queryBuilder.clone().andWhere('integration.status = :status', { status: integration_entity_1.IntegrationStatus.INACTIVE }).getCount(),
            queryBuilder.clone().andWhere('integration.status = :status', { status: integration_entity_1.IntegrationStatus.ERROR }).getCount(),
        ]);
        const executionStats = await queryBuilder.clone()
            .select('SUM(integration.executionCount)', 'totalExecutions')
            .addSelect('SUM(integration.successCount)', 'totalSuccess')
            .getRawOne();
        const totalExecutions = parseInt(executionStats.totalExecutions) || 0;
        const totalSuccess = parseInt(executionStats.totalSuccess) || 0;
        const successRate = totalExecutions > 0 ? (totalSuccess / totalExecutions) * 100 : 0;
        return {
            total,
            active,
            inactive,
            error,
            totalExecutions,
            successRate: Math.round(successRate * 100) / 100,
        };
    }
};
exports.IntegrationsService = IntegrationsService;
exports.IntegrationsService = IntegrationsService = IntegrationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(integration_entity_1.Integration)),
    __param(1, (0, typeorm_1.InjectRepository)(webhook_log_entity_1.WebhookLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        axios_1.HttpService])
], IntegrationsService);
//# sourceMappingURL=integrations.service.js.map