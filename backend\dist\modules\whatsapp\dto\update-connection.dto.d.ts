import { CreateConnectionDto } from './create-connection.dto';
import { ConnectionStatus } from '../../../database/entities';
declare const UpdateConnectionDto_base: import("@nestjs/common").Type<Partial<Omit<CreateConnectionDto, "type" | "phoneNumber" | "companyId">>>;
export declare class UpdateConnectionDto extends UpdateConnectionDto_base {
    status?: ConnectionStatus;
    isActive?: boolean;
}
export {};
