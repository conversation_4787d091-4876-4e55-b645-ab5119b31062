import { ApiProperty } from '@nestjs/swagger';
import { User } from '../../../database/entities';

export class AuthTokensDto {
  @ApiProperty({
    description: 'Token de acesso JWT',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Token de refresh',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Tipo do token',
    example: 'Bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: 'Tempo de expiração em segundos',
    example: 604800,
  })
  expiresIn: number;
}

export class AuthResponseDto {
  @ApiProperty({
    description: 'Dados do usuário autenticado',
  })
  user: Partial<User>;

  @ApiProperty({
    description: 'Tokens de autenticação',
  })
  tokens: AuthTokensDto;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Token de refresh',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;
}
