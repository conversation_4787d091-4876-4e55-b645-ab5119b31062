import { BaseEntity } from './base.entity';
import { Company } from './company.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
export declare enum UserRole {
    SUPER_ADMIN = "super_admin",
    AGENCY_ADMIN = "agency_admin",
    COMPANY_ADMIN = "company_admin",
    SELLER = "seller"
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    PENDING = "pending",
    SUSPENDED = "suspended"
}
export declare class User extends BaseEntity {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    password: string;
    role: UserRole;
    status: UserStatus;
    avatar: string;
    lastLoginAt: Date;
    emailVerifiedAt: Date;
    emailVerificationToken: string;
    passwordResetToken: string;
    passwordResetExpiresAt: Date;
    preferences: Record<string, any>;
    permissions: string[];
    companyId: string;
    company: Company;
    assignedWhatsAppConnections: WhatsAppConnection[];
    get fullName(): string;
    get isActive(): boolean;
    get isSuperAdmin(): boolean;
    get isAgencyAdmin(): boolean;
    get isCompanyAdmin(): boolean;
    get isSeller(): boolean;
    hasPermission(permission: string): boolean;
    canAccessCompany(companyId: string): boolean;
}
