import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConnectionStatus } from '../../../database/entities';

export interface EvolutionInstance {
  instanceName: string;
  status: string;
  qrcode?: string;
  profilePictureUrl?: string;
  profileName?: string;
  number?: string;
}

export interface EvolutionMessage {
  number: string;
  text?: string;
  media?: {
    mediatype: string;
    media: string;
    fileName?: string;
    caption?: string;
  };
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    name?: string;
  };
  contact?: {
    fullName: string;
    wuid: string;
    phoneNumber: string;
  };
  quoted?: {
    messageId: string;
  };
}

export interface EvolutionWebhookData {
  instanceName: string;
  event: string;
  data: any;
}

@Injectable()
export class EvolutionApiService {
  private readonly logger = new Logger(EvolutionApiService.name);
  private readonly baseUrl: string;
  private readonly apiKey: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.baseUrl = this.configService.get('whatsapp.evolution.apiUrl') || 'http://localhost:8080';
    this.apiKey = this.configService.get('whatsapp.evolution.apiKey') || '';
  }

  private getHeaders() {
    return {
      'Content-Type': 'application/json',
      'apikey': this.apiKey,
    };
  }

  async createInstance(instanceName: string, webhookUrl?: string): Promise<EvolutionInstance> {
    try {
      const payload: any = {
        instanceName,
        qrcode: true,
        integration: 'WHATSAPP-BAILEYS',
      };

      if (webhookUrl) {
        payload.webhook = {
          url: webhookUrl,
          events: [
            'APPLICATION_STARTUP',
            'QRCODE_UPDATED',
            'CONNECTION_UPDATE',
            'MESSAGES_UPSERT',
            'MESSAGES_UPDATE',
            'SEND_MESSAGE',
          ],
        };
      }

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/instance/create`,
          payload,
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Instance created: ${instanceName}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to create instance ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao criar instância do WhatsApp');
    }
  }

  async deleteInstance(instanceName: string): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.delete(
          `${this.baseUrl}/instance/delete/${instanceName}`,
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Instance deleted: ${instanceName}`);
    } catch (error) {
      this.logger.error(`Failed to delete instance ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao deletar instância do WhatsApp');
    }
  }

  async getInstanceStatus(instanceName: string): Promise<EvolutionInstance> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/instance/connectionState/${instanceName}`,
          { headers: this.getHeaders() }
        )
      );

      return response.data;
    } catch (error) {
      this.logger.error(`Failed to get instance status ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao obter status da instância');
    }
  }

  async getQRCode(instanceName: string): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/instance/connect/${instanceName}`,
          { headers: this.getHeaders() }
        )
      );

      return response.data.qrcode || response.data.base64;
    } catch (error) {
      this.logger.error(`Failed to get QR code ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao obter QR Code');
    }
  }

  async sendMessage(instanceName: string, message: EvolutionMessage): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/message/sendText/${instanceName}`,
          message,
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Message sent from ${instanceName} to ${message.number}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send message from ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao enviar mensagem');
    }
  }

  async sendMediaMessage(instanceName: string, message: EvolutionMessage): Promise<any> {
    try {
      const endpoint = this.getMediaEndpoint(message.media?.mediatype);
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/message/${endpoint}/${instanceName}`,
          message,
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Media message sent from ${instanceName} to ${message.number}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to send media message from ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao enviar mensagem de mídia');
    }
  }

  async logout(instanceName: string): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.delete(
          `${this.baseUrl}/instance/logout/${instanceName}`,
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Instance logged out: ${instanceName}`);
    } catch (error) {
      this.logger.error(`Failed to logout instance ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao desconectar instância');
    }
  }

  async restart(instanceName: string): Promise<void> {
    try {
      await firstValueFrom(
        this.httpService.put(
          `${this.baseUrl}/instance/restart/${instanceName}`,
          {},
          { headers: this.getHeaders() }
        )
      );

      this.logger.log(`Instance restarted: ${instanceName}`);
    } catch (error) {
      this.logger.error(`Failed to restart instance ${instanceName}:`, error.response?.data || error.message);
      throw new BadRequestException('Falha ao reiniciar instância');
    }
  }

  private getMediaEndpoint(mediaType?: string): string {
    switch (mediaType) {
      case 'image':
        return 'sendMedia';
      case 'audio':
        return 'sendWhatsAppAudio';
      case 'video':
        return 'sendMedia';
      case 'document':
        return 'sendMedia';
      default:
        return 'sendMedia';
    }
  }

  mapEvolutionStatusToConnectionStatus(evolutionStatus: string): ConnectionStatus {
    switch (evolutionStatus?.toLowerCase()) {
      case 'open':
      case 'connected':
        return ConnectionStatus.CONNECTED;
      case 'connecting':
      case 'pairing':
        return ConnectionStatus.CONNECTING;
      case 'close':
      case 'closed':
        return ConnectionStatus.DISCONNECTED;
      default:
        return ConnectionStatus.ERROR;
    }
  }

  generateInstanceName(companyId: string, phoneNumber: string): string {
    // Remove caracteres especiais do número
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    // Pega os primeiros 8 caracteres do companyId
    const shortCompanyId = companyId.substring(0, 8);
    return `${shortCompanyId}_${cleanNumber}`;
  }
}
