import { PartialType, OmitType } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CreateAutomationDto } from './create-automation.dto';
import { AutomationStatus } from '../../../database/entities/automation.schema';

export class UpdateAutomationDto extends PartialType(CreateAutomationDto) {
  @ApiPropertyOptional({
    description: 'Status da automação',
    enum: AutomationStatus,
  })
  @IsOptional()
  @IsEnum(AutomationStatus)
  status?: AutomationStatus;
}
