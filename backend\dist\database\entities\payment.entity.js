"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Payment = exports.PaymentGateway = exports.PaymentMethod = exports.PaymentStatus = void 0;
const typeorm_1 = require("typeorm");
const company_entity_1 = require("./company.entity");
const subscription_entity_1 = require("./subscription.entity");
const invoice_entity_1 = require("./invoice.entity");
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["PENDING"] = "pending";
    PaymentStatus["PROCESSING"] = "processing";
    PaymentStatus["SUCCEEDED"] = "succeeded";
    PaymentStatus["FAILED"] = "failed";
    PaymentStatus["CANCELLED"] = "cancelled";
    PaymentStatus["REFUNDED"] = "refunded";
    PaymentStatus["PARTIALLY_REFUNDED"] = "partially_refunded";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CREDIT_CARD"] = "credit_card";
    PaymentMethod["DEBIT_CARD"] = "debit_card";
    PaymentMethod["BANK_TRANSFER"] = "bank_transfer";
    PaymentMethod["PIX"] = "pix";
    PaymentMethod["BOLETO"] = "boleto";
    PaymentMethod["PAYPAL"] = "paypal";
    PaymentMethod["WALLET"] = "wallet";
    PaymentMethod["CRYPTO"] = "crypto";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var PaymentGateway;
(function (PaymentGateway) {
    PaymentGateway["STRIPE"] = "stripe";
    PaymentGateway["MERCADO_PAGO"] = "mercado_pago";
    PaymentGateway["PAGSEGURO"] = "pagseguro";
    PaymentGateway["PAYPAL"] = "paypal";
    PaymentGateway["ASAAS"] = "asaas";
    PaymentGateway["GERENCIANET"] = "gerencianet";
    PaymentGateway["MANUAL"] = "manual";
})(PaymentGateway || (exports.PaymentGateway = PaymentGateway = {}));
let Payment = class Payment {
    id;
    paymentNumber;
    companyId;
    company;
    subscriptionId;
    subscription;
    invoiceId;
    invoice;
    status;
    paymentMethod;
    gateway;
    amount;
    feeAmount;
    netAmount;
    refundedAmount;
    currency;
    paymentMethodDetails;
    processedAt;
    failedAt;
    refundedAt;
    dueDate;
    gatewayTransactionId;
    gatewayPaymentId;
    gatewayCustomerId;
    gatewayPaymentMethodId;
    failureCode;
    failureMessage;
    attemptCount;
    nextAttemptDate;
    refunds;
    description;
    notes;
    metadata;
    emailSent;
    emailSentAt;
    isReconciled;
    reconciledAt;
    reconciledBy;
    taxDocumentNumber;
    taxDocumentUrl;
    createdAt;
    updatedAt;
    isSucceeded() {
        return this.status === PaymentStatus.SUCCEEDED;
    }
    isFailed() {
        return this.status === PaymentStatus.FAILED;
    }
    isPending() {
        return this.status === PaymentStatus.PENDING;
    }
    isProcessing() {
        return this.status === PaymentStatus.PROCESSING;
    }
    isRefunded() {
        return this.status === PaymentStatus.REFUNDED || this.status === PaymentStatus.PARTIALLY_REFUNDED;
    }
    canBeRefunded() {
        return this.isSucceeded() && this.refundedAmount < this.amount;
    }
    canBeRetried() {
        return this.isFailed() && this.attemptCount < 3;
    }
    getRemainingRefundableAmount() {
        return Math.max(0, this.amount - this.refundedAmount);
    }
    getRefundProgress() {
        if (this.amount === 0)
            return 0;
        return (this.refundedAmount / this.amount) * 100;
    }
    markAsSucceeded(gatewayTransactionId) {
        this.status = PaymentStatus.SUCCEEDED;
        this.processedAt = new Date();
        this.netAmount = this.amount - this.feeAmount;
        if (gatewayTransactionId) {
            this.gatewayTransactionId = gatewayTransactionId;
        }
    }
    markAsFailed(failureCode, failureMessage) {
        this.status = PaymentStatus.FAILED;
        this.failedAt = new Date();
        this.failureCode = failureCode || null;
        this.failureMessage = failureMessage || null;
        this.attemptCount += 1;
        if (this.canBeRetried()) {
            const delayHours = Math.pow(2, this.attemptCount) * 24;
            this.nextAttemptDate = new Date(Date.now() + delayHours * 60 * 60 * 1000);
        }
    }
    addRefund(amount, reason, gatewayRefundId) {
        const refund = {
            refundId: `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            amount,
            reason,
            refundedAt: new Date(),
            gatewayRefundId,
        };
        this.refunds = [...(this.refunds || []), refund];
        this.refundedAmount += amount;
        this.refundedAt = new Date();
        if (this.refundedAmount >= this.amount) {
            this.status = PaymentStatus.REFUNDED;
        }
        else {
            this.status = PaymentStatus.PARTIALLY_REFUNDED;
        }
    }
    retry() {
        if (this.canBeRetried()) {
            this.status = PaymentStatus.PENDING;
            this.failureCode = null;
            this.failureMessage = null;
            this.nextAttemptDate = null;
        }
    }
    cancel() {
        if (this.isPending() || this.isProcessing()) {
            this.status = PaymentStatus.CANCELLED;
        }
    }
    reconcile(reconciledBy) {
        this.isReconciled = true;
        this.reconciledAt = new Date();
        this.reconciledBy = reconciledBy;
    }
    generatePaymentNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const timestamp = Date.now().toString().slice(-6);
        return `PAY-${year}${month}-${timestamp}`;
    }
    getPaymentMethodDisplay() {
        const details = this.paymentMethodDetails;
        if (!details)
            return this.paymentMethod;
        switch (details.type) {
            case PaymentMethod.CREDIT_CARD:
            case PaymentMethod.DEBIT_CARD:
                return `${details.brand} **** ${details.last4}`;
            case PaymentMethod.PIX:
                return `PIX ${details.pixKey ? `(${details.pixKey})` : ''}`;
            case PaymentMethod.BANK_TRANSFER:
                return `Transferência ${details.bankName || ''}`;
            case PaymentMethod.BOLETO:
                return 'Boleto Bancário';
            default:
                return this.paymentMethod;
        }
    }
    getDaysOverdue() {
        if (!this.dueDate || this.isSucceeded())
            return 0;
        const now = new Date();
        if (now <= this.dueDate)
            return 0;
        const diffTime = now.getTime() - this.dueDate.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
};
exports.Payment = Payment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], Payment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], Payment.prototype, "paymentNumber", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Payment.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], Payment.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "subscriptionId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => subscription_entity_1.Subscription, subscription => subscription.payments),
    (0, typeorm_1.JoinColumn)({ name: 'subscriptionId' }),
    __metadata("design:type", subscription_entity_1.Subscription)
], Payment.prototype, "subscription", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "invoiceId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => invoice_entity_1.Invoice, invoice => invoice.payments),
    (0, typeorm_1.JoinColumn)({ name: 'invoiceId' }),
    __metadata("design:type", invoice_entity_1.Invoice)
], Payment.prototype, "invoice", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
        default: PaymentStatus.PENDING,
    }),
    __metadata("design:type", String)
], Payment.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
    }),
    __metadata("design:type", String)
], Payment.prototype, "paymentMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 50,
    }),
    __metadata("design:type", String)
], Payment.prototype, "gateway", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Payment.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Payment.prototype, "feeAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Payment.prototype, "netAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Payment.prototype, "refundedAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 3, default: 'BRL' }),
    __metadata("design:type", String)
], Payment.prototype, "currency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Payment.prototype, "paymentMethodDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "processedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "failedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "refundedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "gatewayTransactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "gatewayPaymentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "gatewayCustomerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "gatewayPaymentMethodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100, nullable: true }),
    __metadata("design:type", Object)
], Payment.prototype, "failureCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", Object)
], Payment.prototype, "failureMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Payment.prototype, "attemptCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Object)
], Payment.prototype, "nextAttemptDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], Payment.prototype, "refunds", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], Payment.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Payment.prototype, "emailSent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "emailSentAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], Payment.prototype, "isReconciled", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "datetime", nullable: true }),
    __metadata("design:type", Date)
], Payment.prototype, "reconciledAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "reconciledBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "taxDocumentNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", length: 255, nullable: true }),
    __metadata("design:type", String)
], Payment.prototype, "taxDocumentUrl", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], Payment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], Payment.prototype, "updatedAt", void 0);
exports.Payment = Payment = __decorate([
    (0, typeorm_1.Entity)('payments')
], Payment);
//# sourceMappingURL=payment.entity.js.map