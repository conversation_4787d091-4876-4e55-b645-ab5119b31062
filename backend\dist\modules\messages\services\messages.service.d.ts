import { Model } from 'mongoose';
import { MessageDocument, MessageType, MessageDirection, MessageStatus } from '../../../database/entities';
import { AuthenticatedUser } from '../../../common/decorators/current-user.decorator';
import { ContactsService } from './contacts.service';
export interface FindMessagesOptions {
    companyId?: string;
    whatsappConnectionId?: string;
    contactPhone?: string;
    conversationId?: string;
    direction?: MessageDirection;
    type?: MessageType;
    status?: MessageStatus;
    isAutomated?: boolean;
    sentBy?: string;
    dateFrom?: Date;
    dateTo?: Date;
    search?: string;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface CreateMessageData {
    messageId: string;
    type: MessageType;
    direction: MessageDirection;
    content?: string;
    media?: any;
    location?: any;
    contact?: any;
    quotedMessageId?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
    whatsappConnectionId: string;
    contactPhone: string;
    sentBy?: string;
    isAutomated?: boolean;
    automationId?: string;
    isTemplate?: boolean;
    templateName?: string;
    conversationId?: string;
    isFirstMessage?: boolean;
    tags?: string[];
}
export declare class MessagesService {
    private messageModel;
    private contactsService;
    private readonly logger;
    constructor(messageModel: Model<MessageDocument>, contactsService: ContactsService);
    create(messageData: CreateMessageData, companyId: string): Promise<MessageDocument>;
    findAll(options: FindMessagesOptions, currentUser: AuthenticatedUser): Promise<{
        messages: MessageDocument[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<MessageDocument>;
    findByMessageId(messageId: string): Promise<MessageDocument | null>;
    updateStatus(messageId: string, status: MessageStatus, timestamp?: Date): Promise<void>;
    getConversation(contactPhone: string, whatsappConnectionId: string, currentUser: AuthenticatedUser, limit?: number): Promise<MessageDocument[]>;
    getMessageStats(currentUser: AuthenticatedUser, dateFrom?: Date, dateTo?: Date): Promise<{
        total: number;
        sent: number;
        received: number;
        automated: number;
        failed: number;
    }>;
    deleteMessage(id: string, currentUser: AuthenticatedUser): Promise<void>;
    private isFirstMessageFromContact;
}
