import { Injectable, Logger } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { EventType, EventCategory, EventProperties } from '../../../database/entities/analytics-event.schema';

@Injectable()
export class TrackingService {
  private readonly logger = new Logger(TrackingService.name);

  constructor(private analyticsService: AnalyticsService) {}

  // Eventos de usuário
  async trackUserLogin(userId: string, companyId: string, sessionId: string, metadata?: any): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.USER_LOGIN,
      EventCategory.USER,
      companyId,
      {
        userId,
        sessionId,
        ...metadata,
      },
      userId,
      sessionId,
    );
  }

  async trackUserLogout(userId: string, companyId: string, sessionId: string, sessionDuration?: number): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.USER_LOGOUT,
      EventCategory.USER,
      companyId,
      {
        userId,
        sessionId,
        duration: sessionDuration,
      },
      userId,
      sessionId,
    );
  }

  async trackUserCreated(userId: string, companyId: string, createdBy: string, userRole: string): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.USER_CREATED,
      EventCategory.USER,
      companyId,
      {
        userId,
        createdBy,
        userRole,
      },
      createdBy,
    );
  }

  // Eventos de WhatsApp
  async trackWhatsAppConnected(connectionId: string, companyId: string, userId: string, phoneNumber: string): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.WHATSAPP_CONNECTED,
      EventCategory.WHATSAPP,
      companyId,
      {
        connectionId,
        phoneNumber,
        userId,
      },
      userId,
    );
  }

  async trackWhatsAppDisconnected(connectionId: string, companyId: string, userId?: string, reason?: string): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.WHATSAPP_DISCONNECTED,
      EventCategory.WHATSAPP,
      companyId,
      {
        connectionId,
        reason,
        userId,
      },
      userId,
    );
  }

  async trackQRCodeGenerated(connectionId: string, companyId: string, userId: string): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.WHATSAPP_QR_GENERATED,
      EventCategory.WHATSAPP,
      companyId,
      {
        connectionId,
        userId,
      },
      userId,
    );
  }

  // Eventos de mensagens
  async trackMessageSent(
    messageId: string,
    companyId: string,
    connectionId: string,
    contactPhone: string,
    messageType: string,
    isAutomated: boolean = false,
    userId?: string,
    automationId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.MESSAGE_SENT,
      EventCategory.MESSAGE,
      companyId,
      {
        messageId,
        connectionId,
        contactPhone,
        messageType,
        messageDirection: 'outbound',
        isAutomated,
        automationId,
        userId,
      },
      userId,
    );
  }

  async trackMessageReceived(
    messageId: string,
    companyId: string,
    connectionId: string,
    contactPhone: string,
    messageType: string,
    isFirstMessage: boolean = false,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.MESSAGE_RECEIVED,
      EventCategory.MESSAGE,
      companyId,
      {
        messageId,
        connectionId,
        contactPhone,
        messageType,
        messageDirection: 'inbound',
        isFirstMessage,
      },
    );
  }

  async trackMessageDelivered(messageId: string, companyId: string, deliveryTime?: number): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.MESSAGE_DELIVERED,
      EventCategory.MESSAGE,
      companyId,
      {
        messageId,
        deliveryTime,
      },
    );
  }

  async trackMessageRead(messageId: string, companyId: string, readTime?: number): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.MESSAGE_READ,
      EventCategory.MESSAGE,
      companyId,
      {
        messageId,
        readTime,
      },
    );
  }

  async trackMessageFailed(messageId: string, companyId: string, errorCode?: string, errorMessage?: string): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.MESSAGE_FAILED,
      EventCategory.MESSAGE,
      companyId,
      {
        messageId,
        errorCode,
        errorMessage,
      },
    );
  }

  async trackBulkMessageSent(
    companyId: string,
    connectionId: string,
    recipientCount: number,
    messageType: string,
    userId: string,
    campaignId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.BULK_MESSAGE_SENT,
      EventCategory.MESSAGE,
      companyId,
      {
        connectionId,
        recipientCount,
        messageType,
        campaignId,
        value: recipientCount,
      },
      userId,
    );
  }

  // Eventos de contatos
  async trackContactCreated(
    contactId: string,
    companyId: string,
    connectionId: string,
    contactPhone: string,
    source?: string,
    userId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.CONTACT_CREATED,
      EventCategory.CONTACT,
      companyId,
      {
        contactId,
        connectionId,
        contactPhone,
        contactSource: source,
      },
      userId,
    );
  }

  async trackContactUpdated(
    contactId: string,
    companyId: string,
    contactPhone: string,
    updatedFields: string[],
    userId: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.CONTACT_UPDATED,
      EventCategory.CONTACT,
      companyId,
      {
        contactId,
        contactPhone,
        updatedFields,
      },
      userId,
    );
  }

  async trackContactTagged(
    contactId: string,
    companyId: string,
    contactPhone: string,
    tagName: string,
    userId: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.CONTACT_TAGGED,
      EventCategory.CONTACT,
      companyId,
      {
        contactId,
        contactPhone,
        tagName,
      },
      userId,
    );
  }

  async trackLeadConverted(
    contactId: string,
    companyId: string,
    contactPhone: string,
    conversionType: string,
    conversionValue?: number,
    userId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.LEAD_CONVERTED,
      EventCategory.CONTACT,
      companyId,
      {
        contactId,
        contactPhone,
        conversionType,
        conversionValue,
        value: conversionValue || 1,
      },
      userId,
    );
  }

  async trackLeadScored(
    contactId: string,
    companyId: string,
    contactPhone: string,
    leadScore: number,
    previousScore?: number,
    userId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.LEAD_SCORED,
      EventCategory.CONTACT,
      companyId,
      {
        contactId,
        contactPhone,
        leadScore,
        previousScore,
        value: leadScore,
      },
      userId,
    );
  }

  // Eventos de conversão
  async trackConversationStarted(
    conversationId: string,
    companyId: string,
    connectionId: string,
    contactPhone: string,
    initiatedBy: 'contact' | 'user',
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.CONVERSATION_STARTED,
      EventCategory.CONVERSION,
      companyId,
      {
        conversationId,
        connectionId,
        contactPhone,
        initiatedBy,
      },
    );
  }

  async trackConversationEnded(
    conversationId: string,
    companyId: string,
    connectionId: string,
    contactPhone: string,
    duration: number,
    messageCount: number,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.CONVERSATION_ENDED,
      EventCategory.CONVERSION,
      companyId,
      {
        conversationId,
        connectionId,
        contactPhone,
        duration,
        messageCount,
        value: messageCount,
      },
    );
  }

  async trackFirstResponse(
    companyId: string,
    connectionId: string,
    contactPhone: string,
    responseTime: number,
    userId: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.FIRST_RESPONSE,
      EventCategory.CONVERSION,
      companyId,
      {
        connectionId,
        contactPhone,
        responseTime,
        value: responseTime,
      },
      userId,
    );
  }

  async trackResponseTime(
    companyId: string,
    connectionId: string,
    contactPhone: string,
    responseTime: number,
    userId: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.RESPONSE_TIME_MEASURED,
      EventCategory.CONVERSION,
      companyId,
      {
        connectionId,
        contactPhone,
        responseTime,
        value: responseTime,
      },
      userId,
    );
  }

  // Eventos de sistema
  async trackApiRequest(
    companyId: string,
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number,
    userId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.API_REQUEST,
      EventCategory.SYSTEM,
      companyId,
      {
        endpoint,
        method,
        responseTime,
        statusCode,
        duration: responseTime,
      },
      userId,
    );
  }

  async trackError(
    companyId: string,
    errorCode: string,
    errorMessage: string,
    context?: string,
    userId?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.ERROR_OCCURRED,
      EventCategory.SYSTEM,
      companyId,
      {
        errorCode,
        errorMessage,
        context,
      },
      userId,
    );
  }

  async trackPerformanceMetric(
    companyId: string,
    metricName: string,
    value: number,
    unit: string,
    context?: string,
  ): Promise<void> {
    await this.analyticsService.trackEvent(
      EventType.PERFORMANCE_METRIC,
      EventCategory.SYSTEM,
      companyId,
      {
        metricName,
        value,
        unit,
        context,
      },
    );
  }
}
