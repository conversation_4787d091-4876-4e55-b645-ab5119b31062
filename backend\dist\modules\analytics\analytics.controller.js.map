{"version": 3, "file": "analytics.controller.js", "sourceRoot": "", "sources": ["../../../src/modules/analytics/analytics.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,6CAMyB;AAEzB,yDAAiD;AACjD,oEAAgE;AAChE,kEAA8D;AAC9D,mEAImC;AACnC,yEAOsC;AACtC,2FAAgG;AAChG,uEAAkE;AAClE,iEAA6D;AAC7D,mEAA+D;AAC/D,yFAAgG;AAChG,6EAAgE;AAChE,sDAAmD;AACnD,2FAA0F;AAMnF,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAEX;IACA;IAFnB,YACmB,gBAAkC,EAClC,eAAgC;QADhC,qBAAgB,GAAhB,gBAAgB,CAAkB;QAClC,oBAAe,GAAf,eAAe,CAAiB;IAChD,CAAC;IAeE,AAAN,KAAK,CAAC,YAAY,CACP,KAAwB,EAClB,WAA8B;QAE7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACpF,OAAO,IAAA,gCAAY,EAAC,yCAAgB,EAAE,OAAO,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IACpF,CAAC;IAaK,AAAN,KAAK,CAAC,wBAAwB,CACnB,KAAwB,EAClB,WAA8B;QAE7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC3F,OAAO,IAAA,gCAAY,EAAC,8CAAqB,EAAE,SAAS,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IAaK,AAAN,KAAK,CAAC,wBAAwB,CACnB,KAAwB,EAClB,WAA8B;QAE7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC3F,OAAO,IAAA,gCAAY,EAAC,8CAAqB,EAAE,SAAS,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3F,CAAC;IAaK,AAAN,KAAK,CAAC,uBAAuB,CAClB,KAAwB,EAClB,WAA8B;QAE7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAC1F,OAAO,IAAA,gCAAY,EAAC,6CAAoB,EAAE,SAAS,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1F,CAAC;IAUK,AAAN,KAAK,CAAC,kBAAkB,CACb,KAAwB,EAClB,WAA8B;QAE7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QACnF,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAC1B,IAAA,gCAAY,EAAC,wCAAe,EAAE,MAAM,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CACzE,CAAC;IACJ,CAAC;IAUK,AAAN,KAAK,CAAC,aAAa,CACR,KAAwB,EAClB,WAA8B;QAE7C,MAAM,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC9E,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,CAAC;YAC7D,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC;YAClE,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC;YAClE,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC;SAClE,CAAC,CAAC;QAEH,MAAM,MAAM,GAA4B;YACtC,SAAS;YACT,aAAa;YACb,YAAY;YACZ,WAAW;YACX,MAAM,EAAE;gBACN,KAAK,EAAE,KAAK,CAAC,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;gBACtF,GAAG,EAAE,KAAK,CAAC,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aAC/C;YACD,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC;QAEF,OAAO,IAAA,gCAAY,EAAC,gDAAuB,EAAE,MAAM,EAAE,EAAE,uBAAuB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1F,CAAC;IAUK,AAAN,KAAK,CAAC,YAAY,CACP,KAAqB,EACf,WAA8B,EACtC,GAAa;QAEpB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC;QAGtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAG5D,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,KAAK;gBACR,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;gBAC1C,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,2CAA2C,CAAC,CAAC;gBAElF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;gBACpC,MAAM;YAER,KAAK,OAAO;gBACV,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,mEAAmE,CAAC,CAAC;gBACnG,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,4CAA4C,CAAC,CAAC;gBAEnF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC;gBACtC,MAAM;YAER;gBACE,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;gBAClD,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,4CAA4C,CAAC,CAAC;gBACnF,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAUK,AAAN,KAAK,CAAC,gBAAgB,CACZ,SAIP,EACc,WAA8B;QAE7C,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACpC,SAAS,CAAC,IAAI,EACd,SAAS,CAAC,QAAQ,EAClB,WAAW,CAAC,SAAS,EACrB,SAAS,CAAC,UAAU,IAAI,EAAE,EAC1B,WAAW,CAAC,EAAE,CACf,CAAC;IACJ,CAAC;IAiBK,AAAN,KAAK,CAAC,kBAAkB;QACtB,OAAO;YACL,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,kCAAS,CAAC;YACpC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,sCAAa,CAAC;SACzC,CAAC;IACJ,CAAC;IAIO,YAAY,CAAC,IAAS;QAE5B,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG;YACX,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,CAAC;YACpJ,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,aAAa,IAAI,CAAC,CAAC;SAErK,CAAC;QAEF,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC;aAClC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACpD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,cAAc,CAAC,IAAS;QAG9B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;CACF,CAAA;AAvPY,kDAAmB;AAmBxB;IAbL,IAAA,YAAG,EAAC,WAAW,CAAC;IAChB,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACxD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACjF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC/E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC1E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACrG,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,+BAA+B,EAAE,CAAC;IACvH,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,yCAAgB;KACvB,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;uDAKlC;AAaK;IAXL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACjF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC/E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC1E,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,wBAAwB;QACrC,IAAI,EAAE,8CAAqB;KAC5B,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;mEAKlC;AAaK;IAXL,IAAA,YAAG,EAAC,eAAe,CAAC;IACpB,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0CAA0C,EAAE,CAAC;IACrE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACjF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC/E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC1E,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,oCAAoC;QACjD,IAAI,EAAE,8CAAqB;KAC5B,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;mEAKlC;AAaK;IAXL,IAAA,YAAG,EAAC,aAAa,CAAC;IAClB,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;IAC3D,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;IACjF,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC;IAC/E,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IAC1E,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,6CAAoB;KAC3B,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;kEAKlC;AAUK;IARL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC;IACzD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,CAAC,wCAAe,CAAC;KACxB,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;6DAOlC;AAUK;IARL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,0CAAkB,EAAC,mCAAW,CAAC,gBAAgB,CAAC;IAChD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iCAAiC;QAC9C,IAAI,EAAE,gDAAuB;KAC9B,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;;qCADE,uCAAiB;;wDAuBlC;AAUK;IARL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,0CAAkB,EAAC,mCAAW,CAAC,gBAAgB,CAAC;IAChD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,2CAA2C,EAAE,CAAC;IACtE,IAAA,kBAAQ,EAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,mBAAmB,EAAE,CAAC;IAC/G,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,sBAAsB;KACpC,CAAC;IAEC,WAAA,IAAA,cAAK,GAAE,CAAA;IACP,WAAA,IAAA,oCAAW,GAAE,CAAA;IACb,WAAA,IAAA,YAAG,GAAE,CAAA;;qCAFU,oCAAc;;uDA8B/B;AAUK;IARL,IAAA,aAAI,EAAC,OAAO,CAAC;IACb,IAAA,0CAAkB,EAAC,mCAAW,CAAC,cAAc,CAAC;IAC9C,IAAA,iBAAQ,EAAC,mBAAU,CAAC,UAAU,CAAC;IAC/B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,6BAA6B,EAAE,CAAC;IACxD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,8BAA8B;KAC5C,CAAC;IAEC,WAAA,IAAA,aAAI,GAAE,CAAA;IAKN,WAAA,IAAA,oCAAW,GAAE,CAAA;;;;2DASf;AAiBK;IAfL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,uBAAK,EAAC,mBAAQ,CAAC,WAAW,EAAE,mBAAQ,CAAC,YAAY,CAAC;IAClD,IAAA,0CAAkB,EAAC,mCAAW,CAAC,iBAAiB,CAAC;IACjD,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,wCAAwC;QACrD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;gBACxD,UAAU,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;aACzD;SACF;KACF,CAAC;;;;6DAMD;8BA9NU,mBAAmB;IAJ/B,IAAA,iBAAO,EAAC,WAAW,CAAC;IACpB,IAAA,mBAAU,EAAC,WAAW,CAAC;IACvB,IAAA,kBAAS,EAAC,6BAAY,EAAE,0BAAW,EAAE,wBAAU,CAAC;IAChD,IAAA,uBAAa,GAAE;qCAGuB,oCAAgB;QACjB,kCAAe;GAHxC,mBAAmB,CAuP/B"}