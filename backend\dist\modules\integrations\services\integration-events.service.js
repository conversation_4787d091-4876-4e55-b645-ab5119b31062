"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var IntegrationEventsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationEventsService = void 0;
const common_1 = require("@nestjs/common");
const integrations_service_1 = require("./integrations.service");
const integration_entity_1 = require("../../../database/entities/integration.entity");
let IntegrationEventsService = IntegrationEventsService_1 = class IntegrationEventsService {
    integrationsService;
    logger = new common_1.Logger(IntegrationEventsService_1.name);
    constructor(integrationsService) {
        this.integrationsService = integrationsService;
    }
    async onMessageReceived(data) {
        this.logger.log(`Triggering MESSAGE_RECEIVED integrations for contact ${data.contactPhone}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.MESSAGE_RECEIVED, {
            message: {
                id: data.messageId,
                content: data.content,
                type: data.type,
                timestamp: data.timestamp,
            },
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
                tags: data.contactTags || [],
            },
            connection: {
                id: data.connectionId,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: data.timestamp,
        }, data.companyId);
    }
    async onMessageSent(data) {
        this.logger.log(`Triggering MESSAGE_SENT integrations for contact ${data.contactPhone}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.MESSAGE_SENT, {
            message: {
                id: data.messageId,
                content: data.content,
                type: data.type,
                timestamp: data.timestamp,
            },
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
                tags: data.contactTags || [],
            },
            connection: {
                id: data.connectionId,
            },
            user: {
                id: data.userId,
            },
            timestamp: data.timestamp,
        }, data.companyId);
    }
    async onContactCreated(data) {
        this.logger.log(`Triggering CONTACT_CREATED integrations for contact ${data.contactPhone}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.CONTACT_CREATED, {
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
                tags: data.contactTags || [],
                source: data.source,
                customFields: data.customFields || {},
            },
            connection: {
                id: data.connectionId,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onContactUpdated(data) {
        this.logger.log(`Triggering CONTACT_UPDATED integrations for contact ${data.contactPhone}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.CONTACT_UPDATED, {
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
                tags: data.contactTags || [],
                customFields: data.customFields || {},
            },
            connection: {
                id: data.connectionId,
            },
            user: {
                id: data.userId,
            },
            changes: data.changes,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onLeadConverted(data) {
        this.logger.log(`Triggering LEAD_CONVERTED integrations for lead ${data.leadId}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.LEAD_CONVERTED, {
            lead: {
                id: data.leadId,
                conversionData: data.conversionData || {},
            },
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
                tags: data.contactTags || [],
            },
            connection: {
                id: data.connectionId,
            },
            user: {
                id: data.userId,
            },
            timestamp: new Date(),
        }, data.companyId);
    }
    async onAutomationTriggered(data) {
        this.logger.log(`Triggering AUTOMATION_TRIGGERED integrations for automation ${data.automationName}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.AUTOMATION_TRIGGERED, {
            automation: {
                id: data.automationId,
                name: data.automationName,
                executionId: data.executionId,
                triggerType: data.triggerType,
            },
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
            },
            connection: {
                id: data.connectionId,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onAutomationCompleted(data) {
        this.logger.log(`Triggering AUTOMATION_COMPLETED integrations for automation ${data.automationName}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.AUTOMATION_COMPLETED, {
            automation: {
                id: data.automationId,
                name: data.automationName,
                executionId: data.executionId,
                status: data.status,
                duration: data.duration,
                results: data.results,
            },
            contact: {
                id: data.contactId,
                phoneNumber: data.contactPhone,
                name: data.contactName,
            },
            connection: {
                id: data.connectionId,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onConnectionStatusChanged(data) {
        this.logger.log(`Triggering CONNECTION_STATUS_CHANGED integrations for connection ${data.connectionName}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.CONNECTION_STATUS_CHANGED, {
            connection: {
                id: data.connectionId,
                name: data.connectionName,
                phoneNumber: data.phoneNumber,
                oldStatus: data.oldStatus,
                newStatus: data.newStatus,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onUserCreated(data) {
        this.logger.log(`Triggering USER_CREATED integrations for user ${data.userName}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.USER_CREATED, {
            user: {
                id: data.userId,
                name: data.userName,
                email: data.userEmail,
                role: data.userRole,
            },
            createdBy: {
                id: data.createdBy,
            },
            timestamp: new Date(),
        }, data.companyId);
    }
    async onSubscriptionChanged(data) {
        this.logger.log(`Triggering SUBSCRIPTION_CHANGED integrations for subscription ${data.subscriptionId}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.SUBSCRIPTION_CHANGED, {
            subscription: {
                id: data.subscriptionId,
                planName: data.planName,
                oldStatus: data.oldStatus,
                newStatus: data.newStatus,
                changeType: data.changeType,
            },
            user: data.userId ? { id: data.userId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
    async onPaymentReceived(data) {
        this.logger.log(`Triggering PAYMENT_RECEIVED integrations for payment ${data.paymentId}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.PAYMENT_RECEIVED, {
            payment: {
                id: data.paymentId,
                subscriptionId: data.subscriptionId,
                amount: data.amount,
                currency: data.currency,
                paymentMethod: data.paymentMethod,
                status: data.status,
            },
            timestamp: new Date(),
        }, data.companyId);
    }
    async onCustomEvent(data) {
        this.logger.log(`Triggering CUSTOM_EVENT integrations for event ${data.eventName}`);
        await this.integrationsService.triggerIntegration(integration_entity_1.TriggerEvent.CUSTOM_EVENT, {
            customEvent: {
                name: data.eventName,
                data: data.eventData,
            },
            user: data.userId ? { id: data.userId } : undefined,
            contact: data.contactId ? { id: data.contactId } : undefined,
            connection: data.connectionId ? { id: data.connectionId } : undefined,
            timestamp: new Date(),
        }, data.companyId);
    }
};
exports.IntegrationEventsService = IntegrationEventsService;
exports.IntegrationEventsService = IntegrationEventsService = IntegrationEventsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [integrations_service_1.IntegrationsService])
], IntegrationEventsService);
//# sourceMappingURL=integration-events.service.js.map