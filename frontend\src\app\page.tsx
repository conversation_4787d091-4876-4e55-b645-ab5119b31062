﻿'use client'

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, MessageSquare, Users, BarChart3, Zap, Shield, Globe } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
        <div className="absolute top-0 -right-4 w-72 h-72 bg-yellow-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Navigation */}
      <nav className="relative z-10 p-6">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center space-x-2"
          >
            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-white" />
            </div>
            <span className="text-white text-xl font-bold">WhatsApp Platform</span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex space-x-4"
          >
            <Button variant="ghost" className="text-white hover:bg-white/10">
              <a href="/auth/login">Login</a>
            </Button>
            <Button variant="whatsapp" size="lg">
              <a href="/auth/register">Começar Agora</a>
            </Button>
          </motion.div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 pt-20 pb-32">
        <div className="text-center">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight"
          >
            O Futuro do
            <span className="bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent"> WhatsApp Business</span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Transforme sua comunicação empresarial com nossa plataforma inovadora.
            Gerencie múltiplas conexões, automatize processos e acompanhe métricas em tempo real.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button variant="gradient" size="xl" className="group">
              <a href="/auth/register" className="flex items-center">
                Começar Gratuitamente
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </a>
            </Button>
            <Button variant="glass" size="xl">
              <a href="#features">Ver Demonstração</a>
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Features Section */}
      <div id="features" className="relative z-10 max-w-7xl mx-auto px-6 pb-20">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Recursos Inovadores
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Descubra como nossa plataforma pode revolucionar sua comunicação empresarial
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[
            {
              icon: MessageSquare,
              title: "Múltiplas Conexões",
              description: "Gerencie várias contas WhatsApp Business simultaneamente com facilidade total",
              color: "from-green-400 to-green-600"
            },
            {
              icon: Zap,
              title: "Tempo Real",
              description: "Mensagens instantâneas com WebSocket para comunicação ultra-rápida",
              color: "from-yellow-400 to-orange-600"
            },
            {
              icon: BarChart3,
              title: "Analytics Avançado",
              description: "Relatórios detalhados e métricas em tempo real para otimizar sua estratégia",
              color: "from-blue-400 to-purple-600"
            },
            {
              icon: Users,
              title: "Gestão de Contatos",
              description: "Organize e segmente seus contatos de forma inteligente e eficiente",
              color: "from-purple-400 to-pink-600"
            },
            {
              icon: Shield,
              title: "Segurança Total",
              description: "Criptografia end-to-end e proteção avançada para seus dados",
              color: "from-red-400 to-red-600"
            },
            {
              icon: Globe,
              title: "API Completa",
              description: "Integre facilmente com seus sistemas existentes através da nossa API",
              color: "from-cyan-400 to-blue-600"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <div className="glass p-8 rounded-2xl h-full hover:bg-white/20 transition-all duration-300">
                <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative z-10 max-w-4xl mx-auto px-6 pb-20">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="glass p-12 rounded-3xl text-center"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Pronto para Revolucionar sua Comunicação?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Junte-se a milhares de empresas que já transformaram sua comunicação com nossa plataforma
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="whatsapp" size="xl" className="group">
              <a href="/auth/register" className="flex items-center">
                Começar Agora - É Grátis
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </a>
            </Button>
            <Button variant="outline" size="xl" className="border-white/20 text-white hover:bg-white/10">
              <a href="/auth/login">Já tenho uma conta</a>
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
