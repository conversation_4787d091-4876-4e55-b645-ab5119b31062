﻿export default function Home() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">WhatsApp Platform</h1>
        <p className="text-xl text-gray-600 mb-8">Plataforma de gerenciamento de WhatsApp para empresas</p>

        <div className="space-x-4">
          <a
            href="/auth/login"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Fazer Login
          </a>
          <a
            href="/auth/register"
            className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cadastrar
          </a>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <span className="text-green-600 text-2xl">📱</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Conexões WhatsApp</h3>
            <p className="text-gray-600">Gerencie múltiplas conexões WhatsApp Business</p>
          </div>

          <div className="text-center">
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <span className="text-blue-600 text-2xl">💬</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Mensagens em Tempo Real</h3>
            <p className="text-gray-600">Envie e receba mensagens instantaneamente</p>
          </div>

          <div className="text-center">
            <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <span className="text-purple-600 text-2xl">📊</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Analytics</h3>
            <p className="text-gray-600">Acompanhe métricas e relatórios detalhados</p>
          </div>
        </div>
      </div>
    </div>
  )
}
