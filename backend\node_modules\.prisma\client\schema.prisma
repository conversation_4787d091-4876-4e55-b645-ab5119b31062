// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  ADMIN
  MANAGER
  SELLER
  SUPPORT
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

enum CompanyStatus {
  TRIAL
  ACTIVE
  SUSPENDED
  CANCELLED
}

enum ConnectionType {
  EVOLUTION_API
  BAILEYS
  WEB_WHATSAPP
}

enum ConnectionStatus {
  CONNECTED
  DISCONNECTED
  PENDING
  ERROR
  CONNECTING
}

enum MessageType {
  TEXT
  IMAGE
  VIDEO
  AUDIO
  DOCUMENT
  LOCATION
  CONTACT
  STICKER
}

enum MessageDirection {
  INBOUND
  OUTBOUND
}

enum MessageStatus {
  PENDING
  SENT
  DELIVERED
  READ
  FAILED
}

// Models
model Company {
  id        String        @id @default(cuid())
  name      String
  cnpj      String        @unique
  email     String
  phone     String
  address   String?
  website   String?
  status    CompanyStatus @default(TRIAL)
  settings  Json?
  logo      String?
  agencyId  String?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Relations
  users               User[]
  whatsappConnections WhatsAppConnection[]
  messages            Message[]
  contacts            Contact[]
  conversations       Conversation[]

  @@map("companies")
}

model User {
  id                 String     @id @default(cuid())
  firstName          String
  lastName           String
  email              String     @unique
  password           String
  phone              String?
  role               UserRole   @default(SELLER)
  status             UserStatus @default(ACTIVE)
  avatar             String?
  lastLoginAt        DateTime?
  emailVerifiedAt    DateTime?
  preferences        Json?
  permissions        String[]
  companyId          String
  refreshToken       String?
  refreshTokenExpiry DateTime?
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt

  // Relations
  company               Company              @relation(fields: [companyId], references: [id], onDelete: Cascade)
  assignedConnections   WhatsAppConnection[] @relation("AssignedUser")
  sentMessages          Message[]            @relation("MessageSender")
  assignedConversations Conversation[]       @relation("AssignedUser")

  @@map("users")
}

model WhatsAppConnection {
  id                 String           @id @default(cuid())
  name               String
  phoneNumber        String
  type               ConnectionType   @default(EVOLUTION_API)
  status             ConnectionStatus @default(DISCONNECTED)
  qrCode             String?
  instanceId         String?
  connectionData     Json?
  settings           Json?
  lastConnectedAt    DateTime?
  lastDisconnectedAt DateTime?
  errorMessage       String?
  isActive           Boolean          @default(true)
  companyId          String
  assignedUserId     String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt

  // Relations
  company       Company        @relation(fields: [companyId], references: [id], onDelete: Cascade)
  assignedUser  User?          @relation("AssignedUser", fields: [assignedUserId], references: [id])
  messages      Message[]
  contacts      Contact[]
  conversations Conversation[]

  @@map("whatsapp_connections")
}

model Contact {
  id                   String    @id @default(cuid())
  phone                String
  name                 String?
  email                String?
  avatar               String?
  tags                 String[]
  notes                String?
  customFields         Json?
  lastMessageAt        DateTime?
  messageCount         Int       @default(0)
  companyId            String
  whatsappConnectionId String
  isBlocked            Boolean   @default(false)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Relations
  company            Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  whatsappConnection WhatsAppConnection @relation(fields: [whatsappConnectionId], references: [id], onDelete: Cascade)
  messages           Message[]
  conversations      Conversation[]

  @@unique([phone, whatsappConnectionId])
  @@map("contacts")
}

model Conversation {
  id                   String    @id @default(cuid())
  contactPhone         String
  contactId            String?
  whatsappConnectionId String
  lastMessageId        String?
  lastMessageAt        DateTime?
  unreadCount          Int       @default(0)
  isArchived           Boolean   @default(false)
  assignedUserId       String?
  tags                 String[]
  notes                String?
  companyId            String
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  // Relations
  company            Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  contact            Contact?           @relation(fields: [contactId], references: [id])
  whatsappConnection WhatsAppConnection @relation(fields: [whatsappConnectionId], references: [id], onDelete: Cascade)
  assignedUser       User?              @relation("AssignedUser", fields: [assignedUserId], references: [id])
  lastMessage        Message?           @relation("LastMessage", fields: [lastMessageId], references: [id])
  messages           Message[]          @relation("ConversationMessages")

  @@unique([contactPhone, whatsappConnectionId])
  @@map("conversations")
}

model Message {
  id                   String           @id @default(cuid())
  messageId            String           @unique // ID da mensagem no WhatsApp
  conversationId       String?
  whatsappConnectionId String
  contactPhone         String
  contactId            String?
  content              Json // Conteúdo da mensagem (texto, mídia, etc.)
  type                 MessageType      @default(TEXT)
  direction            MessageDirection
  status               MessageStatus    @default(PENDING)
  timestamp            DateTime
  readAt               DateTime?
  deliveredAt          DateTime?
  metadata             Json?
  companyId            String
  userId               String? // Usuário que enviou (se outbound)
  createdAt            DateTime         @default(now())
  updatedAt            DateTime         @updatedAt

  // Relations
  company            Company            @relation(fields: [companyId], references: [id], onDelete: Cascade)
  whatsappConnection WhatsAppConnection @relation(fields: [whatsappConnectionId], references: [id], onDelete: Cascade)
  contact            Contact?           @relation(fields: [contactId], references: [id])
  conversation       Conversation?      @relation("ConversationMessages", fields: [conversationId], references: [id])
  user               User?              @relation("MessageSender", fields: [userId], references: [id])
  lastMessageOf      Conversation[]     @relation("LastMessage")

  @@map("messages")
}

// Tabelas auxiliares para analytics e logs
model Analytics {
  id        String   @id @default(cuid())
  companyId String
  date      DateTime
  metrics   Json // Métricas do dia (mensagens enviadas, recebidas, etc.)
  createdAt DateTime @default(now())

  @@unique([companyId, date])
  @@map("analytics")
}

model AuditLog {
  id         String   @id @default(cuid())
  userId     String?
  companyId  String
  action     String
  resource   String
  resourceId String?
  oldData    Json?
  newData    Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())

  @@map("audit_logs")
}

// Tabela para automações (futuro)
model Automation {
  id          String   @id @default(cuid())
  name        String
  description String?
  trigger     Json // Condições para disparar
  actions     Json // Ações a serem executadas
  isActive    Boolean  @default(true)
  companyId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("automations")
}

// Tabela para templates de mensagem
model MessageTemplate {
  id        String   @id @default(cuid())
  name      String
  content   String
  variables String[] // Variáveis disponíveis no template
  category  String?
  companyId String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("message_templates")
}
