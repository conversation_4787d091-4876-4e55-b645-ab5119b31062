"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const core_1 = require("@nestjs/core");
const analytics_event_schema_1 = require("../../database/entities/analytics-event.schema");
const analytics_service_1 = require("./services/analytics.service");
const tracking_service_1 = require("./services/tracking.service");
const analytics_controller_1 = require("./analytics.controller");
const analytics_interceptor_1 = require("./interceptors/analytics.interceptor");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: analytics_event_schema_1.AnalyticsEvent.name, schema: analytics_event_schema_1.AnalyticsEventSchema },
            ]),
        ],
        controllers: [analytics_controller_1.AnalyticsController],
        providers: [
            analytics_service_1.AnalyticsService,
            tracking_service_1.TrackingService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: analytics_interceptor_1.AnalyticsInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: analytics_interceptor_1.AuthAnalyticsInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: analytics_interceptor_1.WhatsAppAnalyticsInterceptor,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: analytics_interceptor_1.ContactAnalyticsInterceptor,
            },
        ],
        exports: [analytics_service_1.AnalyticsService, tracking_service_1.TrackingService],
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map