import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import configuration from './config/configuration';
import { getDatabaseConfig } from './config/database.config';
import { TypeOrmEntities } from './database/entities';

// Módulos básicos apenas
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';

@Module({
  imports: [
    // Configuração
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),

    // PostgreSQL (SQLite em memória para desenvolvimento)
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: getDatabaseConfig,
      inject: [ConfigService],
    }),

    // Rate limiting (removido temporariamente)

    // Módulos da aplicação (apenas os básicos)
    AuthModule,
    UsersModule,
  ],
  controllers: [],
  providers: [],
})
export class AppSimpleModule {}
