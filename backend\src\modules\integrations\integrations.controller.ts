import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { IntegrationsService, FindIntegrationsOptions, FindWebhookLogsOptions } from './services/integrations.service';
import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { CurrentUser, AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { RequirePermissions, PERMISSIONS } from '../../common/decorators/permissions.decorator';
import { IntegrationType, IntegrationStatus, TriggerEvent } from '../../database/entities/integration.entity';
import { WebhookStatus } from '../../database/entities/webhook-log.entity';

@ApiTags('Integrações')
@Controller('integrations')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@ApiBearerAuth()
export class IntegrationsController {
  constructor(private readonly integrationsService: IntegrationsService) {}

  @Post()
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_CREATE)
  @ApiOperation({ summary: 'Criar nova integração' })
  @ApiResponse({
    status: 201,
    description: 'Integração criada com sucesso',
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe uma integração com este nome',
  })
  async create(
    @Body() createIntegrationDto: CreateIntegrationDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.create(createIntegrationDto, currentUser);
  }

  @Get()
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_READ)
  @ApiOperation({ summary: 'Listar integrações' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filtrar por empresa' })
  @ApiQuery({ name: 'type', required: false, enum: IntegrationType, description: 'Filtrar por tipo' })
  @ApiQuery({ name: 'status', required: false, enum: IntegrationStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'active', required: false, type: Boolean, description: 'Filtrar por ativo/inativo' })
  @ApiQuery({ name: 'triggerEvent', required: false, enum: TriggerEvent, description: 'Filtrar por evento' })
  @ApiQuery({ name: 'search', required: false, description: 'Buscar por nome ou descrição' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiResponse({
    status: 200,
    description: 'Lista de integrações',
  })
  async findAll(
    @Query() query: FindIntegrationsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.findAll(query, currentUser);
  }

  @Get('stats')
  @RequirePermissions(PERMISSIONS.ANALYTICS_READ)
  @ApiOperation({ summary: 'Obter estatísticas de integrações' })
  @ApiResponse({
    status: 200,
    description: 'Estatísticas de integrações',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        inactive: { type: 'number' },
        error: { type: 'number' },
        totalExecutions: { type: 'number' },
        successRate: { type: 'number' },
      },
    },
  })
  async getStats(@CurrentUser() currentUser: AuthenticatedUser) {
    return this.integrationsService.getIntegrationStats(currentUser);
  }

  @Get('types')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_READ)
  @ApiOperation({ summary: 'Listar tipos de integração disponíveis' })
  @ApiResponse({
    status: 200,
    description: 'Lista de tipos de integração',
  })
  async getTypes() {
    return {
      types: Object.values(IntegrationType).map(type => ({
        value: type,
        label: this.getTypeLabel(type),
        description: this.getTypeDescription(type),
        category: this.getTypeCategory(type),
      })),
      events: Object.values(TriggerEvent).map(event => ({
        value: event,
        label: this.getEventLabel(event),
        description: this.getEventDescription(event),
      })),
    };
  }

  @Get(':id')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_READ)
  @ApiOperation({ summary: 'Obter integração por ID' })
  @ApiResponse({
    status: 200,
    description: 'Dados da integração',
  })
  @ApiResponse({
    status: 404,
    description: 'Integração não encontrada',
  })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.findOne(id, currentUser);
  }

  @Patch(':id')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_UPDATE)
  @ApiOperation({ summary: 'Atualizar integração' })
  @ApiResponse({
    status: 200,
    description: 'Integração atualizada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Integração não encontrada',
  })
  async update(
    @Param('id') id: string,
    @Body() updateIntegrationDto: UpdateIntegrationDto,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.update(id, updateIntegrationDto, currentUser);
  }

  @Delete(':id')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_DELETE)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Deletar integração' })
  @ApiResponse({
    status: 204,
    description: 'Integração deletada com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Integração não encontrada',
  })
  async remove(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.remove(id, currentUser);
  }

  @Post(':id/test')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_UPDATE)
  @ApiOperation({ summary: 'Testar integração' })
  @ApiResponse({
    status: 200,
    description: 'Teste da integração executado',
  })
  async test(
    @Param('id') id: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.testIntegration(id, currentUser);
  }

  @Get(':id/logs')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_READ)
  @ApiOperation({ summary: 'Listar logs da integração' })
  @ApiQuery({ name: 'status', required: false, enum: WebhookStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'eventType', required: false, description: 'Filtrar por tipo de evento' })
  @ApiQuery({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' })
  @ApiQuery({ name: 'dateTo', required: false, type: Date, description: 'Data final' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiResponse({
    status: 200,
    description: 'Lista de logs da integração',
  })
  async getLogs(
    @Param('id') id: string,
    @Query() query: FindWebhookLogsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    query.integrationId = id;
    return this.integrationsService.getWebhookLogs(query, currentUser);
  }

  @Get('logs/all')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_READ)
  @ApiOperation({ summary: 'Listar todos os logs de webhook' })
  @ApiQuery({ name: 'integrationId', required: false, description: 'Filtrar por integração' })
  @ApiQuery({ name: 'status', required: false, enum: WebhookStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'eventType', required: false, description: 'Filtrar por tipo de evento' })
  @ApiQuery({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' })
  @ApiQuery({ name: 'dateTo', required: false, type: Date, description: 'Data final' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiResponse({
    status: 200,
    description: 'Lista de logs de webhook',
  })
  async getAllLogs(
    @Query() query: FindWebhookLogsOptions,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.getWebhookLogs(query, currentUser);
  }

  @Post('logs/:logId/retry')
  @RequirePermissions(PERMISSIONS.INTEGRATIONS_UPDATE)
  @ApiOperation({ summary: 'Tentar novamente webhook falhado' })
  @ApiResponse({
    status: 200,
    description: 'Webhook reagendado para nova tentativa',
  })
  async retryWebhook(
    @Param('logId') logId: string,
    @CurrentUser() currentUser: AuthenticatedUser,
  ) {
    return this.integrationsService.retryWebhook(logId, currentUser);
  }

  // Métodos auxiliares para labels e descrições
  private getTypeLabel(type: IntegrationType): string {
    const labels = {
      [IntegrationType.WEBHOOK]: 'Webhook',
      [IntegrationType.GOOGLE_SHEETS]: 'Google Sheets',
      [IntegrationType.ZAPIER]: 'Zapier',
      [IntegrationType.CRM_HUBSPOT]: 'HubSpot CRM',
      [IntegrationType.CRM_SALESFORCE]: 'Salesforce CRM',
      [IntegrationType.CRM_PIPEDRIVE]: 'Pipedrive CRM',
      [IntegrationType.EMAIL_MAILCHIMP]: 'Mailchimp',
      [IntegrationType.EMAIL_SENDGRID]: 'SendGrid',
      [IntegrationType.SMS_TWILIO]: 'Twilio SMS',
      [IntegrationType.SLACK]: 'Slack',
      [IntegrationType.DISCORD]: 'Discord',
      [IntegrationType.TELEGRAM]: 'Telegram',
      [IntegrationType.CUSTOM_API]: 'API Customizada',
    };
    return labels[type] || type;
  }

  private getTypeDescription(type: IntegrationType): string {
    const descriptions = {
      [IntegrationType.WEBHOOK]: 'Envie dados para qualquer URL via HTTP',
      [IntegrationType.GOOGLE_SHEETS]: 'Sincronize dados com planilhas do Google',
      [IntegrationType.ZAPIER]: 'Conecte com milhares de aplicativos via Zapier',
      [IntegrationType.CRM_HUBSPOT]: 'Integre com HubSpot CRM',
      [IntegrationType.CRM_SALESFORCE]: 'Integre com Salesforce CRM',
      [IntegrationType.CRM_PIPEDRIVE]: 'Integre with Pipedrive CRM',
      [IntegrationType.EMAIL_MAILCHIMP]: 'Envie contatos para listas do Mailchimp',
      [IntegrationType.EMAIL_SENDGRID]: 'Envie emails via SendGrid',
      [IntegrationType.SMS_TWILIO]: 'Envie SMS via Twilio',
      [IntegrationType.SLACK]: 'Envie notificações para o Slack',
      [IntegrationType.DISCORD]: 'Envie notificações para o Discord',
      [IntegrationType.TELEGRAM]: 'Envie notificações para o Telegram',
      [IntegrationType.CUSTOM_API]: 'Integre com APIs customizadas',
    };
    return descriptions[type] || '';
  }

  private getTypeCategory(type: IntegrationType): string {
    const categories = {
      [IntegrationType.WEBHOOK]: 'webhook',
      [IntegrationType.GOOGLE_SHEETS]: 'productivity',
      [IntegrationType.ZAPIER]: 'automation',
      [IntegrationType.CRM_HUBSPOT]: 'crm',
      [IntegrationType.CRM_SALESFORCE]: 'crm',
      [IntegrationType.CRM_PIPEDRIVE]: 'crm',
      [IntegrationType.EMAIL_MAILCHIMP]: 'email',
      [IntegrationType.EMAIL_SENDGRID]: 'email',
      [IntegrationType.SMS_TWILIO]: 'sms',
      [IntegrationType.SLACK]: 'communication',
      [IntegrationType.DISCORD]: 'communication',
      [IntegrationType.TELEGRAM]: 'communication',
      [IntegrationType.CUSTOM_API]: 'custom',
    };
    return categories[type] || 'other';
  }

  private getEventLabel(event: TriggerEvent): string {
    const labels = {
      [TriggerEvent.MESSAGE_RECEIVED]: 'Mensagem Recebida',
      [TriggerEvent.MESSAGE_SENT]: 'Mensagem Enviada',
      [TriggerEvent.CONTACT_CREATED]: 'Contato Criado',
      [TriggerEvent.CONTACT_UPDATED]: 'Contato Atualizado',
      [TriggerEvent.LEAD_CONVERTED]: 'Lead Convertido',
      [TriggerEvent.AUTOMATION_TRIGGERED]: 'Automação Disparada',
      [TriggerEvent.AUTOMATION_COMPLETED]: 'Automação Concluída',
      [TriggerEvent.CONNECTION_STATUS_CHANGED]: 'Status da Conexão Alterado',
      [TriggerEvent.USER_CREATED]: 'Usuário Criado',
      [TriggerEvent.SUBSCRIPTION_CHANGED]: 'Assinatura Alterada',
      [TriggerEvent.PAYMENT_RECEIVED]: 'Pagamento Recebido',
      [TriggerEvent.CUSTOM_EVENT]: 'Evento Customizado',
    };
    return labels[event] || event;
  }

  private getEventDescription(event: TriggerEvent): string {
    const descriptions = {
      [TriggerEvent.MESSAGE_RECEIVED]: 'Disparado quando uma mensagem é recebida',
      [TriggerEvent.MESSAGE_SENT]: 'Disparado quando uma mensagem é enviada',
      [TriggerEvent.CONTACT_CREATED]: 'Disparado quando um novo contato é criado',
      [TriggerEvent.CONTACT_UPDATED]: 'Disparado quando um contato é atualizado',
      [TriggerEvent.LEAD_CONVERTED]: 'Disparado quando um lead é convertido',
      [TriggerEvent.AUTOMATION_TRIGGERED]: 'Disparado quando uma automação é iniciada',
      [TriggerEvent.AUTOMATION_COMPLETED]: 'Disparado quando uma automação é concluída',
      [TriggerEvent.CONNECTION_STATUS_CHANGED]: 'Disparado quando o status de uma conexão muda',
      [TriggerEvent.USER_CREATED]: 'Disparado quando um novo usuário é criado',
      [TriggerEvent.SUBSCRIPTION_CHANGED]: 'Disparado quando uma assinatura é alterada',
      [TriggerEvent.PAYMENT_RECEIVED]: 'Disparado quando um pagamento é recebido',
      [TriggerEvent.CUSTOM_EVENT]: 'Disparado por eventos customizados',
    };
    return descriptions[event] || '';
  }
}
