"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var BillingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const plan_entity_1 = require("../../../database/entities/plan.entity");
const subscription_entity_1 = require("../../../database/entities/subscription.entity");
const invoice_entity_1 = require("../../../database/entities/invoice.entity");
const payment_entity_1 = require("../../../database/entities/payment.entity");
const company_entity_1 = require("../../../database/entities/company.entity");
const entities_1 = require("../../../database/entities");
let BillingService = BillingService_1 = class BillingService {
    planRepository;
    subscriptionRepository;
    invoiceRepository;
    paymentRepository;
    companyRepository;
    logger = new common_1.Logger(BillingService_1.name);
    constructor(planRepository, subscriptionRepository, invoiceRepository, paymentRepository, companyRepository) {
        this.planRepository = planRepository;
        this.subscriptionRepository = subscriptionRepository;
        this.invoiceRepository = invoiceRepository;
        this.paymentRepository = paymentRepository;
        this.companyRepository = companyRepository;
    }
    async createPlan(createPlanDto, currentUser) {
        const existingPlan = await this.planRepository.findOne({
            where: { name: createPlanDto.name },
        });
        if (existingPlan) {
            throw new common_1.ConflictException('Já existe um plano com este nome');
        }
        const plan = this.planRepository.create(createPlanDto);
        const savedPlan = await this.planRepository.save(plan);
        this.logger.log(`Plan created: ${savedPlan.id} by ${currentUser.email}`);
        return savedPlan;
    }
    async findAllPlans(options) {
        const { type, status, isPublic, isAgencyPlan, targetAudience, search, page = 1, limit = 10, sortBy = 'sortOrder', sortOrder = 'ASC', } = options;
        const queryBuilder = this.planRepository.createQueryBuilder('plan');
        if (type) {
            queryBuilder.andWhere('plan.type = :type', { type });
        }
        if (status) {
            queryBuilder.andWhere('plan.status = :status', { status });
        }
        if (typeof isPublic === 'boolean') {
            queryBuilder.andWhere('plan.isPublic = :isPublic', { isPublic });
        }
        if (typeof isAgencyPlan === 'boolean') {
            queryBuilder.andWhere('plan.isAgencyPlan = :isAgencyPlan', { isAgencyPlan });
        }
        if (targetAudience) {
            queryBuilder.andWhere('plan.targetAudience = :targetAudience', { targetAudience });
        }
        if (search) {
            queryBuilder.andWhere('(plan.name ILIKE :search OR plan.description ILIKE :search)', { search: `%${search}%` });
        }
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        queryBuilder.orderBy(`plan.${sortBy}`, sortOrder);
        const [plans, total] = await queryBuilder.getManyAndCount();
        return { plans, total, page, limit };
    }
    async findPlanById(id) {
        const plan = await this.planRepository.findOne({ where: { id } });
        if (!plan) {
            throw new common_1.NotFoundException('Plano não encontrado');
        }
        return plan;
    }
    async updatePlan(id, updateData, currentUser) {
        const plan = await this.findPlanById(id);
        if (updateData.name && updateData.name !== plan.name) {
            const existingPlan = await this.planRepository.findOne({
                where: { name: updateData.name },
            });
            if (existingPlan) {
                throw new common_1.ConflictException('Já existe um plano com este nome');
            }
        }
        Object.assign(plan, updateData);
        const updatedPlan = await this.planRepository.save(plan);
        this.logger.log(`Plan updated: ${id} by ${currentUser.email}`);
        return updatedPlan;
    }
    async deletePlan(id, currentUser) {
        const plan = await this.findPlanById(id);
        const activeSubscriptions = await this.subscriptionRepository.count({
            where: { planId: id, status: subscription_entity_1.SubscriptionStatus.ACTIVE },
        });
        if (activeSubscriptions > 0) {
            throw new common_1.ConflictException('Não é possível deletar plano com assinaturas ativas');
        }
        await this.planRepository.remove(plan);
        this.logger.log(`Plan deleted: ${id} by ${currentUser.email}`);
    }
    async createSubscription(createSubscriptionDto, currentUser) {
        const company = await this.companyRepository.findOne({
            where: { id: createSubscriptionDto.companyId },
        });
        if (!company) {
            throw new common_1.NotFoundException('Empresa não encontrada');
        }
        const plan = await this.findPlanById(createSubscriptionDto.planId);
        const existingSubscription = await this.subscriptionRepository.findOne({
            where: {
                companyId: createSubscriptionDto.companyId,
                status: subscription_entity_1.SubscriptionStatus.ACTIVE,
            },
        });
        if (existingSubscription) {
            throw new common_1.ConflictException('Empresa já possui uma assinatura ativa');
        }
        const startDate = createSubscriptionDto.startDate ? new Date(createSubscriptionDto.startDate) : new Date();
        const endDate = this.calculateEndDate(startDate, plan.billingCycle);
        const subscription = this.subscriptionRepository.create({
            ...createSubscriptionDto,
            startDate,
            endDate,
            price: createSubscriptionDto.price || plan.price,
            setupFee: createSubscriptionDto.setupFee || plan.setupFee,
            status: createSubscriptionDto.trialEndDate ? subscription_entity_1.SubscriptionStatus.TRIAL : subscription_entity_1.SubscriptionStatus.PENDING,
            currentUsage: {
                connections: 0,
                contacts: 0,
                messages: 0,
                users: 0,
                automations: 0,
                chatbots: 0,
                storageUsedGB: 0,
                apiCalls: 0,
                webhookCalls: 0,
                lastUpdated: new Date(),
            },
        });
        const savedSubscription = await this.subscriptionRepository.save(subscription);
        this.logger.log(`Subscription created: ${savedSubscription.id} for company ${createSubscriptionDto.companyId}`);
        return savedSubscription;
    }
    async findAllSubscriptions(options, currentUser) {
        const { companyId, status, type, agencyId, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'DESC', } = options;
        const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription')
            .leftJoinAndSelect('subscription.company', 'company')
            .leftJoinAndSelect('subscription.plan', 'plan');
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('subscription.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('subscription.companyId = :companyId', { companyId });
        }
        if (status) {
            queryBuilder.andWhere('subscription.status = :status', { status });
        }
        if (type) {
            queryBuilder.andWhere('subscription.type = :type', { type });
        }
        if (agencyId) {
            queryBuilder.andWhere('subscription.agencyId = :agencyId', { agencyId });
        }
        const skip = (page - 1) * limit;
        queryBuilder.skip(skip).take(limit);
        queryBuilder.orderBy(`subscription.${sortBy}`, sortOrder);
        const [subscriptions, total] = await queryBuilder.getManyAndCount();
        return { subscriptions, total, page, limit };
    }
    async findSubscriptionById(id, currentUser) {
        const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription')
            .leftJoinAndSelect('subscription.company', 'company')
            .leftJoinAndSelect('subscription.plan', 'plan')
            .where('subscription.id = :id', { id });
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.andWhere('subscription.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        const subscription = await queryBuilder.getOne();
        if (!subscription) {
            throw new common_1.NotFoundException('Assinatura não encontrada');
        }
        return subscription;
    }
    async updateSubscription(id, updateSubscriptionDto, currentUser) {
        const subscription = await this.findSubscriptionById(id, currentUser);
        Object.assign(subscription, updateSubscriptionDto);
        const updatedSubscription = await this.subscriptionRepository.save(subscription);
        this.logger.log(`Subscription updated: ${id} by ${currentUser.email}`);
        return updatedSubscription;
    }
    async changePlan(id, changePlanDto, currentUser) {
        const subscription = await this.findSubscriptionById(id, currentUser);
        const newPlan = await this.findPlanById(changePlanDto.newPlanId);
        if (!subscription.canUpgrade() && !subscription.canDowngrade()) {
            throw new common_1.BadRequestException('Não é possível alterar o plano desta assinatura');
        }
        const currentPlan = await this.findPlanById(subscription.planId);
        const isUpgrade = newPlan.price > currentPlan.price;
        const isDowngrade = newPlan.price < currentPlan.price;
        if (isUpgrade && !currentPlan.canUpgradeTo(changePlanDto.newPlanId)) {
            throw new common_1.BadRequestException('Upgrade para este plano não é permitido');
        }
        if (isDowngrade && !currentPlan.canDowngradeTo(changePlanDto.newPlanId)) {
            throw new common_1.BadRequestException('Downgrade para este plano não é permitido');
        }
        subscription.pendingPlanId = changePlanDto.newPlanId;
        subscription.planChangeDate = changePlanDto.changeDate ? new Date(changePlanDto.changeDate) : new Date();
        subscription.isProrated = changePlanDto.isProrated ?? true;
        if (changePlanDto.customPrice) {
            subscription.price = changePlanDto.customPrice;
        }
        const updatedSubscription = await this.subscriptionRepository.save(subscription);
        this.logger.log(`Plan change scheduled: ${id} to plan ${changePlanDto.newPlanId} by ${currentUser.email}`);
        return updatedSubscription;
    }
    async cancelSubscription(id, cancelSubscriptionDto, currentUser) {
        const subscription = await this.findSubscriptionById(id, currentUser);
        if (!subscription.canCancel()) {
            throw new common_1.BadRequestException('Não é possível cancelar esta assinatura');
        }
        subscription.cancelledAt = new Date();
        subscription.cancelReason = cancelSubscriptionDto.reason || null;
        if (cancelSubscriptionDto.immediately) {
            subscription.status = subscription_entity_1.SubscriptionStatus.CANCELLED;
        }
        else {
            subscription.cancelAtPeriodEnd = true;
        }
        const updatedSubscription = await this.subscriptionRepository.save(subscription);
        this.logger.log(`Subscription cancelled: ${id} by ${currentUser.email}`);
        return updatedSubscription;
    }
    async updateUsage(id, usageDto, currentUser) {
        const subscription = await this.findSubscriptionById(id, currentUser);
        subscription.currentUsage = {
            ...usageDto,
            lastUpdated: new Date(),
        };
        const monthKey = new Date().toISOString().substring(0, 7);
        subscription.usageHistory = {
            ...subscription.usageHistory,
            [monthKey]: subscription.currentUsage,
        };
        const updatedSubscription = await this.subscriptionRepository.save(subscription);
        this.logger.log(`Usage updated for subscription: ${id}`);
        return updatedSubscription;
    }
    calculateEndDate(startDate, billingCycle) {
        const endDate = new Date(startDate);
        switch (billingCycle) {
            case 'MONTHLY':
                endDate.setMonth(endDate.getMonth() + 1);
                break;
            case 'QUARTERLY':
                endDate.setMonth(endDate.getMonth() + 3);
                break;
            case 'SEMI_ANNUAL':
                endDate.setMonth(endDate.getMonth() + 6);
                break;
            case 'ANNUAL':
                endDate.setFullYear(endDate.getFullYear() + 1);
                break;
            default:
                endDate.setMonth(endDate.getMonth() + 1);
        }
        return endDate;
    }
    async getSubscriptionStats(currentUser) {
        const queryBuilder = this.subscriptionRepository.createQueryBuilder('subscription');
        if (currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            queryBuilder.where('subscription.companyId = :userCompanyId', {
                userCompanyId: currentUser.companyId
            });
        }
        const [total, active, trial, cancelled, expired] = await Promise.all([
            queryBuilder.getCount(),
            queryBuilder.clone().andWhere('subscription.status = :status', { status: subscription_entity_1.SubscriptionStatus.ACTIVE }).getCount(),
            queryBuilder.clone().andWhere('subscription.status = :status', { status: subscription_entity_1.SubscriptionStatus.TRIAL }).getCount(),
            queryBuilder.clone().andWhere('subscription.status = :status', { status: subscription_entity_1.SubscriptionStatus.CANCELLED }).getCount(),
            queryBuilder.clone().andWhere('subscription.status = :status', { status: subscription_entity_1.SubscriptionStatus.EXPIRED }).getCount(),
        ]);
        const revenueQuery = queryBuilder.clone()
            .select('SUM(subscription.price)', 'totalRevenue')
            .andWhere('subscription.status IN (:...statuses)', { statuses: [subscription_entity_1.SubscriptionStatus.ACTIVE, subscription_entity_1.SubscriptionStatus.TRIAL] });
        const revenueResult = await revenueQuery.getRawOne();
        const totalRevenue = parseFloat(revenueResult.totalRevenue) || 0;
        const monthlyRevenue = totalRevenue;
        return {
            total,
            active,
            trial,
            cancelled,
            expired,
            totalRevenue,
            monthlyRevenue,
        };
    }
};
exports.BillingService = BillingService;
exports.BillingService = BillingService = BillingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(plan_entity_1.Plan)),
    __param(1, (0, typeorm_1.InjectRepository)(subscription_entity_1.Subscription)),
    __param(2, (0, typeorm_1.InjectRepository)(invoice_entity_1.Invoice)),
    __param(3, (0, typeorm_1.InjectRepository)(payment_entity_1.Payment)),
    __param(4, (0, typeorm_1.InjectRepository)(company_entity_1.Company)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], BillingService);
//# sourceMappingURL=billing.service.js.map