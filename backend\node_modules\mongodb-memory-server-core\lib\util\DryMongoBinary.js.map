{"version": 3, "file": "DryMongoBinary.js", "sourceRoot": "", "sources": ["../../src/util/DryMongoBinary.ts"], "names": [], "mappings": ";;;;AAAA,0DAA0B;AAC1B,mDAMyB;AACzB,mCAMiB;AACjB,mDAA6B;AAC7B,2BAA6C;AAC7C,4EAA0C;AAC1C,mCAA2D;AAC3D,qCAA0F;AAC1F,qEAAkE;AAElE,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,wBAAwB,CAAC,CAAC;AA+C5C;;GAEG;AACH,MAAa,cAAc;IAMzB;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAA2B;QACnD,GAAG,CAAC,sDAAsD,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAEjD,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC3B,GAAG,CAAC,+DAA+D,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;YAE5F,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;YAElF,IAAI,IAAA,yBAAiB,EAAC,YAAY,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,4BAAmB,CAAC,OAAO,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;YACzE,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClD,GAAG,CAAC,qDAAqD,IAAI,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;YAEtF,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAE7D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;YACpB,GAAG,CAAC,gDAAgD,CAAC,CAAC;YAEtD,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,yEAAyE;QACzE,wEAAwE;QACxE,oEAAoE;QACpE,IACE,WAAW,CAAC,CAAC,CAAC;YACd,CAAC,MAAM,IAAA,kBAAU,EAAC,IAAA,oBAAY,EAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAC/E,CAAC;YACD,GAAG,CAAC,8EAA8E,CAAC,CAAC;YAEpF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,GAAG,CAAC,kCAAkC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAEnD,OAAO,WAAW,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,iBAAiB,CAAC,IAAgC;QACvD,MAAM,cAAc,GAAG,IAAA,6BAAa,EAAC,sCAAsB,CAAC,OAAO,CAAC,IAAI,+BAAe,CAAC;QAExF,OAAO,IAAA,yBAAiB,EAAC,IAAI,CAAC;YAC5B,CAAC,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE;YAC7B,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,cAAc,EAAE,CAAC;IAC3D,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,eAAe,CAC1B,IAAgC;QAEhC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACvB,MAAM,WAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAE3D,MAAM,KAAK,GAAoC;YAC7C,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,WAAW,EACT,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,IAAI,WAAW,CAAC,WAAW,IAAI,EAAE;YACrF,EAAE,EAAE,WAAW,CAAC,EAAE,IAAI,CAAC,MAAM,IAAA,aAAK,GAAE,CAAC;YACrC,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,IAAA,aAAQ,GAAE;YAC5C,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,IAAA,SAAI,GAAE;YAChC,YAAY,EACV,IAAA,6BAAa,EAAC,sCAAsB,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,YAAY,IAAI,EAAE;SACxF,CAAC;QAEF,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9E,YAAY;QACZ,IACE,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC;YAClD,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,EAClD,CAAC;YACD,kDAAkD;YAClD,4IAA4I;YAC5I,MAAM,KAAK,GAAG,CAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC;gBAC/D,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAW,CAAC;YAEhE,GAAG,CACD,oGAAoG,KAAK,IAAI,CAC9G,CAAC;YAEF,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,qBAAqB,CAC1B,KAAa,EACb,IAAqC;QAErC,GAAG,CAAC,kCAAkC,KAAK,IAAI,CAAC,CAAC;QAEjD,MAAM,cAAc,GAClB,4IAA4I,CAAC,IAAI,CAC/I,KAAK,CACN,CAAC;QAEJ,IAAA,iBAAS,EAAC,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,EAAE,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;QAE9E,oHAAoH;QACpH,IAAA,iBAAS,EAAC,CAAC,IAAA,yBAAiB,EAAC,cAAc,CAAC,MAAM,CAAC,EAAE,IAAI,0BAAiB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE/F,MAAM,MAAM,GAAqC,cAAc,CAAC,MAAM,CAAC;QAEvE,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAC/D,IAAI,+BAAsB,CAAC,SAAS,CAAC,CACtC,CAAC;QACF,2HAA2H;QAC3H,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EACjE,IAAI,+BAAsB,CAAC,UAAU,CAAC,CACvC,CAAC;QACF,IAAA,iBAAS,EACP,OAAO,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,EAC1D,IAAI,+BAAsB,CAAC,MAAM,CAAC,CACnC,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QAExB,IAAI,MAAM,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAEhF,IAAI,CAAC,EAAE,GAAG;gBACR,EAAE,EAAE,OAAO;gBACX,IAAI,EAAE,OAAO,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;gBACvE,wDAAwD;gBACxD,OAAO,EAAE,EAAE;aACZ,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAa,CAAC;QAC/C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,IAA+B;QACxD,GAAG,CAAC,eAAe,CAAC,CAAC;QAErB,IAAI,UAAkB,CAAC;QAEvB,IAAI,IAAA,yBAAS,EAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,gCAAgC,CAAC,CAAC,EAAE,CAAC;YACtF,MAAM,WAAW,GAAG,MAAM,IAAI,+CAAsB,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;YAC5E,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,GAAG,IAAA,iBAAS,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;YAE5D,UAAU,GAAG,UAAU,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;QACtE,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAgB,EAAE,UAAkB;QAC3D,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEzB,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,YAAoB;QAC7C,GAAG,CAAC,eAAe,CAAC,CAAC;QACrB,IAAI,CAAC;YACH,MAAM,IAAA,8BAAsB,EAAC,YAAY,CAAC,CAAC;YAE3C,GAAG,CAAC,+CAA+C,YAAY,GAAG,CAAC,CAAC;YAEpE,OAAO,YAAY,CAAC,CAAC,oCAAoC;QAC3D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,GAAG,CACD,+CAA+C,YAAY,OACzD,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GACvC,EAAE,CACH,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,KAAK,CAAC,aAAa,CACxB,IAAuD;QAEvD,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC3B,MAAM,KAAK,GAAwB;YACjC,SAAS,EAAE,EAAE;YACb,YAAY,EAAE,EAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,aAAa,EAAE,EAAE;SAClB,CAAC;QACF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAClD,+CAA+C;QAE/C,gEAAgE;QAChE,mEAAmE;QACnE,IAAI,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;QAChE,4FAA4F;QAC5F,OAAO,gBAAgB,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,GAAG,uBAAuB,CAAC,EAAE,CAAC;YACjF,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAA,+BAAe,GAAE,CAAC;QAE5C,yGAAyG;QACzG,IAAI,iBAAiB,IAAI,CAAC,MAAM,IAAA,kBAAU,EAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7F,gBAAgB,GAAG,iBAAiB,CAAC;QACvC,CAAC;QAED,MAAM,eAAe,GAAG,IAAA,wBAAY,EAAC;YACnC,IAAI,EAAE,uBAAuB;YAC7B,GAAG,EAAE,gBAAgB;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,yBAAiB,EAAC,eAAe,CAAC,EAAE,CAAC;YACxC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,UAAU,CAAC,CAAC;QACzF,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,yBAAyB,CAAC,CAAC;QAE1E,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAEhE,kFAAkF;QAClF,MAAM,kBAAkB,GACtB,IAAI,CAAC,WAAW,IAAI,IAAA,6BAAa,EAAC,sCAAsB,CAAC,YAAY,CAAC,CAAC;QAEzE,IAAI,CAAC,IAAA,yBAAiB,EAAC,kBAAkB,CAAC,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,GAAG,CAAC,gDAAgD,CAAC,CAAC;YACtD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAC/E,CAAC;QAED,qDAAqD;QACrD,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CACrC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,EAC/C,UAAU,CACX,CAAC;QAEF,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAC/B,IAAuD;QAEvD,MAAM,YAAY,GAAG,IAAA,yBAAS,EAAC,IAAA,6BAAa,EAAC,sCAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACzF,GAAG,CAAC,kEAAkE,YAAY,GAAG,CAAC,CAAC;QACvF,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7C,GAAG,CAAC,8BAA8B,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAE9D,iEAAiE;QACjE,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,IAAA,kBAAU,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;YACjE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAE5D,IAAI,CAAC,IAAA,yBAAiB,EAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,wDAAwD;QACxD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YAC1C,GAAG,CACD,wEAAwE,KAAK,CAAC,aAAa,GAAG,CAC/F,CAAC;YAEF,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,GAAG,CAAC,qDAAqD,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;YAE7E,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;QACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;YACzC,GAAG,CAAC,wDAAwD,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;YAEnF,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,MAAM,IAAA,kBAAU,EAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,GAAG,CAAC,oDAAoD,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YAE3E,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,oEAAoE;QACpE,GAAG,CAAC,yDAAyD,IAAI,CAAC,OAAO,aAAa,CAAC,CAAC;QAExF,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,GAAG,CAAC,6DAA6D,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC;YAEzF,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,YAAY,IAAI,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACtC,GAAG,CAAC,sDAAsD,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;YAE9E,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC;QACD,4FAA4F;QAC5F,IAAI,KAAK,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,GAAG,CAAC,6CAA6C,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;YAExE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;QACrC,CAAC;QAED,GAAG,CAAC,yCAAyC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;QAEhE,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,OAAO;QACpB,OAAO,IAAA,YAAO,GAAE,CAAC;IACnB,CAAC;;AAjXH,wCAkXC;AAjXC;;GAEG;AACI,0BAAW,GAAwB,IAAI,GAAG,EAAE,CAAC"}