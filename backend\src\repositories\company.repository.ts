import { Company, CompanyStatus, Prisma } from '@prisma/client'
import prisma from '../lib/prisma'

export interface CreateCompanyData {
  name: string
  cnpj: string
  email: string
  phone: string
  address?: string
  website?: string
  status?: CompanyStatus
  settings?: any
  logo?: string
  agencyId?: string
}

export interface UpdateCompanyData {
  name?: string
  cnpj?: string
  email?: string
  phone?: string
  address?: string
  website?: string
  status?: CompanyStatus
  settings?: any
  logo?: string
  agencyId?: string
}

export interface CompanyFilters {
  status?: CompanyStatus
  agencyId?: string
  search?: string
}

export class CompanyRepository {
  async create(data: CreateCompanyData): Promise<Company> {
    return prisma.company.create({
      data,
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async findById(id: string): Promise<Company | null> {
    return prisma.company.findUnique({
      where: { id },
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async findByCnpj(cnpj: string): Promise<Company | null> {
    return prisma.company.findUnique({
      where: { cnpj },
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async update(id: string, data: UpdateCompanyData): Promise<Company> {
    return prisma.company.update({
      where: { id },
      data,
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async delete(id: string): Promise<Company> {
    return prisma.company.delete({
      where: { id }
    })
  }

  async findMany(filters: CompanyFilters = {}, page = 1, limit = 10): Promise<{
    companies: Company[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.CompanyWhereInput = {}

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.agencyId) {
      where.agencyId = filters.agencyId
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { email: { contains: filters.search, mode: 'insensitive' } },
        { cnpj: { contains: filters.search, mode: 'insensitive' } }
      ]
    }

    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        include: {
          users: true,
          whatsappConnections: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.company.count({ where })
    ])

    return {
      companies,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async updateSettings(id: string, settings: any): Promise<Company> {
    return prisma.company.update({
      where: { id },
      data: { settings },
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async updateStatus(id: string, status: CompanyStatus): Promise<Company> {
    return prisma.company.update({
      where: { id },
      data: { status },
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }

  async getStats(id: string): Promise<{
    totalUsers: number
    totalConnections: number
    totalMessages: number
    totalContacts: number
  }> {
    const [totalUsers, totalConnections, totalMessages, totalContacts] = await Promise.all([
      prisma.user.count({ where: { companyId: id } }),
      prisma.whatsAppConnection.count({ where: { companyId: id } }),
      prisma.message.count({ where: { companyId: id } }),
      prisma.contact.count({ where: { companyId: id } })
    ])

    return {
      totalUsers,
      totalConnections,
      totalMessages,
      totalContacts
    }
  }

  async findByAgency(agencyId: string): Promise<Company[]> {
    return prisma.company.findMany({
      where: { agencyId },
      include: {
        users: true,
        whatsappConnections: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })
  }

  async countByStatus(status: CompanyStatus): Promise<number> {
    return prisma.company.count({
      where: { status }
    })
  }

  async findExpiredTrials(): Promise<Company[]> {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    return prisma.company.findMany({
      where: {
        status: CompanyStatus.TRIAL,
        createdAt: {
          lt: thirtyDaysAgo
        }
      },
      include: {
        users: true,
        whatsappConnections: true
      }
    })
  }
}
