import * as express from 'express'
import { createServer } from 'http'
import * as cors from 'cors'
import { initializeWebSocket } from './services/websocket.service'
import { connectDatabase } from './lib/prisma'
import webhookRoutes from './routes/webhook.routes'

const app = express()
const server = createServer(app)
const port = process.env.PORT || 3000

// CORS
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3001',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}))

// Body parsing
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString()
  console.log(`[${timestamp}] ${req.method} ${req.path} - ${req.ip}`)
  next()
})

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  })
})

// API Routes
app.get('/', (req, res) => {
  res.json({
    name: 'WhatsApp Platform API',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/health',
      webhooks: '/api/v1/webhooks',
      docs: '/api/docs'
    }
  })
})

// Webhooks
app.use('/api/v1/webhooks', webhookRoutes)

// Outras rotas da API (quando estiverem prontas)
// app.use('/api/v1/auth', authRoutes)
// app.use('/api/v1/whatsapp', whatsappRoutes)

// Documentação simples
app.get('/api/docs', (req, res) => {
  res.json({
    title: 'WhatsApp Platform API',
    version: '1.0.0',
    description: 'API para gerenciamento de WhatsApp Business',
    endpoints: {
      'GET /': 'Informações da API',
      'GET /health': 'Health check',
      'POST /api/v1/webhooks/evolution': 'Webhook da Evolution API',
      'GET /api/v1/webhooks/health': 'Health check dos webhooks'
    },
    websocket: {
      url: `ws://localhost:${port}`,
      events: [
        'new_message',
        'message_update',
        'connection_update',
        'qr_code_update',
        'notification'
      ]
    }
  })
})

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('[Error]', err)
  
  // Não expor detalhes do erro em produção
  const isDevelopment = process.env.NODE_ENV === 'development'
  
  res.status(err.status || 500).json({
    error: isDevelopment ? err.message : 'Erro interno do servidor',
    ...(isDevelopment && { stack: err.stack })
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint não encontrado',
    path: req.path,
    method: req.method
  })
})

// Inicializar serviços
async function startServer() {
  try {
    // Conectar ao banco de dados (comentado para teste)
    // await connectDatabase()

    // Inicializar WebSocket
    const wsService = initializeWebSocket(server)
    
    // Iniciar servidor
    server.listen(port, () => {
      console.log('🚀 Servidor iniciado com sucesso!')
      console.log(`📡 HTTP Server: http://localhost:${port}`)
      console.log(`🔌 WebSocket Server: ws://localhost:${port}`)
      console.log(`📚 Documentação: http://localhost:${port}/api/docs`)
      console.log(`💚 Health Check: http://localhost:${port}/health`)
      console.log(`🎯 Environment: ${process.env.NODE_ENV || 'development'}`)
      
      // Log das estatísticas do WebSocket a cada 30 segundos
      setInterval(() => {
        const stats = wsService.getConnectionStats()
        if (stats.totalConnections > 0) {
          console.log(`[WebSocket] Conexões ativas: ${stats.totalConnections} usuários em ${stats.companiesWithUsers} empresas`)
        }
      }, 30000)
    })

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('🛑 Recebido SIGTERM, encerrando servidor...')
      server.close(() => {
        console.log('✅ Servidor encerrado com sucesso')
        process.exit(0)
      })
    })

    process.on('SIGINT', () => {
      console.log('🛑 Recebido SIGINT, encerrando servidor...')
      server.close(() => {
        console.log('✅ Servidor encerrado com sucesso')
        process.exit(0)
      })
    })

  } catch (error) {
    console.error('❌ Erro ao iniciar servidor:', error)
    process.exit(1)
  }
}

// Tratar erros não capturados
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
})

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error)
  process.exit(1)
})

// Iniciar servidor
startServer()

export { app, server }
