export { BaseEntity } from './base.entity';
export { Company, CompanyStatus } from './company.entity';
export { User, UserRole, UserStatus } from './user.entity';
export { WhatsAppConnection, ConnectionType, ConnectionStatus } from './whatsapp-connection.entity';
export { Subscription, SubscriptionStatus, SubscriptionType, UsageMetrics } from './subscription.entity';
export { Plan, PlanType, PlanStatus, BillingCycle, PlanFeatures } from './plan.entity';
export { Invoice, InvoiceStatus, InvoiceType, InvoiceLineItem, TaxDetails, DiscountDetails } from './invoice.entity';
export { Payment, PaymentStatus, PaymentMethod, PaymentGateway, PaymentMethodDetails, RefundDetails } from './payment.entity';
export { Integration, IntegrationType, IntegrationStatus, TriggerEvent, IntegrationConfig } from './integration.entity';
export { WebhookLog, WebhookStatus, WebhookRequest, WebhookResponse } from './webhook-log.entity';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
import { Plan } from './plan.entity';
import { Invoice } from './invoice.entity';
import { Payment } from './payment.entity';
import { Integration } from './integration.entity';
import { WebhookLog } from './webhook-log.entity';
export declare const TypeOrmEntities: (typeof WhatsAppConnection | typeof Company | typeof User | typeof Plan | typeof Subscription | typeof Payment | typeof Invoice | typeof WebhookLog | typeof Integration)[];
