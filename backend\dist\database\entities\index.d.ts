export { BaseEntity } from './base.entity';
export { Company, CompanyStatus } from './company.entity';
export { User, UserRole, UserStatus } from './user.entity';
export { WhatsAppConnection, ConnectionType, ConnectionStatus } from './whatsapp-connection.entity';
export { Subscription, SubscriptionStatus, SubscriptionPlan } from './subscription.entity';
export { Contact, ContactDocument, ContactStatus, ContactTag, ContactSource } from './contact.schema';
export { Message, MessageDocument, MessageType, MessageDirection, MessageStatus, MessageMedia, MessageLocation, MessageContact } from './message.schema';
export { AnalyticsEvent, AnalyticsEventDocument, EventType, EventCategory, EventProperties } from './analytics-event.schema';
export { Automation, AutomationDocument, AutomationType, AutomationStatus, TriggerType, ActionType, TriggerCondition, ActionStep, FlowStep } from './automation.schema';
export { AutomationExecution, AutomationExecutionDocument, ExecutionStatus, StepStatus, ExecutionContext, ExecutionStep } from './automation-execution.schema';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WhatsAppConnection } from './whatsapp-connection.entity';
import { Subscription } from './subscription.entity';
import { Contact } from './contact.schema';
import { Message } from './message.schema';
import { AnalyticsEvent } from './analytics-event.schema';
import { Automation } from './automation.schema';
import { AutomationExecution } from './automation-execution.schema';
export declare const TypeOrmEntities: (typeof WhatsAppConnection | typeof Company | typeof User | typeof Subscription)[];
export declare const MongooseSchemas: ({
    name: string;
    schema: import("mongoose").Schema<Contact, import("mongoose").Model<Contact, any, any, any, import("mongoose").Document<unknown, any, Contact, any> & Contact & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Contact, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<Contact>, {}> & import("mongoose").FlatRecord<Contact> & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
} | {
    name: string;
    schema: import("mongoose").Schema<Message, import("mongoose").Model<Message, any, any, any, import("mongoose").Document<unknown, any, Message, any> & Message & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Message, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<Message>, {}> & import("mongoose").FlatRecord<Message> & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
} | {
    name: string;
    schema: import("mongoose").Schema<AnalyticsEvent, import("mongoose").Model<AnalyticsEvent, any, any, any, import("mongoose").Document<unknown, any, AnalyticsEvent, any> & AnalyticsEvent & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AnalyticsEvent, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<AnalyticsEvent>, {}> & import("mongoose").FlatRecord<AnalyticsEvent> & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
} | {
    name: string;
    schema: import("mongoose").Schema<Automation, import("mongoose").Model<Automation, any, any, any, import("mongoose").Document<unknown, any, Automation, any> & Automation & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Automation, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<Automation>, {}> & import("mongoose").FlatRecord<Automation> & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
} | {
    name: string;
    schema: import("mongoose").Schema<AutomationExecution, import("mongoose").Model<AutomationExecution, any, any, any, import("mongoose").Document<unknown, any, AutomationExecution, any> & AutomationExecution & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, AutomationExecution, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<AutomationExecution>, {}> & import("mongoose").FlatRecord<AutomationExecution> & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
})[];
export { ContactSchema } from './contact.schema';
export { MessageSchema } from './message.schema';
export { AnalyticsEventSchema } from './analytics-event.schema';
export { AutomationSchema } from './automation.schema';
export { AutomationExecutionSchema } from './automation-execution.schema';
