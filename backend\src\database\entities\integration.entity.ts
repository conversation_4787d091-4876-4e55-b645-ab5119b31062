import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  <PERSON>To<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Company } from './company.entity';
import { User } from './user.entity';
import { WebhookLog } from './webhook-log.entity';

export enum IntegrationType {
  WEBHOOK = 'webhook',
  GOOGLE_SHEETS = 'google_sheets',
  ZAPIER = 'zapier',
  CRM_HUBSPOT = 'crm_hubspot',
  CRM_SALESFORCE = 'crm_salesforce',
  CRM_PIPEDRIVE = 'crm_pipedrive',
  EMAIL_MAILCHIMP = 'email_mailchimp',
  EMAIL_SENDGRID = 'email_sendgrid',
  SMS_TWILIO = 'sms_twilio',
  SLACK = 'slack',
  DISCORD = 'discord',
  TELEGRAM = 'telegram',
  CUSTOM_API = 'custom_api',
}

export enum IntegrationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  PENDING = 'pending',
  EXPIRED = 'expired',
}

export enum TriggerEvent {
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_SENT = 'message_sent',
  CONTACT_CREATED = 'contact_created',
  CONTACT_UPDATED = 'contact_updated',
  LEAD_CONVERTED = 'lead_converted',
  AUTOMATION_TRIGGERED = 'automation_triggered',
  AUTOMATION_COMPLETED = 'automation_completed',
  CONNECTION_STATUS_CHANGED = 'connection_status_changed',
  USER_CREATED = 'user_created',
  SUBSCRIPTION_CHANGED = 'subscription_changed',
  PAYMENT_RECEIVED = 'payment_received',
  CUSTOM_EVENT = 'custom_event',
}

export interface IntegrationConfig {
  // Configurações gerais
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;

  // Autenticação
  authType?: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth2';
  apiKey?: string;
  bearerToken?: string;
  basicAuth?: {
    username: string;
    password: string;
  };
  oauth2?: {
    clientId: string;
    clientSecret: string;
    accessToken: string;
    refreshToken: string;
    tokenExpiry?: Date;
    scope?: string[];
  };

  // Configurações específicas por tipo
  googleSheets?: {
    spreadsheetId: string;
    sheetName: string;
    range?: string;
    serviceAccountKey?: string;
  };

  crm?: {
    apiUrl: string;
    objectType: string; // contact, lead, deal, etc.
    fieldMapping: Record<string, string>;
    customFields?: Record<string, any>;
  };

  email?: {
    listId?: string;
    templateId?: string;
    fromEmail?: string;
    fromName?: string;
  };

  sms?: {
    fromNumber?: string;
    messagingServiceSid?: string;
  };

  slack?: {
    channelId: string;
    botToken: string;
    messageFormat?: string;
  };

  // Filtros e condições
  filters?: {
    contactTags?: string[];
    messageTypes?: string[];
    connectionIds?: string[];
    userIds?: string[];
    customConditions?: Record<string, any>;
  };

  // Mapeamento de dados
  dataMapping?: {
    staticFields?: Record<string, any>;
    dynamicFields?: Record<string, string>;
    transformations?: Array<{
      field: string;
      type: 'uppercase' | 'lowercase' | 'date_format' | 'number_format' | 'custom';
      config?: any;
    }>;
  };
}

@Entity('integrations')
export class Integration {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'varchar',
    length: 50,
  })
  type: IntegrationType;

  @Column({
    type: 'varchar',
    length: 50,
    default: IntegrationStatus.PENDING,
  })
  status: IntegrationStatus;

  @Column()
  companyId: string;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'companyId' })
  company: Company;

  @Column()
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdBy' })
  creator: User;

  @Column({ type: 'simple-array' })
  triggerEvents: TriggerEvent[];

  @Column({ type: 'json' })
  config: IntegrationConfig;

  @Column({ default: true })
  active: boolean;

  @Column({ default: 0 })
  executionCount: number;

  @Column({ default: 0 })
  successCount: number;

  @Column({ default: 0 })
  errorCount: number;

  @Column({ type: "datetime", nullable: true })
  lastExecutedAt: Date;

  @Column({ type: "datetime", nullable: true })
  lastSuccessAt: Date;

  @Column({ type: "datetime", nullable: true })
  lastErrorAt: Date;

  @Column({ type: 'text', nullable: true })
  lastError: string | null;

  // Configurações de rate limiting
  @Column({ default: 0 })
  rateLimit: number; // Requests per minute

  @Column({ default: 0 })
  currentRateCount: number;

  @Column({ type: "datetime", nullable: true })
  rateLimitResetAt: Date;

  // Configurações de retry
  @Column({ default: 3 })
  maxRetries: number;

  @Column({ default: 5000 })
  retryDelay: number; // milliseconds

  // Configurações de timeout
  @Column({ default: 30000 })
  timeout: number; // milliseconds

  // Metadados
  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  tags: string[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => WebhookLog, log => log.integration)
  logs: WebhookLog[];

  // Métodos auxiliares
  isActive(): boolean {
    return this.status === IntegrationStatus.ACTIVE && this.active;
  }

  canExecute(): boolean {
    if (!this.isActive()) return false;
    
    // Verificar rate limit
    if (this.rateLimit > 0) {
      const now = new Date();
      if (this.rateLimitResetAt && now < this.rateLimitResetAt) {
        return this.currentRateCount < this.rateLimit;
      } else {
        // Reset rate limit
        this.currentRateCount = 0;
        this.rateLimitResetAt = new Date(now.getTime() + 60000); // 1 minute
      }
    }

    return true;
  }

  incrementExecution(success: boolean): void {
    this.executionCount += 1;
    this.lastExecutedAt = new Date();
    
    if (success) {
      this.successCount += 1;
      this.lastSuccessAt = new Date();
      this.lastError = null;
    } else {
      this.errorCount += 1;
      this.lastErrorAt = new Date();
    }

    // Increment rate limit counter
    if (this.rateLimit > 0) {
      this.currentRateCount += 1;
    }
  }

  setError(error: string): void {
    this.lastError = error;
    this.lastErrorAt = new Date();
    
    // Auto-disable if too many consecutive errors
    if (this.errorCount > 10 && this.successCount === 0) {
      this.status = IntegrationStatus.ERROR;
    }
  }

  getSuccessRate(): number {
    if (this.executionCount === 0) return 0;
    return (this.successCount / this.executionCount) * 100;
  }

  hasEvent(event: TriggerEvent): boolean {
    return this.triggerEvents.includes(event);
  }

  shouldTrigger(event: TriggerEvent, data: any): boolean {
    if (!this.hasEvent(event)) return false;
    if (!this.canExecute()) return false;

    // Verificar filtros
    const filters = this.config.filters;
    if (!filters) return true;

    // Filtro por tags de contato
    if (filters.contactTags && data.contact?.tags) {
      const hasMatchingTag = filters.contactTags.some(tag => 
        data.contact.tags.includes(tag)
      );
      if (!hasMatchingTag) return false;
    }

    // Filtro por tipo de mensagem
    if (filters.messageTypes && data.message?.type) {
      if (!filters.messageTypes.includes(data.message.type)) return false;
    }

    // Filtro por conexão
    if (filters.connectionIds && data.connectionId) {
      if (!filters.connectionIds.includes(data.connectionId)) return false;
    }

    // Filtro por usuário
    if (filters.userIds && data.userId) {
      if (!filters.userIds.includes(data.userId)) return false;
    }

    return true;
  }

  transformData(data: any): any {
    const mapping = this.config.dataMapping;
    if (!mapping) return data;

    const result = { ...data };

    // Adicionar campos estáticos
    if (mapping.staticFields) {
      Object.assign(result, mapping.staticFields);
    }

    // Mapear campos dinâmicos
    if (mapping.dynamicFields) {
      for (const [targetField, sourceField] of Object.entries(mapping.dynamicFields)) {
        const value = this.getNestedValue(data, sourceField);
        if (value !== undefined) {
          this.setNestedValue(result, targetField, value);
        }
      }
    }

    // Aplicar transformações
    if (mapping.transformations) {
      for (const transformation of mapping.transformations) {
        const value = this.getNestedValue(result, transformation.field);
        if (value !== undefined) {
          const transformedValue = this.applyTransformation(value, transformation);
          this.setNestedValue(result, transformation.field, transformedValue);
        }
      }
    }

    return result;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  private applyTransformation(value: any, transformation: any): any {
    switch (transformation.type) {
      case 'uppercase':
        return String(value).toUpperCase();
      case 'lowercase':
        return String(value).toLowerCase();
      case 'date_format':
        return new Date(value).toISOString();
      case 'number_format':
        return Number(value);
      default:
        return value;
    }
  }
}
