import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Contact, ContactSchema, Message, MessageSchema } from '../../database/entities';
import { ContactsService } from './services/contacts.service';
import { MessagesService } from './services/messages.service';
import { ContactsController } from './controllers/contacts.controller';
import { MessagesController } from './controllers/messages.controller';
import { MessagesGateway } from './gateways/messages.gateway';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Contact.name, schema: ContactSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('jwt.secret'),
        signOptions: {
          expiresIn: configService.get('jwt.expiresIn'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [ContactsController, MessagesController],
  providers: [ContactsService, MessagesService, MessagesGateway],
  exports: [ContactsService, MessagesService, MessagesGateway],
})
export class MessagesModule {}
