"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TrackingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrackingService = void 0;
const common_1 = require("@nestjs/common");
const analytics_service_1 = require("./analytics.service");
const analytics_event_schema_1 = require("../../../database/entities/analytics-event.schema");
let TrackingService = TrackingService_1 = class TrackingService {
    analyticsService;
    logger = new common_1.Logger(TrackingService_1.name);
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    async trackUserLogin(userId, companyId, sessionId, metadata) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.USER_LOGIN, analytics_event_schema_1.EventCategory.USER, companyId, {
            userId,
            sessionId,
            ...metadata,
        }, userId, sessionId);
    }
    async trackUserLogout(userId, companyId, sessionId, sessionDuration) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.USER_LOGOUT, analytics_event_schema_1.EventCategory.USER, companyId, {
            userId,
            sessionId,
            duration: sessionDuration,
        }, userId, sessionId);
    }
    async trackUserCreated(userId, companyId, createdBy, userRole) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.USER_CREATED, analytics_event_schema_1.EventCategory.USER, companyId, {
            userId,
            createdBy,
            userRole,
        }, createdBy);
    }
    async trackWhatsAppConnected(connectionId, companyId, userId, phoneNumber) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.WHATSAPP_CONNECTED, analytics_event_schema_1.EventCategory.WHATSAPP, companyId, {
            connectionId,
            phoneNumber,
            userId,
        }, userId);
    }
    async trackWhatsAppDisconnected(connectionId, companyId, userId, reason) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.WHATSAPP_DISCONNECTED, analytics_event_schema_1.EventCategory.WHATSAPP, companyId, {
            connectionId,
            reason,
            userId,
        }, userId);
    }
    async trackQRCodeGenerated(connectionId, companyId, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.WHATSAPP_QR_GENERATED, analytics_event_schema_1.EventCategory.WHATSAPP, companyId, {
            connectionId,
            userId,
        }, userId);
    }
    async trackMessageSent(messageId, companyId, connectionId, contactPhone, messageType, isAutomated = false, userId, automationId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.MESSAGE_SENT, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            messageId,
            connectionId,
            contactPhone,
            messageType,
            messageDirection: 'outbound',
            isAutomated,
            automationId,
            userId,
        }, userId);
    }
    async trackMessageReceived(messageId, companyId, connectionId, contactPhone, messageType, isFirstMessage = false) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.MESSAGE_RECEIVED, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            messageId,
            connectionId,
            contactPhone,
            messageType,
            messageDirection: 'inbound',
            isFirstMessage,
        });
    }
    async trackMessageDelivered(messageId, companyId, deliveryTime) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.MESSAGE_DELIVERED, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            messageId,
            deliveryTime,
        });
    }
    async trackMessageRead(messageId, companyId, readTime) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.MESSAGE_READ, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            messageId,
            readTime,
        });
    }
    async trackMessageFailed(messageId, companyId, errorCode, errorMessage) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.MESSAGE_FAILED, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            messageId,
            errorCode,
            errorMessage,
        });
    }
    async trackBulkMessageSent(companyId, connectionId, recipientCount, messageType, userId, campaignId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.BULK_MESSAGE_SENT, analytics_event_schema_1.EventCategory.MESSAGE, companyId, {
            connectionId,
            recipientCount,
            messageType,
            campaignId,
            value: recipientCount,
        }, userId);
    }
    async trackContactCreated(contactId, companyId, connectionId, contactPhone, source, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.CONTACT_CREATED, analytics_event_schema_1.EventCategory.CONTACT, companyId, {
            contactId,
            connectionId,
            contactPhone,
            contactSource: source,
        }, userId);
    }
    async trackContactUpdated(contactId, companyId, contactPhone, updatedFields, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.CONTACT_UPDATED, analytics_event_schema_1.EventCategory.CONTACT, companyId, {
            contactId,
            contactPhone,
            updatedFields,
        }, userId);
    }
    async trackContactTagged(contactId, companyId, contactPhone, tagName, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.CONTACT_TAGGED, analytics_event_schema_1.EventCategory.CONTACT, companyId, {
            contactId,
            contactPhone,
            tagName,
        }, userId);
    }
    async trackLeadConverted(contactId, companyId, contactPhone, conversionType, conversionValue, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.LEAD_CONVERTED, analytics_event_schema_1.EventCategory.CONTACT, companyId, {
            contactId,
            contactPhone,
            conversionType,
            conversionValue,
            value: conversionValue || 1,
        }, userId);
    }
    async trackLeadScored(contactId, companyId, contactPhone, leadScore, previousScore, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.LEAD_SCORED, analytics_event_schema_1.EventCategory.CONTACT, companyId, {
            contactId,
            contactPhone,
            leadScore,
            previousScore,
            value: leadScore,
        }, userId);
    }
    async trackConversationStarted(conversationId, companyId, connectionId, contactPhone, initiatedBy) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.CONVERSATION_STARTED, analytics_event_schema_1.EventCategory.CONVERSION, companyId, {
            conversationId,
            connectionId,
            contactPhone,
            initiatedBy,
        });
    }
    async trackConversationEnded(conversationId, companyId, connectionId, contactPhone, duration, messageCount) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.CONVERSATION_ENDED, analytics_event_schema_1.EventCategory.CONVERSION, companyId, {
            conversationId,
            connectionId,
            contactPhone,
            duration,
            messageCount,
            value: messageCount,
        });
    }
    async trackFirstResponse(companyId, connectionId, contactPhone, responseTime, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.FIRST_RESPONSE, analytics_event_schema_1.EventCategory.CONVERSION, companyId, {
            connectionId,
            contactPhone,
            responseTime,
            value: responseTime,
        }, userId);
    }
    async trackResponseTime(companyId, connectionId, contactPhone, responseTime, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.RESPONSE_TIME_MEASURED, analytics_event_schema_1.EventCategory.CONVERSION, companyId, {
            connectionId,
            contactPhone,
            responseTime,
            value: responseTime,
        }, userId);
    }
    async trackApiRequest(companyId, endpoint, method, responseTime, statusCode, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.API_REQUEST, analytics_event_schema_1.EventCategory.SYSTEM, companyId, {
            endpoint,
            method,
            responseTime,
            statusCode,
            duration: responseTime,
        }, userId);
    }
    async trackError(companyId, errorCode, errorMessage, context, userId) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.ERROR_OCCURRED, analytics_event_schema_1.EventCategory.SYSTEM, companyId, {
            errorCode,
            errorMessage,
            context,
        }, userId);
    }
    async trackPerformanceMetric(companyId, metricName, value, unit, context) {
        await this.analyticsService.trackEvent(analytics_event_schema_1.EventType.PERFORMANCE_METRIC, analytics_event_schema_1.EventCategory.SYSTEM, companyId, {
            metricName,
            value,
            unit,
            context,
        });
    }
};
exports.TrackingService = TrackingService;
exports.TrackingService = TrackingService = TrackingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], TrackingService);
//# sourceMappingURL=tracking.service.js.map