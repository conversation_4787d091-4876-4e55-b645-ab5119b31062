"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AutomationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutomationService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const automation_schema_1 = require("../../../database/entities/automation.schema");
const automation_execution_schema_1 = require("../../../database/entities/automation-execution.schema");
const entities_1 = require("../../../database/entities");
let AutomationService = AutomationService_1 = class AutomationService {
    automationModel;
    executionModel;
    logger = new common_1.Logger(AutomationService_1.name);
    constructor(automationModel, executionModel) {
        this.automationModel = automationModel;
        this.executionModel = executionModel;
    }
    async create(createAutomationDto, currentUser) {
        const existingAutomation = await this.automationModel.findOne({
            name: createAutomationDto.name,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
        if (existingAutomation) {
            throw new common_1.ConflictException('Já existe uma automação com este nome');
        }
        this.validateTriggerConfiguration(createAutomationDto.trigger);
        if (createAutomationDto.actions) {
            this.validateActionSteps(createAutomationDto.actions);
        }
        if (createAutomationDto.flowSteps) {
            this.validateFlowSteps(createAutomationDto.flowSteps);
        }
        const automation = new this.automationModel({
            ...createAutomationDto,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
            createdBy: currentUser.id,
            status: automation_schema_1.AutomationStatus.DRAFT,
            executionCount: 0,
            successCount: 0,
            errorCount: 0,
            version: 1,
            lastModifiedAt: new Date(),
        });
        const savedAutomation = await automation.save();
        this.logger.log(`Automation created: ${savedAutomation._id} by ${currentUser.email}`);
        return savedAutomation;
    }
    async findAll(options, currentUser) {
        const { companyId, type, status, triggerType, isTemplate, templateCategory, createdBy, search, page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', } = options;
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        };
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            filter.companyId = new mongoose_2.Types.ObjectId(companyId);
        }
        if (type) {
            filter.type = type;
        }
        if (status) {
            filter.status = status;
        }
        if (triggerType) {
            filter['trigger.type'] = triggerType;
        }
        if (typeof isTemplate === 'boolean') {
            filter.isTemplate = isTemplate;
        }
        if (templateCategory) {
            filter.templateCategory = templateCategory;
        }
        if (createdBy) {
            filter.createdBy = createdBy;
        }
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        const skip = (page - 1) * limit;
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const [automations, total] = await Promise.all([
            this.automationModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.automationModel.countDocuments(filter),
        ]);
        return {
            automations,
            total,
            page,
            limit,
        };
    }
    async findOne(id, currentUser) {
        const automation = await this.automationModel.findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
        if (!automation) {
            throw new common_1.NotFoundException('Automação não encontrada');
        }
        return automation;
    }
    async update(id, updateAutomationDto, currentUser) {
        const automation = await this.findOne(id, currentUser);
        if (automation.createdBy !== currentUser.id && currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            throw new common_1.ForbiddenException('Não é possível editar esta automação');
        }
        if (updateAutomationDto.name && updateAutomationDto.name !== automation.name) {
            const existingAutomation = await this.automationModel.findOne({
                name: updateAutomationDto.name,
                companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
                _id: { $ne: automation._id },
            });
            if (existingAutomation) {
                throw new common_1.ConflictException('Já existe uma automação com este nome');
            }
        }
        if (updateAutomationDto.trigger) {
            this.validateTriggerConfiguration(updateAutomationDto.trigger);
        }
        if (updateAutomationDto.actions) {
            this.validateActionSteps(updateAutomationDto.actions);
        }
        if (updateAutomationDto.flowSteps) {
            this.validateFlowSteps(updateAutomationDto.flowSteps);
        }
        if (updateAutomationDto.trigger || updateAutomationDto.actions || updateAutomationDto.flowSteps) {
            automation.version = automation.version + 1;
        }
        Object.assign(automation, updateAutomationDto);
        automation.updatedBy = currentUser.id;
        automation.lastModifiedAt = new Date();
        const updatedAutomation = await automation.save();
        this.logger.log(`Automation updated: ${id} by ${currentUser.email}`);
        return updatedAutomation;
    }
    async remove(id, currentUser) {
        const automation = await this.findOne(id, currentUser);
        if (automation.createdBy !== currentUser.id && currentUser.role !== entities_1.UserRole.SUPER_ADMIN) {
            throw new common_1.ForbiddenException('Não é possível deletar esta automação');
        }
        const runningExecutions = await this.executionModel.countDocuments({
            automationId: automation._id,
            status: { $in: [automation_execution_schema_1.ExecutionStatus.PENDING, automation_execution_schema_1.ExecutionStatus.RUNNING] },
        });
        if (runningExecutions > 0) {
            throw new common_1.ConflictException('Não é possível deletar automação com execuções em andamento');
        }
        await this.automationModel.findByIdAndDelete(automation._id);
        this.logger.log(`Automation deleted: ${id} by ${currentUser.email}`);
    }
    async activate(id, currentUser) {
        const automation = await this.findOne(id, currentUser);
        if (automation.status === automation_schema_1.AutomationStatus.ACTIVE) {
            throw new common_1.ConflictException('Automação já está ativa');
        }
        this.validateAutomationCompleteness(automation);
        automation.status = automation_schema_1.AutomationStatus.ACTIVE;
        automation.updatedBy = currentUser.id;
        automation.lastModifiedAt = new Date();
        const updatedAutomation = await automation.save();
        this.logger.log(`Automation activated: ${id} by ${currentUser.email}`);
        return updatedAutomation;
    }
    async deactivate(id, currentUser) {
        const automation = await this.findOne(id, currentUser);
        if (automation.status !== automation_schema_1.AutomationStatus.ACTIVE) {
            throw new common_1.ConflictException('Automação não está ativa');
        }
        automation.status = automation_schema_1.AutomationStatus.INACTIVE;
        automation.updatedBy = currentUser.id;
        automation.lastModifiedAt = new Date();
        const updatedAutomation = await automation.save();
        this.logger.log(`Automation deactivated: ${id} by ${currentUser.email}`);
        return updatedAutomation;
    }
    async duplicate(id, currentUser) {
        const originalAutomation = await this.findOne(id, currentUser);
        const duplicatedData = {
            name: `${originalAutomation.name} (Cópia)`,
            description: originalAutomation.description,
            type: originalAutomation.type,
            trigger: originalAutomation.trigger,
            actions: originalAutomation.actions,
            flowSteps: originalAutomation.flowSteps,
            startStepId: originalAutomation.startStepId,
            settings: originalAutomation.settings,
            metadata: originalAutomation.metadata,
        };
        return this.create(duplicatedData, currentUser);
    }
    async getExecutions(options, currentUser) {
        const { automationId, companyId, status, contactPhone, connectionId, triggeredBy, dateFrom, dateTo, page = 1, limit = 10, } = options;
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        };
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            filter.companyId = new mongoose_2.Types.ObjectId(companyId);
        }
        if (automationId) {
            filter.automationId = new mongoose_2.Types.ObjectId(automationId);
        }
        if (status) {
            filter.status = status;
        }
        if (contactPhone) {
            filter['context.contact.phoneNumber'] = contactPhone;
        }
        if (connectionId) {
            filter['context.connection.id'] = connectionId;
        }
        if (triggeredBy) {
            filter.triggeredBy = triggeredBy;
        }
        if (dateFrom || dateTo) {
            filter.startedAt = {};
            if (dateFrom)
                filter.startedAt.$gte = dateFrom;
            if (dateTo)
                filter.startedAt.$lte = dateTo;
        }
        const skip = (page - 1) * limit;
        const [executions, total] = await Promise.all([
            this.executionModel
                .find(filter)
                .sort({ startedAt: -1 })
                .skip(skip)
                .limit(limit)
                .exec(),
            this.executionModel.countDocuments(filter),
        ]);
        return {
            executions,
            total,
            page,
            limit,
        };
    }
    async getAutomationStats(currentUser) {
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const [total, active, inactive, draft, executionStats] = await Promise.all([
            this.automationModel.countDocuments(companyFilter),
            this.automationModel.countDocuments({ ...companyFilter, status: automation_schema_1.AutomationStatus.ACTIVE }),
            this.automationModel.countDocuments({ ...companyFilter, status: automation_schema_1.AutomationStatus.INACTIVE }),
            this.automationModel.countDocuments({ ...companyFilter, status: automation_schema_1.AutomationStatus.DRAFT }),
            this.automationModel.aggregate([
                { $match: companyFilter },
                {
                    $group: {
                        _id: null,
                        totalExecutions: { $sum: '$executionCount' },
                        totalSuccess: { $sum: '$successCount' },
                    },
                },
            ]),
        ]);
        const stats = executionStats[0] || { totalExecutions: 0, totalSuccess: 0 };
        const successRate = stats.totalExecutions > 0 ? (stats.totalSuccess / stats.totalExecutions) * 100 : 0;
        return {
            total,
            active,
            inactive,
            draft,
            totalExecutions: stats.totalExecutions,
            successRate: Math.round(successRate * 100) / 100,
        };
    }
    validateTriggerConfiguration(trigger) {
        if (!trigger.type) {
            throw new common_1.ConflictException('Tipo de gatilho é obrigatório');
        }
        switch (trigger.type) {
            case automation_schema_1.TriggerType.KEYWORD_MATCH:
                if (!trigger.keywords || trigger.keywords.length === 0) {
                    throw new common_1.ConflictException('Palavras-chave são obrigatórias para gatilho de palavra-chave');
                }
                break;
            case automation_schema_1.TriggerType.SCHEDULE:
                if (!trigger.schedule) {
                    throw new common_1.ConflictException('Configuração de agendamento é obrigatória');
                }
                break;
            case automation_schema_1.TriggerType.INACTIVITY:
                if (!trigger.inactivityMinutes || trigger.inactivityMinutes < 1) {
                    throw new common_1.ConflictException('Minutos de inatividade devem ser maior que 0');
                }
                break;
            case automation_schema_1.TriggerType.WEBHOOK:
                if (!trigger.webhookUrl) {
                    throw new common_1.ConflictException('URL do webhook é obrigatória');
                }
                break;
        }
    }
    validateActionSteps(actions) {
        if (actions.length === 0) {
            throw new common_1.ConflictException('Pelo menos uma ação é obrigatória');
        }
        const stepIds = new Set();
        for (const action of actions) {
            if (!action.id) {
                throw new common_1.ConflictException('ID do passo é obrigatório');
            }
            if (stepIds.has(action.id)) {
                throw new common_1.ConflictException(`ID do passo duplicado: ${action.id}`);
            }
            stepIds.add(action.id);
            if (!action.type) {
                throw new common_1.ConflictException('Tipo da ação é obrigatório');
            }
            if (!action.config) {
                throw new common_1.ConflictException('Configuração da ação é obrigatória');
            }
        }
    }
    validateFlowSteps(flowSteps) {
        if (flowSteps.length === 0) {
            throw new common_1.ConflictException('Pelo menos um passo do fluxo é obrigatório');
        }
        const stepIds = new Set();
        for (const step of flowSteps) {
            if (!step.id) {
                throw new common_1.ConflictException('ID do passo é obrigatório');
            }
            if (stepIds.has(step.id)) {
                throw new common_1.ConflictException(`ID do passo duplicado: ${step.id}`);
            }
            stepIds.add(step.id);
            if (!step.type) {
                throw new common_1.ConflictException('Tipo do passo é obrigatório');
            }
            if (!step.config) {
                throw new common_1.ConflictException('Configuração do passo é obrigatória');
            }
        }
    }
    validateAutomationCompleteness(automation) {
        if (!automation.trigger) {
            throw new common_1.ConflictException('Gatilho não configurado');
        }
        const hasActions = automation.actions && automation.actions.length > 0;
        const hasFlowSteps = automation.flowSteps && automation.flowSteps.length > 0;
        if (!hasActions && !hasFlowSteps) {
            throw new common_1.ConflictException('Automação deve ter pelo menos uma ação ou passo de fluxo');
        }
        if (hasFlowSteps && !automation.startStepId) {
            throw new common_1.ConflictException('Passo inicial é obrigatório para fluxos');
        }
    }
};
exports.AutomationService = AutomationService;
exports.AutomationService = AutomationService = AutomationService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(automation_schema_1.Automation.name)),
    __param(1, (0, mongoose_1.InjectModel)(automation_execution_schema_1.AutomationExecution.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], AutomationService);
//# sourceMappingURL=automation.service.js.map