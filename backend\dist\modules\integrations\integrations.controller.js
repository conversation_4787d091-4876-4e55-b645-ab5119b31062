"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const integrations_service_1 = require("./services/integrations.service");
const create_integration_dto_1 = require("./dto/create-integration.dto");
const update_integration_dto_1 = require("./dto/update-integration.dto");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const tenant_guard_1 = require("../../common/guards/tenant.guard");
const permissions_decorator_1 = require("../../common/decorators/permissions.decorator");
const integration_entity_1 = require("../../database/entities/integration.entity");
const webhook_log_entity_1 = require("../../database/entities/webhook-log.entity");
let IntegrationsController = class IntegrationsController {
    integrationsService;
    constructor(integrationsService) {
        this.integrationsService = integrationsService;
    }
    async create(createIntegrationDto, currentUser) {
        return this.integrationsService.create(createIntegrationDto, currentUser);
    }
    async findAll(query, currentUser) {
        return this.integrationsService.findAll(query, currentUser);
    }
    async getStats(currentUser) {
        return this.integrationsService.getIntegrationStats(currentUser);
    }
    async getTypes() {
        return {
            types: Object.values(integration_entity_1.IntegrationType).map(type => ({
                value: type,
                label: this.getTypeLabel(type),
                description: this.getTypeDescription(type),
                category: this.getTypeCategory(type),
            })),
            events: Object.values(integration_entity_1.TriggerEvent).map(event => ({
                value: event,
                label: this.getEventLabel(event),
                description: this.getEventDescription(event),
            })),
        };
    }
    async findOne(id, currentUser) {
        return this.integrationsService.findOne(id, currentUser);
    }
    async update(id, updateIntegrationDto, currentUser) {
        return this.integrationsService.update(id, updateIntegrationDto, currentUser);
    }
    async remove(id, currentUser) {
        return this.integrationsService.remove(id, currentUser);
    }
    async test(id, currentUser) {
        return this.integrationsService.testIntegration(id, currentUser);
    }
    async getLogs(id, query, currentUser) {
        query.integrationId = id;
        return this.integrationsService.getWebhookLogs(query, currentUser);
    }
    async getAllLogs(query, currentUser) {
        return this.integrationsService.getWebhookLogs(query, currentUser);
    }
    async retryWebhook(logId, currentUser) {
        return this.integrationsService.retryWebhook(logId, currentUser);
    }
    getTypeLabel(type) {
        const labels = {
            [integration_entity_1.IntegrationType.WEBHOOK]: 'Webhook',
            [integration_entity_1.IntegrationType.GOOGLE_SHEETS]: 'Google Sheets',
            [integration_entity_1.IntegrationType.ZAPIER]: 'Zapier',
            [integration_entity_1.IntegrationType.CRM_HUBSPOT]: 'HubSpot CRM',
            [integration_entity_1.IntegrationType.CRM_SALESFORCE]: 'Salesforce CRM',
            [integration_entity_1.IntegrationType.CRM_PIPEDRIVE]: 'Pipedrive CRM',
            [integration_entity_1.IntegrationType.EMAIL_MAILCHIMP]: 'Mailchimp',
            [integration_entity_1.IntegrationType.EMAIL_SENDGRID]: 'SendGrid',
            [integration_entity_1.IntegrationType.SMS_TWILIO]: 'Twilio SMS',
            [integration_entity_1.IntegrationType.SLACK]: 'Slack',
            [integration_entity_1.IntegrationType.DISCORD]: 'Discord',
            [integration_entity_1.IntegrationType.TELEGRAM]: 'Telegram',
            [integration_entity_1.IntegrationType.CUSTOM_API]: 'API Customizada',
        };
        return labels[type] || type;
    }
    getTypeDescription(type) {
        const descriptions = {
            [integration_entity_1.IntegrationType.WEBHOOK]: 'Envie dados para qualquer URL via HTTP',
            [integration_entity_1.IntegrationType.GOOGLE_SHEETS]: 'Sincronize dados com planilhas do Google',
            [integration_entity_1.IntegrationType.ZAPIER]: 'Conecte com milhares de aplicativos via Zapier',
            [integration_entity_1.IntegrationType.CRM_HUBSPOT]: 'Integre com HubSpot CRM',
            [integration_entity_1.IntegrationType.CRM_SALESFORCE]: 'Integre com Salesforce CRM',
            [integration_entity_1.IntegrationType.CRM_PIPEDRIVE]: 'Integre with Pipedrive CRM',
            [integration_entity_1.IntegrationType.EMAIL_MAILCHIMP]: 'Envie contatos para listas do Mailchimp',
            [integration_entity_1.IntegrationType.EMAIL_SENDGRID]: 'Envie emails via SendGrid',
            [integration_entity_1.IntegrationType.SMS_TWILIO]: 'Envie SMS via Twilio',
            [integration_entity_1.IntegrationType.SLACK]: 'Envie notificações para o Slack',
            [integration_entity_1.IntegrationType.DISCORD]: 'Envie notificações para o Discord',
            [integration_entity_1.IntegrationType.TELEGRAM]: 'Envie notificações para o Telegram',
            [integration_entity_1.IntegrationType.CUSTOM_API]: 'Integre com APIs customizadas',
        };
        return descriptions[type] || '';
    }
    getTypeCategory(type) {
        const categories = {
            [integration_entity_1.IntegrationType.WEBHOOK]: 'webhook',
            [integration_entity_1.IntegrationType.GOOGLE_SHEETS]: 'productivity',
            [integration_entity_1.IntegrationType.ZAPIER]: 'automation',
            [integration_entity_1.IntegrationType.CRM_HUBSPOT]: 'crm',
            [integration_entity_1.IntegrationType.CRM_SALESFORCE]: 'crm',
            [integration_entity_1.IntegrationType.CRM_PIPEDRIVE]: 'crm',
            [integration_entity_1.IntegrationType.EMAIL_MAILCHIMP]: 'email',
            [integration_entity_1.IntegrationType.EMAIL_SENDGRID]: 'email',
            [integration_entity_1.IntegrationType.SMS_TWILIO]: 'sms',
            [integration_entity_1.IntegrationType.SLACK]: 'communication',
            [integration_entity_1.IntegrationType.DISCORD]: 'communication',
            [integration_entity_1.IntegrationType.TELEGRAM]: 'communication',
            [integration_entity_1.IntegrationType.CUSTOM_API]: 'custom',
        };
        return categories[type] || 'other';
    }
    getEventLabel(event) {
        const labels = {
            [integration_entity_1.TriggerEvent.MESSAGE_RECEIVED]: 'Mensagem Recebida',
            [integration_entity_1.TriggerEvent.MESSAGE_SENT]: 'Mensagem Enviada',
            [integration_entity_1.TriggerEvent.CONTACT_CREATED]: 'Contato Criado',
            [integration_entity_1.TriggerEvent.CONTACT_UPDATED]: 'Contato Atualizado',
            [integration_entity_1.TriggerEvent.LEAD_CONVERTED]: 'Lead Convertido',
            [integration_entity_1.TriggerEvent.AUTOMATION_TRIGGERED]: 'Automação Disparada',
            [integration_entity_1.TriggerEvent.AUTOMATION_COMPLETED]: 'Automação Concluída',
            [integration_entity_1.TriggerEvent.CONNECTION_STATUS_CHANGED]: 'Status da Conexão Alterado',
            [integration_entity_1.TriggerEvent.USER_CREATED]: 'Usuário Criado',
            [integration_entity_1.TriggerEvent.SUBSCRIPTION_CHANGED]: 'Assinatura Alterada',
            [integration_entity_1.TriggerEvent.PAYMENT_RECEIVED]: 'Pagamento Recebido',
            [integration_entity_1.TriggerEvent.CUSTOM_EVENT]: 'Evento Customizado',
        };
        return labels[event] || event;
    }
    getEventDescription(event) {
        const descriptions = {
            [integration_entity_1.TriggerEvent.MESSAGE_RECEIVED]: 'Disparado quando uma mensagem é recebida',
            [integration_entity_1.TriggerEvent.MESSAGE_SENT]: 'Disparado quando uma mensagem é enviada',
            [integration_entity_1.TriggerEvent.CONTACT_CREATED]: 'Disparado quando um novo contato é criado',
            [integration_entity_1.TriggerEvent.CONTACT_UPDATED]: 'Disparado quando um contato é atualizado',
            [integration_entity_1.TriggerEvent.LEAD_CONVERTED]: 'Disparado quando um lead é convertido',
            [integration_entity_1.TriggerEvent.AUTOMATION_TRIGGERED]: 'Disparado quando uma automação é iniciada',
            [integration_entity_1.TriggerEvent.AUTOMATION_COMPLETED]: 'Disparado quando uma automação é concluída',
            [integration_entity_1.TriggerEvent.CONNECTION_STATUS_CHANGED]: 'Disparado quando o status de uma conexão muda',
            [integration_entity_1.TriggerEvent.USER_CREATED]: 'Disparado quando um novo usuário é criado',
            [integration_entity_1.TriggerEvent.SUBSCRIPTION_CHANGED]: 'Disparado quando uma assinatura é alterada',
            [integration_entity_1.TriggerEvent.PAYMENT_RECEIVED]: 'Disparado quando um pagamento é recebido',
            [integration_entity_1.TriggerEvent.CUSTOM_EVENT]: 'Disparado por eventos customizados',
        };
        return descriptions[event] || '';
    }
};
exports.IntegrationsController = IntegrationsController;
__decorate([
    (0, common_1.Post)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_CREATE),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova integração' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Integração criada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Já existe uma integração com este nome',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_integration_dto_1.CreateIntegrationDto, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar integrações' }),
    (0, swagger_1.ApiQuery)({ name: 'companyId', required: false, description: 'Filtrar por empresa' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, enum: integration_entity_1.IntegrationType, description: 'Filtrar por tipo' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: integration_entity_1.IntegrationStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'active', required: false, type: Boolean, description: 'Filtrar por ativo/inativo' }),
    (0, swagger_1.ApiQuery)({ name: 'triggerEvent', required: false, enum: integration_entity_1.TriggerEvent, description: 'Filtrar por evento' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, description: 'Buscar por nome ou descrição' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de integrações',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.ANALYTICS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter estatísticas de integrações' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Estatísticas de integrações',
        schema: {
            type: 'object',
            properties: {
                total: { type: 'number' },
                active: { type: 'number' },
                inactive: { type: 'number' },
                error: { type: 'number' },
                totalExecutions: { type: 'number' },
                successRate: { type: 'number' },
            },
        },
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('types'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar tipos de integração disponíveis' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de tipos de integração',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "getTypes", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Obter integração por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da integração',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Integração não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar integração' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Integração atualizada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Integração não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_integration_dto_1.UpdateIntegrationDto, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_DELETE),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Deletar integração' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Integração deletada com sucesso',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Integração não encontrada',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/test'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Testar integração' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Teste da integração executado',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "test", null);
__decorate([
    (0, common_1.Get)(':id/logs'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar logs da integração' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: webhook_log_entity_1.WebhookStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'eventType', required: false, description: 'Filtrar por tipo de evento' }),
    (0, swagger_1.ApiQuery)({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' }),
    (0, swagger_1.ApiQuery)({ name: 'dateTo', required: false, type: Date, description: 'Data final' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de logs da integração',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "getLogs", null);
__decorate([
    (0, common_1.Get)('logs/all'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar todos os logs de webhook' }),
    (0, swagger_1.ApiQuery)({ name: 'integrationId', required: false, description: 'Filtrar por integração' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: webhook_log_entity_1.WebhookStatus, description: 'Filtrar por status' }),
    (0, swagger_1.ApiQuery)({ name: 'eventType', required: false, description: 'Filtrar por tipo de evento' }),
    (0, swagger_1.ApiQuery)({ name: 'dateFrom', required: false, type: Date, description: 'Data inicial' }),
    (0, swagger_1.ApiQuery)({ name: 'dateTo', required: false, type: Date, description: 'Data final' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Página' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Itens por página' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de logs de webhook',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "getAllLogs", null);
__decorate([
    (0, common_1.Post)('logs/:logId/retry'),
    (0, permissions_decorator_1.RequirePermissions)(permissions_decorator_1.PERMISSIONS.INTEGRATIONS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Tentar novamente webhook falhado' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Webhook reagendado para nova tentativa',
    }),
    __param(0, (0, common_1.Param)('logId')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], IntegrationsController.prototype, "retryWebhook", null);
exports.IntegrationsController = IntegrationsController = __decorate([
    (0, swagger_1.ApiTags)('Integrações'),
    (0, common_1.Controller)('integrations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, tenant_guard_1.TenantGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [integrations_service_1.IntegrationsService])
], IntegrationsController);
//# sourceMappingURL=integrations.controller.js.map