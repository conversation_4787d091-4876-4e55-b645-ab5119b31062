"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookLog = exports.WebhookStatus = void 0;
const typeorm_1 = require("typeorm");
const integration_entity_1 = require("./integration.entity");
const company_entity_1 = require("./company.entity");
var WebhookStatus;
(function (WebhookStatus) {
    WebhookStatus["PENDING"] = "pending";
    WebhookStatus["SUCCESS"] = "success";
    WebhookStatus["FAILED"] = "failed";
    WebhookStatus["RETRYING"] = "retrying";
    WebhookStatus["CANCELLED"] = "cancelled";
})(WebhookStatus || (exports.WebhookStatus = WebhookStatus = {}));
let WebhookLog = class WebhookLog {
    id;
    integrationId;
    integration;
    companyId;
    company;
    status;
    eventType;
    eventData;
    request;
    response;
    errorMessage;
    errorCode;
    errorStack;
    attemptCount;
    maxAttempts;
    nextRetryAt;
    completedAt;
    duration;
    metadata;
    createdAt;
    isSuccess() {
        return this.status === WebhookStatus.SUCCESS;
    }
    isFailed() {
        return this.status === WebhookStatus.FAILED;
    }
    isPending() {
        return this.status === WebhookStatus.PENDING;
    }
    isRetrying() {
        return this.status === WebhookStatus.RETRYING;
    }
    canRetry() {
        return this.isFailed() &&
            this.attemptCount < this.maxAttempts &&
            (!this.nextRetryAt || new Date() >= this.nextRetryAt);
    }
    markAsSuccess(response) {
        this.status = WebhookStatus.SUCCESS;
        this.response = response;
        this.completedAt = new Date();
        this.duration = response.duration;
        this.errorMessage = null;
        this.errorCode = null;
        this.errorStack = null;
    }
    markAsFailed(error, response) {
        this.status = WebhookStatus.FAILED;
        this.response = response || null;
        this.completedAt = new Date();
        this.errorMessage = error.message || 'Unknown error';
        this.errorCode = error.code || 'UNKNOWN_ERROR';
        this.errorStack = error.stack || null;
        if (response) {
            this.duration = response.duration;
        }
        this.attemptCount += 1;
        if (this.canRetry()) {
            const delayMinutes = Math.pow(2, this.attemptCount) * 5;
            this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
            this.status = WebhookStatus.RETRYING;
        }
    }
    retry() {
        if (this.canRetry()) {
            this.status = WebhookStatus.PENDING;
            this.nextRetryAt = null;
            this.errorMessage = null;
            this.errorCode = null;
            this.errorStack = null;
        }
    }
    cancel() {
        this.status = WebhookStatus.CANCELLED;
        this.completedAt = new Date();
    }
    getRetryDelay() {
        return Math.pow(2, this.attemptCount) * 5 * 60 * 1000;
    }
    isHttpSuccess() {
        return Boolean(this.response && this.response.status >= 200 && this.response.status < 300);
    }
    getStatusDescription() {
        switch (this.status) {
            case WebhookStatus.PENDING:
                return 'Aguardando execução';
            case WebhookStatus.SUCCESS:
                return 'Executado com sucesso';
            case WebhookStatus.FAILED:
                return `Falhou após ${this.attemptCount} tentativa(s)`;
            case WebhookStatus.RETRYING:
                return `Tentativa ${this.attemptCount}/${this.maxAttempts} - Próxima em ${this.getNextRetryDescription()}`;
            case WebhookStatus.CANCELLED:
                return 'Cancelado';
            default:
                return 'Status desconhecido';
        }
    }
    getNextRetryDescription() {
        if (!this.nextRetryAt)
            return 'breve';
        const now = new Date();
        const diff = this.nextRetryAt.getTime() - now.getTime();
        if (diff <= 0)
            return 'agora';
        const minutes = Math.ceil(diff / (1000 * 60));
        if (minutes < 60)
            return `${minutes} minuto(s)`;
        const hours = Math.ceil(minutes / 60);
        return `${hours} hora(s)`;
    }
};
exports.WebhookLog = WebhookLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], WebhookLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WebhookLog.prototype, "integrationId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => integration_entity_1.Integration, integration => integration.logs),
    (0, typeorm_1.JoinColumn)({ name: 'integrationId' }),
    __metadata("design:type", integration_entity_1.Integration)
], WebhookLog.prototype, "integration", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WebhookLog.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company),
    (0, typeorm_1.JoinColumn)({ name: 'companyId' }),
    __metadata("design:type", company_entity_1.Company)
], WebhookLog.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: WebhookStatus,
        default: WebhookStatus.PENDING,
    }),
    __metadata("design:type", String)
], WebhookLog.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], WebhookLog.prototype, "eventType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "eventData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "request", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "response", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "errorCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "errorStack", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], WebhookLog.prototype, "attemptCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 3 }),
    __metadata("design:type", Number)
], WebhookLog.prototype, "maxAttempts", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "nextRetryAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], WebhookLog.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], WebhookLog.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], WebhookLog.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], WebhookLog.prototype, "createdAt", void 0);
exports.WebhookLog = WebhookLog = __decorate([
    (0, typeorm_1.Entity)('webhook_logs'),
    (0, typeorm_1.Index)(['integrationId', 'status']),
    (0, typeorm_1.Index)(['companyId', 'createdAt']),
    (0, typeorm_1.Index)(['eventType', 'createdAt'])
], WebhookLog);
//# sourceMappingURL=webhook-log.entity.js.map