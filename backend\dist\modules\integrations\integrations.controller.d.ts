import { IntegrationsService, FindIntegrationsOptions, FindWebhookLogsOptions } from './services/integrations.service';
import { CreateIntegrationDto } from './dto/create-integration.dto';
import { UpdateIntegrationDto } from './dto/update-integration.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { IntegrationType, TriggerEvent } from '../../database/entities/integration.entity';
export declare class IntegrationsController {
    private readonly integrationsService;
    constructor(integrationsService: IntegrationsService);
    create(createIntegrationDto: CreateIntegrationDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities/integration.entity").Integration>;
    findAll(query: FindIntegrationsOptions, currentUser: AuthenticatedUser): Promise<{
        integrations: import("../../database/entities/integration.entity").Integration[];
        total: number;
        page: number;
        limit: number;
    }>;
    getStats(currentUser: AuthenticatedUser): Promise<{
        total: number;
        active: number;
        inactive: number;
        error: number;
        totalExecutions: number;
        successRate: number;
    }>;
    getTypes(): Promise<{
        types: {
            value: IntegrationType;
            label: string;
            description: string;
            category: string;
        }[];
        events: {
            value: TriggerEvent;
            label: string;
            description: string;
        }[];
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<import("../../database/entities/integration.entity").Integration>;
    update(id: string, updateIntegrationDto: UpdateIntegrationDto, currentUser: AuthenticatedUser): Promise<import("../../database/entities/integration.entity").Integration>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    test(id: string, currentUser: AuthenticatedUser): Promise<any>;
    getLogs(id: string, query: FindWebhookLogsOptions, currentUser: AuthenticatedUser): Promise<{
        logs: import("../../database/entities/webhook-log.entity").WebhookLog[];
        total: number;
        page: number;
        limit: number;
    }>;
    getAllLogs(query: FindWebhookLogsOptions, currentUser: AuthenticatedUser): Promise<{
        logs: import("../../database/entities/webhook-log.entity").WebhookLog[];
        total: number;
        page: number;
        limit: number;
    }>;
    retryWebhook(logId: string, currentUser: AuthenticatedUser): Promise<import("../../database/entities/webhook-log.entity").WebhookLog>;
    private getTypeLabel;
    private getTypeDescription;
    private getTypeCategory;
    private getEventLabel;
    private getEventDescription;
}
