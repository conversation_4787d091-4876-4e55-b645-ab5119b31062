{"version": 3, "file": "ModifierTagSet.js", "sourceRoot": "", "sources": ["../../src/details/ModifierTagSet.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAG3D,0EAAkG;AAElG;;;;;;;;GAQG;AACH;IAAA;QACmB,WAAM,GAAkB,EAAE,CAAC;QAE5C,qFAAqF;QACrF,0FAA0F;QAC1F,wCAAwC;QACvB,iBAAY,GAA6B,IAAI,GAAG,EAAuB,CAAC;IAsD3F,CAAC;IAjDC,sBAAW,iCAAK;QAHhB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED;;;;OAIG;IACI,mCAAU,GAAjB,UAAkB,eAAuB;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACI,+BAAM,GAAb,UAAc,qBAAyC;QACrD,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACI,kCAAS,GAAhB,UAAiB,qBAAyC;QACxD,IAAI,qBAAqB,CAAC,UAAU,KAAK,uCAAkB,CAAC,WAAW,EAAE,CAAC;YACxE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,oBAAoB,CAAC,CAAC;IAC3E,CAAC;IAED;;;OAGG;IACI,+BAAM,GAAb,UAAc,QAAqB;QACjC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3B,OAAO,IAAI,CAAC;IACd,CAAC;IACH,qBAAC;AAAD,CAAC,AA5DD,IA4DC;AA5DY,wCAAc", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport type { DocBlockTag } from '../nodes/DocBlockTag';\r\nimport { type TSDocTagDefinition, TSDocTagSyntaxKind } from '../configuration/TSDocTagDefinition';\r\n\r\n/**\r\n * Represents a set of modifier tags that were extracted from a doc comment.\r\n *\r\n * @remarks\r\n * TSDoc modifier tags are block tags that do not have any associated rich text content.\r\n * Instead, their presence or absence acts as an on/off switch, indicating some aspect\r\n * of the underlying API item.  For example, the `@internal` modifier indicates that a\r\n * signature is internal (i.e. not part of the public API contract).\r\n */\r\nexport class ModifierTagSet {\r\n  private readonly _nodes: DocBlockTag[] = [];\r\n\r\n  // NOTE: To implement case insensitivity, the keys in this set are always upper-case.\r\n  // This convention makes the normalization more obvious (and as a general practice handles\r\n  // the Turkish \"i\" character correctly).\r\n  private readonly _nodesByName: Map<string, DocBlockTag> = new Map<string, DocBlockTag>();\r\n\r\n  /**\r\n   * The original block tag nodes that defined the modifiers in this set, excluding duplicates.\r\n   */\r\n  public get nodes(): ReadonlyArray<DocBlockTag> {\r\n    return this._nodes;\r\n  }\r\n\r\n  /**\r\n   * Returns true if the set contains a DocBlockTag with the specified tag name.\r\n   * Note that synonyms are not considered.  The comparison is case-insensitive.\r\n   * @param modifierTagName - The name of the tag, including the `@` prefix  For example, `@internal`\r\n   */\r\n  public hasTagName(modifierTagName: string): boolean {\r\n    return this._nodesByName.has(modifierTagName.toUpperCase());\r\n  }\r\n\r\n  /**\r\n   * Returns true if the set contains a DocBlockTag matching the specified tag definition.\r\n   * Note that synonyms are not considered.  The comparison is case-insensitive.\r\n   * The TSDocTagDefinition must be a modifier tag.\r\n   * @param tagName - The name of the tag, including the `@` prefix  For example, `@internal`\r\n   */\r\n  public hasTag(modifierTagDefinition: TSDocTagDefinition): boolean {\r\n    return !!this.tryGetTag(modifierTagDefinition);\r\n  }\r\n\r\n  /**\r\n   * Returns a DocBlockTag matching the specified tag definition, or undefined if no such\r\n   * tag was added to the set.  If there were multiple instances, returned object will be\r\n   * the first one to be added.\r\n   */\r\n  public tryGetTag(modifierTagDefinition: TSDocTagDefinition): DocBlockTag | undefined {\r\n    if (modifierTagDefinition.syntaxKind !== TSDocTagSyntaxKind.ModifierTag) {\r\n      throw new Error('The tag definition is not a modifier tag');\r\n    }\r\n    return this._nodesByName.get(modifierTagDefinition.tagNameWithUpperCase);\r\n  }\r\n\r\n  /**\r\n   * Adds a new modifier tag to the set.  If a tag already exists with the same name,\r\n   * then no change is made, and the return value is false.\r\n   */\r\n  public addTag(blockTag: DocBlockTag): boolean {\r\n    if (this._nodesByName.has(blockTag.tagNameWithUpperCase)) {\r\n      return false;\r\n    }\r\n\r\n    this._nodesByName.set(blockTag.tagNameWithUpperCase, blockTag);\r\n    this._nodes.push(blockTag);\r\n\r\n    return true;\r\n  }\r\n}\r\n"]}