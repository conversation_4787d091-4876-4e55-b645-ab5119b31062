{"version": 3, "file": "DocLinkTag.js", "sourceRoot": "", "sources": ["../../src/nodes/DocLinkTag.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;;;;;;;;;;;AAE3D,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEjD,OAAO,EACL,gBAAgB,EAGjB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AA+BvD;;GAEG;AACH;IAAgC,8BAAgB;IAgB9C;;;OAGG;IACH,oBAAmB,UAA+D;QAChF,YAAA,MAAK,YAAC,UAAU,CAAC,SAAC;QAElB,IAAI,KAAI,CAAC,oBAAoB,KAAK,OAAO,EAAE,CAAC;YAC1C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;QAED,KAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC;QAEnD,IAAI,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3C,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,qBAAqB,KAAK,SAAS,EAAE,CAAC;gBAC/F,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;YACrG,CAAC;YAED,IAAI,UAAU,CAAC,qBAAqB,EAAE,CAAC;gBACrC,KAAI,CAAC,sBAAsB,GAAG,IAAI,UAAU,CAAC;oBAC3C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,sBAAsB;oBAC/C,OAAO,EAAE,UAAU,CAAC,qBAAqB;iBAC1C,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,8BAA8B,EAAE,CAAC;gBAC9C,KAAI,CAAC,+BAA+B,GAAG,IAAI,UAAU,CAAC;oBACpD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,8BAA8B;iBACnD,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC3B,KAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC;oBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,YAAY;oBACrC,OAAO,EAAE,UAAU,CAAC,WAAW;iBAChC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACvC,KAAI,CAAC,wBAAwB,GAAG,IAAI,UAAU,CAAC;oBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,uBAAuB;iBAC5C,CAAC,CAAC;YACL,CAAC;YAED,IAAI,UAAU,CAAC,eAAe,EAAE,CAAC;gBAC/B,KAAI,CAAC,gBAAgB,GAAG,IAAI,UAAU,CAAC;oBACrC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,gBAAgB;oBACzC,OAAO,EAAE,UAAU,CAAC,eAAe;iBACpC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,UAAU,CAAC,2BAA2B,EAAE,CAAC;gBAC3C,KAAI,CAAC,4BAA4B,GAAG,IAAI,UAAU,CAAC;oBACjD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,WAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,2BAA2B;iBAChD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,UAAU,CAAC,eAAe,KAAK,SAAS,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gBACxF,MAAM,IAAI,KAAK,CAAC,iFAAiF,CAAC,CAAC;YACrG,CAAC;YAED,KAAI,CAAC,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC;YACjD,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;QACvC,CAAC;;IACH,CAAC;IAGD,sBAAW,4BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,WAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;;;OAAA;IAQD,sBAAW,uCAAe;QAN1B;;;;;WAKG;aACH;YACE,OAAO,IAAI,CAAC,gBAAgB,CAAC;QAC/B,CAAC;;;OAAA;IAQD,sBAAW,sCAAc;QANzB;;;;;WAKG;aACH;YACE,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;gBACvC,IAAI,IAAI,CAAC,sBAAsB,KAAK,SAAS,EAAE,CAAC;oBAC9C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACxE,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAwBD,sBAAW,gCAAQ;QAtBnB;;;;;;;;;;;;;;;;;;;;;WAqBG;aACH;YACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACjC,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;oBACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAC5D,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAED,gBAAgB;IACN,4CAAuB,GAAjC;QACE,WAAW;QACX,OAAO;YACL,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,sBAAsB;YAC3B,IAAI,CAAC,+BAA+B;YACpC,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,4BAA4B;SAClC,CAAC;IACJ,CAAC;IACH,iBAAC;AAAD,CAAC,AAlKD,CAAgC,gBAAgB,GAkK/C", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { DocNodeKind, DocNode } from './DocNode';\r\nimport type { DocDeclarationReference } from './DocDeclarationReference';\r\nimport {\r\n  DocInlineTagBase,\r\n  type IDocInlineTagBaseParsedParameters,\r\n  type IDocInlineTagBaseParameters\r\n} from './DocInlineTagBase';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\nimport type { TokenSequence } from '../parser/TokenSequence';\r\n\r\n/**\r\n * Constructor parameters for {@link DocLinkTag}.\r\n */\r\nexport interface IDocLinkTagParameters extends IDocInlineTagBaseParameters {\r\n  codeDestination?: DocDeclarationReference;\r\n\r\n  urlDestination?: string;\r\n\r\n  linkText?: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocLinkTag}.\r\n */\r\nexport interface IDocLinkTagParsedParameters extends IDocInlineTagBaseParsedParameters {\r\n  codeDestination?: DocDeclarationReference;\r\n\r\n  urlDestinationExcerpt?: TokenSequence;\r\n\r\n  spacingAfterDestinationExcerpt?: TokenSequence;\r\n\r\n  pipeExcerpt?: TokenSequence;\r\n  spacingAfterPipeExcerpt?: TokenSequence;\r\n\r\n  linkTextExcerpt?: TokenSequence;\r\n  spacingAfterLinkTextExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents an `{@link}` tag.\r\n */\r\nexport class DocLinkTag extends DocInlineTagBase {\r\n  private readonly _codeDestination: DocDeclarationReference | undefined;\r\n\r\n  private _urlDestination: string | undefined;\r\n  private readonly _urlDestinationExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _spacingAfterDestinationExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _pipeExcerpt: DocExcerpt | undefined;\r\n  private readonly _spacingAfterPipeExcerpt: DocExcerpt | undefined;\r\n\r\n  private _linkText: string | undefined;\r\n  private readonly _spacingAfterLinkTextExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _linkTextExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocLinkTagParameters | IDocLinkTagParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (this.tagNameWithUpperCase !== '@LINK') {\r\n      throw new Error('DocLinkTag requires the tag name to be \"{@link}\"');\r\n    }\r\n\r\n    this._codeDestination = parameters.codeDestination;\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.codeDestination !== undefined && parameters.urlDestinationExcerpt !== undefined) {\r\n        throw new Error('Either the codeDestination or the urlDestination may be specified, but not both');\r\n      }\r\n\r\n      if (parameters.urlDestinationExcerpt) {\r\n        this._urlDestinationExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.LinkTag_UrlDestination,\r\n          content: parameters.urlDestinationExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterDestinationExcerpt) {\r\n        this._spacingAfterDestinationExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterDestinationExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.pipeExcerpt) {\r\n        this._pipeExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.LinkTag_Pipe,\r\n          content: parameters.pipeExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterPipeExcerpt) {\r\n        this._spacingAfterPipeExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterPipeExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.linkTextExcerpt) {\r\n        this._linkTextExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.LinkTag_LinkText,\r\n          content: parameters.linkTextExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterLinkTextExcerpt) {\r\n        this._spacingAfterLinkTextExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterLinkTextExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      if (parameters.codeDestination !== undefined && parameters.urlDestination !== undefined) {\r\n        throw new Error('Either the codeDestination or the urlDestination may be specified, but not both');\r\n      }\r\n\r\n      this._urlDestination = parameters.urlDestination;\r\n      this._linkText = parameters.linkText;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.LinkTag;\r\n  }\r\n\r\n  /**\r\n   * If the link tag refers to a declaration, this returns the declaration reference object;\r\n   * otherwise this property is undefined.\r\n   * @remarks\r\n   * Either the `codeDestination` or the `urlDestination` property will be defined, but never both.\r\n   */\r\n  public get codeDestination(): DocDeclarationReference | undefined {\r\n    return this._codeDestination;\r\n  }\r\n\r\n  /**\r\n   * If the link tag was an ordinary URI, this returns the URL string;\r\n   * otherwise this property is undefined.\r\n   * @remarks\r\n   * Either the `codeDestination` or the `urlDestination` property will be defined, but never both.\r\n   */\r\n  public get urlDestination(): string | undefined {\r\n    if (this._urlDestination === undefined) {\r\n      if (this._urlDestinationExcerpt !== undefined) {\r\n        this._urlDestination = this._urlDestinationExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._urlDestination;\r\n  }\r\n\r\n  /**\r\n   * An optional text string that is the hyperlink text.  If omitted, the documentation\r\n   * renderer will use a default string based on the link itself (e.g. the URL text\r\n   * or the declaration identifier).\r\n   *\r\n   * @remarks\r\n   *\r\n   * In HTML, the hyperlink can include leading/trailing space characters around the link text.\r\n   * For example, this HTML will cause a web browser to `y` and also the space character before\r\n   * and after it:\r\n   *\r\n   * ```html\r\n   * x<a href=\"#Button\"> y </a> z\r\n   * ```\r\n   *\r\n   * Unlike HTML, TSDoc trims leading/trailing spaces.  For example, this TSDoc will be\r\n   * displayed `xy z` and underline only the `y` character:\r\n   *\r\n   * ```\r\n   * x{@link Button | y } z\r\n   * ```\r\n   */\r\n  public get linkText(): string | undefined {\r\n    if (this._linkText === undefined) {\r\n      if (this._linkTextExcerpt !== undefined) {\r\n        this._linkText = this._linkTextExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._linkText;\r\n  }\r\n\r\n  /** @override */\r\n  protected getChildNodesForContent(): ReadonlyArray<DocNode | undefined> {\r\n    // abstract\r\n    return [\r\n      this._codeDestination,\r\n      this._urlDestinationExcerpt,\r\n      this._spacingAfterDestinationExcerpt,\r\n      this._pipeExcerpt,\r\n      this._spacingAfterPipeExcerpt,\r\n      this._linkTextExcerpt,\r\n      this._spacingAfterLinkTextExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}