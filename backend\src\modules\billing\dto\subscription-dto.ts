import {
  IsString,
  IsEnum,
  IsN<PERSON>ber,
  IsBoolean,
  IsOptional,
  IsObject,
  IsDateString,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SubscriptionStatus, SubscriptionType } from '../../../database/entities/subscription.entity';

export class CreateSubscriptionDto {
  @ApiProperty({ description: 'ID da empresa' })
  @IsString()
  companyId: string;

  @ApiProperty({ description: 'ID do plano' })
  @IsString()
  planId: string;

  @ApiPropertyOptional({ description: 'Tipo da assinatura', enum: SubscriptionType, default: SubscriptionType.DIRECT })
  @IsOptional()
  @IsEnum(SubscriptionType)
  type?: SubscriptionType;

  @ApiPropertyOptional({ description: 'Data de início', example: '2023-12-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'Data de fim do trial', example: '2023-12-15T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  trialEndDate?: string;

  @ApiPropertyOptional({ description: 'Preço customizado', example: 199.99 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({ description: 'Taxa de setup', example: 50, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  setupFee?: number;

  @ApiPropertyOptional({ description: 'Percentual de desconto', example: 10, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  discountPercentage?: number;

  @ApiPropertyOptional({ description: 'Valor do desconto', example: 50, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  discountAmount?: number;

  @ApiPropertyOptional({ description: 'Data de fim do desconto', example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  discountEndDate?: string;

  @ApiPropertyOptional({ description: 'Código do cupom', example: 'DESCONTO20' })
  @IsOptional()
  @IsString()
  couponCode?: string;

  @ApiPropertyOptional({ description: 'ID da agência' })
  @IsOptional()
  @IsString()
  agencyId?: string;

  @ApiPropertyOptional({ description: 'Percentual de comissão da agência', example: 15, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  agencyCommissionPercentage?: number;

  @ApiPropertyOptional({ description: 'Comissão fixa da agência', example: 30, default: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  agencyFixedCommission?: number;

  @ApiPropertyOptional({ description: 'ID do método de pagamento' })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({ description: 'Renovação automática', default: true })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiPropertyOptional({ description: 'É proration', default: false })
  @IsOptional()
  @IsBoolean()
  isProrated?: boolean;

  @ApiPropertyOptional({ description: 'Funcionalidades customizadas' })
  @IsOptional()
  @IsObject()
  customFeatures?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Metadados adicionais' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Notificações por email', default: true })
  @IsOptional()
  @IsBoolean()
  emailNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notificações por webhook', default: false })
  @IsOptional()
  @IsBoolean()
  webhookNotifications?: boolean;

  @ApiPropertyOptional({ description: 'URL do webhook' })
  @IsOptional()
  @IsString()
  webhookUrl?: string;
}

export class UpdateSubscriptionDto {
  @ApiPropertyOptional({ description: 'Status da assinatura', enum: SubscriptionStatus })
  @IsOptional()
  @IsEnum(SubscriptionStatus)
  status?: SubscriptionStatus;

  @ApiPropertyOptional({ description: 'Data de fim', example: '2024-12-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Data de fim do trial', example: '2023-12-15T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  trialEndDate?: string;

  @ApiPropertyOptional({ description: 'Preço customizado', example: 199.99 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({ description: 'Percentual de desconto', example: 10 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  discountPercentage?: number;

  @ApiPropertyOptional({ description: 'Valor do desconto', example: 50 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  discountAmount?: number;

  @ApiPropertyOptional({ description: 'Data de fim do desconto', example: '2024-01-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  discountEndDate?: string;

  @ApiPropertyOptional({ description: 'Código do cupom', example: 'DESCONTO20' })
  @IsOptional()
  @IsString()
  couponCode?: string;

  @ApiPropertyOptional({ description: 'ID do método de pagamento' })
  @IsOptional()
  @IsString()
  paymentMethodId?: string;

  @ApiPropertyOptional({ description: 'Renovação automática' })
  @IsOptional()
  @IsBoolean()
  autoRenew?: boolean;

  @ApiPropertyOptional({ description: 'Cancelar no fim do período' })
  @IsOptional()
  @IsBoolean()
  cancelAtPeriodEnd?: boolean;

  @ApiPropertyOptional({ description: 'Funcionalidades customizadas' })
  @IsOptional()
  @IsObject()
  customFeatures?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Metadados adicionais' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Notificações por email' })
  @IsOptional()
  @IsBoolean()
  emailNotifications?: boolean;

  @ApiPropertyOptional({ description: 'Notificações por webhook' })
  @IsOptional()
  @IsBoolean()
  webhookNotifications?: boolean;

  @ApiPropertyOptional({ description: 'URL do webhook' })
  @IsOptional()
  @IsString()
  webhookUrl?: string;
}

export class ChangePlanDto {
  @ApiProperty({ description: 'ID do novo plano' })
  @IsString()
  newPlanId: string;

  @ApiPropertyOptional({ description: 'Data da mudança', example: '2023-12-01T00:00:00Z' })
  @IsOptional()
  @IsDateString()
  changeDate?: string;

  @ApiPropertyOptional({ description: 'É proration', default: true })
  @IsOptional()
  @IsBoolean()
  isProrated?: boolean;

  @ApiPropertyOptional({ description: 'Preço customizado para o novo plano', example: 299.99 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  customPrice?: number;

  @ApiPropertyOptional({ description: 'Motivo da mudança', example: 'Upgrade para mais funcionalidades' })
  @IsOptional()
  @IsString()
  reason?: string;
}

export class CancelSubscriptionDto {
  @ApiPropertyOptional({ description: 'Cancelar imediatamente', default: false })
  @IsOptional()
  @IsBoolean()
  immediately?: boolean;

  @ApiPropertyOptional({ description: 'Motivo do cancelamento', example: 'Não preciso mais do serviço' })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({ description: 'Feedback adicional', example: 'Preço muito alto' })
  @IsOptional()
  @IsString()
  feedback?: string;
}

export class SubscriptionUsageDto {
  @ApiProperty({ description: 'Conexões utilizadas', example: 3 })
  @IsNumber()
  @Min(0)
  connections: number;

  @ApiProperty({ description: 'Contatos utilizados', example: 5000 })
  @IsNumber()
  @Min(0)
  contacts: number;

  @ApiProperty({ description: 'Mensagens utilizadas', example: 25000 })
  @IsNumber()
  @Min(0)
  messages: number;

  @ApiProperty({ description: 'Usuários utilizados', example: 5 })
  @IsNumber()
  @Min(0)
  users: number;

  @ApiProperty({ description: 'Automações utilizadas', example: 10 })
  @IsNumber()
  @Min(0)
  automations: number;

  @ApiProperty({ description: 'Chatbots utilizados', example: 2 })
  @IsNumber()
  @Min(0)
  chatbots: number;

  @ApiProperty({ description: 'Armazenamento utilizado em GB', example: 25.5 })
  @IsNumber()
  @Min(0)
  storageUsedGB: number;

  @ApiProperty({ description: 'Chamadas de API utilizadas', example: 10000 })
  @IsNumber()
  @Min(0)
  apiCalls: number;

  @ApiProperty({ description: 'Chamadas de webhook utilizadas', example: 500 })
  @IsNumber()
  @Min(0)
  webhookCalls: number;
}
