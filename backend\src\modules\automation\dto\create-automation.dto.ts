import {
  IsString,
  <PERSON>Enum,
  IsOptional,
  IsObject,
  IsArray,
  IsBoolean,
  IsNumber,
  ValidateNested,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  AutomationType, 
  TriggerType, 
  ActionType, 
  TriggerCondition, 
  ActionStep, 
  FlowStep 
} from '../../../database/entities/automation.schema';

export class TriggerConditionDto implements TriggerCondition {
  @ApiProperty({
    description: 'Tipo do gatilho',
    enum: TriggerType,
  })
  @IsEnum(TriggerType)
  type: TriggerType;

  @ApiPropertyOptional({
    description: 'Palavras-chave para gatilho',
    type: [String],
    example: ['oi', 'olá', 'help'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({
    description: 'Configuração de agendamento',
    example: { time: '09:00', days: [1, 2, 3, 4, 5], timezone: 'America/Sao_Paulo' },
  })
  @IsOptional()
  @IsObject()
  schedule?: {
    time: string;
    days: number[];
    timezone: string;
  };

  @ApiPropertyOptional({
    description: 'Minutos de inatividade para gatilho',
    example: 30,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  inactivityMinutes?: number;

  @ApiPropertyOptional({
    description: 'URL do webhook',
    example: 'https://example.com/webhook',
  })
  @IsOptional()
  @IsString()
  webhookUrl?: string;

  @ApiPropertyOptional({
    description: 'Condições customizadas',
  })
  @IsOptional()
  @IsObject()
  customConditions?: Record<string, any>;
}

export class ActionStepDto implements ActionStep {
  @ApiProperty({
    description: 'ID único do passo',
    example: 'step-1',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Tipo da ação',
    enum: ActionType,
  })
  @IsEnum(ActionType)
  type: ActionType;

  @ApiProperty({
    description: 'Nome do passo',
    example: 'Enviar mensagem de boas-vindas',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição do passo',
    example: 'Envia uma mensagem de boas-vindas para novos contatos',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Configuração da ação',
    example: { message: 'Olá! Bem-vindo ao nosso atendimento.' },
  })
  @IsObject()
  config: Record<string, any>;

  @ApiPropertyOptional({
    description: 'ID do próximo passo',
    example: 'step-2',
  })
  @IsOptional()
  @IsString()
  nextStepId?: string;

  @ApiPropertyOptional({
    description: 'Condições para próximos passos',
    type: [Object],
  })
  @IsOptional()
  @IsArray()
  conditions?: Array<{
    field: string;
    operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
    value: any;
    nextStepId: string;
  }>;

  @ApiPropertyOptional({
    description: 'Delay em segundos antes de executar',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  delay?: number;
}

export class FlowStepDto implements FlowStep {
  @ApiProperty({
    description: 'ID único do passo',
    example: 'flow-step-1',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Nome do passo',
    example: 'Mensagem inicial',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Tipo do passo',
    enum: ['message', 'condition', 'action', 'ai', 'human_handoff'],
  })
  @IsEnum(['message', 'condition', 'action', 'ai', 'human_handoff'])
  type: 'message' | 'condition' | 'action' | 'ai' | 'human_handoff';

  @ApiProperty({
    description: 'Posição no canvas',
    example: { x: 100, y: 200 },
  })
  @IsObject()
  position: { x: number; y: number };

  @ApiProperty({
    description: 'Configuração do passo',
  })
  @IsObject()
  config: {
    message?: {
      type: 'text' | 'image' | 'audio' | 'video' | 'document';
      content?: string;
      mediaUrl?: string;
      buttons?: Array<{
        id: string;
        text: string;
        nextStepId?: string;
      }>;
    };
    condition?: {
      field: string;
      operator: string;
      value: any;
      trueStepId?: string;
      falseStepId?: string;
    };
    action?: {
      type: ActionType;
      config: Record<string, any>;
    };
    ai?: {
      provider: 'openai' | 'google' | 'anthropic';
      model: string;
      prompt: string;
      maxTokens?: number;
      temperature?: number;
    };
  };

  @ApiPropertyOptional({
    description: 'IDs dos próximos passos',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  nextSteps?: string[];
}

export class AutomationSettingsDto {
  @ApiPropertyOptional({
    description: 'Se a automação está ativa',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Prioridade da automação',
    example: 1,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({
    description: 'Máximo de execuções por contato',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxExecutionsPerContact?: number;

  @ApiPropertyOptional({
    description: 'Cooldown em minutos',
    example: 60,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  cooldownMinutes?: number;

  @ApiPropertyOptional({
    description: 'Apenas em horário comercial',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  businessHoursOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Configuração de horário comercial',
  })
  @IsOptional()
  @IsObject()
  businessHours?: {
    start: string;
    end: string;
    timezone: string;
    days: number[];
  };

  @ApiPropertyOptional({
    description: 'Conexões permitidas',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  allowedConnections?: string[];

  @ApiPropertyOptional({
    description: 'Contatos excluídos',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludedContacts?: string[];

  @ApiPropertyOptional({
    description: 'Tags necessárias',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class CreateAutomationDto {
  @ApiProperty({
    description: 'Nome da automação',
    example: 'Boas-vindas para novos contatos',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Descrição da automação',
    example: 'Envia mensagem de boas-vindas e coleta informações básicas',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Tipo da automação',
    enum: AutomationType,
  })
  @IsEnum(AutomationType)
  type: AutomationType;

  @ApiProperty({
    description: 'Configuração do gatilho',
    type: TriggerConditionDto,
  })
  @ValidateNested()
  @Type(() => TriggerConditionDto)
  trigger: TriggerConditionDto;

  @ApiPropertyOptional({
    description: 'Passos de ação (para automações simples)',
    type: [ActionStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActionStepDto)
  actions?: ActionStepDto[];

  @ApiPropertyOptional({
    description: 'Passos do fluxo (para chatbot)',
    type: [FlowStepDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FlowStepDto)
  flowSteps?: FlowStepDto[];

  @ApiPropertyOptional({
    description: 'ID do passo inicial',
    example: 'step-1',
  })
  @IsOptional()
  @IsString()
  startStepId?: string;

  @ApiPropertyOptional({
    description: 'Configurações da automação',
    type: AutomationSettingsDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => AutomationSettingsDto)
  settings?: AutomationSettingsDto;

  @ApiPropertyOptional({
    description: 'Se é um template',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;

  @ApiPropertyOptional({
    description: 'Categoria do template',
    example: 'vendas',
  })
  @IsOptional()
  @IsString()
  templateCategory?: string;

  @ApiPropertyOptional({
    description: 'Metadados adicionais',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
