"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ContactsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContactsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const entities_1 = require("../../../database/entities");
let ContactsService = ContactsService_1 = class ContactsService {
    contactModel;
    logger = new common_1.Logger(ContactsService_1.name);
    constructor(contactModel) {
        this.contactModel = contactModel;
    }
    async create(createContactDto, currentUser) {
        const existingContact = await this.contactModel.findOne({
            phoneNumber: createContactDto.phoneNumber,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
        if (existingContact) {
            throw new common_1.ConflictException('Contato já existe para este número');
        }
        const contactData = {
            ...createContactDto,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
            createdBy: currentUser.id,
            tags: createContactDto.tags?.map(tag => ({
                ...tag,
                id: new mongoose_2.Types.ObjectId().toString(),
                createdAt: new Date(),
                createdBy: currentUser.id,
            })),
            source: createContactDto.source ? {
                ...createContactDto.source,
                capturedAt: new Date(),
            } : undefined,
        };
        const contact = new this.contactModel(contactData);
        const savedContact = await contact.save();
        this.logger.log(`Contact created: ${savedContact._id} for phone ${createContactDto.phoneNumber}`);
        return savedContact;
    }
    async findAll(options, currentUser) {
        const { companyId, whatsappConnectionId, status, isLead, assignedTo, tags, search, page = 1, limit = 10, sortBy = 'lastMessageAt', sortOrder = 'desc', } = options;
        const filter = {
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        };
        if (companyId && currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            filter.companyId = new mongoose_2.Types.ObjectId(companyId);
        }
        if (whatsappConnectionId) {
            filter.whatsappConnectionId = whatsappConnectionId;
        }
        if (status) {
            filter.status = status;
        }
        if (typeof isLead === 'boolean') {
            filter.isLead = isLead;
        }
        if (assignedTo) {
            filter.assignedTo = assignedTo;
        }
        if (tags && tags.length > 0) {
            filter['tags.name'] = { $in: tags };
        }
        if (search) {
            filter.$or = [
                { name: { $regex: search, $options: 'i' } },
                { phoneNumber: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
                { notes: { $regex: search, $options: 'i' } },
            ];
        }
        const skip = (page - 1) * limit;
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        const [contacts, total] = await Promise.all([
            this.contactModel
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.contactModel.countDocuments(filter),
        ]);
        return {
            contacts,
            total,
            page,
            limit,
        };
    }
    async findOne(id, currentUser) {
        const contact = await this.contactModel.findOne({
            _id: new mongoose_2.Types.ObjectId(id),
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
        if (!contact) {
            throw new common_1.NotFoundException('Contato não encontrado');
        }
        return contact;
    }
    async findByPhone(phoneNumber, whatsappConnectionId, currentUser) {
        return this.contactModel.findOne({
            phoneNumber,
            whatsappConnectionId,
            companyId: new mongoose_2.Types.ObjectId(currentUser.companyId),
        });
    }
    async update(id, updateContactDto, currentUser) {
        const contact = await this.findOne(id, currentUser);
        if (updateContactDto.tags) {
            updateContactDto.tags = updateContactDto.tags.map(tag => ({
                ...tag,
                id: tag.id || new mongoose_2.Types.ObjectId().toString(),
                createdAt: tag.createdAt || new Date(),
                createdBy: tag.createdBy || currentUser.id,
            }));
        }
        const updatedContact = await this.contactModel.findByIdAndUpdate(contact._id, {
            ...updateContactDto,
            updatedBy: currentUser.id,
        }, { new: true });
        this.logger.log(`Contact updated: ${id}`);
        return updatedContact;
    }
    async remove(id, currentUser) {
        const contact = await this.findOne(id, currentUser);
        await this.contactModel.findByIdAndDelete(contact._id);
        this.logger.log(`Contact deleted: ${id}`);
    }
    async addTag(id, tagName, tagColor, currentUser) {
        const contact = await this.findOne(id, currentUser);
        const existingTag = contact.tags?.find(tag => tag.name === tagName);
        if (existingTag) {
            throw new common_1.ConflictException('Tag já existe para este contato');
        }
        const newTag = {
            id: new mongoose_2.Types.ObjectId().toString(),
            name: tagName,
            color: tagColor,
            createdAt: new Date(),
            createdBy: currentUser.id,
        };
        const updatedContact = await this.contactModel.findByIdAndUpdate(contact._id, {
            $push: { tags: newTag },
            updatedBy: currentUser.id,
        }, { new: true });
        this.logger.log(`Tag added to contact: ${id} - ${tagName}`);
        return updatedContact;
    }
    async removeTag(id, tagId, currentUser) {
        const contact = await this.findOne(id, currentUser);
        const updatedContact = await this.contactModel.findByIdAndUpdate(contact._id, {
            $pull: { tags: { id: tagId } },
            updatedBy: currentUser.id,
        }, { new: true });
        this.logger.log(`Tag removed from contact: ${id} - ${tagId}`);
        return updatedContact;
    }
    async updateLastMessage(phoneNumber, whatsappConnectionId, companyId) {
        await this.contactModel.updateOne({
            phoneNumber,
            whatsappConnectionId,
            companyId: new mongoose_2.Types.ObjectId(companyId),
        }, {
            $set: {
                lastMessageAt: new Date(),
                lastInteractionAt: new Date(),
            },
            $inc: { messageCount: 1 },
        });
    }
    async getContactStats(currentUser) {
        const companyFilter = { companyId: new mongoose_2.Types.ObjectId(currentUser.companyId) };
        const [total, active, leads, blocked, archived] = await Promise.all([
            this.contactModel.countDocuments(companyFilter),
            this.contactModel.countDocuments({ ...companyFilter, status: entities_1.ContactStatus.ACTIVE }),
            this.contactModel.countDocuments({ ...companyFilter, isLead: true }),
            this.contactModel.countDocuments({ ...companyFilter, status: entities_1.ContactStatus.BLOCKED }),
            this.contactModel.countDocuments({ ...companyFilter, status: entities_1.ContactStatus.ARCHIVED }),
        ]);
        return { total, active, leads, blocked, archived };
    }
    async getOrCreateContact(phoneNumber, whatsappConnectionId, companyId, name) {
        let contact = await this.contactModel.findOne({
            phoneNumber,
            whatsappConnectionId,
            companyId: new mongoose_2.Types.ObjectId(companyId),
        });
        if (!contact) {
            contact = new this.contactModel({
                phoneNumber,
                whatsappConnectionId,
                companyId: new mongoose_2.Types.ObjectId(companyId),
                name,
                status: entities_1.ContactStatus.ACTIVE,
                isLead: false,
                messageCount: 0,
            });
            await contact.save();
            this.logger.log(`Auto-created contact for phone: ${phoneNumber}`);
        }
        else if (name && !contact.name) {
            contact.name = name;
            await contact.save();
        }
        return contact;
    }
};
exports.ContactsService = ContactsService;
exports.ContactsService = ContactsService = ContactsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(entities_1.Contact.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ContactsService);
//# sourceMappingURL=contacts.service.js.map