import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { WhatsAppConnection, ConnectionStatus, ConnectionType } from '../../database/entities';
import { CreateConnectionDto } from './dto/create-connection.dto';
import { UpdateConnectionDto } from './dto/update-connection.dto';
import { SendMessageDto, BulkMessageDto } from './dto/send-message.dto';
import { AuthenticatedUser } from '../../common/decorators/current-user.decorator';
import { EvolutionApiService } from './services/evolution-api.service';
export interface FindConnectionsOptions {
    companyId?: string;
    status?: ConnectionStatus;
    type?: ConnectionType;
    assignedUserId?: string;
    search?: string;
    page?: number;
    limit?: number;
}
export declare class WhatsAppService {
    private connectionRepository;
    private evolutionApiService;
    private configService;
    private readonly logger;
    constructor(connectionRepository: Repository<WhatsAppConnection>, evolutionApiService: EvolutionApiService, configService: ConfigService);
    createConnection(createConnectionDto: CreateConnectionDto, currentUser: AuthenticatedUser): Promise<WhatsAppConnection>;
    findAll(options: FindConnectionsOptions, currentUser: AuthenticatedUser): Promise<{
        connections: WhatsAppConnection[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string, currentUser: AuthenticatedUser): Promise<WhatsAppConnection>;
    update(id: string, updateConnectionDto: UpdateConnectionDto, currentUser: AuthenticatedUser): Promise<WhatsAppConnection>;
    remove(id: string, currentUser: AuthenticatedUser): Promise<void>;
    connect(id: string, currentUser: AuthenticatedUser): Promise<{
        qrCode?: string;
    }>;
    disconnect(id: string, currentUser: AuthenticatedUser): Promise<void>;
    sendMessage(connectionId: string, sendMessageDto: SendMessageDto, currentUser: AuthenticatedUser): Promise<any>;
    sendBulkMessage(connectionId: string, bulkMessageDto: BulkMessageDto, currentUser: AuthenticatedUser): Promise<Array<{
        phoneNumber: string;
        success: boolean;
        result?: any;
        error?: string;
    }>>;
    handleWebhook(data: any): Promise<void>;
    private createEvolutionInstance;
    private mapToEvolutionMessage;
    private handleConnectionUpdate;
    private handleQRCodeUpdate;
    private handleMessageReceived;
}
