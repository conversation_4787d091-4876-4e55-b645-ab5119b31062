import { MessageType, MessageDirection, MessageStatus, MessageMedia, MessageLocation, MessageContact } from '../../../database/entities';
export declare class MessageResponseDto {
    id: string;
    messageId: string;
    type: MessageType;
    direction: MessageDirection;
    status: MessageStatus;
    content?: string;
    media?: MessageMedia;
    location?: MessageLocation;
    contact?: MessageContact;
    quotedMessageId?: string;
    metadata?: Record<string, any>;
    timestamp: Date;
    sentAt?: Date;
    deliveredAt?: Date;
    readAt?: Date;
    errorMessage?: string;
    isAutomated: boolean;
    automationId?: string;
    isTemplate: boolean;
    templateName?: string;
    whatsappConnectionId: string;
    contactPhone: string;
    sentBy?: string;
    conversationId?: string;
    isFirstMessage: boolean;
    responseTime?: number;
    tags?: string[];
    createdAt: Date;
    updatedAt: Date;
}
