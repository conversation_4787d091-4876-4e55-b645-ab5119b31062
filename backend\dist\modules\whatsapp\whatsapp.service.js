"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WhatsAppService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const entities_1 = require("../../database/entities");
const evolution_api_service_1 = require("./services/evolution-api.service");
let WhatsAppService = WhatsAppService_1 = class WhatsAppService {
    connectionRepository;
    evolutionApiService;
    configService;
    logger = new common_1.Logger(WhatsAppService_1.name);
    constructor(connectionRepository, evolutionApiService, configService) {
        this.connectionRepository = connectionRepository;
        this.evolutionApiService = evolutionApiService;
        this.configService = configService;
    }
    async createConnection(createConnectionDto, currentUser) {
        if (currentUser.canAccessCompany && !currentUser.canAccessCompany(createConnectionDto.companyId)) {
            throw new common_1.ForbiddenException('Não é possível criar conexão nesta empresa');
        }
        const existingConnection = await this.connectionRepository.findOne({
            where: { phoneNumber: createConnectionDto.phoneNumber },
        });
        if (existingConnection) {
            throw new common_1.ConflictException('Número de telefone já está em uso');
        }
        const connection = this.connectionRepository.create({
            ...createConnectionDto,
            status: entities_1.ConnectionStatus.PENDING,
            createdBy: currentUser.id,
        });
        const savedConnection = await this.connectionRepository.save(connection);
        if (createConnectionDto.type === entities_1.ConnectionType.EVOLUTION_API) {
            try {
                await this.createEvolutionInstance(savedConnection);
            }
            catch (error) {
                await this.connectionRepository.delete(savedConnection.id);
                throw error;
            }
        }
        return savedConnection;
    }
    async findAll(options, currentUser) {
        const { companyId, status, type, assignedUserId, search, page = 1, limit = 10 } = options;
        const queryBuilder = this.connectionRepository.createQueryBuilder('connection')
            .leftJoinAndSelect('connection.company', 'company')
            .leftJoinAndSelect('connection.assignedUser', 'assignedUser');
        if (currentUser.role === entities_1.UserRole.SUPER_ADMIN) {
            if (companyId) {
                queryBuilder.andWhere('connection.companyId = :companyId', { companyId });
            }
        }
        else if (currentUser.role === entities_1.UserRole.AGENCY_ADMIN) {
            queryBuilder.andWhere('(connection.companyId = :currentCompanyId OR connection.companyId IN (SELECT c.id FROM companies c WHERE c.agencyId = :currentCompanyId))', { currentCompanyId: currentUser.companyId });
        }
        else {
            queryBuilder.andWhere('connection.companyId = :companyId', { companyId: currentUser.companyId });
        }
        if (status) {
            queryBuilder.andWhere('connection.status = :status', { status });
        }
        if (type) {
            queryBuilder.andWhere('connection.type = :type', { type });
        }
        if (assignedUserId) {
            queryBuilder.andWhere('connection.assignedUserId = :assignedUserId', { assignedUserId });
        }
        if (search) {
            queryBuilder.andWhere('(connection.name ILIKE :search OR connection.phoneNumber ILIKE :search)', { search: `%${search}%` });
        }
        const offset = (page - 1) * limit;
        queryBuilder.skip(offset).take(limit);
        queryBuilder.orderBy('connection.createdAt', 'DESC');
        const [connections, total] = await queryBuilder.getManyAndCount();
        return {
            connections,
            total,
            page,
            limit,
        };
    }
    async findOne(id, currentUser) {
        const connection = await this.connectionRepository.findOne({
            where: { id },
            relations: ['company', 'assignedUser'],
        });
        if (!connection) {
            throw new common_1.NotFoundException('Conexão não encontrada');
        }
        if (currentUser.canAccessCompany && !currentUser.canAccessCompany(connection.companyId)) {
            throw new common_1.ForbiddenException('Acesso negado a esta conexão');
        }
        return connection;
    }
    async update(id, updateConnectionDto, currentUser) {
        const connection = await this.findOne(id, currentUser);
        Object.assign(connection, updateConnectionDto);
        connection.updatedBy = currentUser.id;
        return this.connectionRepository.save(connection);
    }
    async remove(id, currentUser) {
        const connection = await this.findOne(id, currentUser);
        if (connection.type === entities_1.ConnectionType.EVOLUTION_API && connection.instanceId) {
            try {
                await this.evolutionApiService.deleteInstance(connection.instanceId);
            }
            catch (error) {
                this.logger.warn(`Failed to delete Evolution instance: ${error.message}`);
            }
        }
        await this.connectionRepository.softDelete(id);
    }
    async connect(id, currentUser) {
        const connection = await this.findOne(id, currentUser);
        if (connection.status === entities_1.ConnectionStatus.CONNECTED) {
            throw new common_1.BadRequestException('Conexão já está ativa');
        }
        if (connection.type === entities_1.ConnectionType.EVOLUTION_API) {
            try {
                const qrCode = await this.evolutionApiService.getQRCode(connection.instanceId);
                connection.updateStatus(entities_1.ConnectionStatus.CONNECTING);
                connection.qrCode = qrCode;
                await this.connectionRepository.save(connection);
                return { qrCode };
            }
            catch (error) {
                connection.updateStatus(entities_1.ConnectionStatus.ERROR, error.message);
                await this.connectionRepository.save(connection);
                throw error;
            }
        }
        throw new common_1.BadRequestException('Tipo de conexão não suportado');
    }
    async disconnect(id, currentUser) {
        const connection = await this.findOne(id, currentUser);
        if (connection.type === entities_1.ConnectionType.EVOLUTION_API && connection.instanceId) {
            try {
                await this.evolutionApiService.logout(connection.instanceId);
                connection.updateStatus(entities_1.ConnectionStatus.DISCONNECTED);
                await this.connectionRepository.save(connection);
            }
            catch (error) {
                connection.updateStatus(entities_1.ConnectionStatus.ERROR, error.message);
                await this.connectionRepository.save(connection);
                throw error;
            }
        }
    }
    async sendMessage(connectionId, sendMessageDto, currentUser) {
        const connection = await this.findOne(connectionId, currentUser);
        if (!connection.isConnected) {
            throw new common_1.BadRequestException('Conexão não está ativa');
        }
        if (connection.type === entities_1.ConnectionType.EVOLUTION_API) {
            const evolutionMessage = this.mapToEvolutionMessage(sendMessageDto);
            if (sendMessageDto.media) {
                return this.evolutionApiService.sendMediaMessage(connection.instanceId, evolutionMessage);
            }
            else {
                return this.evolutionApiService.sendMessage(connection.instanceId, evolutionMessage);
            }
        }
        throw new common_1.BadRequestException('Tipo de conexão não suportado');
    }
    async sendBulkMessage(connectionId, bulkMessageDto, currentUser) {
        const connection = await this.findOne(connectionId, currentUser);
        if (!connection.isConnected) {
            throw new common_1.BadRequestException('Conexão não está ativa');
        }
        const results = [];
        const { minInterval = 1, maxInterval = 5 } = bulkMessageDto;
        for (const phoneNumber of bulkMessageDto.to) {
            try {
                const messageDto = {
                    to: phoneNumber,
                    type: bulkMessageDto.type,
                    content: bulkMessageDto.content,
                    media: bulkMessageDto.media,
                    metadata: bulkMessageDto.metadata,
                };
                const result = await this.sendMessage(connectionId, messageDto, currentUser);
                results.push({ phoneNumber, success: true, result });
                if (bulkMessageDto.to.indexOf(phoneNumber) < bulkMessageDto.to.length - 1) {
                    const delay = Math.random() * (maxInterval - minInterval) + minInterval;
                    await new Promise(resolve => setTimeout(resolve, delay * 1000));
                }
            }
            catch (error) {
                results.push({ phoneNumber, success: false, error: error.message });
            }
        }
        return results;
    }
    async handleWebhook(data) {
        this.logger.log('Webhook received:', JSON.stringify(data, null, 2));
        switch (data.event) {
            case 'CONNECTION_UPDATE':
                await this.handleConnectionUpdate(data);
                break;
            case 'QRCODE_UPDATED':
                await this.handleQRCodeUpdate(data);
                break;
            case 'MESSAGES_UPSERT':
                await this.handleMessageReceived(data);
                break;
            default:
                this.logger.log(`Unhandled webhook event: ${data.event}`);
        }
    }
    async createEvolutionInstance(connection) {
        const instanceName = this.evolutionApiService.generateInstanceName(connection.companyId, connection.phoneNumber);
        const webhookUrl = `${this.configService.get('cors.origin')}/api/v1/whatsapp/webhook`;
        await this.evolutionApiService.createInstance(instanceName, webhookUrl);
        connection.instanceId = instanceName;
        await this.connectionRepository.save(connection);
    }
    mapToEvolutionMessage(sendMessageDto) {
        const message = {
            number: sendMessageDto.to.replace(/\D/g, ''),
        };
        if (sendMessageDto.content) {
            message.text = sendMessageDto.content;
        }
        if (sendMessageDto.media) {
            message.media = {
                mediatype: sendMessageDto.type,
                media: sendMessageDto.media.url,
                fileName: sendMessageDto.media.filename,
                caption: sendMessageDto.media.caption,
            };
        }
        if (sendMessageDto.location) {
            message.location = sendMessageDto.location;
        }
        if (sendMessageDto.contact) {
            message.contact = {
                fullName: sendMessageDto.contact.name,
                wuid: sendMessageDto.contact.phone,
                phoneNumber: sendMessageDto.contact.phone,
            };
        }
        if (sendMessageDto.quotedMessageId) {
            message.quoted = {
                messageId: sendMessageDto.quotedMessageId,
            };
        }
        return message;
    }
    async handleConnectionUpdate(data) {
        const connection = await this.connectionRepository.findOne({
            where: { instanceId: data.instanceName },
        });
        if (connection) {
            const newStatus = this.evolutionApiService.mapEvolutionStatusToConnectionStatus(data.data.state);
            connection.updateStatus(newStatus);
            await this.connectionRepository.save(connection);
        }
    }
    async handleQRCodeUpdate(data) {
        const connection = await this.connectionRepository.findOne({
            where: { instanceId: data.instanceName },
        });
        if (connection) {
            connection.qrCode = data.data.qrcode;
            await this.connectionRepository.save(connection);
        }
    }
    async handleMessageReceived(data) {
        this.logger.log(`Message received for instance ${data.instanceName}`);
    }
};
exports.WhatsAppService = WhatsAppService;
exports.WhatsAppService = WhatsAppService = WhatsAppService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.WhatsAppConnection)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        evolution_api_service_1.EvolutionApiService,
        config_1.ConfigService])
], WhatsAppService);
//# sourceMappingURL=whatsapp.service.js.map