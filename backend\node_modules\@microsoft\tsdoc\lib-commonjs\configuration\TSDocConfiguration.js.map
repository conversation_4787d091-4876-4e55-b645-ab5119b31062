{"version": 3, "file": "TSDocConfiguration.js", "sourceRoot": "", "sources": ["../../src/configuration/TSDocConfiguration.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,wDAAuD;AAEvD,+EAA8E;AAC9E,mDAAkD;AAClD,4DAA2D;AAC3D,2DAA0G;AAE1G;;GAEG;AACH;IAQE;QACE,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,qBAAqB,GAAG,IAAI,GAAG,EAA8B,CAAC;QACnE,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAsB,CAAC;QAC9D,IAAI,CAAC,WAAW,GAAG,IAAI,2DAA4B,EAAE,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,+BAAc,EAAE,CAAC;QAC5C,IAAI,CAAC,sBAAsB,GAAG,IAAI,GAAG,EAAE,CAAC;QAExC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAElB,mCAAmC;QACnC,iCAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,kCAAK,GAAZ,UAAa,cAA+B;QAA/B,+BAAA,EAAA,sBAA+B;QAC1C,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,qBAAqB,GAAG,KAAK,CAAC;QAC/C,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,KAAK,CAAC;QACvD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QAEpC,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,+BAA+B;YAC/B,IAAI,CAAC,iBAAiB,CAAC,2BAAY,CAAC,cAAc,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAQD,sBAAW,8CAAc;QANzB;;;;;WAKG;aACH;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAUD,sBAAW,uDAAuB;QARlC;;;;;;;WAOG;aACH;YAAA,iBAEC;YADC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAtB,CAAsB,CAAC,CAAC;QACnE,CAAC;;;OAAA;IAKD,sBAAW,0CAAU;QAHrB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAKD,sBAAW,qDAAqB;QAHhC;;WAEG;aACH;YACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1D,CAAC;;;OAAA;IAKD,sBAAW,8CAAc;QAHzB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;;;OAAA;IAED;;;OAGG;IACI,gDAAmB,GAA1B,UAA2B,OAAe;QACxC,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED;;;OAGG;IACI,6DAAgC,GAAvC,UAAwC,uBAA+B;QACrE,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACjE,CAAC;IAED;;;;;;;OAOG;IACI,6CAAgB,GAAvB,UAAwB,aAAiC;QACvD,IAAM,kBAAkB,GAAmC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CACvF,aAAa,CAAC,oBAAoB,CACnC,CAAC;QAEF,IAAI,kBAAkB,KAAK,aAAa,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,kDAA2C,kBAAkB,CAAC,OAAO,CAAE,CAAC,CAAC;QAC3F,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,aAAa,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACpF,CAAC;IAED;;;;;;OAMG;IACI,8CAAiB,GAAxB,UACE,cAAiD,EACjD,SAA+B;QAE/B,KAA4B,UAAc,EAAd,iCAAc,EAAd,4BAAc,EAAd,IAAc,EAAE,CAAC;YAAxC,IAAM,aAAa,uBAAA;YACtB,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAErC,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,2CAAc,GAArB,UAAsB,aAAiC;QACrD,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;OAUG;IACI,6CAAgB,GAAvB,UAAwB,aAAiC,EAAE,SAAkB;QAC3E,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAC;QAC3C,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,qBAAqB,GAAG,IAAI,CAAC;IAC/C,CAAC;IAED;;;;;;;;OAQG;IACI,8CAAiB,GAAxB,UAAyB,cAAiD,EAAE,SAAkB;QAC5F,KAA4B,UAAc,EAAd,iCAAc,EAAd,4BAAc,EAAd,IAAc,EAAE,CAAC;YAAxC,IAAM,aAAa,uBAAA;YACtB,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,qDAAwB,GAA/B,UAAgC,QAAkB;QAChD,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,CAAC,6BAA6B,GAAG,IAAI,CAAC;QACtD,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;YAA5B,IAAM,OAAO,iBAAA;YAChB,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,mDAAsB,GAA7B,UAA8B,OAAe;QAC3C,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;;;;;;;OAQG;IACI,6CAAgB,GAAvB,UAAwB,SAAkC;QACxD,OAAO,sCAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC9C,CAAC;IAUD,sBAAW,kDAAkB;QAR7B;;;;;;;WAOG;aACH;YACE,OAAO,mCAAmD,CAAC;QAC7D,CAAC;;;OAAA;IAEO,mDAAsB,GAA9B,UAA+B,aAAiC;QAC9D,IAAM,QAAQ,GAAmC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAC7E,aAAa,CAAC,oBAAoB,CACnC,CAAC;QACF,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;QACH,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,6EAA6E,CAAC,CAAC;IACjG,CAAC;IACH,yBAAC;AAAD,CAAC,AAtPD,IAsPC;AAtPY,gDAAkB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport { StandardTags } from '../details/StandardTags';\r\nimport type { TSDocTagDefinition } from './TSDocTagDefinition';\r\nimport { TSDocValidationConfiguration } from './TSDocValidationConfiguration';\r\nimport { DocNodeManager } from './DocNodeManager';\r\nimport { BuiltInDocNodes } from '../nodes/BuiltInDocNodes';\r\nimport { type TSDocMessageId, allTsdocMessageIds, allTsdocMessageIdsSet } from '../parser/TSDocMessageId';\r\n\r\n/**\r\n * Configuration for the TSDocParser.\r\n */\r\nexport class TSDocConfiguration {\r\n  private readonly _tagDefinitions: TSDocTagDefinition[];\r\n  private readonly _tagDefinitionsByName: Map<string, TSDocTagDefinition>;\r\n  private readonly _supportedTagDefinitions: Set<TSDocTagDefinition>;\r\n  private readonly _validation: TSDocValidationConfiguration;\r\n  private readonly _docNodeManager: DocNodeManager;\r\n  private readonly _supportedHtmlElements: Set<string>;\r\n\r\n  public constructor() {\r\n    this._tagDefinitions = [];\r\n    this._tagDefinitionsByName = new Map<string, TSDocTagDefinition>();\r\n    this._supportedTagDefinitions = new Set<TSDocTagDefinition>();\r\n    this._validation = new TSDocValidationConfiguration();\r\n    this._docNodeManager = new DocNodeManager();\r\n    this._supportedHtmlElements = new Set();\r\n\r\n    this.clear(false);\r\n\r\n    // Register the built-in node kinds\r\n    BuiltInDocNodes.register(this);\r\n  }\r\n\r\n  /**\r\n   * Resets the `TSDocConfiguration` object to its initial empty state.\r\n   * @param noStandardTags - The `TSDocConfiguration` constructor normally adds definitions for the\r\n   * standard TSDoc tags.  Set `noStandardTags` to true for a completely empty `tagDefinitions` collection.\r\n   */\r\n  public clear(noStandardTags: boolean = false): void {\r\n    this._tagDefinitions.length = 0;\r\n    this._tagDefinitionsByName.clear();\r\n    this._supportedTagDefinitions.clear();\r\n    this._validation.ignoreUndefinedTags = false;\r\n    this._validation.reportUnsupportedTags = false;\r\n    this._validation.reportUnsupportedHtmlElements = false;\r\n    this._supportedHtmlElements.clear();\r\n\r\n    if (!noStandardTags) {\r\n      // Define all the standard tags\r\n      this.addTagDefinitions(StandardTags.allDefinitions);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * The TSDoc tags that are defined in this configuration.\r\n   *\r\n   * @remarks\r\n   * The subset of \"supported\" tags is tracked by {@link TSDocConfiguration.supportedTagDefinitions}.\r\n   */\r\n  public get tagDefinitions(): ReadonlyArray<TSDocTagDefinition> {\r\n    return this._tagDefinitions;\r\n  }\r\n\r\n  /**\r\n   * Returns the subset of {@link TSDocConfiguration.tagDefinitions}\r\n   * that are supported in this configuration.\r\n   *\r\n   * @remarks\r\n   * This property is only used when\r\n   * {@link TSDocValidationConfiguration.reportUnsupportedTags} is enabled.\r\n   */\r\n  public get supportedTagDefinitions(): ReadonlyArray<TSDocTagDefinition> {\r\n    return this.tagDefinitions.filter((x) => this.isTagSupported(x));\r\n  }\r\n\r\n  /**\r\n   * Enable/disable validation checks performed by the parser.\r\n   */\r\n  public get validation(): TSDocValidationConfiguration {\r\n    return this._validation;\r\n  }\r\n\r\n  /**\r\n   * The HTML element names that are supported in this configuration. Used in conjunction with the `reportUnsupportedHtmlElements` setting.\r\n   */\r\n  public get supportedHtmlElements(): string[] {\r\n    return Array.from(this._supportedHtmlElements.values());\r\n  }\r\n\r\n  /**\r\n   * Register custom DocNode subclasses.\r\n   */\r\n  public get docNodeManager(): DocNodeManager {\r\n    return this._docNodeManager;\r\n  }\r\n\r\n  /**\r\n   * Return the tag that was defined with the specified name, or undefined\r\n   * if not found.\r\n   */\r\n  public tryGetTagDefinition(tagName: string): TSDocTagDefinition | undefined {\r\n    return this._tagDefinitionsByName.get(tagName.toUpperCase());\r\n  }\r\n\r\n  /**\r\n   * Return the tag that was defined with the specified name, or undefined\r\n   * if not found.\r\n   */\r\n  public tryGetTagDefinitionWithUpperCase(alreadyUpperCaseTagName: string): TSDocTagDefinition | undefined {\r\n    return this._tagDefinitionsByName.get(alreadyUpperCaseTagName);\r\n  }\r\n\r\n  /**\r\n   * Define a new TSDoc tag to be recognized by the TSDocParser, and mark it as unsupported.\r\n   * Use {@link TSDocConfiguration.setSupportForTag} to mark it as supported.\r\n   *\r\n   * @remarks\r\n   * If a tag is \"defined\" this means that the parser recognizes it and understands its syntax.\r\n   * Whereas if a tag is \"supported\", this means it is defined AND the application implements the tag.\r\n   */\r\n  public addTagDefinition(tagDefinition: TSDocTagDefinition): void {\r\n    const existingDefinition: TSDocTagDefinition | undefined = this._tagDefinitionsByName.get(\r\n      tagDefinition.tagNameWithUpperCase\r\n    );\r\n\r\n    if (existingDefinition === tagDefinition) {\r\n      return;\r\n    }\r\n\r\n    if (existingDefinition) {\r\n      throw new Error(`A tag is already defined using the name ${existingDefinition.tagName}`);\r\n    }\r\n\r\n    this._tagDefinitions.push(tagDefinition);\r\n    this._tagDefinitionsByName.set(tagDefinition.tagNameWithUpperCase, tagDefinition);\r\n  }\r\n\r\n  /**\r\n   * Calls {@link TSDocConfiguration.addTagDefinition} for a list of definitions,\r\n   * and optionally marks them as supported.\r\n   * @param tagDefinitions - the definitions to be added\r\n   * @param supported - if specified, calls the {@link TSDocConfiguration.setSupportForTag}\r\n   *    method to mark the definitions as supported or unsupported\r\n   */\r\n  public addTagDefinitions(\r\n    tagDefinitions: ReadonlyArray<TSDocTagDefinition>,\r\n    supported?: boolean | undefined\r\n  ): void {\r\n    for (const tagDefinition of tagDefinitions) {\r\n      this.addTagDefinition(tagDefinition);\r\n\r\n      if (supported !== undefined) {\r\n        this.setSupportForTag(tagDefinition, supported);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns true if the tag is supported in this configuration.\r\n   */\r\n  public isTagSupported(tagDefinition: TSDocTagDefinition): boolean {\r\n    this._requireTagToBeDefined(tagDefinition);\r\n    return this._supportedTagDefinitions.has(tagDefinition);\r\n  }\r\n\r\n  /**\r\n   * Specifies whether the tag definition is supported in this configuration.\r\n   * The parser may issue warnings for unsupported tags.\r\n   *\r\n   * @remarks\r\n   * If a tag is \"defined\" this means that the parser recognizes it and understands its syntax.\r\n   * Whereas if a tag is \"supported\", this means it is defined AND the application implements the tag.\r\n   *\r\n   * This function automatically sets {@link TSDocValidationConfiguration.reportUnsupportedTags}\r\n   * to true.\r\n   */\r\n  public setSupportForTag(tagDefinition: TSDocTagDefinition, supported: boolean): void {\r\n    this._requireTagToBeDefined(tagDefinition);\r\n    if (supported) {\r\n      this._supportedTagDefinitions.add(tagDefinition);\r\n    } else {\r\n      this._supportedTagDefinitions.delete(tagDefinition);\r\n    }\r\n\r\n    this.validation.reportUnsupportedTags = true;\r\n  }\r\n\r\n  /**\r\n   * Specifies whether the tag definition is supported in this configuration.\r\n   * This operation sets {@link TSDocValidationConfiguration.reportUnsupportedTags} to `true`.\r\n   *\r\n   * @remarks\r\n   * The parser may issue warnings for unsupported tags.\r\n   * If a tag is \"defined\" this means that the parser recognizes it and understands its syntax.\r\n   * Whereas if a tag is \"supported\", this means it is defined AND the application implements the tag.\r\n   */\r\n  public setSupportForTags(tagDefinitions: ReadonlyArray<TSDocTagDefinition>, supported: boolean): void {\r\n    for (const tagDefinition of tagDefinitions) {\r\n      this.setSupportForTag(tagDefinition, supported);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Assigns the `supportedHtmlElements` property, replacing any previous elements.\r\n   * This operation sets {@link TSDocValidationConfiguration.reportUnsupportedHtmlElements} to `true`.\r\n   */\r\n  public setSupportedHtmlElements(htmlTags: string[]): void {\r\n    this._supportedHtmlElements.clear();\r\n    this._validation.reportUnsupportedHtmlElements = true;\r\n    for (const htmlTag of htmlTags) {\r\n      this._supportedHtmlElements.add(htmlTag);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns true if the html element is supported in this configuration.\r\n   */\r\n  public isHtmlElementSupported(htmlTag: string): boolean {\r\n    return this._supportedHtmlElements.has(htmlTag);\r\n  }\r\n\r\n  /**\r\n   * Returns true if the specified {@link TSDocMessageId} string is implemented by this release of the TSDoc parser.\r\n   * This can be used to detect misspelled identifiers.\r\n   *\r\n   * @privateRemarks\r\n   *\r\n   * Why this API is associated with TSDocConfiguration:  In the future, if we enable support for custom extensions\r\n   * of the TSDoc parser, we may provide a way to register custom message identifiers.\r\n   */\r\n  public isKnownMessageId(messageId: TSDocMessageId | string): boolean {\r\n    return allTsdocMessageIdsSet.has(messageId);\r\n  }\r\n\r\n  /**\r\n   * Returns the list of {@link TSDocMessageId} strings that are implemented by this release of the TSDoc parser.\r\n   *\r\n   * @privateRemarks\r\n   *\r\n   * Why this API is associated with TSDocConfiguration:  In the future, if we enable support for custom extensions\r\n   * of the TSDoc parser, we may provide a way to register custom message identifiers.\r\n   */\r\n  public get allTsdocMessageIds(): ReadonlyArray<TSDocMessageId> {\r\n    return allTsdocMessageIds as ReadonlyArray<TSDocMessageId>;\r\n  }\r\n\r\n  private _requireTagToBeDefined(tagDefinition: TSDocTagDefinition): void {\r\n    const matching: TSDocTagDefinition | undefined = this._tagDefinitionsByName.get(\r\n      tagDefinition.tagNameWithUpperCase\r\n    );\r\n    if (matching) {\r\n      if (matching === tagDefinition) {\r\n        return;\r\n      }\r\n    }\r\n    throw new Error('The specified TSDocTagDefinition is not defined for this TSDocConfiguration');\r\n  }\r\n}\r\n"]}