"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Company = exports.CompanyStatus = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("./base.entity");
const user_entity_1 = require("./user.entity");
const whatsapp_connection_entity_1 = require("./whatsapp-connection.entity");
const subscription_entity_1 = require("./subscription.entity");
var CompanyStatus;
(function (CompanyStatus) {
    CompanyStatus["ACTIVE"] = "active";
    CompanyStatus["INACTIVE"] = "inactive";
    CompanyStatus["SUSPENDED"] = "suspended";
    CompanyStatus["TRIAL"] = "trial";
})(CompanyStatus || (exports.CompanyStatus = CompanyStatus = {}));
let Company = class Company extends base_entity_1.BaseEntity {
    name;
    cnpj;
    email;
    phone;
    address;
    website;
    status;
    settings;
    logo;
    agencyId;
    agency;
    users;
    whatsappConnections;
    subscriptions;
    clients;
    get isAgency() {
        return this.agencyId === null;
    }
    get activeUsersCount() {
        return this.users?.filter(user => user.isActive).length || 0;
    }
};
exports.Company = Company;
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255 }),
    __metadata("design:type", String)
], Company.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 14, unique: true }),
    __metadata("design:type", String)
], Company.prototype, "cnpj", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, unique: true }),
    __metadata("design:type", String)
], Company.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20 }),
    __metadata("design:type", String)
], Company.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], Company.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Company.prototype, "website", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: CompanyStatus,
        default: CompanyStatus.TRIAL,
    }),
    __metadata("design:type", String)
], Company.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Company.prototype, "settings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], Company.prototype, "logo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], Company.prototype, "agencyId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Company, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'agencyId' }),
    __metadata("design:type", Company)
], Company.prototype, "agency", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_entity_1.User, (user) => user.company),
    __metadata("design:type", Array)
], Company.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => whatsapp_connection_entity_1.WhatsAppConnection, (connection) => connection.company),
    __metadata("design:type", Array)
], Company.prototype, "whatsappConnections", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => subscription_entity_1.Subscription, (subscription) => subscription.company),
    __metadata("design:type", Array)
], Company.prototype, "subscriptions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Company, (company) => company.agency),
    __metadata("design:type", Array)
], Company.prototype, "clients", void 0);
exports.Company = Company = __decorate([
    (0, typeorm_1.Entity)('companies')
], Company);
//# sourceMappingURL=company.entity.js.map