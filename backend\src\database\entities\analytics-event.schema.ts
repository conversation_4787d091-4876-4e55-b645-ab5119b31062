import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AnalyticsEventDocument = AnalyticsEvent & Document;

export enum EventType {
  // Eventos de usuário
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_CREATED = 'user_created',
  
  // Eventos de conexão WhatsApp
  WHATSAPP_CONNECTED = 'whatsapp_connected',
  WHATSAPP_DISCONNECTED = 'whatsapp_disconnected',
  WHATSAPP_QR_GENERATED = 'whatsapp_qr_generated',
  
  // Eventos de mensagens
  MESSAGE_SENT = 'message_sent',
  MESSAGE_RECEIVED = 'message_received',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_READ = 'message_read',
  MESSAGE_FAILED = 'message_failed',
  BULK_MESSAGE_SENT = 'bulk_message_sent',
  
  // Eventos de contatos
  CONTACT_CREATED = 'contact_created',
  CONTACT_UPDATED = 'contact_updated',
  CONTACT_TAGGED = 'contact_tagged',
  LEAD_CONVERTED = 'lead_converted',
  LEAD_SCORED = 'lead_scored',
  
  // Eventos de automação
  AUTOMATION_TRIGGERED = 'automation_triggered',
  AUTOMATION_COMPLETED = 'automation_completed',
  CHATBOT_INTERACTION = 'chatbot_interaction',
  
  // Eventos de conversão
  CONVERSATION_STARTED = 'conversation_started',
  CONVERSATION_ENDED = 'conversation_ended',
  FIRST_RESPONSE = 'first_response',
  RESPONSE_TIME_MEASURED = 'response_time_measured',
  
  // Eventos de faturamento
  SUBSCRIPTION_CREATED = 'subscription_created',
  SUBSCRIPTION_UPDATED = 'subscription_updated',
  PAYMENT_PROCESSED = 'payment_processed',
  PAYMENT_FAILED = 'payment_failed',
  
  // Eventos de sistema
  API_REQUEST = 'api_request',
  ERROR_OCCURRED = 'error_occurred',
  PERFORMANCE_METRIC = 'performance_metric',
}

export enum EventCategory {
  USER = 'user',
  WHATSAPP = 'whatsapp',
  MESSAGE = 'message',
  CONTACT = 'contact',
  AUTOMATION = 'automation',
  CONVERSION = 'conversion',
  BILLING = 'billing',
  SYSTEM = 'system',
}

export interface EventProperties {
  // Propriedades comuns
  userId?: string;
  connectionId?: string;
  contactPhone?: string;
  messageId?: string;
  
  // Propriedades específicas de mensagens
  messageType?: string;
  messageDirection?: string;
  responseTime?: number;
  isAutomated?: boolean;
  
  // Propriedades de contatos
  contactSource?: string;
  leadScore?: number;
  leadStage?: string;
  tags?: string[];
  
  // Propriedades de automação
  automationId?: string;
  flowStep?: string;
  aiProvider?: string;
  
  // Propriedades de conversão
  conversionType?: string;
  conversionValue?: number;
  funnelStage?: string;
  
  // Propriedades de performance
  duration?: number;
  errorCode?: string;
  errorMessage?: string;
  
  // Propriedades customizadas
  [key: string]: any;
}

@Schema({ timestamps: true })
export class AnalyticsEvent {
  @Prop({ required: true, type: String, enum: EventType })
  type: EventType;

  @Prop({ required: true, type: String, enum: EventCategory })
  category: EventCategory;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Company' })
  companyId: Types.ObjectId;

  @Prop({ type: String })
  userId?: string;

  @Prop({ type: String })
  sessionId?: string;

  @Prop({ type: Object, default: {} })
  properties: EventProperties;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  // Campos para agregação
  @Prop({ type: String })
  date: string; // YYYY-MM-DD

  @Prop({ type: String })
  hour: string; // YYYY-MM-DD HH

  @Prop({ type: String })
  month: string; // YYYY-MM

  @Prop({ type: String })
  year: string; // YYYY

  @Prop({ type: Number })
  dayOfWeek: number; // 0-6 (Sunday-Saturday)

  @Prop({ type: Number })
  hourOfDay: number; // 0-23

  // Campos para performance
  @Prop({ type: Number })
  value?: number; // Valor numérico para métricas

  @Prop({ type: Number })
  duration?: number; // Duração em milissegundos

  // Geolocalização (se disponível)
  @Prop({ type: String })
  country?: string;

  @Prop({ type: String })
  region?: string;

  @Prop({ type: String })
  city?: string;

  @Prop({ type: String })
  timezone?: string;

  // User Agent (para eventos web)
  @Prop({ type: String })
  userAgent?: string;

  @Prop({ type: String })
  ipAddress?: string;
}

export const AnalyticsEventSchema = SchemaFactory.createForClass(AnalyticsEvent);

// Índices para performance
AnalyticsEventSchema.index({ companyId: 1, type: 1, timestamp: -1 });
AnalyticsEventSchema.index({ companyId: 1, category: 1, timestamp: -1 });
AnalyticsEventSchema.index({ companyId: 1, date: 1 });
AnalyticsEventSchema.index({ companyId: 1, month: 1 });
AnalyticsEventSchema.index({ companyId: 1, userId: 1, timestamp: -1 });
AnalyticsEventSchema.index({ timestamp: -1 });
AnalyticsEventSchema.index({ 'properties.connectionId': 1, timestamp: -1 });
AnalyticsEventSchema.index({ 'properties.contactPhone': 1, timestamp: -1 });

// Índice TTL para limpeza automática (opcional - manter dados por 2 anos)
AnalyticsEventSchema.index({ timestamp: 1 }, { expireAfterSeconds: 63072000 }); // 2 anos
