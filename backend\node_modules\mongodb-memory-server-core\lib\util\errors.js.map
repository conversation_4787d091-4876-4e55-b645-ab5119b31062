{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/util/errors.ts"], "names": [], "mappings": ";;;AAAA,mCAA4C;AAE5C,MAAa,UAAW,SAAQ,KAAK;IACnC,YACS,YAAsB,EACtB,QAAgB;QAEvB,KAAK,CACH,mCAAmC,QAAQ,wBAAwB,YAAY,CAAC,IAAI,CAClF,GAAG,CACJ,MAAM;YACL,kHAAkH;YAClH,+JAA+J,CAClK,CAAC;QATK,iBAAY,GAAZ,YAAY,CAAU;QACtB,aAAQ,GAAR,QAAQ,CAAQ;IASzB,CAAC;CACF;AAbD,gCAaC;AAED,MAAa,0BAA2B,SAAQ,KAAK;IACnD,YAAmB,MAAc;QAC/B,KAAK,CAAC,6BAA6B,MAAM,GAAG,CAAC,CAAC;QAD7B,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,2BAA4B,SAAQ,KAAK;IACpD,YACS,YAAqB,EACrB,IAAY;QAEnB,KAAK,CACH,uBAAuB,IAAI,uCACzB,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAC9B,EAAE,CACH,CAAC;QAPK,iBAAY,GAAZ,YAAY,CAAS;QACrB,SAAI,GAAJ,IAAI,CAAQ;IAOrB,CAAC;CACF;AAXD,kEAWC;AAED,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAmB,QAAgB;QACjC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,CAAC,CAAC;QADxB,aAAQ,GAAR,QAAQ,CAAQ;IAEnC,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,wBAAyB,SAAQ,KAAK;IACjD,YACS,IAAY,EACZ,QAAiB;QAExB,KAAK,EAAE,CAAC;QAHD,SAAI,GAAJ,IAAI,CAAQ;QACZ,aAAQ,GAAR,QAAQ,CAAS;QAIxB,IAAI,CAAC,IAAA,yBAAiB,EAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,GAAG,yDAAyD,IAAI,iBAAiB,QAAQ,GAAG,CAAC;QAC3G,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,GAAG,8BAA8B,IAAI,GAAG,CAAC;QACvD,CAAC;IACH,CAAC;CACF;AAbD,4DAaC;AAED,MAAa,0BAA2B,SAAQ,KAAK;IACnD,YACS,OAAe,EACf,KAAc;QAErB,KAAK,CAAC,mBAAmB,OAAO,2CAA2C,KAAK,IAAI,CAAC,CAAC;QAH/E,YAAO,GAAP,OAAO,CAAQ;QACf,UAAK,GAAL,KAAK,CAAS;IAGvB,CAAC;CACF;AAPD,gEAOC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YACS,SAAiB,EACjB,YAAoB;QAE3B,KAAK,CAAC,oCAAoC,SAAS,wBAAwB,YAAY,GAAG,CAAC,CAAC;QAHrF,cAAS,GAAT,SAAS,CAAQ;QACjB,iBAAY,GAAZ,YAAY,CAAQ;IAG7B,CAAC;CACF;AAPD,kDAOC;AAED,MAAa,sBAAuB,SAAQ,KAAK;IAC/C,YAAmB,MAAc;QAC/B,KAAK,CAAC,2DAA2D,MAAM,GAAG,CAAC,CAAC;QAD3D,WAAM,GAAN,MAAM,CAAQ;IAEjC,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,iBAAkB,SAAQ,KAAK;IAC1C,YAAmB,KAAa;QAC9B,KAAK,CAAC,sEAAsE,KAAK,IAAI,CAAC,CAAC;QADtE,UAAK,GAAL,KAAK,CAAQ;IAEhC,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C;QACE,KAAK,CAAC,gDAAgD,CAAC,CAAC;IAC1D,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,KAAK;IAC3C;QACE,KAAK,CAAC,+CAA+C,CAAC,CAAC;IACzD,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,4BAA6B,SAAQ,KAAK;IACrD,YAAmB,IAAY;QAC7B,KAAK,CAAC,SAAS,IAAI,uEAAuE,CAAC,CAAC;QAD3E,SAAI,GAAJ,IAAI,CAAQ;IAE/B,CAAC;CACF;AAJD,oEAIC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YACS,IAAY,EACZ,QAAgB,EAAE;QAEzB,KAAK,CAAC,sBAAsB,IAAI,wBAAwB,KAAK,EAAE,CAAC,CAAC;QAH1D,SAAI,GAAJ,IAAI,CAAQ;QACZ,UAAK,GAAL,KAAK,CAAa;IAG3B,CAAC;CACF;AAPD,kDAOC;AAED;;GAEG;AACH,MAAa,sBAAuB,SAAQ,KAAK;IAC/C;QACE,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC3C,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAmB,KAAa;QAC9B,KAAK,CAAC,6DAA6D,KAAK,IAAI,CAAC,CAAC;QAD7D,UAAK,GAAL,KAAK,CAAQ;IAEhC,CAAC;CACF;AAJD,oDAIC;AAED,MAAa,sBAAuB,SAAQ,KAAK;IAC/C,YAAmB,GAAW;QAC5B,KAAK,CAAC,aAAa,GAAG,+BAA+B,CAAC,CAAC;QADtC,QAAG,GAAH,GAAG,CAAQ;IAE9B,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,iBAAkB,SAAQ,KAAK;IAC1C,YACS,IAAY,EACZ,KAAc;QAErB,KAAK,EAAE,CAAC;QAHD,SAAI,GAAJ,IAAI,CAAQ;QACZ,UAAK,GAAL,KAAK,CAAS;QAGrB,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,aAAa,IAAI,0BAA0B,QAAQ,EAAE,CAAC;IACvE,CAAC;CACF;AATD,8CASC;AAED,MAAa,gCAAiC,SAAQ,KAAK;IACzD,YACS,IAAY,EACZ,iBAAyB,EACzB,kBAA0B,EAC1B,KAAc;QAErB,KAAK,EAAE,CAAC;QALD,SAAI,GAAJ,IAAI,CAAQ;QACZ,sBAAiB,GAAjB,iBAAiB,CAAQ;QACzB,uBAAkB,GAAlB,kBAAkB,CAAQ;QAC1B,UAAK,GAAL,KAAK,CAAS;QAIrB,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,GAAG,sBAAsB,iBAAiB,2BAA2B,IAAI,2BAA2B,kBAAkB,IAAI,QAAQ,EAAE,CAAC;IACnJ,CAAC;CACF;AAZD,4EAYC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,+EAA+E;IAC/E,YAAY,GAAW;QACrB,KAAK,CAAC,GAAG,CAAC,CAAC;IACb,CAAC;CACF;AALD,kDAKC;AAED;;GAEG;AACH,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,IAAmB,EAAE,MAAqB;QACpD,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,OAAO,GAAG,2CAA2C,IAAI,iBAAiB,MAAM,GAAG,CAAC;QAEzF,IAAI,MAAM,IAAI,QAAQ,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO;gBACV,qJAAqJ,CAAC;QAC1J,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC;YAC7D,IAAI,CAAC,OAAO;gBACV,qMAAqM,CAAC;QAC1M,CAAC;IACH,CAAC;CACF;AAhBD,oDAgBC;AAED;;GAEG;AACH,MAAa,mBAAoB,SAAQ,KAAK;IAC5C,YAAmB,OAAe;QAChC,KAAK,CAAC,2DAA2D,OAAO,IAAI,CAAC,CAAC;QAD7D,YAAO,GAAP,OAAO,CAAQ;IAElC,CAAC;CACF;AAJD,kDAIC;AAED;;GAEG;AACH,MAAa,aAAc,SAAQ,KAAK;IACtC,YACS,GAAW,EACX,GAAW;QAElB,KAAK,CAAC,6BAA6B,GAAG,iBAAiB,GAAG,EAAE,CAAC,CAAC;QAHvD,QAAG,GAAH,GAAG,CAAQ;QACX,QAAG,GAAH,GAAG,CAAQ;IAGpB,CAAC;CACF;AAPD,sCAOC;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,KAAK;IAC3C,YACS,MAAc,EACd,OAAiB;QAExB,KAAK,CAAC,8BAA8B,MAAM,iBAAiB,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAH5E,WAAM,GAAN,MAAM,CAAQ;QACd,YAAO,GAAP,OAAO,CAAU;IAG1B,CAAC;CACF;AAPD,gDAOC;AAED,wCAAwC;AACxC,MAAa,eAAgB,SAAQ,KAAK;CAAG;AAA7C,0CAA6C"}