import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { TrackingService } from '../services/tracking.service';
export declare class AnalyticsInterceptor implements NestInterceptor {
    private trackingService;
    private readonly logger;
    constructor(trackingService: TrackingService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
}
export declare class AuthAnalyticsInterceptor implements NestInterceptor {
    private trackingService;
    private readonly logger;
    constructor(trackingService: TrackingService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private generateSessionId;
}
export declare class WhatsAppAnalyticsInterceptor implements NestInterceptor {
    private trackingService;
    private readonly logger;
    constructor(trackingService: TrackingService);
    intercept(context: ExecutionContext, next: CallHandler): Observable<any>;
    private extractConnectionId;
}
export declare class ContactAnalyticsInterceptor implements NestInterceptor {
    private trackingService;
    private readonly logger;
    constructor(trackingService: TrackingService);
    intercept(context: ExecutionContext, next: <PERSON><PERSON><PERSON><PERSON>): Observable<any>;
    private extractContactId;
}
