import { AutomationType, TriggerType, ActionType, TriggerCondition, ActionStep, FlowStep } from '../../../database/entities/automation.schema';
export declare class TriggerConditionDto implements TriggerCondition {
    type: TriggerType;
    keywords?: string[];
    schedule?: {
        time: string;
        days: number[];
        timezone: string;
    };
    inactivityMinutes?: number;
    webhookUrl?: string;
    customConditions?: Record<string, any>;
}
export declare class ActionStepDto implements ActionStep {
    id: string;
    type: ActionType;
    name: string;
    description?: string;
    config: Record<string, any>;
    nextStepId?: string;
    conditions?: Array<{
        field: string;
        operator: 'equals' | 'contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than';
        value: any;
        nextStepId: string;
    }>;
    delay?: number;
}
export declare class FlowStepDto implements FlowStep {
    id: string;
    name: string;
    type: 'message' | 'condition' | 'action' | 'ai' | 'human_handoff';
    position: {
        x: number;
        y: number;
    };
    config: {
        message?: {
            type: 'text' | 'image' | 'audio' | 'video' | 'document';
            content?: string;
            mediaUrl?: string;
            buttons?: Array<{
                id: string;
                text: string;
                nextStepId?: string;
            }>;
        };
        condition?: {
            field: string;
            operator: string;
            value: any;
            trueStepId?: string;
            falseStepId?: string;
        };
        action?: {
            type: ActionType;
            config: Record<string, any>;
        };
        ai?: {
            provider: 'openai' | 'google' | 'anthropic';
            model: string;
            prompt: string;
            maxTokens?: number;
            temperature?: number;
        };
    };
    nextSteps?: string[];
}
export declare class AutomationSettingsDto {
    isActive?: boolean;
    priority?: number;
    maxExecutionsPerContact?: number;
    cooldownMinutes?: number;
    businessHoursOnly?: boolean;
    businessHours?: {
        start: string;
        end: string;
        timezone: string;
        days: number[];
    };
    allowedConnections?: string[];
    excludedContacts?: string[];
    tags?: string[];
}
export declare class CreateAutomationDto {
    name: string;
    description?: string;
    type: AutomationType;
    trigger: TriggerConditionDto;
    actions?: ActionStepDto[];
    flowSteps?: FlowStepDto[];
    startStepId?: string;
    settings?: AutomationSettingsDto;
    isTemplate?: boolean;
    templateCategory?: string;
    metadata?: Record<string, any>;
}
