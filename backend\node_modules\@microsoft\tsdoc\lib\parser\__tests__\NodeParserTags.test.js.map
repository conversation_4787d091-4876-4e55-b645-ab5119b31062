{"version": 3, "file": "NodeParserTags.test.js", "sourceRoot": "", "sources": ["../../../src/parser/__tests__/NodeParserTags.test.ts"], "names": [], "mappings": "AAAA,4FAA4F;AAC5F,2DAA2D;AAE3D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAE5C,IAAI,CAAC,kCAAkC,EAAE;IACvC,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChG,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kCAAkC,EAAE;IACvC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjE,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kCAAkC,EAAE;IACvC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,qBAAqB,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACvG,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,kCAAkC,EAAE;IACvC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACtF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,mCAAmC,EAAE;IACxC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClF,CAAC;IACF,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,sBAAsB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACjG,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oCAAoC,EAAE;IACzC,WAAW,CAAC,+BAA+B,CACzC,CAAC,KAAK,EAAE,8CAA8C,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1E,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,IAAI,CAAC,oCAAoC,EAAE;IACzC,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5F,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5F,WAAW,CAAC,+BAA+B,CAAC,CAAC,KAAK,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACxF,CAAC,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TestHelpers } from './TestHelpers';\r\n\r\ntest('00 Block tags: positive examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * @one ', ' * @two', ' */'].join('\\n'));\r\n});\r\n\r\ntest('01 Block tags: negative examples', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * @ one ', ' * +@two ', ' * @two+ ', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('02 Inline tags: simple, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@one} ', ' * {@two } ', ' * {@three}{@four} ', ' * {@five ', ' *   } ', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('03 Inline tags: simple, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@ one} ', ' * {@two~} ', ' * { @three} ', ' * {@four', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('04 Inline tags: complex, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@one some content}', ' * {@two multi', ' * line}', ' */'].join('\\n')\r\n  );\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@three @taglike}', ' */'].join('\\n'));\r\n});\r\n\r\ntest('05 Inline tags: escaping, positive', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(\r\n    ['/**', ' * {@one left \\\\{ right \\\\} backslash \\\\\\\\ }', ' */'].join('\\n')\r\n  );\r\n});\r\n\r\ntest('06 Inline tags: escaping, negative', () => {\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@one curly\\\\}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * {@two curly{}}', ' */'].join('\\n'));\r\n  TestHelpers.parseAndMatchNodeParserSnapshot(['/**', ' * three: }', ' */'].join('\\n'));\r\n});\r\n"]}