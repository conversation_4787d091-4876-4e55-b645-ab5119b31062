"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsReportResponse = exports.PerformanceAnalytics = exports.UserActivityAnalytics = exports.ConversationAnalytics = exports.DashboardMetrics = exports.AnalyticsMetric = exports.MetricDataPoint = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class MetricDataPoint {
    timestamp;
    value;
    previousValue;
    changePercent;
    metadata;
}
exports.MetricDataPoint = MetricDataPoint;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp do ponto de dados',
        example: '2023-12-01T00:00:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], MetricDataPoint.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valor da métrica',
        example: 150,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], MetricDataPoint.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Valor do período anterior (para comparação)',
        example: 120,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], MetricDataPoint.prototype, "previousValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Percentual de mudança',
        example: 25.0,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], MetricDataPoint.prototype, "changePercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Metadados adicionais',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], MetricDataPoint.prototype, "metadata", void 0);
class AnalyticsMetric {
    name;
    value;
    previousValue;
    changePercent;
    trend;
    timeSeries;
    unit;
    format;
}
exports.AnalyticsMetric = AnalyticsMetric;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da métrica',
        example: 'messages_sent',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AnalyticsMetric.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valor atual',
        example: 1250,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AnalyticsMetric.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Valor do período anterior',
        example: 980,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AnalyticsMetric.prototype, "previousValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Percentual de mudança',
        example: 27.55,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], AnalyticsMetric.prototype, "changePercent", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tendência (up, down, stable)',
        example: 'up',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AnalyticsMetric.prototype, "trend", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Série temporal de dados',
        type: [MetricDataPoint],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], AnalyticsMetric.prototype, "timeSeries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unidade da métrica',
        example: 'count',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AnalyticsMetric.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Formato de exibição',
        example: 'number',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AnalyticsMetric.prototype, "format", void 0);
class DashboardMetrics {
    messagesSent;
    messagesReceived;
    deliveryRate;
    readRate;
    averageResponseTime;
    newContacts;
    leadsConverted;
    conversionRate;
    activeConnections;
    activeUsers;
}
exports.DashboardMetrics = DashboardMetrics;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de mensagens enviadas',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "messagesSent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de mensagens recebidas',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "messagesReceived", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de entrega',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "deliveryRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de leitura',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "readRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tempo médio de resposta (segundos)',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "averageResponseTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Novos contatos',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "newContacts", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Leads convertidos',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "leadsConverted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de conversão (%)',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "conversionRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conexões ativas',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "activeConnections", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuários ativos',
        type: AnalyticsMetric,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", AnalyticsMetric)
], DashboardMetrics.prototype, "activeUsers", void 0);
class ConversationAnalytics {
    totalConversations;
    activeConversations;
    averageConversationDuration;
    averageMessagesPerConversation;
    firstResponseRate;
    averageFirstResponseTime;
}
exports.ConversationAnalytics = ConversationAnalytics;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de conversas',
        example: 450,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "totalConversations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conversas ativas',
        example: 120,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "activeConversations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tempo médio de conversa (minutos)',
        example: 15.5,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "averageConversationDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mensagens por conversa (média)',
        example: 8.2,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "averageMessagesPerConversation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de primeira resposta (%)',
        example: 85.5,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "firstResponseRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tempo médio de primeira resposta (minutos)',
        example: 12.3,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], ConversationAnalytics.prototype, "averageFirstResponseTime", void 0);
class UserActivityAnalytics {
    activeToday;
    activeThisWeek;
    activeThisMonth;
    averageSessionDuration;
    activityByHour;
    activityByDayOfWeek;
}
exports.UserActivityAnalytics = UserActivityAnalytics;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuários ativos hoje',
        example: 25,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserActivityAnalytics.prototype, "activeToday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuários ativos esta semana',
        example: 45,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserActivityAnalytics.prototype, "activeThisWeek", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuários ativos este mês',
        example: 78,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserActivityAnalytics.prototype, "activeThisMonth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tempo médio de sessão (minutos)',
        example: 45.2,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserActivityAnalytics.prototype, "averageSessionDuration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Atividade por hora do dia',
        type: [MetricDataPoint],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], UserActivityAnalytics.prototype, "activityByHour", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Atividade por dia da semana',
        type: [MetricDataPoint],
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Array)
], UserActivityAnalytics.prototype, "activityByDayOfWeek", void 0);
class PerformanceAnalytics {
    averageApiResponseTime;
    errorRate;
    uptime;
    requestsPerMinute;
    activeWebSocketConnections;
}
exports.PerformanceAnalytics = PerformanceAnalytics;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tempo médio de resposta da API (ms)',
        example: 150,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PerformanceAnalytics.prototype, "averageApiResponseTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Taxa de erro (%)',
        example: 0.5,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PerformanceAnalytics.prototype, "errorRate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Uptime (%)',
        example: 99.9,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PerformanceAnalytics.prototype, "uptime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Requests por minuto',
        example: 1250,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PerformanceAnalytics.prototype, "requestsPerMinute", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conexões WebSocket ativas',
        example: 85,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PerformanceAnalytics.prototype, "activeWebSocketConnections", void 0);
class AnalyticsReportResponse {
    dashboard;
    conversations;
    userActivity;
    performance;
    period;
    generatedAt;
}
exports.AnalyticsReportResponse = AnalyticsReportResponse;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Métricas do dashboard',
        type: DashboardMetrics,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", DashboardMetrics)
], AnalyticsReportResponse.prototype, "dashboard", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Analytics de conversas',
        type: ConversationAnalytics,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", ConversationAnalytics)
], AnalyticsReportResponse.prototype, "conversations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Analytics de atividade de usuários',
        type: UserActivityAnalytics,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", UserActivityAnalytics)
], AnalyticsReportResponse.prototype, "userActivity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Analytics de performance',
        type: PerformanceAnalytics,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", PerformanceAnalytics)
], AnalyticsReportResponse.prototype, "performance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Período do relatório',
        example: { start: '2023-12-01T00:00:00Z', end: '2023-12-07T23:59:59Z' },
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Object)
], AnalyticsReportResponse.prototype, "period", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp de geração',
        example: '2023-12-08T10:30:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], AnalyticsReportResponse.prototype, "generatedAt", void 0);
//# sourceMappingURL=analytics-response.dto.js.map