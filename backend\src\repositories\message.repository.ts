import { Message, MessageType, MessageDirection, MessageStatus, Prisma } from '@prisma/client'
import prisma from '../lib/prisma'

export interface CreateMessageData {
  messageId: string
  conversationId?: string
  whatsappConnectionId: string
  contactPhone: string
  contactId?: string
  content: any
  type?: MessageType
  direction: MessageDirection
  status?: MessageStatus
  timestamp: Date
  readAt?: Date
  deliveredAt?: Date
  metadata?: any
  companyId: string
  userId?: string
}

export interface UpdateMessageData {
  conversationId?: string
  contactId?: string
  content?: any
  status?: MessageStatus
  readAt?: Date
  deliveredAt?: Date
  metadata?: any
}

export interface MessageFilters {
  companyId?: string
  whatsappConnectionId?: string
  contactPhone?: string
  contactId?: string
  conversationId?: string
  type?: MessageType
  direction?: MessageDirection
  status?: MessageStatus
  userId?: string
  startDate?: Date
  endDate?: Date
  search?: string
}

export class MessageRepository {
  async create(data: CreateMessageData): Promise<Message> {
    return prisma.message.create({
      data,
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      }
    })
  }

  async findById(id: string): Promise<Message | null> {
    return prisma.message.findUnique({
      where: { id },
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      }
    })
  }

  async findByMessageId(messageId: string): Promise<Message | null> {
    return prisma.message.findUnique({
      where: { messageId },
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      }
    })
  }

  async update(id: string, data: UpdateMessageData): Promise<Message> {
    return prisma.message.update({
      where: { id },
      data,
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      }
    })
  }

  async delete(id: string): Promise<Message> {
    return prisma.message.delete({
      where: { id }
    })
  }

  async findMany(filters: MessageFilters = {}, page = 1, limit = 50): Promise<{
    messages: Message[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.MessageWhereInput = {}

    if (filters.companyId) {
      where.companyId = filters.companyId
    }

    if (filters.whatsappConnectionId) {
      where.whatsappConnectionId = filters.whatsappConnectionId
    }

    if (filters.contactPhone) {
      where.contactPhone = filters.contactPhone
    }

    if (filters.contactId) {
      where.contactId = filters.contactId
    }

    if (filters.conversationId) {
      where.conversationId = filters.conversationId
    }

    if (filters.type) {
      where.type = filters.type
    }

    if (filters.direction) {
      where.direction = filters.direction
    }

    if (filters.status) {
      where.status = filters.status
    }

    if (filters.userId) {
      where.userId = filters.userId
    }

    if (filters.startDate || filters.endDate) {
      where.timestamp = {}
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate
      }
    }

    if (filters.search) {
      where.OR = [
        { contactPhone: { contains: filters.search, mode: 'insensitive' } },
        { 
          content: {
            path: ['text'],
            string_contains: filters.search
          }
        }
      ]
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        include: {
          company: true,
          whatsappConnection: true,
          contact: true,
          conversation: true,
          user: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          timestamp: 'desc'
        }
      }),
      prisma.message.count({ where })
    ])

    return {
      messages,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async findConversation(
    contactPhone: string, 
    whatsappConnectionId: string, 
    page = 1, 
    limit = 50
  ): Promise<{
    messages: Message[]
    total: number
    totalPages: number
  }> {
    const where: Prisma.MessageWhereInput = {
      contactPhone,
      whatsappConnectionId
    }

    const [messages, total] = await Promise.all([
      prisma.message.findMany({
        where,
        include: {
          company: true,
          whatsappConnection: true,
          contact: true,
          conversation: true,
          user: true
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          timestamp: 'asc'
        }
      }),
      prisma.message.count({ where })
    ])

    return {
      messages,
      total,
      totalPages: Math.ceil(total / limit)
    }
  }

  async updateStatus(id: string, status: MessageStatus): Promise<Message> {
    const updateData: UpdateMessageData = { status }

    if (status === MessageStatus.DELIVERED) {
      updateData.deliveredAt = new Date()
    } else if (status === MessageStatus.READ) {
      updateData.readAt = new Date()
      updateData.deliveredAt = updateData.deliveredAt || new Date()
    }

    return prisma.message.update({
      where: { id },
      data: updateData,
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      }
    })
  }

  async markAsRead(messageIds: string[]): Promise<number> {
    const result = await prisma.message.updateMany({
      where: {
        id: { in: messageIds },
        status: { not: MessageStatus.READ }
      },
      data: {
        status: MessageStatus.READ,
        readAt: new Date()
      }
    })

    return result.count
  }

  async getStats(companyId: string, startDate?: Date, endDate?: Date): Promise<{
    total: number
    sent: number
    received: number
    delivered: number
    read: number
    failed: number
  }> {
    const where: Prisma.MessageWhereInput = { companyId }

    if (startDate || endDate) {
      where.timestamp = {}
      if (startDate) {
        where.timestamp.gte = startDate
      }
      if (endDate) {
        where.timestamp.lte = endDate
      }
    }

    const [total, sent, received, delivered, read, failed] = await Promise.all([
      prisma.message.count({ where }),
      prisma.message.count({ 
        where: { ...where, direction: MessageDirection.OUTBOUND } 
      }),
      prisma.message.count({ 
        where: { ...where, direction: MessageDirection.INBOUND } 
      }),
      prisma.message.count({ 
        where: { ...where, status: MessageStatus.DELIVERED } 
      }),
      prisma.message.count({ 
        where: { ...where, status: MessageStatus.READ } 
      }),
      prisma.message.count({ 
        where: { ...where, status: MessageStatus.FAILED } 
      })
    ])

    return {
      total,
      sent,
      received,
      delivered,
      read,
      failed
    }
  }

  async findLatestByContact(contactPhone: string, whatsappConnectionId: string): Promise<Message | null> {
    return prisma.message.findFirst({
      where: {
        contactPhone,
        whatsappConnectionId
      },
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      },
      orderBy: {
        timestamp: 'desc'
      }
    })
  }

  async countUnread(companyId: string, whatsappConnectionId?: string): Promise<number> {
    const where: Prisma.MessageWhereInput = {
      companyId,
      direction: MessageDirection.INBOUND,
      status: { not: MessageStatus.READ }
    }

    if (whatsappConnectionId) {
      where.whatsappConnectionId = whatsappConnectionId
    }

    return prisma.message.count({ where })
  }

  async findPendingMessages(companyId: string): Promise<Message[]> {
    return prisma.message.findMany({
      where: {
        companyId,
        status: MessageStatus.PENDING,
        direction: MessageDirection.OUTBOUND
      },
      include: {
        company: true,
        whatsappConnection: true,
        contact: true,
        conversation: true,
        user: true
      },
      orderBy: {
        timestamp: 'asc'
      }
    })
  }
}
