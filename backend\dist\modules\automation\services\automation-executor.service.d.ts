import { Model } from 'mongoose';
import { AutomationDocument, TriggerType } from '../../../database/entities/automation.schema';
import { AutomationExecutionDocument } from '../../../database/entities/automation-execution.schema';
import { TrackingService } from '../../analytics/services/tracking.service';
import { WhatsAppService } from '../../whatsapp/whatsapp.service';
import { ContactsService } from '../../messages/services/contacts.service';
import { MessagesService } from '../../messages/services/messages.service';
export interface TriggerContext {
    type: TriggerType;
    contactPhone: string;
    connectionId: string;
    companyId: string;
    messageId?: string;
    messageContent?: string;
    userId?: string;
    metadata?: Record<string, any>;
}
export declare class AutomationExecutorService {
    private automationModel;
    private executionModel;
    private trackingService;
    private whatsappService;
    private contactsService;
    private messagesService;
    private readonly logger;
    constructor(automationModel: Model<AutomationDocument>, executionModel: Model<AutomationExecutionDocument>, trackingService: TrackingService, whatsappService: WhatsAppService, contactsService: ContactsService, messagesService: MessagesService);
    triggerAutomations(context: TriggerContext): Promise<void>;
    executeAutomation(automation: AutomationDocument, triggerContext: TriggerContext): Promise<AutomationExecutionDocument>;
    retryExecution(executionId: string): Promise<void>;
    private findTriggeredAutomations;
    private executeSteps;
    private executeActionSteps;
    private executeFlowSteps;
    private executeAction;
    private executeFlowStep;
    private executeSendMessage;
    private executeAddTag;
    private executeRemoveTag;
    private executeUpdateContact;
    private executeWait;
    private evaluateCondition;
    private executeAIInteraction;
    private executeHumanHandoff;
    private generateExecutionId;
    private extractKeywords;
    private getFieldValue;
    private delay;
}
