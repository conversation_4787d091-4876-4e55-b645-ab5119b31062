"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const logger = new common_1.Logger('Bootstrap');
    const apiPrefix = configService.get('apiPrefix');
    app.setGlobalPrefix(apiPrefix);
    app.enableCors({
        origin: configService.get('cors.origin'),
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    if (configService.get('nodeEnv') === 'development') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('WhatsApp Management Platform API')
            .setDescription('API para plataforma de gestão e automação de WhatsApp')
            .setVersion('1.0')
            .addBearerAuth()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup(`${apiPrefix}/docs`, app, document);
    }
    const port = configService.get('port');
    await app.listen(port);
    logger.log(`🚀 Application is running on: http://localhost:${port}/${apiPrefix}`);
    if (configService.get('nodeEnv') === 'development') {
        logger.log(`📚 Swagger documentation: http://localhost:${port}/${apiPrefix}/docs`);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map