import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { UserRepository } from '../repositories/user.repository'
import { CompanyRepository } from '../repositories/company.repository'
import { UserRole, UserStatus, CompanyStatus } from '@prisma/client'

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  phone?: string
  companyName: string
  cnpj: string
}

export interface AuthResponse {
  user: any
  accessToken: string
  refreshToken: string
}

export class AuthService {
  private userRepository: UserRepository
  private companyRepository: CompanyRepository

  constructor() {
    this.userRepository = new UserRepository()
    this.companyRepository = new CompanyRepository()
  }

  private generateTokens(userId: string) {
    const accessToken = jwt.sign(
      { userId },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
    )

    const refreshToken = jwt.sign(
      { userId },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d' }
    )

    return { accessToken, refreshToken }
  }

  async login(data: LoginRequest): Promise<AuthResponse> {
    const { email, password } = data

    // Buscar usuário por email
    const user = await this.userRepository.findByEmail(email)
    if (!user) {
      throw new Error('Credenciais inválidas')
    }

    // Verificar senha
    const isValidPassword = await bcrypt.compare(password, user.password)
    if (!isValidPassword) {
      throw new Error('Credenciais inválidas')
    }

    // Verificar se usuário está ativo
    if (user.status !== UserStatus.ACTIVE) {
      throw new Error('Usuário inativo')
    }

    // Verificar se empresa está ativa
    if (user.company.status === CompanyStatus.SUSPENDED || user.company.status === CompanyStatus.CANCELLED) {
      throw new Error('Empresa inativa')
    }

    // Gerar tokens
    const { accessToken, refreshToken } = this.generateTokens(user.id)

    // Salvar refresh token
    const refreshTokenExpiry = new Date()
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30)
    await this.userRepository.setRefreshToken(user.id, refreshToken, refreshTokenExpiry)

    // Atualizar último login
    await this.userRepository.updateLastLogin(user.id)

    // Remover senha do retorno
    const { password: _, refreshToken: __, refreshTokenExpiry: ___, ...userWithoutPassword } = user

    return {
      user: userWithoutPassword,
      accessToken,
      refreshToken
    }
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    const { firstName, lastName, email, password, phone, companyName, cnpj } = data

    // Verificar se email já existe
    const existingUser = await this.userRepository.findByEmail(email)
    if (existingUser) {
      throw new Error('Email já está em uso')
    }

    // Verificar se CNPJ já existe
    const existingCompany = await this.companyRepository.findByCnpj(cnpj)
    if (existingCompany) {
      throw new Error('CNPJ já está em uso')
    }

    // Hash da senha
    const hashedPassword = await bcrypt.hash(password, 12)

    // Criar empresa
    const company = await this.companyRepository.create({
      name: companyName,
      cnpj,
      email,
      phone: phone || '',
      status: CompanyStatus.TRIAL
    })

    // Criar usuário
    const user = await this.userRepository.create({
      firstName,
      lastName,
      email,
      password: hashedPassword,
      phone,
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      companyId: company.id
    })

    // Gerar tokens
    const { accessToken, refreshToken } = this.generateTokens(user.id)

    // Salvar refresh token
    const refreshTokenExpiry = new Date()
    refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30)
    await this.userRepository.setRefreshToken(user.id, refreshToken, refreshTokenExpiry)

    // Remover senha do retorno
    const { password: _, refreshToken: __, refreshTokenExpiry: ___, ...userWithoutPassword } = user

    return {
      user: userWithoutPassword,
      accessToken,
      refreshToken
    }
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    try {
      // Verificar se o refresh token é válido
      const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as { userId: string }
      
      // Buscar usuário pelo refresh token
      const user = await this.userRepository.findByRefreshToken(refreshToken)
      if (!user) {
        throw new Error('Refresh token inválido')
      }

      // Verificar se usuário está ativo
      if (user.status !== UserStatus.ACTIVE) {
        throw new Error('Usuário inativo')
      }

      // Gerar novos tokens
      const tokens = this.generateTokens(user.id)

      // Salvar novo refresh token
      const refreshTokenExpiry = new Date()
      refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30)
      await this.userRepository.setRefreshToken(user.id, tokens.refreshToken, refreshTokenExpiry)

      // Remover dados sensíveis do retorno
      const { password: _, refreshToken: __, refreshTokenExpiry: ___, ...userWithoutPassword } = user

      return {
        user: userWithoutPassword,
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken
      }
    } catch (error) {
      throw new Error('Refresh token inválido')
    }
  }

  async logout(userId: string): Promise<void> {
    await this.userRepository.clearRefreshToken(userId)
  }

  async getMe(userId: string): Promise<any> {
    const user = await this.userRepository.findById(userId)
    if (!user) {
      throw new Error('Usuário não encontrado')
    }

    // Remover dados sensíveis
    const { password: _, refreshToken: __, refreshTokenExpiry: ___, ...userWithoutPassword } = user

    return userWithoutPassword
  }

  async verifyToken(token: string): Promise<{ userId: string }> {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET!) as { userId: string }
      return decoded
    } catch (error) {
      throw new Error('Token inválido')
    }
  }

  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findById(userId)
    if (!user) {
      throw new Error('Usuário não encontrado')
    }

    // Verificar senha atual
    const isValidPassword = await bcrypt.compare(currentPassword, user.password)
    if (!isValidPassword) {
      throw new Error('Senha atual incorreta')
    }

    // Hash da nova senha
    const hashedPassword = await bcrypt.hash(newPassword, 12)

    // Atualizar senha
    await this.userRepository.update(userId, { password: hashedPassword })

    // Limpar refresh tokens para forçar novo login
    await this.userRepository.clearRefreshToken(userId)
  }

  async forgotPassword(email: string): Promise<void> {
    const user = await this.userRepository.findByEmail(email)
    if (!user) {
      // Não revelar se o email existe ou não
      return
    }

    // TODO: Implementar envio de email com token de reset
    // Por enquanto, apenas log
    console.log(`Password reset requested for user: ${user.id}`)
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    // TODO: Implementar reset de senha com token
    // Por enquanto, apenas placeholder
    throw new Error('Reset de senha não implementado')
  }
}
